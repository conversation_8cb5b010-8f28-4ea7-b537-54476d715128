//  import css slider
import 'slick-carousel/slick/slick.css'
import 'slick-carousel/slick/slick-theme.css'

import { ThemeProvider } from './components/theme-provider'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { wrapper, useAuth, PageWrapperProps } from '@/components/wrapper'
import i18n from '@/i18n'
import { I18nextProvider } from 'react-i18next'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ApolloProvider } from '@apollo/client'
import { gqlClient } from './lib/gql/apollo-client'
import { Provider } from 'react-redux'
import { store } from '@/redux/store'
import { WagmiProvider } from 'wagmi'
import { configWagmi } from './lib/wagmiClient'
import { darkTheme, lightTheme, Locale, RainbowKitProvider } from '@rainbow-me/rainbowkit'
import { Helmet } from 'react-helmet'
import SolanaProviders from './components/solana-providers'
import TelegramCallbackHandler from './pages/telegram'
import MemeDetailPage from '@pages/detail'
import FuturesDetailPage from '@pages/futures'
import MemeDemoPage from '@pages/meme-demo'
import PriceChart from '@/pages/webview/PriceChart'
import { ToastProvider } from '@/components/toast/ToastContext'
import RangoPage from '@pages/rango'
import DebugPage from '@pages/debug'
import CopyTradingDetailsPage from '@pages/copy-trading/details'
import CopyTradeWalletSettings from '@/pages/copy-trading/wallet-settings'
import FuturesDiscover from './pages/futures-discover'
import HyDemo from './pages/hy-demo'
import { APP_PATH } from './lib/constant'
import CryptoTransactionHistory from './pages/crypto-transaction-history'
import { AssetChartWebView } from '@pages/webview/AssetChartWebView.tsx'
import VaultDetail from './pages/vault-detail'
import CategoryDetailPage from '@pages/category-detail'
import AssetsPage from '@pages/assets'
import PositionPage from '@/pages/position'
import AssetsTokenDetailPage from '@/pages/assets/token'
import AssetsWalletDetailPage from '@/pages/assets/wallet'
import TermsOfUseAndPrivacyPolicyPage from './pages/terms-of-use'
import TermsOfUsePage from './pages/terms-of-use'
import Withdrawal from './pages/withdrawal'
import RangoDemo from './pages/rango-demo'
import Deposit from './pages/crypto-deposit'
import DepositPage from '@pages/deposit'
import { GoogleAuthenticatorPage } from '@pages/settings/google-authenticator.tsx'
import { ResetGoogleAuthenticatorPage } from '@pages/settings/reset-google-authenticator.tsx'
import DepositExternalPage from './pages/deposit/external'
import { TransferPage } from '@pages/transfer'
import CrossChainBridgePage from './pages/cross-chain-bridge'
import CrossChainBridgeStatusPage from './pages/cross-chain-bridge/exchange-status'
import SmartMoneyPage from '@pages/smart-money/smartMoneyPage.tsx'
import GoogleAuth from './pages/google-auth'
import FuturesMarket from './pages/futures-market'
import { MemeDiscoverPage } from '@pages/meme/new-discover.tsx'
import CryptoTransferStatus from './pages/crypto-deposit/crypto-transfer-status'
import { NotificationsPage } from '@pages/notifications'
import { LanguagesSettingsPage } from '@pages/settings/languages.tsx'
import { ColorsSettingsPage } from '@pages/settings/colors-settings.tsx'
import { NotificationsSettingsPage } from '@pages/settings/notifications-settings.tsx'
import { AboutUsPage } from '@pages/settings/about-us.tsx'
import { FundingHistoryPage } from '@pages/settings/funding-history.tsx'
import { BrowsingHistoryPage } from '@pages/settings/browsing-history.tsx'
import { FCMListener } from '@components/common/FCMListener.tsx'
import { registerServiceWorker } from '@/lib/firebase.ts'
import { UserFeedbackPage } from '@pages/settings/user-feedback.tsx'
import { UserFeedbackViewProgressPage } from '@pages/settings/user-feedback-view-progress.tsx'
import FuturesTransfer from './pages/futures-transfer'
import MonitoringPage from '@/pages/monitoring'
import GooglePinCode from './pages/google-auth/GooglePinCode'
import GoogleWhiteList from './pages/google-auth/GoogleWhiteList'
import { UserSettingsFetcher } from '@components/common/UserSettingsFetcher.tsx'
import AllAlerts from './components/futuresDetails/allAlerts'
import FuturesPositionPage from './pages/futures-position'

const appleItunesAppId = import.meta.env.VITE_APPLE_ITUNES_APP_ID

type AppRoute = {
  path: string
  element: React.ReactNode
} & PageWrapperProps

const authPages: AppRoute[] = [
  {
    path: APP_PATH.MEME_TREND,
    element: <></>,
  },
]

const publicPages: AppRoute[] = [
  {
    path: '/',
    element: <Navigate to={APP_PATH.MEME_DISCOVER} replace />,
  },
  {
    path: APP_PATH.MEME_TELEGRAM_AUTH,
    element: <TelegramCallbackHandler />,
  },
  {
    path: APP_PATH.MEME_DISCOVER,
    element: <MemeDiscoverPage />,
    showNotifications: true,
  },
  {
    path: APP_PATH.MEME_NEW_PAIRS_DEMO,
    element: <MemeDemoPage />,
  },
  {
    path: APP_PATH.MEME_TOKEN_DETAIL,
    element: <MemeDetailPage />,
    showHeader: false,
  },
  {
    path: '/hy',
    element: <HyDemo />,
  },
  {
    path: '/rango',
    element: <RangoPage />,
  },
  {
    path: '/debug',
    element: <DebugPage />,
  },
  {
    path: APP_PATH.COPY_TRADING_WALLET_SETTINGS,
    element: <CopyTradeWalletSettings />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.COPY_TRADING_WALLET_SETTINGS + '/:id',
    element: <CopyTradeWalletSettings />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.COPY_TRADING_WALLET_SETTINGS + '/:id',
    element: <CopyTradeWalletSettings />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.WALLET_COPY + '/:address',
    element: <CopyTradingDetailsPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.WEBVIEW_PRICE_CHART,
    element: <PriceChart />,
    isWebview: true,
  },
  {
    path: APP_PATH.WEBVIEW_ASSET_CHART,
    element: <AssetChartWebView />,
    isWebview: true,
  },
  {
    path: APP_PATH.CATEGORY_DETAIL,
    element: <CategoryDetailPage />,
    showHeader: false,
  },
  {
    path: APP_PATH.MEME_SETTINGS_GOOGLE_AUTH,
    element: <GoogleAuthenticatorPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_SETTINGS_RESET_GOOGLE_AUTH,
    element: <ResetGoogleAuthenticatorPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_SETTINGS_LANGUAGE,
    element: <LanguagesSettingsPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_COLORS_SETTINGS,
    element: <ColorsSettingsPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_NOTIFICATION_SETTINGS,
    element: <NotificationsSettingsPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_SETTINGS_ABOUT_US,
    element: <AboutUsPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_SETTINGS_FUNDING_HISTORY,
    element: <FundingHistoryPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_SETTINGS_BROWSING_HISTORY,
    element: <BrowsingHistoryPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_SETTINGS_USER_FEEDBACK,
    element: <UserFeedbackPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_SETTINGS_USER_FEEDBACK_PROGRESS,
    element: <UserFeedbackViewProgressPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_SETTINGS_GOOGLE_AUTH,
    element: <GoogleAuthenticatorPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_SETTINGS_RESET_GOOGLE_AUTH,
    element: <ResetGoogleAuthenticatorPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_SETTINGS_LANGUAGE,
    element: <LanguagesSettingsPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_COLORS_SETTINGS,
    element: <ColorsSettingsPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_NOTIFICATION_SETTINGS,
    element: <NotificationsSettingsPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_SETTINGS_ABOUT_US,
    element: <AboutUsPage />,
    showHeader: false,
    isShowFooter: false,
  },

  {
    path: APP_PATH.MEME_ASSETS,
    element: <AssetsPage />,
    showHeader: false,
  },
  {
    path: APP_PATH.MEME_POSITION + '/:id',
    element: <PositionPage />,
    showHeader: false,
  },
  {
    path: APP_PATH.FUTURES_POSITION + '/:id',
    element: <FuturesPositionPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_ASSETS_TOKEN + '/:address',
    element: <AssetsTokenDetailPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_ASSETS_WALLET + '/:address',
    element: <AssetsWalletDetailPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.TERMS_OF_USE,
    element: <TermsOfUseAndPrivacyPolicyPage />,
    showHeader: false,
  },
  {
    path: APP_PATH.PRIVACY_POLICY,
    element: <TermsOfUseAndPrivacyPolicyPage />,
    showHeader: false,
  },
  {
    path: APP_PATH.TERMS_OF_USE,
    element: <TermsOfUsePage />,
    showHeader: false,
  },
  {
    path: APP_PATH.WITHDRAWAL,
    element: <Withdrawal />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_ASSETS_DEPOSIT + '/:address',
    element: <DepositPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_ASSETS_DEPOSIT_EXTERNAL,
    element: <DepositExternalPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_TRANSFER,
    element: <TransferPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_NOTIFICATIONS,
    element: <NotificationsPage />,
    showHeader: false,
    isShowFooter: false,
    isScrollable: false,
  },
  {
    path: APP_PATH.MEME_NOTIFICATIONS,
    element: <NotificationsPage />,
    showHeader: false,
    isShowFooter: false,
    isScrollable: false,
  },
  {
    path: APP_PATH.MEME_CROSS_CHAIN_BRIDGE,
    element: <CrossChainBridgePage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_CROSS_CHAIN_BRIDGE_STATUS + '/:txId',
    element: <CrossChainBridgeStatusPage />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_SMART_MONEY,
    element: <SmartMoneyPage />,
    isShowBackgroundImage: false,
  },
  {
    path: APP_PATH.MEME_SETTINGS_CONNECT_GOOGLE_AUTH,
    element: <GoogleAuth />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_SETTINGS_VERIFY_GOOGLE_AUTH,
    element: <GooglePinCode />,
    showHeader: false,
    isShowFooter: false,
  },
  {
    path: APP_PATH.MEME_SETTINGS_WHITELIST_GOOGLE_AUTH,
    element: <GoogleWhiteList />,
    showHeader: false,
    isShowFooter: false,
  },

  // crypto
  /**
   * TODO: 🛑 Pages that belong to dex and display bottom nav must add prop isDex = true 🛑
   */
  {
    path: APP_PATH.FUTURES_DISCOVER,
    element: <FuturesDiscover />,
    isDex: true,
    showNotifications: true,
    footerWithNonePadding: true,
    isHorizontalFlip: true,
    bgColor: '#19191E',
  },
  {
    path: APP_PATH.FUTURES + '/:baseCoin?',
    element: <FuturesDetailPage />,
    showHeader: false,
    isDex: true,
  },

  {
    path: APP_PATH.TRANSACTION_HISTORY,
    element: <CryptoTransactionHistory />,
    showHeader: false,
    isHorizontalFlip: true,
    isShowFooter: false,
    isDex: true,
  },
  {
    path: APP_PATH.ALL_ALERTS,
    element: <AllAlerts />,
    showHeader: false,
    isDex: true,
    isShowFooter: false,
    isShowBackgroundImage: false
  },
  {
    path: APP_PATH.FUTURES_MARKET,
    element: <FuturesMarket />,
    showHeader: false,
    isHorizontalFlip: true,
    isDex: true,
    isScrollable: false,
    footerWithNonePadding: true,
  },
  {
    path: '/vault-detail/:address',
    element: <VaultDetail />,
    showHeader: false,
    isShowFooter: false,
    isDex: true,
  },
  {
    path: APP_PATH.CRYPTO_DEPOSIT,
    element: <Deposit />,
    showHeader: false,
    isDex: true,
  },
  {
    path: APP_PATH.CRYPTO_DEPOSIT_STATUS,
    element: <CryptoTransferStatus />,
    showHeader: false,
    isShowFooter: false,
    isDex: true,
  },
  {
    path: '/rango-demo',
    element: <RangoDemo />,
    showHeader: false,
    isDex: true,
  },
  {
    path: APP_PATH.FUTURES_ASSET,
    element: <AssetsPage />,
    isDex: true,
    showHeader: false,
    isScrollable: false,
  },
  {
    path: APP_PATH.FUTURES_ASSET,
    element: <AssetsPage />,
    isDex: true,
    showHeader: false,
    isScrollable: false,
  },
  {
    path: APP_PATH.FUTURES_TRANSFER,
    element: <FuturesTransfer />,
    isDex: true,
    showHeader: false,
  },
  {
    path: APP_PATH.MEME_MONITORING,
    element: <MonitoringPage />,
  },
]
const queryClient = new QueryClient()

function App() {
  return (
    <>
      <Helmet>
        {/* Smart App Banner */}
        <meta name="apple-itunes-app" content={`app-id=${appleItunesAppId}, app-argument=xbitapp://`} />
      </Helmet>
      <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
        <Provider store={store}>
          <WagmiProvider config={configWagmi}>
            <SolanaProviders>
              <ApolloProvider client={gqlClient}>
                <QueryClientProvider client={queryClient}>
                  <I18nextProvider i18n={i18n}>
                    <ToastProvider>
                      <RainbowKitProvider
                        theme={{
                          lightMode: lightTheme(),
                          darkMode: darkTheme(),
                        }}
                        locale={i18n?.language as Locale}
                      >
                        <Router>
                          <Routes>
                            {authPages.map((route) => (
                              <Route
                                key={route.path}
                                {...route}
                                element={wrapper({
                                  children: useAuth(route.element),
                                  showHeader: route?.showHeader,
                                  isWebview: route?.isWebview,
                                })}
                              />
                            ))}
                            {publicPages.map((route) => (
                              <Route
                                key={route.path}
                                {...route}
                                element={wrapper({
                                  children: route.element,
                                  showHeader: route?.showHeader,
                                  isWebview: route?.isWebview,
                                  isHorizontalFlip: route?.isHorizontalFlip,
                                  isShowFooter: route?.isShowFooter,
                                  isShowBackgroundImage: route?.isShowBackgroundImage,
                                  isDex: route?.isDex,
                                  showNotifications: route?.showNotifications,
                                  isScrollable: route?.isScrollable,
                                  footerWithNonePadding: route?.footerWithNonePadding,
                                  bgColor: route?.bgColor,
                                })}
                              />
                            ))}
                            <Route path="*" element={<Navigate to={`/`} replace />} />
                          </Routes>
                          <FCMListener />
                          <UserSettingsFetcher />
                        </Router>
                      </RainbowKitProvider>
                    </ToastProvider>
                  </I18nextProvider>
                </QueryClientProvider>
              </ApolloProvider>
            </SolanaProviders>
          </WagmiProvider>
        </Provider>
      </ThemeProvider>
    </>
  )
}

registerServiceWorker()

export default App
