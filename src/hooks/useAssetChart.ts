import { useQuery } from '@apollo/client'
import { getAssetChart } from '@services/assets.service.ts'
import { AssetChartItemDto, WalletBalanceUnit, WalletDuration } from '@/@generated/gql/graphql-core.ts'
import { useMemo } from 'react'
import dayjs from 'dayjs'

export type ChartItem = {
  timestamp: number
  price: number
  changeAmount: number
  changePercentage: number
}

export interface UseAssetChartOptions {
  walletAddress?: string
  duration: WalletDuration
}

const normalize = (data: AssetChartItemDto[]): ChartItem[] => {
  const firstItemHasPriceIndex = data.findIndex((item) => Number(item.v) > 0)

  // If no item has a price
  if (firstItemHasPriceIndex === -1) {
    return data.map((item) => ({
      timestamp: dayjs(item.t).unix() * 1000,
      price: Number(item.v),
      changeAmount: 0,
      changePercentage: 0,
    }))
  } else {
    const firstItemHasPrice = data[firstItemHasPriceIndex]
    const firstPrice: number = Number(firstItemHasPrice.v)
    return data.map((item, index) => {
      const timestamp = dayjs(item.t).unix() * 1000
      const price: number = Number(item.v)

      const changeAmount: number = index > firstItemHasPriceIndex ? price - firstPrice : 0
      const changePercentage: number =
        index > firstItemHasPriceIndex ? (firstPrice > 0 ? changeAmount / firstPrice : 0) : 0
      return {
        timestamp,
        price,
        changeAmount,
        changePercentage: changePercentage * 100, // Convert to percentage
      }
    })
  }
}

export const useAssetChart = (options: UseAssetChartOptions) => {
  const { walletAddress, duration } = options
  const { data } = useQuery(getAssetChart, {
    variables: {
      input: {
        walletAddress: walletAddress,
        unit: WalletBalanceUnit.Usd,
        duration: duration,
      },
    },
  })
  const { collapsedData, expandedData } = useMemo(() => {
    if (!data) return { collapsedData: [], expandedData: [] }
    const { getAssetChart, getAssetChartPreview } = data

    const collapsedData: ChartItem[] = [...getAssetChartPreview].reverse().map((item) => ({
      timestamp: dayjs(item.t).unix() * 1000,
      price: Number(item.v),
      changeAmount: 0,
      changePercentage: 0,
    }))
    const expandedData: ChartItem[] = normalize([...getAssetChart].reverse())
    return {
      collapsedData,
      expandedData,
    }
  }, [data])

  return { collapsedData, expandedData }
}
