import { useState, useMemo } from "react";
import { useWebSocketChannel } from "@/hooks/hyperliquid/useWebSocketChannel";
import { WebData2 } from '@/types/hyperliquid'
import { useMultiChainWallet } from '@hooks/useMultiChainWallet.ts'
import isEqual from 'lodash/isEqual';
import { TpslOrderType } from '@/components/futuresDetails/trade/types'
import { fixNumber } from "@/lib/utils"
import { _changeTokenAccount } from '@/redux/modules/auth.slice'
import { useIsLoggedInOnArb } from '@/hooks/hyperliquid/useIsLoggedInOnArb'



const tpslOrderType: TpslOrderType[] = ['Take Profit Limit', 'Stop Limit', 'Take Profit Market', 'Stop Market']



function findTpslOrder (coin: string, openOrders: any[]) {
  return openOrders.filter(item => {
    return item.coin === coin && tpslOrderType.some(citem => citem === item.orderType)
  })
}

function removeChildOidsFromArray(a: any) {
  const oidsToRemove = new Set();

  for (const order of a) {
    if (Array.isArray(order.children) && order.children.length > 0) {
      for (const child of order.children) {
        if (child.oid !== undefined) {
          oidsToRemove.add(child.oid);
        }
      }
    }
  }

  return a.filter((order: any) => !oidsToRemove.has(order.oid));
}


export function useWebData2() {
  const [webData2, setWebData2] = useState<WebData2>({});

  const { activeWallet, activeChain  } = useMultiChainWallet({})


  const isLogin = useIsLoggedInOnArb()

  const channel = 'webData2';

  const params = useMemo(() => {
    if (!isLogin) return null;
    return {
      type: 'webData2',
      user: activeWallet.walletId,
    }
  }, [activeWallet, isLogin]);

  useWebSocketChannel(
    channel,
    params,
    (data) => {
      if (!isLogin) return null;
      setWebData2((prev) => {
        return isEqual(prev, data) ? prev : data;
      });
    }
  );

  const openOrders = removeChildOidsFromArray(webData2?.openOrders ?? [])
  

  const assetPositions = webData2?.clearinghouseState?.assetPositions ?? []
  const symbolListCtxs = webData2?.assetCtxs ?? []
  const universe = webData2?.meta?.universe

  const crossMarginSummary = webData2?.clearinghouseState?.crossMarginSummary


  const withdrawable = crossMarginSummary?.accountValue - crossMarginSummary?.totalMarginUsed

  const availableFundRaw = withdrawable ? fixNumber(withdrawable, 2) : 0
  const availableFund = Number(availableFundRaw) > 0 ? availableFundRaw : 0
  
  
  const positions = assetPositions.map((item: any) => {
    const szi = parseFloat(item?.position?.szi as string)
    const side = szi >= 0 ? 'B' : 'A'
    const coin = item?.position.coin
    const coinIndex = universe.findIndex((u: any) => u.name === coin)
    const ctx = symbolListCtxs[coinIndex]
    const TpslOrder = findTpslOrder(coin, openOrders)

    return {
      ...item.position,
      szi: Math.abs(szi),
      side,
      tpPrice: TpslOrder.find(c => c.orderType.includes('Take Profit'))?.triggerPx,
      slPrice: TpslOrder.find(c => c.orderType.includes('Stop'))?.triggerPx,
      markPrice: ctx?.markPx,
      midPrice: ctx?.midPx,
    }
})
  




  return { 
    webData2,
    openOrders,
    positions,
    availableFund,
    symbolListCtxs
  }
}

