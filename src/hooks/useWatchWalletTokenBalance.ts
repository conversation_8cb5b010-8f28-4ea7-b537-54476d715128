import { useEffect, useState } from 'react'
import { useSubscription } from '@/lib/mqtt'
import { WalletTokenBalanceMsg } from '@/types/token.ts'

type useWatchWalletTokenBalanceProps = {
  address: string
  token: string
}

const useWatchWalletTokenBalance = ({
  address,
  token
}:useWatchWalletTokenBalanceProps) => {
  const [walletTokenBalance, setWalletTokenBalance] = useState<WalletTokenBalanceMsg | undefined>(undefined);
  const { message } = useSubscription(`public/wallet_token_balance/${address}/${token}`);

  useEffect(() => {
    const msg = message?.message?.toString()
    console.log({msg})
    if (msg) {
      const data: WalletTokenBalanceMsg = JSON.parse(msg);
      setWalletTokenBalance(data);
    }
  }, [message]);

  return walletTokenBalance;
}

export default useWatchWalletTokenBalance;
