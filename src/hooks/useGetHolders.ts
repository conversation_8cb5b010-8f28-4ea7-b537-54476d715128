import { useQuery } from '@apollo/client'
import { GetHoldersResponse } from '@/types/responses.ts'
import { getHolder } from '@services/tokens.service.ts'
import { HolderInput } from '@/@generated/gql/graphql-core.ts'
import { gqlClient } from '@/lib/gql/apollo-client.ts'

type useGetHoldersProps = {
  input: HolderInput
  skip?: boolean
}

const useGetHolders = (props: useGetHoldersProps) => {
  const { input, skip } = props

  const {data, loading, error} = useQuery<GetHoldersResponse>(getHolder, {
    client: gqlClient,
    skip,
    variables: {
      input
    }
  })

  return {data, error, loading}
}

export default  useGetHolders
