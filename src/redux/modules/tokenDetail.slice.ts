import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import dayjs from 'dayjs'
import { TradeHistoryDTO } from '@/types/tokenDetail'
import { DateSelectedType, DisplayPriceType, SortByCreateAtType, TimeKey } from '@/types/enums'

export interface TimeWheelDateType {
  year?: string
  month?: string
  day?: string
  hour?: string
  minute?: string
}

export interface TokenDetailState {
  startDate: TimeWheelDateType | undefined
  endDate: TimeWheelDateType | undefined
  data: TradeHistoryDTO[]
  sortByCreatedAt: SortByCreateAtType | undefined
  page: number
  hasMore: boolean
  displayDateTimeMode: boolean
  minAmount: number
  maxAmount: number
  minVolume: number
  maxVolume: number
  address: string
  displayPriceType: DisplayPriceType
}

type TimePayload = {
  type: DateSelectedType
  key: TimeKey
  value: string
}

export const getCurrentTime = (): TimeWheelDateType => {
  const now = dayjs()
  return {
    year: now.year().toString(),
    month: (now.month() + 1).toString().padStart(2, '0'),
    day: now.date().toString().padStart(2, '0'),
    hour: now.hour().toString().padStart(2, '0'),
    minute: now.minute().toString().padStart(2, '0'),
  }
}

export const getInitialState = (): TokenDetailState => ({
  startDate: undefined,
  endDate: undefined,
  data: [],
  sortByCreatedAt: SortByCreateAtType.DESC,
  page: 1,
  hasMore: true,
  displayDateTimeMode: false,
  minAmount: -1,
  maxAmount: -1,
  minVolume: -1,
  maxVolume: -1,
  address: '',
  displayPriceType: DisplayPriceType.PRICE,
})

export const createTokenDetailSlice = (name: string) => {
  return createSlice({
    name,
    initialState: getInitialState(),
    reducers: {
      setStartDate(state, action: PayloadAction<TimeWheelDateType>) {
        state.startDate = { ...state.startDate, ...action.payload }
      },
      setEndDate(state, action: PayloadAction<TimeWheelDateType>) {
        state.endDate = { ...state.endDate, ...action.payload }
      },
      setData(state, action: PayloadAction<TradeHistoryDTO[]>) {
        state.data = action.payload
      },
      setPage(state, action: PayloadAction<number>) {
        state.page = action.payload
      },
      setHasMore(state, action: PayloadAction<boolean>) {
        state.hasMore = action.payload
      },
      setTime(state, action: PayloadAction<TimePayload>) {
        const { type, key, value } = action.payload
        let target = type === 'start' ? state.startDate : state.endDate
        if (!target) target = getCurrentTime()
        target[key] = value
      },
      resetTime(state) {
        state.startDate = getCurrentTime()
        state.endDate = getCurrentTime()
      },
      setSortByCreatedAt(state, action: PayloadAction<SortByCreateAtType | undefined>) {
        state.sortByCreatedAt = action.payload
      },
      setDisplayDateTimeMode(state, action: PayloadAction<boolean>) {
        state.displayDateTimeMode = action.payload
      },
      setMinAmount(state, action: PayloadAction<number>) {
        state.minAmount = action.payload
      },
      setMaxAmount(state, action: PayloadAction<number>) {
        state.maxAmount = action.payload
      },
      setMinVolume(state, action: PayloadAction<number>) {
        state.minVolume = action.payload
      },
      setMaxVolume(state, action: PayloadAction<number>) {
        state.maxVolume = action.payload
      },
      setAddress(state, action: PayloadAction<string>) {
        state.address = action.payload
      },
      setDisplayPriceType(state, action: PayloadAction<DisplayPriceType>) {
        state.displayPriceType = action.payload
      },
      reset: () => getInitialState(),
    },
  })
}

export const tokenDetailSlice = createTokenDetailSlice('tokenDetail')

export const {
  setStartDate,
  setEndDate,
  setData,
  setPage,
  setHasMore,
  setTime,
  resetTime,
  setSortByCreatedAt,
  setDisplayDateTimeMode,
  setMinAmount,
  setMaxAmount,
  setMinVolume,
  setMaxVolume,
  setAddress,
  setDisplayPriceType,
  reset,
} = tokenDetailSlice.actions

export default tokenDetailSlice.reducer
