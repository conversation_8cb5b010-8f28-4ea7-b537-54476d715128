export enum ChainIds {
  Ethereum = 1,
  Bsc = 56,
  BscTest = 97,
  Avalanche = 43114,
  FantomOpera = 250,
  Arbitrum = 42161,
  Polygon = 137,
  Pulse = 369,
  Bitrock = 7171,
  Shibarium = 109,
  Cybria = 6661,
  Base = 8453,
  Solana = 501424,
  BTC = 1800,
  TON = 1100,
  TRX = 100,
}

export const BaseTokenAddress: Record<ChainIds, Record<string, string>> = {
  [ChainIds.Solana]: {
    wrapped: 'So11111111111111111111111111111111111111112',
  },
  [ChainIds.Ethereum]: {
    wrapped: '******************************************',
  },
  [ChainIds.Bsc]: {
    wrapped: '******************************************',
  },
  [ChainIds.BscTest]: {
    wrapped: '',
  },
  [ChainIds.Avalanche]: {
    wrapped: '',
  },
  [ChainIds.FantomOpera]: {
    wrapped: '',
  },
  [ChainIds.Arbitrum]: {
    wrapped: '',
  },
  [ChainIds.Polygon]: {
    wrapped: '',
  },
  [ChainIds.Pulse]: {
    wrapped: '',
  },
  [ChainIds.Bitrock]: {
    wrapped: '',
  },
  [ChainIds.Shibarium]: {
    wrapped: '',
  },
  [ChainIds.Cybria]: {
    wrapped: '',
  },
  [ChainIds.Base]: {
    wrapped: '',
  },
  [ChainIds.TON]: {
    wrapped: '',
  },
  [ChainIds.BTC]: {
    wrapped: '',
  },
  [ChainIds.TRX]: {
    wrapped: '',
  },
}

export enum TokenDetailColumnKeys {
  TIME = 'timestamp',
  TYPE = 'type',
  TRANSACTION_AMOUNT = 'transactionAmount',
  SOLD_PRICE = 'soldPrice',
  VOLUME = 'volume',
  WALLET = 'wallet',
  ACTION = 'action',
}

export enum LatestFollowedColumnKeys {
  HOLDERS = 'holders',
  TIME = 'timestamp',
  TYPE = 'type',
  TOTAL = 'total',
  PRICE = 'price',
  VOLUME = 'volume',
  WALLET = 'wallet',
}

export enum FollowedHolderColumnKeys {
  INDEX = '#',
  WALLET = 'wallet',
  POSITION_PERCENTAGE = 'positionPercentage',
  TOTAL_BUY = 'totalBuy',
  TOTAL_SELL = 'totalSell',
  REALIZED = 'realized',
  UNREALIZED = 'unrealized',
  TOTAL_PROFIT = 'totalProfit',
  SOL_BALANCE = 'solBalance',
  FUND_SOURCE = 'fundSource',
  HOLDING_LENGTH = 'holdingLength',
  AVG_BUY_SELL = 'avgBuySell',
  TRANSACTION_COUNT = 'transactionCount',
  LAST_ACTIVE = 'lastActive',
}

export enum PoolColumnKeys {
  TIME = 'time',
  TYPE = 'type',
  LIQUIDITY = 'liquidity',
  VOLUME = 'volume',
  FEE = 'fee',
  ACTION = 'action',
  QUANTITY = 'quantity',
  TOTAL_VALUE = 'totalValue',
  ADDRESS= 'address',
}

export enum TimeKey {
  YEAR = 'year',
  MONTH = 'month',
  DAY = 'day',
  HOUR = 'hour',
  MINUTE = 'minute',
}

export enum DateSelectedType {
  START = 'start',
  END = 'end',
  NOT_FOCUS = 'notFocus',
}

export enum SortByCreateAtType {
  ASC = 'ASC',
  DESC = 'DESC',
}

export enum FilterTransactionAmountType {
  USDT,
  SOL,
}

export enum TransactionType {
  All = 'all',
  Buy = 'buy',
  Sell = 'sell',
  AddLiquidity = 'add',
  RemoveLiquidity = 'remove',
}

export enum TransactionClassification {
  All = 'All',
  Followed = 'Followed',
  SmartMoney = 'SmartMoney',
  Whale = 'Whale',
  Sniper = 'Sniper',
  ProjectParty = 'ProjectParty',
  Insider = 'Insider',
  Fresh = 'Fresh',
  KOL = 'KOL',
  SameSource = 'SameSource',
}

//"https://solana-rpc.publicnode.com"
export enum DisplayPriceType {
  PRICE,
  MC,
}

export enum PoolTransactionType {
  All = 'All',
  Trading = 'Trading',
  Liquidity = 'Liquidity',
  Buy = 'Buy',
  Sell = 'Sell',
  AddLiquidity = 'AddLiquidity',
  RemoveLiquidity = 'RemoveLiquidity',
  Add = 'Add',
  Remove = 'Remove',
}
