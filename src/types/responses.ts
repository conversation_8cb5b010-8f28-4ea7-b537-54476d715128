import { Ohlc } from '@/types/ohlc.ts'
import { OrderDTO, PortfolioDTO } from '@/types/holding.ts'
import { TokenTrending } from '@/types/token.ts'
import { PopularToken } from '@/types/popularToken.ts'
import { Category, CategoryStatistics, CategoryToken } from '@/types/category.ts'
import { SmartMoneyAction } from './tokenDetail'
import {
  AssetChartItemDto,
  FollowedTransaction,
  HolderDto,
  MemePagination,
  TokenSniperDto,
  Transaction,
} from '@/@generated/gql/graphql-core.ts'
import { AssetHistoryDto, LastTransaction } from '@/@generated/gql/graphql-core.ts'
import { AiAnalyticDto, SmartMoneyDto, TokenOfficialInformation } from '@/@generated/gql/graphql-core.ts'

export interface GraphQLErrorItem {
  message: string
  path: string[]
  extensions: {
    code: string
    meta: any | null
  }
}

export type GeneralResponse<K extends string, V> = {
  [key in K]: V
} & {
  errors: GraphQLErrorItem[]
}

export type GetOHLCResponse = {
  getOHLC: Ohlc[]
}

export interface PortfolioResponse {
  getPortfolio: {
    limit: number
    page: number
    data: PortfolioDTO[]
  }
  errors: GraphQLErrorItem[]
}

export interface OrderResponse {
  createOrder: OrderDTO
  errors: GraphQLErrorItem[]
}

export interface SearchTokenResponse {
  searchToken: TokenTrending[]
}

export interface GetTokenTrendingResponse {
  getTokenTrending: {
    page: number
    limit: number
    data: TokenTrending[]
  }
  errors: GraphQLErrorItem[]
}

export interface GetPopularTokenResponse {
  getPopularTokens: PopularToken[]
}

export interface GetAllCategoriesInput {
  input: {
    chainId: number
  }
}

export interface GetAllCategoriesResponse {
  getAllCategories: {
    data: Category[]
  }
  errors: GraphQLErrorItem[]
}

export interface GetCategoryStatisticsResponse {
  getCategoryStatistic: CategoryStatistics
  errors: GraphQLErrorItem[]
}

export interface GetTokensByCategoryResponse {
  tokensByCategory: {
    page: number
    limit: number
    data: CategoryToken[]
  }
}

export interface GetSmartMoneyResponse {
  getSmartMoneyActions: {
    actions: SmartMoneyAction[]
  }
}

export interface GetTokenSnipersResponse {
  getTokenSniper: TokenSniperDto
}

export interface GetAllAssetHistoryResponse {
  collapse: AssetHistoryDto[]
  expand: AssetHistoryDto[]
}

export interface AiAnalyticResponse {
  getAiAnalyzedInfo: AiAnalyticDto
}

export interface TokenOfficialInformationResponse {
  getTokenOfficialInformation: TokenOfficialInformation
}

export interface GetTokenSnipersResponse {
  getTokenSniper: TokenSniperDto
}

export interface GetFollowingSmartMoneysResponse {
  getFollowingSmartMoneys: SmartMoneyDto[]
}

export interface GetTotalFollowingAddressResponse {
  getFollowingWalletAddressess: string[]
}

export interface GetLastTransactionsResponse {
  lastTransactions: {
    data: LastTransaction[]
    fromTimestamp: number
  }
}

export type GetMemeOutput = {
  getMemeToken: MemePagination
}

export interface GetTransactionsResponse {
  getTransactions: {
    data: Transaction[]
    fromTimestamp: string
  }
}

export interface GetFollowedTransactionsResponse {
  getFollowedTransactions: {
    data: FollowedTransaction[]
    fromTimestamp: number
  }
}

export interface GetAiAnalyzedInfoResponse {
  getAiAnalyzedInfo: AiAnalyticDto
}

export interface GetFollowedHolderResponse {
  getFollowedHolder: {
    page: number
    limit: number
    data: HolderDto[]
  }
}

export interface GetHoldersResponse {
  getHolder: {
    page: number
    limit: number
    data: HolderDto[]
    numberOfHolders: number
    numberOfHolderHistory: number[]
    top10Holders: number
    top10HolderHistory: number[]
    averageHoldingPerWallet: number
    averageHoldingPerWalletHistory: number[]
    insiderHolding: number
    insiderHoldingHistory: number[]
    blueChip: number
    blueChipHistory: number[]
  }
}

export interface GetAssetChartResponse {
  getAssetChartPreview: AssetChartItemDto[]
  getAssetChart: AssetChartItemDto[]
}

export interface GetHoldersResponse {
  getHolder: {
    page: number
    limit: number
    data: HolderDto[]
    numberOfHolders: number
    numberOfHolderHistory: number[]
    top10Holders: number
    top10HolderHistory: number[]
    averageHoldingPerWallet: number
    averageHoldingPerWalletHistory: number[]
    insiderHolding: number
    insiderHoldingHistory: number[]
    blueChip: number
    blueChipHistory: number[]
  }
}
