@import 'tailwindcss';
@config "../tailwind.config.js";

/* :root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
} */

@layer base {
  :root {
    --background: 0, 0%, 7%;
    --background-table: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 0 0% 89.4%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --moving-bg-tabs-bg-color: rgba(236, 236, 237, 0.08);
    --moving-bg-tabs-border-color: rgba(236, 236, 237, 0.08);
    --moving-bg-tabs-color: #b9b9b9;
    --moving-bg-tabs-active-color: #141414;
    --bottom-nav-bg: #191919;
    --bottom-nav-border-color: #ffffff0a;

    --text-primary: rgba(255, 255, 255, 1);
    --text-secondary: rgba(255, 255, 255, 0.8);
    --text-tertiary: rgba(255, 255, 255, 0.6);
    --text-placeholder: rgba(255, 255, 255, 0.36);

    --bg-primary: rgba(30, 29, 31, 0.8);
    --bg-secondary: rgba(236, 236, 237, 0.08);

    --rise: #00ffb4;
    --link: #0b8ecb;
    --fall: #ab57ff;
    --neutral: 'rgba(255, 255, 255, 0.7)';
    --tertiary: #141414;

    --chart-head-bg: #191919;

    --rise-alternate: #00F3AB;
    --fall-alternate: #9945FF;

    --rise-opacity-6: #00FFB40F;
    --rise-opacity-8: #00FFB414;
    --rise-opacity-40: #00FFB466;
    --fall-opacity-6: #ab57ff0f;
    --fall-opacity-8: #ab57ff14;
    --fall-opacity-40: #ab57ff66;

    &.inverse {
      --rise: #ab57ff;
      --fall: #00ffb4;

      --rise-alternate: #9945FF;
      --fall-alternate: #00F3AB;

      --rise-opacity-6: #ab57ff0f;
      --rise-opacity-8: #ab57ff14;
      --rise-opacity-40: #ab57ff66;
      --fall-opacity-6: #00FFB40F;
      --fall-opacity-8: #00FFB414;
      --fall-opacity-40: #00FFB466;
    }
  }
  .dark {
    --background-body: #000000;
    --background: 0, 0%, 7%;
    --background-table: rgb(23, 24, 27);
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --tertiary: #141414;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --moving-bg-tabs-bg-color: rgba(236, 236, 237, 0.08);
    --moving-bg-tabs-border-color: rgba(236, 236, 237, 0.08);
    --moving-bg-tabs-color: #b9b9b9;
    --moving-bg-tabs-active-color: #141414;
    --bottom-nav-bg: #191919;
    --bottom-nav-border-color: #ffffff0a;
  }

  input[type='number'] {
    appearance: textfield;
    -moz-appearance: textfield;
  }
  input[type='number']::-webkit-inner-spin-button,
  input[type='number']::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}

*:focus-visible {
  outline: none;
}

@layer utilities {
  .app-font-light {
    font-family:
      Noto Sans SC,
      system-ui,
      sans-serif;
    font-weight: 300;
  }
  .app-font-regular {
    font-family:
      Noto Sans SC,
      system-ui,
      sans-serif;
    font-weight: 400;
  }
  .app-font-medium {
    font-family:
      Noto Sans SC,
      system-ui,
      sans-serif;
    font-weight: 500;
  }
  .app-font-semibold {
    font-family:
      Noto Sans SC,
      system-ui,
      sans-serif;
    font-weight: 600;
  }
  .gradient-border {
    border: 1px solid transparent;
    background-origin: border-box;
    background-clip: padding-box, border-box;
    position: relative;
  }
  .progressGradientBackground {
    background: linear-gradient(90deg, #9945ff 5.09%, #00f3ab 100%);
  }
  .progressDot {
    background: #514f57;
  }
  .progressDot.active::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 2px;
    background: linear-gradient(90.05deg, #00f3ab -15.07%, #9945ff 118.15%);
    -webkit-mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
  }

  .gradient-border::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(
      43.83deg,
      rgba(232, 67, 254, 0.4) 0%,
      rgba(255, 255, 255, 0.4) 44.73%,
      rgba(255, 255, 255, 0.4) 49.05%,
      rgba(0, 255, 205, 0.4) 103.57%
    );
    -webkit-mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
  }

  .gradient-1-bg {
    background: linear-gradient(
      60deg,
      rgba(225, 73, 248, 0.1) 0%,
      rgba(153, 69, 255, 0.1) 49.05%,
      rgba(0, 243, 171, 0.1) 103.57%
    );
  }
  .gradient-1-border {
    border: 1px solid transparent;
    background-origin: border-box;
    background-clip: padding-box, border-box;
    position: relative;
  }

  .gradient-1-border::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(60deg, rgba(153, 69, 255, 1) 0%, rgba(0, 243, 171, 1) 103.57%);
    -webkit-mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
  }
  .hover-scale {
    transition: transform 0.1s ease-in-out;
  }
  .hover-scale:hover {
    transform: scale(1.03);
  }
  .single-card-bg::before {
    content: '';
    position: absolute;
    background-image: url('/images/futuresDetail/card-bg.png');
    background-size: 100% 100%;
    width: 100%;
    height: 100%;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: -1;
  }

  .bg-green-gradient-card:before {
    content: '';
    position: absolute;
    background-image: url('/images/futuresDetail/tokenDetail/card-bg.webp');
    background-size: 100% 100%;
    width: 100%;
    height: 100%;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: -1;
  }
}
@layer base {
  * {
    @apply border-border z-0;
  }
  body {
    @apply text-foreground;
    background: var(--background-body);
    font-family:
      Noto Sans SC,
      system-ui,
      sans-serif;
  }
}

@layer components {
  .btn-gradient {
    @apply rounded-[50px]
    flex
    flex-1
    align-middle
    justify-center
    relative
    z-1
    py-2
    px-4
    bg-[#ffffff]
    hover:!bg-white
    before:content-['']
    before:absolute
    before:bottom-0
    before:top-0
    before:w-[100%]
    before:rounded-[50px]
    before:bg-[linear-gradient(45deg,#d83bfc,rgba(255,255,255,0.5),transparent)]
    before:z-[-1]
    before:pointer-events-none
    after:content-['']
    after:absolute
    after:right-0
    after:bottom-0
    after:top-0
    after:w-[100%]
    after:rounded-[50px]
    after:bg-[linear-gradient(45deg,transparent,rgba(255,255,255,0.5),#19f5b1)]
    after:z-[-1]
    after:pointer-events-none;
  }

  .btn-gradient-disabled {
    @apply rounded-[50%]
    flex
    flex-1
    align-middle
    justify-center
    relative
    z-1
    py-2
    px-4
    bg-[url('/images/bg-button-disable.svg')] bg-no-repeat bg-cover bg-center;
  }

  .custom-gradient-border {
    /* border: px solid transparent; */
    background-origin: border-box;
    background-clip: padding-box, border-box;
    position: relative;
  }

  .custom-gradient-border::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 0.25px;
    background: linear-gradient(90deg, #9945ff 20%, #00f3ab 100%);
    -webkit-mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
  }

  .border-gradient {
    border: 1px solid transparent;
    background-origin: border-box;
    background-clip: padding-box, border-box;
    position: relative;
  }

  .border-gradient::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(
      10deg,
      rgba(232, 67, 254, 0.4) 0%,
      rgba(255, 255, 255, 0.4) 50%,
      rgba(0, 255, 205, 0.4) 103.57%
    );
    -webkit-mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
  }

  .border-gradient.bottom-card::before {
    background: linear-gradient(
      67.01deg,
      rgba(73, 243, 156, 0.6) 7.21%,
      rgba(110, 198, 253, 0.6) 27.27%,
      rgba(153, 69, 255, 0.6) 47.34%
    );
  }

  .border-gradient.style2::before {
    background: linear-gradient(90deg, rgba(165, 62, 255, 0.5) 20%, rgba(0, 247, 165, 0.5) 100%);
  }

  .orderbook-buy::after {
    content: '';
    position: absolute;
    bottom: 0;
    top: 0;
    left: 0;
    background: linear-gradient(to right, #00000000, #00FFB41F);
    width: var(--after-width);
    border-radius: var(--after-radius);
  }

  .orderbook-sell::after {
    content: '';
    position: absolute;
    bottom: 0;
    top: 0;
    left: 0;
    background: linear-gradient(to right, #00000000, #AB57FF24);
    width: var(--after-width);
    border-radius: var(--after-radius);
  }

  .orderbook-buy-v2::after {
    content: '';
    position: absolute;
    bottom: 0;
    top: 0;
    right: 0;
    /* background: linear-gradient(to left, #00000000, #00FFB42e); */
    background: #00FFB42e;

    width: var(--after-width);
    border-radius: var(--after-radius);
  }

  .orderbook-sell-v2::after {
    content: '';
    position: absolute;
    bottom: 0;
    top: 0;
    left: 0;
    /* background: linear-gradient(to right, #00000000, #ab57ff3d); */
    background: #ab57ff3d;
    width: var(--after-width);
    border-radius: var(--after-radius);
  }

  .assets-gradient-box::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    border: 1px solid #ececed14;
    border-radius: 10px;
    mask-composite: exclude;
    pointer-events: none;
  }
  .tooltip-gradient {
    padding: 3px 5px;
    font-size: 10px;
    line-height: 1;
    color: black;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-image: linear-gradient(45deg, #e843fe, #ffffff, #00ffcd);
    border-radius: 50px;
    position: absolute;
    top: -17px;
    right: 0;
  }
  .tooltip-gradient .arrow {
    display: block;
    width: 40px;
    max-width: 100%;
    background-image: linear-gradient(45deg, #e843fe, #ffffff, #00ffcd);
    position: absolute;
    z-index: 0;
    left: 50%;
    transform: translateX(-50%);
    top: 0;
    clip-path: polygon(0 0, 50% 100%, 100% 0);
    border-radius: 50px;
    background-position-x: center;
  }
}

.container-wrapper {
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  padding: 0 10px;
}

/* Apply to all elements */
*::-webkit-scrollbar {
  width: 2px; /* Width of scrollbar */
  height: 2px;
}

*::-webkit-scrollbar-track {
  background: transparent; /* Track background */
  border-radius: 8px;
}

*::-webkit-scrollbar-thumb {
  background: oklch(0.39 0.01 0); /* Scrollbar thumb */
  border-radius: 8px;
}

*::-webkit-scrollbar-thumb:hover {
  background: oklch(0.39 0.01 0); /* Scrollbar thumb on hover */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply text-foreground;
  }
  .sticky-table-header {
    position: sticky;
    top: 0;
    z-index: 2;
  }
}
.watermark:after {
  content: '';
  position: absolute;
  background-image: url('/images/icons/watermark-logo.png');
  background-size: 100%;
  background-repeat: no-repeat;
  height: 30px;
  width: 75px;
  left: 50%;
  bottom: 30%;
  transform: translate(-50%, -50%);
  font-size: 50px;
  text-align: center;
  pointer-events: none;
  font-weight: 500;
}

._hidescrollbar::-webkit-scrollbar {
  display: none;
}
._hidescrollbar {
  scrollbar-width: none;
}
/* loading indicator */
#pageLoading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #1e1e22;
  z-index: 9999;
}
#pageLoading .pageLoadingSpinner {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -24px;
  margin-top: -24px;
  border: 3px solid;
  border-color: #fff #fff transparent transparent;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}
#pageLoading .pageLoadingSpinner::after,
#pageLoading .pageLoadingSpinner::before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  border: 3px solid;
  border-color: transparent transparent #ff3d00 #ff3d00;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  animation: rotationBack 0.5s linear infinite;
  transform-origin: center center;
}
#pageLoading .pageLoadingSpinner::before {
  width: 32px;
  height: 32px;
  border-color: #ffffff #ffffff transparent transparent;
  animation: rotation 1.5s linear infinite;
}

.containerLoadingSpinner::after,
.containerLoadingSpinner::before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  border: 3px solid;
  border-color: transparent transparent #ab57ff #ab57ff;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  animation: rotation 0.5s linear infinite;
  transform-origin: center center;
}
/* .containerLoadingSpinner::before {
  width: 32px;
  height: 32px;
  border-color: #ffffff #ffffff transparent transparent;
  animation: rotation 1.5s linear infinite;
} */

.tv-loading-screen {
  display: none !important;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotationBack {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}
/* end loading indicator */

@keyframes fade-in-out {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  10% {
    opacity: 1;
    transform: translateY(0);
  }
  90% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-10px);
  }
}

.animate-fade-in-out {
  animation: fade-in-out 3s ease-in-out;
}

.no-scrollbar {
  scrollbar-width: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

button {
  @apply cursor-pointer;
}

button:disabled {
  @apply cursor-not-allowed;
}

input.no-spin-button {
  appearance: textfield;
  -moz-appearance: textfield;
  -webkit-appearance: none;
}

input.no-spin-button::-webkit-inner-spin-button,
input.no-spin-button::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.gradient-border-custom {
  @apply border border-[0.5px] border-solid;
  border-image-source: linear-gradient(90deg, rgba(153, 69, 255, 0.2) 20%, rgba(0, 243, 171, 0.2) 100%);
}

.gradient-border-buttom {
  border-bottom-width: 0.6px;
  border-image-source: linear-gradient(64.61deg, rgba(60, 46, 119, 0.6) 9.27%, rgba(0, 105, 82, 0.6) 67.81%);
  border-image-slice: 1;
}
.bg-gradient {
  background: linear-gradient(180deg, rgba(31, 31, 43, 0.75) 0%, rgba(25, 25, 25, 0.35) 70.26%);
}

.hover-scale-card {
  @apply hover:scale-[101%] transition-all duration-300;
}

.wrap-order {
  background: linear-gradient(180deg, #1d2324 0%, #1a2424 5.58%, #191d1e 100%);
}

.bg-smart-money-gradient {
  background: linear-gradient(
    180deg,
    rgba(43, 19, 70, 0.6) 0%,
    rgba(39, 27, 55, 0.2) 10%,
    rgba(35, 35, 41, 0) 20%,
    rgba(35, 35, 41, 0) 95%,
    rgba(35, 35, 41, 0.6) 100%
  );
}

.fixed-bottom-TrendPage {
  background: linear-gradient(180deg, rgba(24, 45, 45, 1) 0%, rgba(23, 30, 30, 1) 16.09%, rgba(25, 25, 25, 1) 100%);
}

/* Modern version - only applied on browsers that support display-p3 */
@supports (color: color(display-p3 0 0 0)) {
  .fixed-bottom-TrendPage {
    background: linear-gradient(
      180deg,
      color(display-p3 0.114 0.173 0.176 / 1) 0%,
      color(display-p3 0.094 0.118 0.118 / 1) 16.09%,
      color(display-p3 0.098 0.098 0.098 / 1) 100%
    );
  }
}

.btn-long-box-shadow {
  box-shadow:
    0px 1px 2px 0px rgba(0, 101, 65, 0.2),
    0px 2px 5px 0px rgba(56, 253, 182, 0.24),
    0px 2px 6px 0px rgba(0, 243, 155, 0.15),
    inset 0px -1px 0px 0px rgba(255, 255, 255, 0.6);
}

.btn-short-box-shadow {
  box-shadow:
    0px 1.41px 2.21px 0px #ed29ff4d,
    0px 3px 5px 0px #6900ff66,
    0px 2px 6px 0px #6900ff3d,
    0px -1px 0px 0px #ffffff40 inset;
}

.border-gradient-funding-record-success-item,
.border-gradient-funding-record-failed-item {
  border: 1px solid transparent;
  background-origin: border-box;
  background-clip: padding-box, border-box;
  position: relative;
}

.border-gradient-funding-record-success-item::before,
.border-gradient-funding-record-failed-item::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

.border-gradient-funding-record-success-item::before {
  background: linear-gradient(
    104.53deg,
    rgba(0, 255, 174, 0.08) 11.84%,
    rgba(0, 187, 125, 0.04) 40.93%,
    rgba(0, 156, 104, 0) 70.02%,
    rgba(0, 255, 174, 0.4) 91.19%
  );
}

.border-gradient-funding-record-failed-item::before {
  background: linear-gradient(
    100.68deg,
    rgba(255, 255, 255, 0.12) 10.82%,
    rgba(107, 0, 159, 0.06) 27.84%,
    rgba(107, 0, 159, 0.08) 74.62%,
    rgba(184, 81, 255, 0.6) 86.73%
  );
}

.funding-record-success-item-background {
  background: linear-gradient(105.98deg, rgba(0, 238, 159, 0.024) 46.27%, rgba(0, 255, 174, 0.056) 93.65%);
}

.funding-record-failed-item-background {
  background: linear-gradient(105.98deg, rgba(90, 0, 159, 0) 46.27%, rgba(184, 81, 255, 0.12) 93.65%);
}

.virtual-table-custom td:last-child {
  width: 24.5%;
}

@layer drawer {
  --drawer-z-index: 40;

  .drawer-overlay {
    z-index: var(--drawer-z-index) - 1;
  }

  .sidenav {
    z-index: var(--drawer-z-index);
  }

  .nested-drawer {
    z-index: var(--drawer-z-index) + 1;
  }
}

.border-gradient-token-search {
  background: linear-gradient(90deg, #9945FF 20%, #00F3AB 100%);
  padding: 1px;
  border-radius: 9999px;
}

.px-table-cell:first-child,
.px-table-cell:first-child {
  padding-left: 14px !important;
}

.px-table-cell:last-child,
.px-table-cell:last-child {
  padding-right: 14px;
}

.px-cell-11px:first-child,
.px-cell-11px:first-child {
  padding-left: 11px !important;
}

.px-cell-11px:last-child,
.px-cell-11px:last-child {
  padding-right: 11px;
}
.nophone {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
}

.nophone {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
}

.text-gradient-icon-percentage {
  background: linear-gradient(180deg, #00FFAE 0%, #00FFF7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Thêm vào globals.css */
@keyframes slideInFromTop {
  from {
    transform: translateY(0) translateX(-50%);
    opacity: 0;
  }
  to {
    transform: translateY(0) translateX(-50%);
    opacity: 1;
  }
}

@keyframes slideOutToTop {
  from {
    transform: translateY(0) translateX(-50%);
    opacity: 1;
  }
  to {
    transform: translateY(-100%) translateX(-50%);
    opacity: 0;
  }
}

.toast-slide-in {
  animation: slideInFromTop 0.3s ease-out forwards;
}

.toast-slide-out {
  animation: slideOutToTop 0.2s ease-in forwards;
}

/* 数字变化动画 */
@keyframes numberChange {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.02);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.number-change-animation {
  animation: numberChange 0.3s ease-out;
}
