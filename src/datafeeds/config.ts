export enum LineStyle {
  SOLID = 0,
  DOTTED = 1,
  DASHED = 2,
  LARGE_DASHED = 3,
}

const RISE_COLOR = '#00FFB4'
const FALL_COLOR = '#AB57FF'

type Overrides = Record<string, string | number | boolean | object>

export const overRides = (updown: 'gr' | 'rg' = 'gr'): Overrides => ({
  'paneProperties.gridProperties.color': '#ffffff',
  'paneProperties.gridProperties.style': LineStyle.SOLID,
  'paneProperties.background': '#111111',
  'paneProperties.backgroundType': 'solid',
  'paneProperties.backgroundGradientStartColor': '#111111',
  'paneProperties.backgroundGradientEndColor': '#111111',
  'paneProperties.crossHairProperties.color': '#A2ABB1',
  'paneProperties.crossHairProperties.style': LineStyle.DASHED,
  'paneProperties.crossHairProperties.width': 1,
  'mainSeriesProperties.barStyle.downColor': updown === 'gr' ? FALL_COLOR : RISE_COLOR,
  'mainSeriesProperties.barStyle.upColor': updown === 'gr' ? RISE_COLOR : FALL_COLOR,
  'mainSeriesProperties.candleStyle.upColor': updown === 'gr' ? RISE_COLOR : FALL_COLOR,
  'mainSeriesProperties.candleStyle.downColor': updown === 'gr' ? FALL_COLOR : RISE_COLOR,
  'mainSeriesProperties.candleStyle.wickUpColor': updown === 'gr' ? RISE_COLOR : FALL_COLOR,
  'mainSeriesProperties.candleStyle.wickDownColor': updown === 'gr' ? FALL_COLOR : RISE_COLOR,
  'mainSeriesProperties.candleStyle.borderUpColor': updown === 'gr' ? RISE_COLOR : FALL_COLOR,
  'mainSeriesProperties.candleStyle.borderDownColor': updown === 'gr' ? FALL_COLOR : RISE_COLOR,
  'mainSeriesProperties.candleStyle.drawBorder': false,
  'mainSeriesProperties.lineStyle.color': updown === 'gr' ? RISE_COLOR : FALL_COLOR,
  'mainSeriesProperties.hollowCandleStyle.borderDownColor': updown === 'gr' ? FALL_COLOR : RISE_COLOR,
  'mainSeriesProperties.hollowCandleStyle.borderUpColor': updown === 'gr' ? RISE_COLOR : FALL_COLOR,
  'mainSeriesProperties.hollowCandleStyle.downColor': updown === 'gr' ? FALL_COLOR : RISE_COLOR,
  'mainSeriesProperties.hollowCandleStyle.upColor': updown === 'gr' ? RISE_COLOR : FALL_COLOR,
  'mainSeriesProperties.hollowCandleStyle.wickDownColor': updown === 'gr' ? FALL_COLOR : RISE_COLOR,
  'mainSeriesProperties.hollowCandleStyle.wickUpColor': updown === 'gr' ? RISE_COLOR : FALL_COLOR,
  'mainSeriesProperties.haStyle.upColor': updown === 'gr' ? RISE_COLOR : FALL_COLOR,
  'mainSeriesProperties.haStyle.downColor': updown === 'gr' ? FALL_COLOR : RISE_COLOR,
  'mainSeriesProperties.haStyle.wickDownColor': updown === 'gr' ? FALL_COLOR : RISE_COLOR,
  'mainSeriesProperties.haStyle.wickUpColor': updown === 'gr' ? RISE_COLOR : FALL_COLOR,
  'mainSeriesProperties.haStyle.drawBorder': false,
  'mainSeriesProperties.areaStyle.color1': 'rgba(17,195,147, 0.2)',
  'mainSeriesProperties.areaStyle.color2': 'rgba(17,195,147, 0.01)',
  'mainSeriesProperties.areaStyle.linecolor': RISE_COLOR,
  'mainSeriesProperties.areaStyle.linewidth': 1,
  'mainSeriesProperties.areaStyle.priceSource': 'close',
  'mainSeriesProperties.showCountdown': true,
  volumePaneSize: 'medium',
  'volume.volumeFormat': {
    type: 'volume',
    precision: 1,
  },
})

type StudiesOverrides = Record<string, string | number>

export const studiesOverrides = (updown: 'gr' | 'rg' = 'gr'): StudiesOverrides => ({
  'volume.volume.color.0': updown === 'gr' ? FALL_COLOR : RISE_COLOR,
  'volume.volume.color.1': updown === 'gr' ? RISE_COLOR : FALL_COLOR,
  "volume.precision": 4,
  'volume.volume.transparency': 30,
  'bollinger bands.median.color': '#33FF88',
  'bollinger bands.upper.linewidth': 7,
})

interface TvConfig {
  fullscreen: boolean
  autosize: boolean
  toolbar_bg: string
  numeric_formatting: { decimal_sign: string }
  disabled_features: string[]
  enabled_features: string[]
  overrides: Overrides
  studies_overrides: StudiesOverrides
  legend_widget: Record<string, unknown>
  loading_screen: { backgroundColor: string; foregroundColor: string }
  auto_save_delay: number
  // load_last_chart: boolean;
}

export const TvConfig = (updown: 'gr' | 'rg' = 'gr'): TvConfig => ({
  fullscreen: false,
  autosize: true,
  toolbar_bg: '#111',
  numeric_formatting: { decimal_sign: '.' },
  disabled_features: [
    'use_localstorage_for_settings',
    'header_widget',
    'timeframes_toolbar',
    'go_to_date',
    'popup_hints',
    'adaptive_logo',
    'header_undo_redo',
    'remove_library_container_border',
    'symbol_info',
    'header_saveload',
    'countdown',
    'vert_touch_drag_scroll',
    'chart_crosshair_menu',
    'display_market_status',
    'hide_left_toolbar_by_default'
    // 'widget_logo'
  ],
  enabled_features: [
    'legend_widget',
    'volume_force_overlay',
    'hide_last_na_study_output',
    'dont_show_boolean_study_arguments',
    'save_chart_properties_to_local_storage',
    'compare_symbol',
    'hide_left_toolbar_by_default',
    'study_templates',
    'seconds_resolution',
    'study_templates',
    'supports_marks',
    'two_character_bar_marks_labels'
  ],
  overrides: overRides(updown),
  studies_overrides: studiesOverrides(updown),
  legend_widget: {},
  loading_screen: {
    backgroundColor: '#111111',
    foregroundColor: '#111111',
  },
  auto_save_delay: 1,
  // load_last_chart: true,
})
