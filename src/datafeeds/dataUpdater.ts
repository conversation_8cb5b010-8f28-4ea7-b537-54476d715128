import { MqttClient } from 'mqtt'
import { resolutionTfMap, resolutionTimeFrameMap } from './resolution-map'
import eventBus from '@/lib/eventBus'
import MqttClientManager from '@/lib/mqtt/MqttClient'

export interface HistoryData {
  time: number
  open: number
  close: number
  high: number
  low: number
  volume: number
}
export const EVENT_MESSAGE_OHLC_UPDATED = 'EVENT_MESSAGE_OHLC_UPDATED'
export default class DataUpdater {
  // private token: string
  private client: MqttClient
  private lastBars: HistoryData
  private subscribersUID: any[]
  constructor(datafeeds: any) {
    // this.token = ''
    this.subscribersUID = []
    this.lastBars = {} as HistoryData
    this.client = MqttClientManager.getInstance().getClient()
  }

  subscribeBars(
    lastBar: HistoryData,
    symbolInfo: any,
    resolution: string,
    onTick: (newBars: any) => void,
    subscriberUID: string,
    totalSupply: number,
  ): void {
    // Unsubscribe from old topic if exists
    const oldSubscriber = this.subscribersUID.find((obj) => obj.key === subscriberUID)
    if (oldSubscriber) {
      this.client.unsubscribe(oldSubscriber.topic, (err: Error | undefined) => {})
      this.subscribersUID = this.subscribersUID.filter((obj) => obj.key !== subscriberUID)
    }

    const timeFrame = resolutionTfMap[`${resolution}`]
    const timeFrameToSecond = resolutionTimeFrameMap[`${resolution}`]
    const isMarketCap = symbolInfo.name.endsWith('-MC')
    // this.token = symbolInfo?.address
    this.lastBars = lastBar
    this.handleEventMessageOHLCUpdated(this.lastBars)
    const topicMqtt = `public/kline/${timeFrame}/${symbolInfo?.address}`

    // Add new subscriber
    this.subscribersUID.push({
      key: subscriberUID,
      topic: topicMqtt,
    })

    this.client.subscribe(topicMqtt, (err: Error | null) => {})
    this.client.on('message', (topic, message) => {
      if (topic === topicMqtt) {
        let barsUpdated: HistoryData = this.lastBars
        const nextBarTime = this.lastBars?.time + timeFrameToSecond * 1000
        const dataTicker = JSON.parse(message.toString())
        const { st, ts, o, h, l, c, v } = dataTicker
        if (st * 1000 >= this.lastBars?.time) {
          if (st * 1000 >= nextBarTime) {
            barsUpdated = {
              time: st * 1000,
              open: isMarketCap ? this.lastBars?.close * totalSupply : this.lastBars?.close,
              high: isMarketCap ? h * totalSupply : h,
              low: isMarketCap ? l * totalSupply : l,
              close: isMarketCap ? c * totalSupply : c,
              volume: +v ? +v : 0,
            }
            this.lastBars = barsUpdated
            // console.log('[Generate new bar]: ', barsUpdated)
          } else {
            barsUpdated = {
              ...barsUpdated,
              open: isMarketCap ? totalSupply * barsUpdated?.open : barsUpdated?.open,
              close: isMarketCap ? totalSupply * c : c,
              low: isMarketCap ? totalSupply * l : l,
              high: isMarketCap ? totalSupply * h : h,
              volume: +v,
            }
            this.lastBars = barsUpdated
            // console.log('[Bar updated]: ', barsUpdated)
          }
        }
        onTick(barsUpdated)
        this.handleEventMessageOHLCUpdated(barsUpdated)
      }
    })
  }

  handleEventMessageOHLCUpdated(data: any) {
    eventBus.dispatch(EVENT_MESSAGE_OHLC_UPDATED, {
      data: data,
    })
  }

  unsubscribeBars(subscriberUID: string): void {
    const subscriber = this.subscribersUID.find((obj) => obj.key === subscriberUID)
    if (subscriber) {
      this.client.unsubscribe(subscriber.topic, (err: Error | undefined) => {})
      this.subscribersUID = this.subscribersUID.filter((obj) => obj.key !== subscriberUID)
    }
  }

  getNextBarTime(barTime: number, timeFrame: number) {
    return +barTime + +timeFrame * 1000
  }
}
