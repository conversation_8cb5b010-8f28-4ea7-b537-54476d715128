const isDev = import.meta.env.DEV

const isMainnet = true

export class Configs {
  static get commitSHA(): string {
    return (import.meta.env.VITE_COMMIT_SHA || '').trim()
  }
  static get isDev() {
    return isDev
  }
  static get socketUrl() {
    return (import.meta.env.VITE_SOCKET_URL || '').trim()
  }
  static get socketUrlDex() {
    return (import.meta.env.VITE_SOCKET_URL_DEX || '').trim()
  }
  static get trackingId(): string {
    return (import.meta.env.VITE_GA_TRACKING_ID || '').trim()
  }
  static get telegramBot(): string {
    return (import.meta.env.VITE_TELEGRAM_BOT || '').trim()
  }
  static get urlApiJup(): string {
    return ('https://api.jup.ag')
  }
  static getHyperliquidConfig() {
    const isMainnet = import.meta.env.VITE_HYPERLIQUID_NETWORK === 'mainnet'


    const baseUrl = isMainnet
      ? 'https://api.hyperliquid.xyz'
      : 'https://api.hyperliquid-testnet.xyz'

    const chainIdHex = isMainnet ? '0xa4b1' : '0x66eee'
    const env = isMainnet ? 'mainnet' : 'testnet'
    const imgUrl = baseUrl.replace('api.', 'app.') + '/coins'

    const wssUrl = baseUrl.replace('https', 'wss') + '/ws'


    return {
      wss: wssUrl,
      apiUrl: baseUrl,
      chainIdHex,
      env,
      imgUrl,
    }
  }
}
