import { useRef, useState } from 'react'
import ChartHead from '@/components/chart/ChartHead'
import TvChart from '@/components/chart/TvChart'
import { IntervalItem, TradingViewChartTypeItem, intervalMap, tradingviewChartTypeList } from '@/datafeeds/index'
import { TokenDetail } from '@/@generated/gql/graphql-core'
import TradingViewWidget from './TradingViewWidget'
import { RootState, useAppDispatch, useAppSelector } from '@/redux/store'
import { chartActions } from '@/redux/modules/chart.slice'

interface ChartProps {
  tokenData: TokenDetail
}

const Chart = ({ tokenData }: ChartProps) => {
  const env = import.meta.env.VITE_STAGE
  const defaultChartTypeList = tradingviewChartTypeList.filter((item) => {
    return item.value === 1 || item.value === 2
  })
  const [showIntervalMapList, setShowIntervalMapList] = useState<IntervalItem[]>(intervalMap)
  const [showChartTypeList, setShowChartTypeList] = useState<TradingViewChartTypeItem[]>(defaultChartTypeList)
  const dispatch = useAppDispatch()
  const { period, ohlcType, height } = useAppSelector((state: RootState) => state.chart)
  const [currentPeriod, setCurrentPeriod] = useState<string>(period) // default 1m
  const [currentChartType, setCurrentChartType] = useState<number>(showChartTypeList[1].value)
  const [typeOHLC, setTypeOHLC] = useState<'price' | 'marketCap'>(ohlcType)

  // const [isLoading, setIsLoading] = useState<boolean>(true)

  const tvChartRef = useRef(null)

  const handlePeriodChange = (value: string) => {
    setCurrentPeriod(value)
    dispatch(chartActions.updatePeriod(value))
  }

  const handleChartTypeChange = (value: number) => {
    setCurrentChartType(value)
    dispatch(chartActions.updateChartType(value))
    // 2: line style
    if (value !== 2) {
      let newChartTypeList = tradingviewChartTypeList.filter((item) => {
        return item.value === value || item.value === 2
      })
      setShowChartTypeList(newChartTypeList)
    }
  }

  const handleShowPeriodListChange = (value: IntervalItem[]) => {
    setShowIntervalMapList(value)
  }

  const handleIndictorClick = () => {
    tvChartRef?.current?.tvIndictor()
  }

  const handleSettingClick = () => {
    tvChartRef?.current?.tvSetting()
  }

  const handleChartReady = () => {
    // setIsLoading(false)
  }
  return (
    <div className="h-full relative bg-[#111111]">
      <div className="">
        {env === '' ? (
          <TradingViewWidget />
        ) : (
          <>
            <ChartHead
            tokenData={tokenData}
              defaultPeriod={currentPeriod}
              showPeriodList={showIntervalMapList}
              onShowPeriodListChange={handleShowPeriodListChange}
              onPeriodChange={handlePeriodChange}
              defaultChartType={currentChartType}
              chartTypeList={showChartTypeList}
              typeOHLC={typeOHLC}
              setTypeOHLC={(type) => {
                dispatch(chartActions.updateOHLCType(type))
                setTypeOHLC(type)
              }}
              onChartTypeChange={handleChartTypeChange}
              onIndictorClick={handleIndictorClick}
              onSettingClick={handleSettingClick}
            />
            <TvChart
              ref={tvChartRef}
              tokenData={tokenData}
              period={currentPeriod}
              chartType={currentChartType}
              typeOHLC={typeOHLC}
              onChartReady={handleChartReady}
            />
          </>
        )}
      </div>
    </div>
  )
}

export default Chart
