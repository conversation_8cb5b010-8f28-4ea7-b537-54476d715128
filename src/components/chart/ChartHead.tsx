import { Dispatch, SetStateAction, useState } from 'react'
import { cn } from '@/lib/utils.ts'
import ButtonPeriod from '@/components/chart/ButtonPeriod'
import ButtonKlineStyle from '@/components/chart/ButtonKlineStyle'
import { useTranslation } from 'react-i18next'
import { IntervalItem, TradingViewChartTypeItem } from '@/datafeeds/index'
import { TokenDetail } from '@/@generated/gql/graphql-core'
import BubbleMap from '../detailInfo/BubbleMap'

interface showValueOption {
  label: string
  value: string
}

const showValueList: showValueOption[] = [
  {
    label: 'chart.toolbar.price',
    value: 'price',
  },
  {
    label: 'chart.toolbar.marketCap',
    value: 'marketCap',
  },
]
type ChartHeadProps = {
  tokenData: TokenDetail
  defaultPeriod: string
  showPeriodList: IntervalItem[]
  onPeriodChange?: (value: string) => any
  onShowPeriodListChange: (value: IntervalItem[]) => any
  defaultChartType: number
  typeOHLC: 'price' | 'marketCap'
  setTypeOHLC: Dispatch<SetStateAction<'price' | 'marketCap'>>
  chartTypeList: TradingViewChartTypeItem[]
  onChartTypeChange?: (value: number) => any
  onIndictorClick?: () => any
  onSettingClick?: () => any
  // onClickFullScreen?: () => any
  // onClickSave?: () => any
}

function ChartHead({
  tokenData,
  showPeriodList,
  defaultPeriod,
  onPeriodChange,
  onShowPeriodListChange,
  chartTypeList,
  defaultChartType,
  typeOHLC,
  setTypeOHLC,
  onChartTypeChange,
  onIndictorClick,
  onSettingClick,
  // onClickFullScreen,
  // onClickSave,
}: ChartHeadProps) {
  const { t } = useTranslation()
  const [activePeriod, setActivePeriod] = useState<string>(defaultPeriod)
  const [activeChartType, setActiveChartType] = useState<number>(defaultChartType)

  const [showValue, setShowValue] = useState<string>(typeOHLC)

  const handleActivePeriod = (tab: string) => {
    setActivePeriod(tab)
  }

  const handlePeriodChange = (tab: string) => {
    handleActivePeriod(tab)
    if (onPeriodChange) {
      onPeriodChange(tab)
    }
  }

  const handleActiveChartType = (tab: number) => {
    setActiveChartType(tab)
  }

  const handleChartTypeChange = (tab: number) => {
    handleActiveChartType(tab)
    if (onChartTypeChange) {
      onChartTypeChange(tab)
    }
  }

  const handlePeriodListChange = (value: IntervalItem[]) => {
    onShowPeriodListChange(value)
  }

  const handleShowChangeChange = (value: string) => {
    setShowValue(value)
    setTypeOHLC(value)
  }

  return (
    <>
      <div className="bg-[#191919] px-[10px] py-1 flex h-[30px] items-center whitespace-nowrap justify-between flex-nowrap overflow-x-auto _hidescrollbar">
        <div className="flex items-center flex-nowrap">
          <div className="flex items-center flex-shrink-0 mr-2">
            <div className="flex items-center gap-[15px] mr-[6px]">
              {showPeriodList
                .filter((item) => item.show)
                .map((item) => (
                  <div
                    className={cn(
                      'cursor-pointer text-[calc(1rem*(13/16))] ',
                      activePeriod === item.value ? 'text-(--text-primary)' : 'text-(--text-placeholder)',
                    )}
                    key={item.value}
                    onClick={() => {
                      handlePeriodChange(item.value)
                    }}
                  >
                    {item.label}
                  </div>
                ))}
            </div>
            <ButtonPeriod
              currentPeriodList={showPeriodList}
              handlePeriodListChange={handlePeriodListChange}
              activePeriod={activePeriod}
            />
          </div>

          <div className="flex items-center flex-shrink-0 mr-2">
            <div className="flex items-center gap-2 mr-[6px]">
              {chartTypeList.map((item) => (
                <div
                  className="cursor-pointer"
                  key={item.value}
                  onClick={() => {
                    handleChartTypeChange(item.value)
                  }}
                >
                  <img className="size-[16px]" src={activeChartType === item.value ? item.selectedIcon : item.icon} />
                </div>
              ))}
            </div>

            <ButtonKlineStyle currentChartType={activeChartType} handleChartTypeChange={handleChartTypeChange} />
          </div>

          <div className="flex items-center gap-2 flex-shrink-0 mr-2">
            <img
              className="cursor-pointer size-[16px]"
              src="/images/chart/chart-indicator.svg"
              onClick={onIndictorClick}
            />

            <img
              className="cursor-pointer size-[16px]"
              onClick={onSettingClick}
              src="/images/chart/chart-setting.svg"
            />
          </div>

          <div className="flex items-center gap-2 flex-shrink-0 text-[calc(1rem*(10/16))] pl-2 border-l border-solid border-[rgba(236,236,237,0.12)]">
            {showValueList.map((item) => (
              <div
                className={cn(
                  'cursor-pointer',
                  showValue === item.value ? 'text-(--rise)' : 'text-(--text-placeholder)',
                )}
                key={item.value}
                onClick={() => {
                  handleShowChangeChange(item.value)
                }}
              >
                {t(item.label)}
              </div>
            ))}
          </div>
        </div>
        <BubbleMap tokenData={tokenData} />
      </div>
    </>
  )
}

export default ChartHead
