import { useEffect, useState, useRef, memo, useImperativeHandle, forwardRef } from 'react'
import {
  ChartingLibraryWidgetOptions,
  IChartingLibraryWidget,
  LanguageCode,
  ResolutionString,
  ThemeName,
  widget,
} from '../../../public/charting_library'
import { TvConfig, Datafeeds } from '@/datafeeds/index'
import { useTheme } from '@/components/theme-provider.js'
import { capitalizeFirstLetter } from '@/lib/utils.js'
import { TokenDetail } from '@/@generated/gql/graphql-core'
import { formatChartPrice, formatMoney } from '@/utils/helpers'
import { usePreference } from '@/hooks/usePreference'

export interface ChartContainerProps {
  symbol: ChartingLibraryWidgetOptions['symbol']
  interval: ChartingLibraryWidgetOptions['interval']
  datafeedUrl: string
  libraryPath: ChartingLibraryWidgetOptions['library_path']
  chartsStorageUrl: ChartingLibraryWidgetOptions['charts_storage_url']
  chartsStorageApiVersion: ChartingLibraryWidgetOptions['charts_storage_api_version']
  clientId: ChartingLibraryWidgetOptions['client_id']
  userId: ChartingLibraryWidgetOptions['user_id']
  fullscreen: ChartingLibraryWidgetOptions['fullscreen']
  autosize: ChartingLibraryWidgetOptions['autosize']
  studiesOverrides: ChartingLibraryWidgetOptions['studies_overrides']
  container: ChartingLibraryWidgetOptions['container']
}

type TvChartProps = {
  period: string
  chartType: number
  typeOHLC?: 'price' | 'marketCap'
  tokenData: TokenDetail
  onChartReady?: () => void
  isWebview?: boolean
}

const getLanguageFromURL = (): LanguageCode | null => {
  const regex = new RegExp('[\\?&]lang=([^&#]*)')
  const results = regex.exec(location.search)
  return results === null ? null : (decodeURIComponent(results[1].replace(/\+/g, ' ')) as LanguageCode)
}

const getTimeZone = () => {
  let timezone = 'Asia/Shanghai'
  if (Intl && Intl.DateTimeFormat() && Intl.DateTimeFormat().resolvedOptions()) {
    const tempObj = Intl.DateTimeFormat().resolvedOptions()
    timezone = (tempObj && tempObj.timeZone) || 'Asia/Shanghai'
    if (timezone === 'Asia/Saigon') timezone = 'Asia/Ho_Chi_Minh'
  }
  return timezone
}

const TvChart = memo(
  forwardRef(({ period, chartType, tokenData, onChartReady, isWebview, typeOHLC }: TvChartProps, ref) => {
    const { theme } = useTheme()
    const chartContainerRef = useRef<HTMLDivElement>(null) as React.MutableRefObject<HTMLInputElement>
    const tvWidgetRef = useRef<IChartingLibraryWidget>(null)
    const { preference } = usePreference()
    const priceChangeColor = preference?.priceChangeColor || 'normal'

    const defaultProps: Omit<ChartContainerProps, 'container'> = {
      symbol: 'BTC/USD',
      interval: 'D' as ResolutionString,
      datafeedUrl: 'https://demo_feed.tradingview.com',
      libraryPath: '/charting_library/',
      chartsStorageUrl: 'https://saveload.tradingview.com',
      chartsStorageApiVersion: '1.1',
      clientId: 'tradingview.com',
      userId: 'public_user_id',
      fullscreen: false,
      autosize: true,
      studiesOverrides: {},
    }

    function tvIndictor() {
      if (tvWidgetRef && tvWidgetRef?.current) {
        tvWidgetRef?.current?.chart().executeActionById('insertIndicator')
      }
    }

    function tvSetting() {
      if (tvWidgetRef && tvWidgetRef?.current) {
        tvWidgetRef?.current?.chart().executeActionById('chartProperties')
      }
    }

    useImperativeHandle(ref, () => ({
      tvIndictor,
      tvSetting,
    }))

    const getSymbol = () => {
      return {
        name: tokenData?.symbol,
        timezone: getTimeZone(),
        minmov: 1,
        minmov2: 0,
        pointvalue: 1,
        data_status: 'streaming',
        fractional: false,
        session: '24x7',
        has_intraday: true,
        exchange: 'XBIT.LIVE',
        listed_exchange: 'XBIT.LIVE',
        description: `${tokenData.symbol}/USD`,
        // pricescale: Math.pow(10, tokenData.decimals ? Number(tokenData.decimals) : 10),
        pricescale: Math.pow(10, 10),
        ticker: tokenData?.symbol,
        address: tokenData?.address,
        totalSupply: !!tokenData?.totalSupply ? +(+tokenData?.totalSupply / Math.pow(10, +tokenData?.decimals!)) : 1,
        has_weekly_and_monthly: true,
        has_seconds: true,
        seconds_multipliers: [1, 30],
        supported_resolutions: ['1S', '30S', '1', '5', '15', '30', '60', '240', '1D', '1W'],
      }
    }

    const getConfig = () => {
      return {
        supports_time: true,
      }
    }

    const getCatchName = (symbolInfo: any, resolution: any) => {
      const tempName = symbolInfo.description + '_#_' + resolution
      return
    }

    const getTypeOHLC = () => {
      return typeOHLC
    }
    const getOnReadyChart = () => {
      if (onChartReady) {
        onChartReady()
      }
    }

    const externalInstance = {
      getOnReadyChart,
      getConfig,
      getSymbol,
      getCatchName,
      getTypeOHLC,
    }

    useEffect(() => {
      if (!tokenData?.name) return
      const widgetOptions: ChartingLibraryWidgetOptions = Object.assign(
        {
          symbol: defaultProps.symbol as string,
          datafeed: new Datafeeds(externalInstance),
          interval: period as ChartingLibraryWidgetOptions['interval'],
          container: chartContainerRef.current,
          library_path: defaultProps.libraryPath as string,
          timezone: getTimeZone(),
          locale: getLanguageFromURL() || 'en',
          // disabled_features: ['use_localstorage_for_settings'],
          // enabled_features: ['study_templates', 'supports_marks', 'two_character_bar_marks_labels'],
          charts_storage_url: defaultProps.chartsStorageUrl,
          // debug: true,
          charts_storage_api_version: defaultProps.chartsStorageApiVersion,
          client_id: defaultProps.clientId,
          user_id: defaultProps.userId,
          fullscreen: defaultProps.fullscreen,
          autosize: defaultProps.autosize,
          studies_overrides: defaultProps.studiesOverrides,
          custom_formatters: {
            priceFormatterFactory: (symbolInfo: any) => {
              if (symbolInfo === null) {
                return null
              }
              if (symbolInfo.name.endsWith('-MC')) {
                return {
                  format: (value: any) => {
                    return formatMoney(value, false)
                  },
                }
              }
              if (typeOHLC === 'marketCap') {
                return {
                  format: (value: any) => {
                    return formatMoney(value * symbolInfo.totalSupply, false)
                  },
                }
              }
              return {
                format: (value: any) => {
                  return formatChartPrice(value)
                },
              }
            },
          },
          theme: capitalizeFirstLetter(theme) as ThemeName,
        },
        TvConfig(priceChangeColor === 'inverse' ? 'rg' : 'gr'),
      )

      const tvWidget = new widget(widgetOptions)
      tvWidget.onChartReady(() => {
        tvWidgetRef.current = tvWidget
        tvWidget.chart().setChartType(chartType || 1)
        if (onChartReady) {
          onChartReady()
        }
      })

      return () => {
        tvWidget.remove()
      }
    }, [tokenData])

    useEffect(() => {
      if (tvWidgetRef && tvWidgetRef?.current) {
        tvWidgetRef?.current?.chart().setResolution(period as ResolutionString, () => {})
      }
    }, [period])

    useEffect(() => {
      if (tvWidgetRef && tvWidgetRef?.current) {
        tvWidgetRef?.current?.chart().setChartType(chartType)
      }
    }, [chartType])

    useEffect(() => {
      if (tvWidgetRef && tvWidgetRef?.current) {
        tvWidgetRef?.current?.changeTheme(capitalizeFirstLetter(theme) as ThemeName)
      }
    }, [theme])

    const divRef = useRef<HTMLDivElement | null>(null)

    const [height, setHeight] = useState(200)
    const handleMouseDown = (e: React.MouseEvent | React.TouchEvent) => {
      let el = divRef.current!
      e.preventDefault()

      if (!el) return

      const startY = 'clientY' in e ? e.clientY : e.touches[0].clientY
      const screenHeight = window.innerHeight

      const handleMouseMove = (moveEvent: MouseEvent | TouchEvent) => {
        moveEvent.preventDefault()

        const currentY = 'clientY' in moveEvent ? moveEvent.clientY : (moveEvent as TouchEvent).touches[0].clientY
        const newHeight = height + (currentY - startY)
        const maxHeight = screenHeight * 0.75

        setHeight(Math.max(150, Math.min(newHeight, maxHeight)))
      }

      const handleMouseUp = () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
        document.removeEventListener('touchmove', handleMouseMove)
        document.removeEventListener('touchend', handleMouseUp)
      }

      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.addEventListener('touchmove', handleMouseMove, { passive: false })
      document.addEventListener('touchend', handleMouseUp)
    }

    useEffect(() => {
      const el = divRef.current
      if (!el) return

      el.addEventListener('mousedown', handleMouseDown as any)
      el.addEventListener('touchstart', handleMouseDown as any, { passive: false })

      return () => {
        el.removeEventListener('mousedown', handleMouseDown as any)
        el.removeEventListener('touchstart', handleMouseDown as any)
      }
    }, [height])

    useEffect(() => {
      if (typeOHLC) {
        if (tvWidgetRef && tvWidgetRef?.current) {
          const symbol = typeOHLC === 'price' ? tokenData.symbol : `${tokenData.symbol}-MC`
          tvWidgetRef.current.chart().setSymbol(symbol as string)
        }
      }
    }, [typeOHLC])

    return (
      <div className={`watermark relative ${!isWebview ? 'mb-[10px]' : ''}`}>
        <div
          id="chartContainer"
          ref={chartContainerRef}
          className="min-h-[150px] w-full"
          style={
            isWebview
              ? {
                  height: '100vh',
                }
              : {
                  touchAction: 'none',
                  height: `${height}px`,
                  cursor: 'ns-resize',
                }
          }
        />
        <div
          className="absolute w-full bottom-[-10px] flex justify-center cursor-row-resize select-none"
          style={isWebview ? { display: 'none' } : {}}
          ref={divRef}
        >
          <img src="/images/icons/scale-icon.svg" />
        </div>
      </div>
    )
  }),
)

export default TvChart
