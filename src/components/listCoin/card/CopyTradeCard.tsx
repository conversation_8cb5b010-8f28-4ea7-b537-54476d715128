import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ConfirmCollectModal } from '@/components/common/Card/CurrencyListCard'
import ChainCurrencyIcon from '@/components/common/ChainCurrencyIcon'
import useTimeAgoGlobal from '@/hooks/useTimeAgoGlobal'
import { APP_PATH } from '@/lib/constant'
import { listCoinHelper } from '@/utils/list-coin-helper.ts'
import { formatPercent } from '@/utils/numbers'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import TrendChart from './TrendChart'
import WalletTypeBadge from '@/components/common/WalletTypeBadge'
import { formatVolume } from '@/lib/format'
import { formatMoney } from '@/utils/helpers'

// type CopyTradeCardProps = {
//   oneDayPnL: string
//   thirtyDayPnL: string
//   sevenDayAvgBuyCost: string
//   address: string
//   walletType: string | string[]
//   createdTime: number
//   winRate: string
//   marketCap: string
//   trend: 'up' | 'down'
//   lineData: number[]
//   barData: number[]
//   token: string
//   defaultCollect: boolean
//   chainIcon: string
//   currencyIcon: string
// }
export type CopyTradeCardProps = {
  address: string
  name: any
  avatar: any
  tags: string[]
  lastActivityAt: number
  pnl7d: number
  pnl30d: number
  pnl1d: number
  winRate7d: number
  avgCost7d: number
  totalBuy1d: number
  totalBuy7d: number
  totalBuy30d: number
  chainIcon?: string //icon chain, TODO: change to select enums
  currencyIcon?: string //icon currency, TODO: change to select enums
  defaultCollect?: boolean
  dailyProfits: Record<string, number>[]
  isSmartMoneyFormatName?: boolean
  classNameContainer?: string
}

const CopyTradeCard = (props: CopyTradeCardProps) => {
  const {
    address,
    lastActivityAt,
    chainIcon = '/images/solana.webp',
    currencyIcon = '',
    winRate7d,
    totalBuy1d,
    // totalBuy7d,
    totalBuy30d,
    defaultCollect = false,
    dailyProfits = [],
    pnl7d,
    pnl30d,
    pnl1d,
    avgCost7d,
    tags = [],
    isSmartMoneyFormatName = false,
    classNameContainer = ""
  } = props
  const timeAgo = useTimeAgoGlobal(lastActivityAt)
  const profitArr = dailyProfits.map((item) => item?.pnl)
  const { t } = useTranslation()
  const navigate = useNavigate()
  // const { showToastMessengerLink } = useCustomToast()

  const onDetails = () => {
    // example show toast msg link
    // showToastMessengerLink({
    //   title: t('toast.saveSuccess'),
    //   msg: t('listCoin.filters.walletOptions.viewSmartMoney'),
    //   link: `https://t.me/SmartMoneyBot?start=${token}`,
    // });
    navigate(`${APP_PATH.MEME_ASSETS_WALLET}/${address}`)
  }

  return (
    <CardWrapper onClick={onDetails} className={classNameContainer}>
      <div className="p-[0px] rounded-tl-[6px] rounded-tr-[6px]">
        <div className="flex items-center justify-between px-2 py-[calc(1rem*(10/16))] bg-[#12191D] rounded-tl-[6px] rounded-tr-[6px]">
          <div className="flex items-center">
            <div className="flex mr-2 -space-x-4">
              <ChainCurrencyIcon chainIcon={chainIcon} currencyIcon={currencyIcon} avatarClassName="rounded-lg" fallbackImageEnable />
            </div>
            <div className="py-[6px]">
              <div className="mb-2 flex items-center gap-[calc(1rem*(4/16))]">
                <span className="font-medium text-[calc(1rem*(13/16))] leading-[calc(1rem*(13/16))]">
                  {listCoinHelper.formatWalletName(address, isSmartMoneyFormatName ? 10 : 6)}
                </span>
                <WalletTypeBadge type={tags} />
                <span className="text-[calc(1rem*(10/16))] leading-[calc(1rem*(10/16))] text-[#00FFB4]">{timeAgo}</span>
              </div>
              <div className="flex items-center">
                <span className="text-[calc(1rem*(10/16))] leading-[calc(1rem*(10/16))] text-(--text-tertiary) mr-[3px]">
                  {t('listCoin.copyTrade.sevenDayWinRate')}
                </span>
                <span className="font-medium text-[#00FFB4] text-[calc(1rem*(11/16))] leading-[calc(1rem*(11/16))]">
                  {formatPercent(winRate7d)}
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-center">
            <div className="mr-4 text-center">
              <p className="mb-1 font-medium text-[calc(1rem*(13/16))] leading-[calc(1rem*(13/16))] text-[#00FFB4]">
                {formatMoney(pnl7d)}
              </p>
              <p className="text-[calc(1rem*(11/16))] leading-[calc(1rem*(11/16))] text-[#FFFFFFB2]">
                {t('listCoin.copyTrade.sevenDayPnL')}
              </p>
            </div>
            <TrendChart lineData={profitArr} barData={profitArr} barLength={profitArr.length} trend={'up'} />
            <ConfirmCollectModal token={address} defaultCollect={defaultCollect} tokenSymbol={''} isFlow />
          </div>
        </div>
      </div>
      <CardBottom
        className="bg-[linear-gradient(180deg,_rgba(62,_62,_62,_0.35)_0%,_rgba(25,_25,_25,_0.35)_70.26%)]"
        tradeDetails={[
          {
            label: t('listCoin.copyTrade.oneDayPnL'),
            value: `${totalBuy1d ? formatVolume((pnl1d / totalBuy1d) * 100) : '0'}%`,
          },
          {
            label: t('listCoin.copyTrade.thirtyDayPnL'),
            value: `${totalBuy30d ? formatVolume((pnl30d / totalBuy30d) * 100) : '0'}%`,
          },
          { label: t('listCoin.copyTrade.sevenDayAvgBuyCost'), value: `$${avgCost7d ? formatVolume(avgCost7d) : '0'}` },
        ]}
      />
    </CardWrapper>
  )
}

export default CopyTradeCard
