import { useTranslation } from 'react-i18next'
import Container from '@components/common/Container.tsx'
import FollowTypeDropdown from '@components/listCoin/filter/FollowTypeDropdown.tsx'
import { get } from 'lodash-es'
import { SmartMoneyDto, SmartMoneySortType } from '@/@generated/gql/graphql-core.ts'
import { SkeletonList } from '@components/ui/skeleton.tsx'
import CopyTradeCard from '@components/listCoin/card/CopyTradeCard.tsx'
import useGetFollowingSmartMoney from '@hooks/useGetFollowingSmartMoney.ts'
import { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react'
import { IconEmpty, IconSpinner } from '@components/icon'
import useGetTotalFollowingAddress from '@hooks/useGetTotalFollowingAddress.ts'
import ImportExportFollowingWallet from '@components/monitoring/ImportExportFollowingWallet.tsx'
import AddNewFollowingWallet from '@components/monitoring/AddNewFollowingWallet.tsx'

type ListCoinProps = {
  page: number
  sortType: SmartMoneySortType
  setPage: Dispatch<SetStateAction<number>>
}

const ListCoin = ({ page, sortType, setPage }: ListCoinProps) => {
  const { t } = useTranslation()

  const [listData, setListData] = useState<SmartMoneyDto[]>([])
  const [hasMore, setHasMore] = useState(true)
  const observer = useRef<IntersectionObserver | null>(null)
  const lastSmartMoneyRef = useRef<HTMLDivElement | null>(null)

  const { data, loading, error } = useGetFollowingSmartMoney({
    page,
    sortType,
  })
  const listFollowing = get(data, 'getFollowingSmartMoneys', []) as SmartMoneyDto[]

  const handleRenderList = () => {
    if (loading && page === 1) {
      return <SkeletonList count={10} classNameItem="h-[97px]" />
    }

    if (!loading && listData?.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center h-80">
          <IconEmpty />
          <span className="text-[#FFFFFF80] text-[0.75rem]">{t('detail.myPositions.noData')}</span>
        </div>
      )
    }

    return (
      <>
        {listData.map((item, index) => {
          if (index === listFollowing?.length - 1) {
            return (
              <div key={item.address} ref={lastSmartMoneyRef}>
                <CopyTradeCard key={item.address} {...(item as any)} isSmartMoneyFormatName defaultCollect />
              </div>
            )
          }
          return <CopyTradeCard key={item.address} {...(item as any)} isSmartMoneyFormatName defaultCollect />
        })}
        {loading && page > 1 && (
          <div className="h-[89px] w-full flex justify-center items-center">
            <IconSpinner className="size-4 animate-spin" />
          </div>
        )}
      </>
    )
  }
  //handle clear old data
  useEffect(() => {
    return () => {
      setListData([])
    }
  }, [])
  useEffect(() => {
    setListData([])
    setPage(1)
  }, [sortType])
  // Handle data list
  useEffect(() => {
    if (!listFollowing || loading || error) return

    if (page === 1) {
      setListData(listFollowing)
    } else {
      setListData((prev) => [...prev, ...listFollowing])
    }

    setHasMore(listFollowing?.length === 20)
  }, [listFollowing, loading])
  // Handle loadMore
  useEffect(() => {
    if (loading) return

    if (observer.current) {
      observer.current.disconnect()
    }

    observer.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loading) {
          setPage((prevPage) => prevPage + 1)
        }
      },
      { threshold: 0.5 },
    )

    if (lastSmartMoneyRef.current) {
      observer.current.observe(lastSmartMoneyRef.current)
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect()
      }
    }
  }, [loading, hasMore, listData?.length])

  return (
    <>
      <div className="h-[calc(100dvh-72px)] pb-[55px] no-scrollbar">
        <div className="flex flex-col gap-[5px] h-full overflow-y-auto no-scrollbar pb-4 overscroll-y-auto">
          {handleRenderList()}
        </div>
      </div>
    </>
  )
}

const TabSmartMoney = () => {
  const { t } = useTranslation()
  const [page, setPage] = useState<number>(1)
  const [sortType, setSortType] = useState<SmartMoneySortType>(SmartMoneySortType.FollowTime)
  const [openImportExport, setOpenImportExport] = useState(false)
  const [openAddNewFollowing, setOpenAddNewFollowing] = useState(false)

  const { data: totalData, loading: totalLoading } = useGetTotalFollowingAddress()
  const listFollowing = get(totalData, 'getFollowingWalletAddressess') as string[]
  console.log('listFollowing', listFollowing)

  return (
    <Container className="w-full max-h-full flex-1 flex flex-col relative">
      <div className="flex items-center justify-between my-2.5">
        <FollowTypeDropdown sortType={sortType} setSortType={setSortType} />
        <div className="flex items-center gap-2">
          <button
            className="h-7 px-3 font-normal text-[13px] text-white/80 bg-[#ECECED14] rounded-md"
            onClick={() => setOpenImportExport(true)}
          >
            {t('followingWallet.import')}/{t('followingWallet.export')}
          </button>
          <button
            className="h-7 px-3 font-normal text-[13px] text-[#00FFB4] bg-[#ECECED14] rounded-md"
            onClick={() => setOpenAddNewFollowing(true)}
          >
            + {t('followingWallet.addWallet')}
          </button>
        </div>
      </div>
      <ListCoin sortType={sortType} page={page} setPage={setPage} />
      <ImportExportFollowingWallet open={openImportExport} setOpen={setOpenImportExport} listFollowing={listFollowing}/>
      <AddNewFollowingWallet open={openAddNewFollowing} setOpen={setOpenAddNewFollowing} />
    </Container>
  )
}

export default TabSmartMoney
