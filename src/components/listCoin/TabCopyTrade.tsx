import { ConfigStatus } from '@/@generated/gql/graphql-trading'
import { TranslationKeys } from '@/i18n/types'
import { APP_PATH } from '@/lib/constant'
import { tradingClient } from '@/lib/gql/apollo-client'
import { cn } from '@/lib/utils'
import { DataTable } from '@/pages/home/<USER>'
import { getCopyTrades, rankTraders, updateCopyTradeConfigStatus } from '@/services/copytrade.service'
import { ChainIds } from '@/types/enums'
import { totalListKeysInArray } from '@/utils/array'
import { listCoinHelper } from '@/utils/list-coin-helper'
import { formatNumber, formatPercent } from '@/utils/numbers'
import { getTimeAgo } from '@/utils/time'
import { useApolloClient, useMutation, useQuery } from '@apollo/client'
import { useInfiniteQuery } from '@tanstack/react-query'
import { ColumnDef, HeaderContext } from '@tanstack/react-table'
import { useVirtualizer } from '@tanstack/react-virtual'
import { get } from 'lodash-es'
import { HTMLAttributes, ReactNode, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate, useSearchParams } from 'react-router-dom'
import ChainCurrencyIcon from '../common/ChainCurrencyIcon'
import Container from '../common/Container'
import FilterWallet from '../common/FilterWallet'
import WalletTypeBadge, { WalletType } from '../common/WalletTypeBadge'
import {
  IconArrowRight,
  IconCheckCircleSolid,
  IconDelete,
  IconEditParams,
  IconFilteredHead,
  IconSortDown,
  IconSortUp,
  IconSpinner,
} from '../icon'
import { MyWallet, XModal } from '../ui'
import { Button, ButtonProps } from '../ui/button'
import { DialogTitle } from '../ui/dialog'
import { Drawer, DrawerContent, DrawerHeader, DrawerTrigger } from '../ui/drawer'
import { SkeletonList } from '../ui/skeleton'
import CopyTradeCard from './card/CopyTradeCard'
import { useAppSelector } from '@/redux/store'
import { TYPE_ACCOUNT } from '@/lib/blockchain'
import { toast } from 'sonner'
import useGetTotalFollowingAddress from '@/hooks/useGetTotalFollowingAddress'
import { EmptyList } from '../discover/EmptyList'
import { formatCurrency } from '@/lib/number'
import { formatMoney } from '@/utils/helpers'

interface FilterableHeadProps {
  tKey: string
  children?: ReactNode
  open: boolean
  toggle: () => void
}

interface SortHeadProps extends HeaderContext<any, any> {
  tKey: string
}

interface NormalHeadProps {
  tKey: string
  children?: ReactNode
  onClick?: () => void
}

type FilterTagProps = {
  tags: { label: string; value: string }[]
  defaultSelected?: string
  onTagClick?: (tag: string) => any
  className?: string
}

const FilterableHead = (props: FilterableHeadProps) => {
  const { children, open, toggle } = props
  const { t } = useTranslation()
  return (
    <Drawer open={open} onOpenChange={toggle}>
      <DrawerTrigger asChild>
        <div className="flex items-center gap-1">
          <span className="leading-[0.75rem] whitespace-nowrap">{t('listCoin.copyTrade.followStatus')}</span>
          <IconFilteredHead className="!size-[14px]" />
        </div>
      </DrawerTrigger>
      <DrawerContent className="w-full bg-[url('/images/popup-bg.svg')] bg-no-repeat bg-cover max-w-[768px] mx-auto">
        {children}
      </DrawerContent>
    </Drawer>
  )
}

const CopyStatusHead = (props: HeaderContext<any, any>) => {
  const { t } = useTranslation()
  const [open, setOpen] = useState(false)
  const items = useMemo(() => {
    return [
      {
        label: t('listCoin.copyTrade.all'),
        value: 'all',
      },
      {
        label: t('listCoin.copyTrade.running'),
        value: 'running',
      },
      {
        label: t('listCoin.copyTrade.paused'),
        value: 'paused',
      },
      {
        label: t('listCoin.copyTrade.cancel'),
        value: 'canceled',
      },
    ]
  }, [])
  const [selectedItem, setSelectedItem] = useState('all')
  const onSelect = (value: string) => {
    setSelectedItem(value)
    setOpen(false)
    props.column.setFilterValue(value)
  }
  return (
    <FilterableHead tKey="type" open={open} toggle={() => setOpen(!open)} {...props}>
      <>
        <DrawerHeader className="py-0 px-3.5 flex w-full items-center justify-between">
          <DialogTitle className="text-[calc(18rem/16)] leading-[calc(18rem/16)] app-font-medium mb-0.5 text-[#FFFFFF] flex items-center justify-between w-full">
            <span></span>
            <img
              src="/images/icons/icon-x.svg"
              className="w-6 h-6 cursor-pointer"
              onClick={() => setOpen(false)}
              alt=""
            />
          </DialogTitle>
        </DrawerHeader>
        <div className="py-3 px-3.5">
          {items.map((item) => (
            <div
              key={item.value}
              className="py-4 border-b text-[1rem] app-font-medium flex items-center justify-between cursor-pointer"
              onClick={() => onSelect(item.value)}
            >
              <span className="text-[1rem] leading-[1rem]">{item.label}</span>
              {selectedItem === item.value && <IconCheckCircleSolid />}
            </div>
          ))}
        </div>
      </>
    </FilterableHead>
  )
}

const SortHead = (props: SortHeadProps) => {
  const { column, tKey } = props
  const { t } = useTranslation()
  const currentSort = column.getIsSorted()
  const toggleSort = () => {
    if (currentSort === false) {
      column.toggleSorting(true)
    } else if (currentSort === 'desc') {
      column.toggleSorting(false)
    } else if (currentSort === 'asc') {
      column.clearSorting()
    }
  }

  return (
    <div className="text-[calc(11rem/16)] flex items-center text-[#FFFFFF80]">
      <div className="flex items-center cursor-pointer" onClick={toggleSort}>
        <span className="w-max">{t(('listCoin.copyTrade.' + tKey) as TranslationKeys)}</span>
        <div className="flex flex-col ml-1">
          <IconSortUp currentColor={currentSort === 'asc' ? '#FFFFFF' : '#FFFFFF80'} />
          <IconSortDown currentColor={currentSort === 'desc' ? '#FFFFFF' : '#FFFFFF80'} />
        </div>
      </div>
    </div>
  )
}

const NormalHead = (props: NormalHeadProps) => {
  const { tKey, children, onClick } = props
  const { t } = useTranslation()
  return (
    <div className="text-[calc(11rem/16)] flex items-center text-[#FFFFFF80] cursor-pointer w-max" onClick={onClick}>
      {children ?? t(('listCoin.copyTrade.' + tKey) as TranslationKeys)}
    </div>
  )
}

// TODO: make type details
const CopyTradeActions = ({ row }: any) => {
  const { status, id } = row.original
  const { t } = useTranslation()
  const navigate = useNavigate()
  const [isLoading, setIsLoading] = useState(false)
  const [currentStatus, setcurrentStatus] = useState(status);
  const { activeAccount, activeChain, wallets } = useAppSelector((state) => state.wallet)

  const activeWallet =
    activeAccount === TYPE_ACCOUNT.CHAIN ? wallets?.[activeChain]?.chain : wallets?.[activeChain]?.telegram;

  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const onEditParamsSetting = () => {
    navigate(APP_PATH.COPY_TRADING_WALLET_SETTINGS)
  }
  const [mutate] = useMutation(updateCopyTradeConfigStatus, {
    client: tradingClient
  })

  async function handleChangeStatus(setStatus: keyof typeof ConfigStatus) {
    setIsLoading(true)
    let _status: keyof typeof ConfigStatus;
    switch (setStatus) {
      case ConfigStatus.Active:
        _status = ConfigStatus.Paused;
        break;
      case ConfigStatus.Paused:
        _status = ConfigStatus.Active;
        break;
      case ConfigStatus.Canceled:
        _status = ConfigStatus.Canceled;
        break;
      default:
        _status = ConfigStatus.Active;
        break;
    }
    try {
      await mutate({
        variables: {
          input: {
            id,
            status: _status
          },
        },
      });
      setcurrentStatus(_status)
    }
    catch (error) {
      console.error('Error updating status:', error)
    }
    finally {
      setIsLoading(false)
    }
  }

  return (
    <div className={cn('flex items-center gap-1')}>
      <Button
        type='button'
        disabled={currentStatus === ConfigStatus.Canceled}
        isLoading={isLoading}
        status={currentStatus}
        showStatusLabel={true}
        onClick={() => handleChangeStatus(currentStatus)}
        variant="secondary"
        className="flex items-center gap-1 bg-[#ECECED14] text-[#FFFFFFB2] px-2.5 text-[calc(11rem/16)] rounded-full h-7" />
      <Button
        variant="secondary"
        disabled={currentStatus === ConfigStatus.Canceled}
        className="flex items-center gap-1 bg-[#ECECED14] text-[#FFFFFFB2] px-2.5 text-[calc(11rem/16)] rounded-full h-7"
        onClick={onEditParamsSetting}
      >
        <IconEditParams className="!size-3" />
        <span>{t('listCoin.copyTrade.copyTradeEditParams')}</span>
      </Button>
      <Button
        disabled={currentStatus === ConfigStatus.Canceled}
        variant="secondary"
        className="flex items-center gap-1 bg-[#ECECED14] text-[#FFFFFFB2] px-2.5 text-[calc(11rem/16)] rounded-full h-7"
        onClick={() => setShowDeleteModal(true)}
      >
        <img src="/images/icons/close-circle.svg" className="" />
        <span>{t('listCoin.copyTrade.copyTradeDelete')}</span>
      </Button>
      <XModal.Confirmation
        showModal={showDeleteModal}
        setShowModal={setShowDeleteModal}
        title={t('listCoin.copyTrade.confirmDelete')}
        description={`${id}'${t('listCoin.copyTrade.deleteMessage')}`}
        onConfirm={() => {
          setShowDeleteModal(false)
          handleChangeStatus(ConfigStatus.Canceled)
        }}
      />
    </div>
  )
}

const CopyTradeInfo = ({ row }: any) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { status, leaderAddress, leaderNickname, chainIcon, currencyIcon, leaderTags } = row.original
  return (
    <div className={cn('flex items-center', getOpacity(status))} onClick={() => navigate(`${APP_PATH.WALLET_COPY}/${row.original.id}`)}>
      <div className="flex mr-2 -space-x-4">
        <ChainCurrencyIcon chainIcon={chainIcon} currencyIcon={currencyIcon} avatarClassName="rounded-lg" fallbackImageEnable />
      </div>
      <div>
        <div className="mb-1 flex items-center gap-[calc(1rem*(4/16))] mr-4">
          <span className="text-title app-font-medium text-[calc(1rem*(13/16))] leading-[calc(1rem*(13/16))]">
            {leaderNickname.slice(0, 15) || listCoinHelper.formatWalletNameCustom(leaderAddress)}
          </span>
          {status !== ConfigStatus.Canceled ? <span className='w-[3px] h-[3px] rounded-full bg-[#00FFB4]'></span> : null}
          <span className="text-[calc(1rem*(10/16))] leading-[calc(1rem*(10/16))] text-[#FFFFFFB2] mr-[3px]">
            {status === ConfigStatus.Active ? t('listCoin.copyTrade.running') : status === ConfigStatus.Paused ? t('listCoin.copyTrade.paused') : ''}
          </span>
        </div>
        <div className="flex items-center">
          <div className="flex gap-[calc(1rem*(4/16))]">
            {leaderTags?.map((type: WalletType) => <WalletTypeBadge type={type} />)}{' '}
          </div>
          {/* <img src="/images/icons/pump-icon.svg" className="w-[14px] h-[14px]" alt="" /> */}
        </div>
      </div>
    </div>
  )
}

function getOpacity(status: keyof typeof ConfigStatus) {
  return status === ConfigStatus.Canceled ? 'opacity-50' : ''
}

const columns: ColumnDef<any, any>[] = [
  {
    accessorKey: 'leaderAddress',
    header: CopyStatusHead,
    cell: (props) => <CopyTradeInfo row={props.row} />,
    filterFn: (row, _columnId, value) => {
      const cellValue = row.original.status;
      switch (value) {
        case 'running':
          return cellValue === ConfigStatus.Active
        case 'paused':
          return cellValue === ConfigStatus.Paused
        case 'canceled':
          return cellValue === ConfigStatus.Canceled
        default:
          return true
      }
    }
  },
  {
    accessorKey: 'realizedProfit',
    header: () => <NormalHead tKey="realizedProfit" />,
    cell: (props) => {
      //Total PnL:  lấy tổng các giá trị totalProfitInUsd
      //%PnL = sum(totalProfitInUsd) / sum[(totalBuyInUsd - buyCostInUsd)] * 100

      const totalProfitInUsd = get(props, 'row.original.resultStatistics.totalProfitInUsd', 0)
      const totalBuyInUsd = get(props, 'row.original.resultStatistics.totalBuyInUsd', 0)
      const buyCostInUsd = get(props, 'row.original.resultStatistics.buyCostInUsd', 0)
      const profitPercent = (totalProfitInUsd / (totalBuyInUsd - buyCostInUsd)) * 100
      const labelProfit = totalProfitInUsd == 0 ? ' ' : totalProfitInUsd > 0 ? '+' : '-';
      return (
        <div className={cn('app-font-medium text-[#00FFB4]', getOpacity(props.row.original.status))}>
          {/* sum (totalProfitInUsd) */}
          <div className="mb-1 text-[calc(13rem/16)] whitespace-nowrap"><span className="w-[5px]">{labelProfit}</span>{formatCurrency(Math.abs(totalProfitInUsd))}</div>
          {/* sum(totalProfitInUsd) / sum[(totalBuyInUsd - buyCostInUsd)] * 100 */}
          <div className="text-[calc(11rem/16)]"><span className="w-[5px]">{labelProfit}</span>{formatPercent(Math.abs(profitPercent))}</div>
        </div>
      )
    },
  },
  {
    accessorKey: 'resultStatistics.totalBuyInUsd',
    header: (props) => <SortHead {...props} tKey="totalBuy" />,
    cell: (props) => <span className={cn('text-[calc(13rem/16)] text-[#FFFFFF] app-font-medium', getOpacity(props.row.original.status))}>{formatMoney(props.getValue()) || '--'}</span>,
  },
  {
    accessorKey: 'resultStatistics.totalSellInUsd',
    header: (props) => <SortHead {...props} tKey="totalSell" />,
    cell: (props) => <span className={cn('text-[calc(13rem/16)] text-[#FFFFFF] app-font-medium', getOpacity(props.row.original.status))}>{formatMoney(props.getValue()) || '--'}</span>,
  },
  {
    accessorKey: 'buySellCount',
    header: (props) => <SortHead {...props} tKey="buySellCount" />,
    cell: (props) => (
      <span className={cn('text-[calc(13rem/16)] app-font-medium', getOpacity(props.row.original.status))}>
        <span className="text-[#00FFB4]">{get(props, 'row.original.resultStatistics.totalBuy', 0)}</span>
        <span className="text-[#FFFFFFB2]">/</span>
        <span className="text-[#AB57FF]">{get(props, 'row.original.resultStatistics.totalSell', 0)}</span>
      </span>
    ),
  },
  {
    accessorKey: 'statistic.lastTradeTime',
    header: (props) => <SortHead {...props} tKey="lastTradeTime" />,
    cell: (props) => <span className={cn('text-[calc(13rem/16)] text-[#FFFFFFCC] app-font-medium', getOpacity(props.row.original.status))}>{getTimeAgo(props.getValue())}</span>,
  },
  {
    accessorKey: 'myWallet',
    header: (props) => <SortHead {...props} tKey="myWallet" />,
    cell: (props) => {
      const { activeAccount, activeChain, wallets } = useAppSelector((state) => state.wallet)

      const activeWallet = useMemo(() => activeAccount === TYPE_ACCOUNT.CHAIN ? wallets?.[activeChain]?.chain : wallets?.[activeChain]?.telegram, [activeAccount, activeChain, wallets])
      return (<span className={cn('text-[calc(13rem/16)] text-[#FFFFFFCC] app-font-medium', getOpacity(props.row.original.status))}>
        <MyWallet
          userAddress={get(activeWallet, 'walletId', '')}
          avatar={get(activeWallet, 'walletInfo.icon', '')} />
      </span>)
    },
  },
  {
    accessorKey: 'actions',
    header: () => <NormalHead tKey="actions" />,
    cell: (props) => <CopyTradeActions row={props.row} />,
  },
]


type ButtonTagProps = ButtonProps & {
  isActive?: boolean
}

const ButtonTag = ({ className, isActive, children, ...rest }: ButtonTagProps) => {
  return (
    <Button
      className={cn(
        'px-[10px] py-[4px] bg-[#ECECED14] h-auto text-[calc(1rem*(12/16))] text-[#FFFFFF99] font-normal rounded-[4px] leading-[calc(1rem*(14/16))] relative transition-all duration-100 ease-in-out',
        isActive && 'text-[#FFFFFF] text-[calc(1rem*(14/16))] app-font-medium',
        className,
      )}
      {...rest}
    >
      <span
        className={cn(
          'bg-[linear-gradient(45deg,#E149F8,#9945FF,#00F3AB)] absolute inset-0 rounded-[4px] leading-[1] transition-all duration-100 ease-in-out opacity-0 scale-95',
          isActive && 'opacity-100 scale-100',
        )}
      />
      <span
        className={cn(
          'relative transition-all duration-100 ease-in-out leading-[calc(1rem*(14/16))]',
          isActive && 'transform',
        )}
      >
        {children}
      </span>
    </Button>
  )
}

const FilterTag = ({ tags, defaultSelected = tags[0].value, onTagClick, className }: FilterTagProps) => {
  const [selectedTag, setSelectedTag] = useState(defaultSelected)

  const handleTagClick = (tag: string) => {
    setSelectedTag(tag)
    if (onTagClick) {
      onTagClick(tag)
    }
  }

  return (
    <div className={cn('flex align-middle gap-[6px]', className)}>
      {tags.map((tag) => (
        <ButtonTag isActive={selectedTag === tag.value} key={tag.value} onClick={() => handleTagClick(tag.value)}>
          {tag.label}
        </ButtonTag>
      ))}
    </div>
  )
}

const LoadMore = (props: HTMLAttributes<HTMLDivElement>) => {
  return (
    <div className="h-full w-full flex justify-center items-center" {...props}>
      <IconSpinner className="size-4 animate-spin" />
    </div>
  )
}



const TopTraders = () => {
  const currentPage = useRef(1);
  const { t } = useTranslation();
  const [selectedWalletType, setSelectedWalletType] = useState<string | null>(null);
  const { data: totalData } = useGetTotalFollowingAddress()
  const listFollowing = useMemo(() => {
    return get(totalData, 'getFollowingWalletAddressess', []) as string[]
  }, [totalData])
  const walletFilters = useMemo(() => [
    {
      value: null,
      label: t('listCoin.filters.walletOptions.all'),
    },
    {
      value: 'PumpSM',
      label: t('listCoin.filters.walletOptions.pumpSmartMoney')
    },
    {
      value: 'SmartMoney',
      label: t('listCoin.filters.walletOptions.smartMoney')
    },
    {
      value: 'Fresh',
      label: t('listCoin.filters.walletOptions.newWallet')
    },
    {
      value: 'KOL',
      label: t('listCoin.filters.walletOptions.kolVc')
    },
    {
      value: 'Sniper',
      label: t('listCoin.filters.walletOptions.sniper')
    },
  ], [t]);

  // Infinite query configuration
  const tradingClientQuery = useApolloClient()
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading
  } = useInfiniteQuery({
    queryKey: ['topTraders', selectedWalletType],
    queryFn: async ({ pageParam = 1 }) => {
      const response = await tradingClientQuery.query({
        query: rankTraders,
        variables: {
          filter: {
            page: pageParam,
            limit: 20,
            type: selectedWalletType ? [selectedWalletType] : [],
          }
        }
      });
      return response.data;
    },
    getNextPageParam: (lastPage, allPages) => {
      const ranks = get(lastPage, 'rank', []);
      // If the number of items fetched is less than the page size, there are no more pages
      if (ranks.length < 20) return undefined;
      // Use the length of allPages to determine the next page number
      return allPages.length + 1;
    },
    initialPageParam: 1,
  });

  // Flatten all pages into a single array
  const allTraders = data?.pages.flatMap(page => page?.rank || []) || [];

  // Create a reference to the scroll container
  const parentRef = useRef<HTMLDivElement>(null);

  // Virtualizer instance
  const rowVirtualizer = useVirtualizer({
    count: hasNextPage ? allTraders.length + 1 : allTraders.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 105, // Estimate your row height in pixels
    overscan: 10, // Number of items to render outside visible area
  });

  const handleOnLoadMore = useCallback(() => {
    if (!hasNextPage || isFetchingNextPage) return
    fetchNextPage().then((r) => {
      console.log('load more', r)
    })
  }, [hasNextPage, isFetchingNextPage])


  // Handle filter change
  function handleOnChange(index: number) {
    const newWalletType = walletFilters[index].value;
    setSelectedWalletType(newWalletType);
    // The query will automatically refetch when selectedWalletType changes
    // due to it being in the queryKey
  }
  const items = rowVirtualizer.getVirtualItems()

  useEffect(() => {
    const lastItem = items[items.length - 1]
    if (!lastItem) return
    const lastIndex = lastItem.index
    if (lastIndex >= allTraders.length - 3) {
      handleOnLoadMore()
    }
  }, [items])
  return (
    <>
      <div className="px-2 mt-2 flex align-middle justify-between pb-[6px] sticky top-[68px] bg-[#111111] z-20">
        <FilterWallet
          options={walletFilters.map(i => i.label)}
          onChange={handleOnChange}
        />
      </div>
      {/* Virtualized list container */}
      <div className='px-2'>
        {
          isLoading ?
            <SkeletonList count={10} classNameItem="h-[97px]" />
            :
            <div
              ref={parentRef}
              className="h-[calc(100dvh-220px)] pb-[55px] no-scrollbar break-keep overflow-y-auto"
            >
              {/* The scrollable area with total height of all items */}
              <div
                style={{
                  height: `${rowVirtualizer.getTotalSize()}px`,
                  width: '100%',
                  position: 'relative',
                }}
              >
                {
                  items.length ?
                    items.map((virtualItem) => {
                      const isLoaderRow = virtualItem.index > allTraders.length - 1;
                      const trader = allTraders[virtualItem.index];
                      return (
                        <div
                          key={virtualItem.key}
                          data-index={virtualItem.index}
                          style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            transform: `translateY(${virtualItem.start}px)`,
                          }}
                        >
                          {isLoaderRow ? (
                            // Show loading indicator if there are more items to load
                            <div className='my-5'>
                              <LoadMore />
                            </div>
                          ) : (
                            // Show actual trader item
                            <CopyTradeCard
                              key={`${trader.address}-${virtualItem.index}`}
                              {...trader}
                              defaultCollect={listFollowing.includes(trader.address)}
                              classNameContainer='p-[1px] rounded-[6px] bg-[linear-gradient(104.53deg,#00FFB414_11.84%,#01B7820A_40.93%,#00996C00_70.02%,#00FFB466_91.19%)] cursor-pointer'
                            />
                          )}
                        </div>
                      );
                    })
                    : <EmptyList />
                }
              </div>
            </div>
        }
      </div>
    </>
  );
};


export const WalletCopyTrade = () => {
  const { t } = useTranslation();
  const [resultData, setResultData] = useState<any[]>();
  const [selectedWalletType, setSelectedWalletType] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);
  const { activeAccount } = useAppSelector((state) => state.wallet)
  const navigate = useNavigate()

  const onWalletSetting = () => {
    if (activeAccount === TYPE_ACCOUNT.TELEGRAM) {
      navigate(APP_PATH.COPY_TRADING_WALLET_SETTINGS)
    }
    else {
      toast.error(t('listCoin.copyTrade.toast.warningActiveTelegram'), { position: 'bottom-center' })
    }
  }
  // Create query variables using useMemo to prevent unnecessary re-renders
  const queryVariables = useMemo(() => ({
    input: {
      page: currentPage,
      size: itemsPerPage,
      chainId: ChainIds.Solana,
    }
  }), [currentPage, itemsPerPage, selectedWalletType]);

  // Use the Apollo useQuery hook
  const { data, loading } = useQuery(getCopyTrades, {
    variables: queryVariables,
    client: tradingClient,
  });

  useEffect(() => {
    const _rawData = get(data, 'listCopyTradeConfig.items', [])
    const _result: any = [];
    if (_rawData.length > 0) {
      //calculate statistics
      _rawData.forEach((item: any) => {
        const statistics = get(item, 'statistic.copyTradeTokenHoldingStatistics', [])
        const resultStatistics = totalListKeysInArray(statistics, ['totalProfitInUsd', 'totalBuyInUsd', 'buyCostInUsd', 'totalSellInUsd', 'totalBuy', 'totalSell'])
        _result.push({
          ...item,
          resultStatistics
        });
      });
    }
    setResultData(_result)
  }, [data]);

  // function handleRowClick(row: any) {
  //   console.log('Row clicked:', row)
  //   // Handle row click event here
  //   navigate(`${APP_PATH.WALLET_COPY}/${row.id}`)
  // }

  return (
    <div className="h-[calc(100vh_-_125px)] pb-[55px] no-scrollbar break-keep pl-2 pr-2">
      <div className="pt-3 pb-1">
        <Button
          className="gap-0 px-2 bg-[linear-gradient(45deg,#E149F8,#9945FF,#00F3AB)] rounded-full relative transition-all duration-100 hover:scale-[1] w-full h-[40px]"
          variant={'gradient'}
          onClick={onWalletSetting}
        >
          <span className="text-[calc(1rem*(12/16))] font-[400] text-black">
            {t('listCoin.copyTrade.createCopyTrade')}
          </span>
        </Button>
      </div>
      {
        loading ? (
          <SkeletonList count={10} classNameItem="h-[97px]" />
        ) : (
          <DataTable
            columns={columns}
            // data={walletCopyData}
            data={resultData!}
            containerClassName="border-none overflow-x-auto no-scrollbar"
            tableHeadClassName="text-[calc(11rem/16)] leading-[0.75rem] text-[#FFFFFF80] cursor-pointer"
            tableCellClassName="text-[calc(13rem/16)] leading-[0.75rem] break-keep"
          />
        )
      }
    </div>
  )
}

const TabCopyTrade = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const [searchParams, setSearchParams] = useSearchParams();
  const tagFilters: { label: string; value: string }[] = [
    {
      label: t('listCoin.filters.topTraders'),
      value: 'topTraders',
    },
    {
      label: t('listCoin.filters.walletCopyTrade'),
      value: 'walletCopyTrade',
    }
  ];

  const [currentTag, setCurrentTag] = useState<string>(searchParams.get('tag') || tagFilters[0].value)

  const onWalletSetting = () => {
    navigate(APP_PATH.COPY_TRADING_WALLET_SETTINGS)
  }

  const handleClickFilter = (tag: string) => {
    setCurrentTag(tag)
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev)
      newParams.set('tag', tag)
      return newParams
    });
  }

  return (
    <Container className="bg-[#111111] w-full max-h-full flex-1 flex flex-col relative">
      <div className="sticky top-9 flex justify-between items-center gap-4 bg-[#111111] z-20 pt-2 overflow-auto no-scrollbar">
        <FilterTag tags={tagFilters} onTagClick={handleClickFilter} defaultSelected={currentTag} />
        <Button
          className="gap-0 h-6 px-2 bg-[linear-gradient(45deg,#E149F8,#9945FF,#00F3AB)] rounded-[12px] relative transition-all duration-100 hover:scale-[1]"
          onClick={onWalletSetting}
        >
          <span className="text-[calc(1rem*(12/16))] font-[400] text-[#FFFFFF]">
            {t('listCoin.copyTrade.createCopyTrade')}
          </span>
          <IconArrowRight className="!size-3" />
        </Button>
      </div>
      {currentTag === 'topTraders' && <TopTraders />}
      {currentTag === 'walletCopyTrade' && <WalletCopyTrade />}
    </Container>
  )
}

TabCopyTrade.TopTraders = TopTraders
TabCopyTrade.WalletCopyTrade = WalletCopyTrade;

export default TabCopyTrade