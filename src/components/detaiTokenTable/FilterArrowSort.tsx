import { SortByCreateAtType } from '@/types/enums.ts'
import { IconSortDown, IconSortUp } from '@components/icon'

type FilterArrowSortProps = {
  sortByCreatedAt?: SortByCreateAtType
  handleOnclickSort?: () => void
  type?: string
  currentType?: string
}

const FilterArrowSort = ({
  handleOnclickSort,
  sortByCreatedAt,
  type,
  currentType
}: FilterArrowSortProps) => {
  return (
    <div className="flex flex-col ml-1" onClick={handleOnclickSort}>
      <IconSortUp
        currentColor={
          sortByCreatedAt === SortByCreateAtType.ASC && ((!type && !currentType) || (type === currentType))
            ? '#FFFFFF'
            : '#FFFFFF80'
        }
      />
      <IconSortDown
        currentColor={
          sortByCreatedAt === SortByCreateAtType.DESC && ((!type && !currentType) || (type === currentType))
            ? '#FFFFFF'
            : '#FFFFFF80'
        }
      />
    </div>
  )
}

export default FilterArrowSort
