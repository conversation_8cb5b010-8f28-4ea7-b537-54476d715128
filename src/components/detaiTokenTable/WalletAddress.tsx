import { useState, MouseEvent } from 'react'
import { ChainIds } from '@/types/enums.ts'
import { WalletStatisticTooltip } from '@components/detaiTokenTable/WalletStatisticTooltip.tsx'

type WalletAttributes = {
  isDev?: boolean
  isWhale?: boolean
  isInsider?: boolean
  isNativeWallet?: boolean
  isTop10?: boolean
  isSmartMoney?: boolean
  isKOL?: boolean
  isNewWallet?: boolean
}

type WalletAddressProps = {
  address: string
  walletAttributes: WalletAttributes
  tx24h?: number
  onFilterClick?: () => void
  selectedWallet?: string
  holdingPercentage?: number
  chainId?: number
  token: string
  currentPrice?: string
}

const renderIcon = (attributes: WalletAttributes) => {
  if (attributes.isDev) {
    // return <img src="/images/icons/ic-dev.svg" alt="dev" />
    return null
  }
  if (attributes.isWhale) {
    return <img src="/images/icons/ic-whale.svg" alt="whale" />
  }
  if (attributes.isInsider) {
    return <img src="/images/icons/ic-insider.svg" alt="insider" />
  }
  if (attributes.isNativeWallet) {
    return <img src="/images/icons/ic-native-wallet.svg" alt="native wallet" />
  }
  if (attributes.isTop10) {
    return <img src="/images/icons/ic-top-10.svg" alt="top 10" />
  }
  if (attributes.isSmartMoney) {
    return <img src="/images/icons/ic-smart-money.svg" alt="smart money" />
  }
  if (attributes.isKOL) {
    // return <img src="/images/icons/ic-kol.svg" alt="KOL" />
    return null
  }
  if (attributes.isNewWallet) {
    return <img src="/images/icons/ic-new-wallet.svg" alt="new wallet" />
  }
}

const WalletAddress = ({
  address,
  walletAttributes,
  tx24h,
  onFilterClick,
  selectedWallet,
  holdingPercentage = 0,
  chainId = ChainIds.Solana,
  token,
  currentPrice,
}: WalletAddressProps) => {
  const [open, setOpen] = useState<boolean>(false)

  const handleAddressClick = () => {
    setOpen((prev) => !prev)
  }

  const handleFilterClick = (event: MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()
    if (onFilterClick) {
      onFilterClick()
    }
  }

  return (
    <div className={'relative'}>
      <div className="flex items-center gap-1" onClick={handleAddressClick}>
        <div className="w-5">{renderIcon(walletAttributes)}</div>
        <WalletStatisticTooltip
          tx24h={tx24h}
          address={address}
          holdingPercentage={holdingPercentage}
          chainId={chainId}
          token={token}
          currentPrice={currentPrice}
        />
        <img
          src={address === selectedWallet ? '/images/icons/icon-filter-solid.svg' : '/images/icons/icon-filter.svg'}
          alt="filter"
          className="cursor-pointer"
          onClick={handleFilterClick}
        />
      </div>

      {open && <div className="absolute bottom-0 z-10 right-0 bg-[#363642] rounded-[8px]"></div>}
    </div>
  )
}

export default WalletAddress
