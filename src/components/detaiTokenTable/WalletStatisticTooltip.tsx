import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TooltipTrigger } from '@components/ui/tooltip.tsx'
import { fShortenNumber } from '@/lib/number.ts'
import { cn } from '@/lib/utils.ts'
import { CHAIN_EXPLORER_ADDRESS_URLS } from '@/lib/constant.ts'
import { Button } from '@components/ui/button.tsx'
import { useTranslation } from 'react-i18next'
import { useEffect, useMemo, useState } from 'react'
import { useQuery } from '@apollo/client'
import { getWalletStatistics } from '@services/tokens.service.ts'
import { Skeleton } from '@components/ui/skeleton.tsx'

export type WalletStatistics = {
  totalBuyTxs: number // number of buy transactions
  totalSellTxs: number // number of sell transactions
  totalUsdBuyAmount: number // total USD amount of buy transactions
  totalUsdSellAmount: number // total USD amount of sell transactions
  maxHolding: number // maximum token holding
  currentHolding: number // current token holding
  holdingDuration: string // duration of holding the token
}

export interface WalletStatisticTooltipProps {
  tx24h?: number
  address: string
  holdingPercentage: number
  chainId: number
  token: string
  currentPrice?: string
}

export const WalletStatisticTooltip = (props: WalletStatisticTooltipProps) => {
  const { tx24h, address, holdingPercentage, chainId, token, currentPrice } = props
  const [open, setOpen] = useState(false)
  const { t } = useTranslation()

  const { data, loading } = useQuery(getWalletStatistics, {
    variables: {
      input: {
        address: address,
        token: token,
        chainId: chainId,
      },
    },
    skip: !open, // Only fetch data when the tooltip is open
  })

  const walletStatistics = useMemo(() => {
    if (!data || !data.getWalletStatistic) return undefined
    return data.getWalletStatistic as WalletStatistics
  }, [data])

  const profit = useMemo(() => {
    if (!walletStatistics?.totalUsdBuyAmount || !walletStatistics?.totalUsdSellAmount) return null
    return walletStatistics.totalUsdSellAmount - walletStatistics.totalUsdBuyAmount
  }, [walletStatistics])

  const formattedProfit = useMemo(() => {
    if (profit === null || profit === undefined) return '--'
    const isPositive = profit >= 0
    const absValue = Math.abs(profit)
    return (isPositive ? '+' : '-') + '$' + fShortenNumber(absValue)
  }, [profit])

  const positionValue = useMemo(() => {
    if (!walletStatistics?.currentHolding || !currentPrice) return '--'
    const price = parseFloat(currentPrice)
    return `$${fShortenNumber(walletStatistics.currentHolding * price)}`
  }, [walletStatistics, currentPrice])

  useEffect(() => {
    if (open) {
      console.log('wallet started', walletStatistics)
    }
  }, [open, walletStatistics])

  return (
    <TooltipProvider>
      <Tooltip open={open} onOpenChange={setOpen}>
        <TooltipTrigger>
          <div>
            <div className="flex items-center gap-1 app-font-regular leading-[1]">
              <span className="text-[calc(1rem*(9/16))] text-[#00FFB4]">{address.slice(0, 5)}</span>
              {tx24h ? (
                <span className="text-[calc(1rem*(8/16))] text-[#00FFF6] p-0.5 rounded-xs bg-[#00FFF61A] flex items-center justify-center">
                  {fShortenNumber(tx24h)}
                </span>
              ) : null}
            </div>
            <div className="mt-1 bg-[#00FFB433] h-1 w-[50px] relative rounded-r-full">
              <div
                className="absolute w-1/5 h-full bg-[#00FFB4] rounded-r-full"
                style={{ width: `${holdingPercentage}` }}
              ></div>
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent className="bg-[linear-gradient(37.15deg,#E149F8_13.23%,#9945FF_37.52%,#00F3AB_93.06%)] p-[1px] rounded-[8px]">
          {loading && (
            <div className="space-y-2 bg-[#363642] w-48">
              {Array.from({ length: 6 }).map((_, index) => (
                <Skeleton key={index} className="w-full h-4" />
              ))}
            </div>
          )}
          {!loading && (
            <div className="text-[calc(1rem*(11/16))]  bg-[#363642] w-48 rounded-[8px] p-2.5 space-y-3">
              <div className="flex items-center justify-between w-full">
                <span className="text-[#FFFFFF]">
                  {t('detail.tokenDetail.bought', { times: walletStatistics?.totalBuyTxs ?? '--' })}
                </span>
                <span className="text-rise text-[calc(1rem*(12/16))]">
                  {walletStatistics?.totalUsdBuyAmount
                    ? `$${fShortenNumber(walletStatistics.totalUsdBuyAmount)}`
                    : '--'}
                </span>
              </div>
              <div className="flex items-center justify-between w-full">
                <span className="text-[#FFFFFF]">
                  {t('detail.tokenDetail.sold', { times: walletStatistics?.totalSellTxs ?? '--' })}
                </span>
                <span className="text-fall text-[calc(1rem*(12/16))]">
                  {walletStatistics?.totalUsdSellAmount
                    ? `$${fShortenNumber(walletStatistics.totalUsdSellAmount)}`
                    : '--'}
                </span>
              </div>
              <div className="flex items-center justify-between w-full">
                <span className="text-[#FFFFFF]">{t('detail.tokenDetail.pnl')}</span>
                <span className={cn('text-[calc(1rem*(12/16))]', profit && profit < 0 ? 'text-fall' : 'text-rise')}>
                  {formattedProfit}
                </span>
              </div>
              <div className="flex items-center justify-between w-full">
                <span className="text-[#FFFFFF]">{t('detail.tokenDetail.positionValue')}</span>
                <span className="text-white text-[calc(1rem*(12/16))]">{positionValue}</span>
              </div>
              <div className="flex items-center justify-between w-full">
                <span className="text-[#FFFFFF]">{t('detail.tokenDetail.balance')}</span>
                <div className="w-16">
                  <div className="flex justify-between items-center w-full text-[calc(1rem*(9/16))] mb-0.5">
                    <span className="text-white">
                      {walletStatistics?.currentHolding ? `${fShortenNumber(walletStatistics.currentHolding)}` : '--'}
                    </span>
                    <span className="text-white">
                      {walletStatistics?.maxHolding ? `${fShortenNumber(walletStatistics.maxHolding)}` : '--'}
                    </span>
                  </div>
                  <div className="rounded-r-full bg-[#ECECED2E] w-full">
                    <div
                      className="bg-gradient-to-r from-[#A53EFF] to-[#00F7A5] h-1 rounded-r-full"
                      style={{
                        width:
                          walletStatistics?.currentHolding && walletStatistics.maxHolding
                            ? `${(walletStatistics.currentHolding * 100) / walletStatistics.maxHolding}%`
                            : '0%',
                      }}
                    ></div>
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-between w-full">
                <span className="text-[#FFFFFF]">{t('detail.tokenDetail.holdingDuration')}</span>
                <span className="text-white text-[calc(1rem*(12/16))]">
                  {walletStatistics?.holdingDuration ? walletStatistics.holdingDuration : '--'}
                </span>
              </div>
              <a href={CHAIN_EXPLORER_ADDRESS_URLS[chainId] + '/' + address} target="_blank" rel="noopener noreferrer">
                <Button
                  variant="gradient"
                  className="rounded-[8px] before:rounded-[8px] after:rounded-[8px] w-full text-[#1A1A1A] text-[calc(11rem/16)] leading-[calc(11rem/16)]"
                >
                  {t('detail.tokenDetail.viewOnExplorer')}
                  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M10.2083 4.66667C11.0137 4.66667 11.6667 4.01374 11.6667 3.20833C11.6667 2.40292 11.0137 1.75 10.2083 1.75C9.40292 1.75 8.75 2.40292 8.75 3.20833C8.75 4.01374 9.40292 4.66667 10.2083 4.66667Z"
                      stroke="#1A1A1A"
                      strokeWidth="1.16667"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M3.79232 8.45768C4.59773 8.45768 5.25065 7.80476 5.25065 6.99935C5.25065 6.19394 4.59773 5.54102 3.79232 5.54102C2.98691 5.54102 2.33398 6.19394 2.33398 6.99935C2.33398 7.80476 2.98691 8.45768 3.79232 8.45768Z"
                      stroke="#1A1A1A"
                      strokeWidth="1.16667"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M8.75141 3.95898L5.05859 6.19633"
                      stroke="#1A1A1A"
                      strokeWidth="1.16667"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M5.05859 7.74805L8.94954 10.0473"
                      stroke="#1A1A1A"
                      strokeWidth="1.16667"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M10.2083 9.33398C11.0137 9.33398 11.6667 9.98691 11.6667 10.7923C11.6667 11.5977 11.0137 12.2507 10.2083 12.2507C9.40292 12.2507 8.75 11.5977 8.75 10.7923C8.75 9.98691 9.40292 9.33398 10.2083 9.33398Z"
                      stroke="#1A1A1A"
                      strokeWidth="1.16667"
                      strokeLinejoin="round"
                    />
                  </svg>
                </Button>
              </a>
            </div>
          )}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
