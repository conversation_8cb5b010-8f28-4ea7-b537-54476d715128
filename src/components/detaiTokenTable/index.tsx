import { cn } from '@/lib/utils.ts'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import MovingBgGridFilterTags from '@components/common/MovingBgGridFilterTags.tsx'
import { UITab } from '@/types/uiTabs.ts'
import MovingLineTabs from '@components/common/MovingLineTabs.tsx'
import CheckboxWithLabel from '@components/common/CheckboxWithLabel.tsx'
import { ColumnDef } from '@tanstack/react-table'
import { TYPE_BUY, TYPE_SELL, TYPE_TPSL } from '@const/tokenDetail.ts'
import {
  ChainIds,
  DisplayPriceType,
  FilterTransactionAmountType,
  SortByCreateAtType,
  TokenDetailColumnKeys,
} from '@/types/enums.ts'
import TokenDetailDataTable from '@components/detaiTokenTable/TokenDetailDataTable.tsx'
import { mapTransactionType } from '@/utils/mappingType.ts'
import ModalDateTimePicker, { ModalDateTimePickerHandle } from '@components/detaiTokenTable/ModalDateTimePicker.tsx'
import FilterArrowSort from '@components/detaiTokenTable/FilterArrowSort.tsx'
import FilterTransactionAmount, {
  FilterTransactionAmountHandle,
} from '@components/detaiTokenTable/FilterTransactionAmount.tsx'
import FilterVolume, { FilterVolumeHandle } from '@components/detaiTokenTable/FilterVolume.tsx'
import FilterAddress, { FilterAddressHandle } from '@components/detaiTokenTable/FilterAddress.tsx'
import WalletAddress from '@components/detaiTokenTable/WalletAddress.tsx'
import { LIMIT_GET_TRADE_HISTORY } from '@hooks/useGetTradeHistory.ts'
import { useLocation } from 'react-router-dom'
import { RootState, useAppDispatch, useAppSelector } from '@/redux/store'
import { useInfiniteQuery } from '@tanstack/react-query'
import {
  setAddress,
  setData,
  setDisplayDateTimeMode,
  setDisplayPriceType,
  setEndDate,
  setMaxAmount,
  setMinAmount,
  setSortByCreatedAt,
  setStartDate,
  TokenDetailState,
} from '@/redux/modules/tokenDetail.slice.ts'
import { convertTimeWheelToTimestamp, formatLongValue, formatSmartTimeDiff } from '@/utils/helpers.ts'
import dayjs from 'dayjs'
import { useApolloClient } from '@apollo/client'
import { getTradingTransactions } from '@services/tokens.service.ts'
import { TYPE_CHAIN } from '@/lib/blockchain.ts'
import { FollowedTransaction, TransactionClassification, TransactionType } from '@/@generated/gql/graphql-core.ts'
import { Button } from '@components/ui/button.tsx'
import { CHAIN_EXPLORER_TX_URLS } from '@/lib/constant.ts'

const chainMap: Record<string, ChainIds> = {
  [TYPE_CHAIN.SOLANA]: ChainIds.Solana,
  [TYPE_CHAIN.ETH]: ChainIds.Ethereum,
}

const calculateMarketcap = (price: number, totalSupply: number, decimals: number): number => {
  if (price <= 0 || totalSupply <= 0) return 0
  return (price * totalSupply) / Math.pow(10, decimals)
}

const transactionTypeMap: Record<string, TransactionType> = {
  all: TransactionType.All,
  payOrder: TransactionType.Buy,
  sellOrder: TransactionType.Sell,
}

const sortByMap: Record<SortByCreateAtType, string> = {
  [SortByCreateAtType.DESC]: '-timestamp',
  [SortByCreateAtType.ASC]: '+timestamp',
}

const formatTimestamp = (timestamp?: number): string => {
  if (!timestamp) return '0'
  return dayjs.unix(timestamp).format('MM/DD HH:mm:ss')
}

export interface DetailTokenTableProps {
  symbol?: string
  price?: string
}

const DetailTokenTable = (props: DetailTokenTableProps) => {
  const { symbol, price } = props
  const { t } = useTranslation()
  const location = useLocation()

  const dispatch = useAppDispatch()
  const activeChain = useAppSelector((state: RootState) => state.wallet.activeChain)

  const {
    displayDateTimeMode,
    minAmount,
    maxAmount,
    minVolume,
    maxVolume,
    address,
    displayPriceType,
    startDate,
    endDate,
    sortByCreatedAt,
  } = useAppSelector((state: RootState) => state.tokenDetail as TokenDetailState)

  const tagFilters: string[] = [
    t('detail.filters.all'),
    t('detail.filters.followed'),
    t('detail.filters.smartMoney'),
    t('detail.filters.whale'),
    t('detail.filters.sniper'),
    t('detail.filters.projectParty'),
    t('detail.filters.ratWarehouse'),
    t('detail.filters.newWallet'),
    t('detail.filters.kol'),
    t('detail.filters.sameOrigin'),
  ]

  const classificationTypeMap: Record<string, TransactionClassification> = useMemo(() => {
    return {
      [t('detail.filters.followed')]: TransactionClassification.Followed,
      [t('detail.filters.smartMoney')]: TransactionClassification.SmartMoney,
      [t('detail.filters.whale')]: TransactionClassification.Whale,
      [t('detail.filters.sniper')]: TransactionClassification.Sniper,
      [t('detail.filters.projectParty')]: TransactionClassification.ProjectParty,
      [t('detail.filters.ratWarehouse')]: TransactionClassification.Insider,
      [t('detail.filters.newWallet')]: TransactionClassification.Fresh,
      [t('detail.filters.kol')]: TransactionClassification.Kol,
      [t('detail.filters.sameOrigin')]: TransactionClassification.SameSource,
    }
  }, [tagFilters])

  const listTabs: UITab[] = [
    {
      value: 'all',
      label: t('detail.tabs.all'),
    },
    {
      value: 'payOrder',
      label: t('detail.tabs.payOrder'),
    },
    {
      value: 'sellOrder',
      label: t('detail.tabs.sellOrder'),
    },
  ]

  const [currentTab, setCurrentTab] = useState<string>(tagFilters[0])
  const [isRobot, setIsRobot] = useState<boolean>(true)
  const [isBasicVersion, setIsBasicVersion] = useState<boolean>(true)
  const [currentType, setCurrentType] = useState<string>(listTabs[0].value)
  const [paused, setPaused] = useState<boolean>(false)
  const datePickerRef = useRef<ModalDateTimePickerHandle>(null)
  const amountRef = useRef<FilterTransactionAmountHandle>(null)
  const volumeRef = useRef<FilterVolumeHandle>(null)
  const addressRef = useRef<FilterAddressHandle>(null)

  const tokenAddress = useMemo(() => {
    const segments = location.pathname.split('/').filter(Boolean)
    return segments.at(-1) || ''
  }, [location.pathname])

  const apolloClient = useApolloClient()

  const {
    data: transactions,
    isFetchingNextPage,
    hasNextPage,
    isLoading,
    fetchNextPage,
  } = useInfiniteQuery({
    queryKey: [
      'tradingTransactions',
      tokenAddress,
      activeChain,
      currentType,
      address,
      minAmount,
      maxAmount,
      minVolume,
      maxVolume,
      startDate,
      endDate,
      sortByCreatedAt,
      isRobot,
      currentTab,
    ],
    initialPageParam: undefined,
    refetchInterval: 5000,
    enabled: !paused,
    getNextPageParam: (lastPage: FollowedTransaction[]) => {
      if (!lastPage || lastPage.length < LIMIT_GET_TRADE_HISTORY) return undefined
      const lastItem = lastPage.slice(-1)[0]
      return lastItem ? lastItem.timestamp : undefined
    },
    queryFn: async ({ pageParam }) => {
      const { data } = await apolloClient.query({
        query: getTradingTransactions,
        variables: {
          input: {
            token: tokenAddress,
            chainId: chainMap[activeChain] || ChainIds.Solana,
            type: transactionTypeMap[currentType] ?? TransactionType.All,
            address: address ? address : undefined,
            transactionUsdAmountFrom: minAmount > 0 ? minAmount : undefined,
            transactionUsdAmountTo: maxAmount > 0 ? maxAmount : undefined,
            transactionVolumeFrom: minVolume > 0 ? Number(minVolume) : undefined,
            transactionVolumeTo: maxVolume > 0 ? Number(maxVolume) : undefined,
            timestampFrom: startDate ? Number(convertTimeWheelToTimestamp(startDate)) / 1000 : undefined,
            timestampTo: endDate ? Number(convertTimeWheelToTimestamp(endDate)) / 1000 : undefined,
            sortBy: sortByCreatedAt ? sortByMap[sortByCreatedAt] : undefined,
            lastTimestamp: pageParam ? Number(pageParam as string) : undefined,
            filterRobot: !isRobot,
            classification: classificationTypeMap[currentTab] ?? undefined,
          },
        },
      })
      return data.getTradingTransactions.data as FollowedTransaction[]
    },
  })

  const tradingTransactions = useMemo(() => {
    if (!transactions || transactions.pages.length === 0) return []
    return transactions.pages.flatMap((page) => page)
  }, [transactions])

  const handleClickSoldPrice = () => {
    dispatch(
      setDisplayPriceType(displayPriceType === DisplayPriceType.PRICE ? DisplayPriceType.MC : DisplayPriceType.PRICE),
    )
  }

  const handleTextColor = (type: string) => {
    switch (type) {
      case TYPE_BUY:
        return 'text-rise'
      case TYPE_SELL:
        return 'text-fall'
      case TYPE_TPSL:
        return 'text-white'
      default:
        return 'text-white'
    }
  }

  const handleSort = () => {
    const currentDirection = sortByCreatedAt
    if (currentDirection === SortByCreateAtType.DESC) {
      dispatch(setSortByCreatedAt(SortByCreateAtType.ASC))
    } else if (currentDirection === SortByCreateAtType.ASC) {
      dispatch(setSortByCreatedAt(undefined))
    } else {
      dispatch(setSortByCreatedAt(SortByCreateAtType.DESC))
    }
  }

  const tokenDetailColumns: ColumnDef<FollowedTransaction>[] = [
    {
      accessorKey: TokenDetailColumnKeys.TIME,
      header: () => (
        <div className="flex items-center gap-[2px] min-w-[80px]">
          <div>{t('detail.tokenDetail.time')}</div>
          <div className="flex items-center cursor-pointer justify-center gap-1">
            <FilterArrowSort sortByCreatedAt={sortByCreatedAt} handleOnclickSort={handleSort} />
            <img
              src="/images/tokenDetail/icon-clock.svg"
              className="w-[12px] h-[12px]"
              alt="icon clock"
              onClick={() => dispatch(setDisplayDateTimeMode(!displayDateTimeMode))}
            />
            <Button size="xs" className="rounded-full bg-transparent p-0" onClick={() => datePickerRef.current?.open()}>
              <img
                src={startDate || endDate ? '/images/icons/icon-filter-solid.svg' : '/images/icons/icon-filter.svg'}
                className="w-[11px] h-[11px]"
                alt="icon filter"
              />
            </Button>
          </div>
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex items-center text-[12px] leading-[1] app-font-regular h-[31px]">
          {displayDateTimeMode
            ? formatTimestamp(row?.original?.timestamp ? +row?.original?.timestamp : 0)
            : formatSmartTimeDiff(parseInt(row?.original?.timestamp) * 1000)}
        </div>
      ),
    },
    {
      accessorKey: TokenDetailColumnKeys.TYPE,
      header: () => <div className="min-w-[64px]">{t('detail.tokenDetail.direction')}</div>,
      cell: ({ row }) => (
        <div className={cn(handleTextColor(row?.original?.type), 'text-[calc(13rem/16)] font-medium')}>
          {mapTransactionType(row?.original?.type)}
        </div>
      ),
    },
    {
      accessorKey: TokenDetailColumnKeys.TRANSACTION_AMOUNT, // volume
      header: () => (
        <div className="flex items-center gap-[2px] min-w-[80px]">
          <div>{t('detail.tokenDetail.turnover')}</div>
          <div className="flex items-center cursor-pointer justify-center gap-1">
            <Button size="xs" className="rounded-full bg-transparent p-0" onClick={() => amountRef.current?.open()}>
              <img
                src={
                  minAmount > 0 || maxAmount > 0
                    ? '/images/icons/icon-filter-solid.svg'
                    : '/images/icons/icon-filter.svg'
                }
                className="w-[11px] h-[11px]"
                alt="icon filter"
              />
            </Button>
            <img
              src="/images/orderBook/icon-refund.svg"
              className="w-[14px] h-[14px] cursor-not-allowed"
              alt="icon refund"
            />
          </div>
        </div>
      ),
      cell: ({ row }) => (
        <div className={cn(handleTextColor(row?.original?.type), 'text-[calc(13rem/16)] font-medium')}>
          ${formatLongValue(parseFloat(row?.original?.usdAmount))}
        </div>
      ),
    },
    {
      accessorKey: TokenDetailColumnKeys.SOLD_PRICE,
      header: () => (
        <div className="min-w-[80px]">
          <div
            onClick={handleClickSoldPrice}
            className="flex items-center gap-[2px] w-fit px-2 py-[4.5px] bg-[#ECECED14] cursor-pointer rounded-[3px]"
          >
            <div className="cursor-pointer">
              {displayPriceType === DisplayPriceType.PRICE
                ? t('detail.tokenDetail.finalPrice')
                : t('detail.tokenDetail.marketCap')}
            </div>
            <img src="/images/futuresDetail/arrow-swap-icon.svg" className="block w-[9px] h-[9px]" alt="icon swap" />
          </div>
        </div>
      ),
      cell: ({ row }) => (
        <div className={cn(handleTextColor(row?.original?.type), 'text-[calc(13rem/16)] font-medium')}>
          {formatLongValue(
            parseFloat(
              displayPriceType === DisplayPriceType.PRICE
                ? row?.original?.price
                : calculateMarketcap(
                    row?.original?.price,
                    row?.original?.totalSupply,
                    row?.original?.decimals as number,
                  ),
            ),
          )}
        </div>
      ),
    },
    {
      accessorKey: TokenDetailColumnKeys.VOLUME,
      header: () => (
        <div className="flex items-center gap-[2px] min-w-[80px]">
          <div>{t('detail.tokenDetail.volume')}</div>
          <div className="flex items-center cursor-pointer justify-center">
            <Button size="xs" className="rounded-full bg-transparent p-0" onClick={() => volumeRef.current?.open()}>
              <img
                src={
                  minVolume > 0 || maxVolume > 0
                    ? '/images/icons/icon-filter-solid.svg'
                    : '/images/icons/icon-filter.svg'
                }
                className="w-[11px] h-[11px]"
                alt="icon filter"
              />
            </Button>
          </div>
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-[calc(13rem/16)] font-medium">
          {formatLongValue(parseFloat(row?.original?.baseAmount))}
        </div>
      ),
    },
    {
      accessorKey: TokenDetailColumnKeys.WALLET,
      header: () => (
        <div className="flex items-center gap-[2px] min-w-[120px] pl-6">
          <div>{t('detail.tokenDetail.wallet')}</div>
          <div className="flex items-center cursor-pointer justify-center">
            <Button
              size="xs"
              className="rounded-full bg-transparent p-0"
              onClick={() => addressRef.current?.open(address)}
            >
              <img
                src={address ? '/images/icons/icon-filter-solid.svg' : '/images/icons/icon-filter.svg'}
                className="w-[11px] h-[11px]"
                alt="icon filter"
              />
            </Button>
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const walletAttributes = {
          isDev: row?.original?.isDev,
          isWhale: row?.original?.isWhale,
          isInsider: row?.original?.isInsider,
          isNativeWallet: row?.original?.isNativeWallet,
          isTop10: row?.original?.isTopTrader,
          isSmartMoney: row?.original?.isSmartMoney,
          isKOL: row?.original?.isKOL,
          isNewWallet: row?.original?.isFreshWallet,
        }
        return (
          <WalletAddress
            address={row?.original.maker}
            walletAttributes={walletAttributes}
            tx24h={row?.original?.tx24h ?? 0}
            selectedWallet={address}
            chainId={chainMap[activeChain]}
            holdingPercentage={row?.original?.holderPct ? +row?.original?.holderPct : 0}
            token={row.original.baseToken}
            currentPrice={price}
            onFilterClick={() => {
              if (!address) {
                dispatch(setAddress(row?.original.maker))
              } else {
                dispatch(setAddress(''))
              }
            }}
          />
        )
      },
    },
    {
      accessorKey: TokenDetailColumnKeys.ACTION,
      header: () => <div className="min-w-[64px] text-center">{t('detail.tokenDetail.action')}</div>,
      cell: ({ row }) => (
        <div className="flex items-center justify-center gap-2">
          <a
            href={`${CHAIN_EXPLORER_TX_URLS[chainMap[activeChain]]}/${row.original.txHash}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M14.5833 6.66667C15.7339 6.66667 16.6667 5.73392 16.6667 4.58333C16.6667 3.43274 15.7339 2.5 14.5833 2.5C13.4327 2.5 12.5 3.43274 12.5 4.58333C12.5 5.73392 13.4327 6.66667 14.5833 6.66667Z"
                stroke="#CBCDD4"
                strokeWidth="1.66667"
                strokeLinejoin="round"
              />
              <path
                d="M5.41536 12.0827C6.56595 12.0827 7.4987 11.1499 7.4987 9.99935C7.4987 8.84877 6.56595 7.91602 5.41536 7.91602C4.26478 7.91602 3.33203 8.84877 3.33203 9.99935C3.33203 11.1499 4.26478 12.0827 5.41536 12.0827Z"
                stroke="#CBCDD4"
                strokeWidth="1.66667"
                strokeLinejoin="round"
              />
              <path
                d="M12.5001 5.65625L7.22461 8.85246"
                stroke="#CBCDD4"
                strokeWidth="1.66667"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M7.22461 11.0684L12.7831 14.3529"
                stroke="#CBCDD4"
                strokeWidth="1.66667"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M14.5833 13.334C15.7339 13.334 16.6667 14.2667 16.6667 15.4173C16.6667 16.5679 15.7339 17.5007 14.5833 17.5007C13.4327 17.5007 12.5 16.5679 12.5 15.4173C12.5 14.2667 13.4327 13.334 14.5833 13.334Z"
                stroke="#CBCDD4"
                strokeWidth="1.66667"
                strokeLinejoin="round"
              />
            </svg>
          </a>
        </div>
      ),
    },
  ]

  const handleTabChange = (tab: string) => {
    setCurrentTab(tab)
  }
  const handleOnChangeIsRobot = (status?: boolean) => {
    setIsRobot(!!status)
  }
  const handleOnChangeIsBasicVersion = (status?: boolean) => {
    setIsBasicVersion(!status)
  }

  const loadMoreFn = () => {
    if (isLoading || isFetchingNextPage || !hasNextPage) return false
    fetchNextPage().then(() => {})
    return true
  }

  useEffect(() => {
    return () => {
      dispatch(setData([]))
    }
  }, [])

  return (
    <div className="sticky top-[30px] translate-y-[-30px] mt-8 z-[1] px-3">
      {/* Select filter */}
      <div className={cn('relative pt-[1px] mt-[5px]')}>
        <MovingBgGridFilterTags
          tabs={tagFilters}
          defaultTab={tagFilters[0]}
          activeTab={currentTab}
          onTabChange={handleTabChange}
          containerId="token-detail-pairs"
          containerClassName="mt-2.5 w-full overflow-x-auto no-scrollbar"
          tabsTriggerClassName="px-1"
        />
      </div>
      {/* Type Filter */}
      <div className="relative z-[2] flex mt-3 items-center justify-between">
        <MovingLineTabs
          containerClassName="justify-start bg-transparent after:hidden"
          tabsListClassName="p-0 gap-2 h-[19px] md:gap-5"
          itemClassName="px-0 pt-0 pb-1.5 text-[calc(1rem*(13/16))] leading-[1] font-medium "
          tabs={listTabs}
          defaultTab={currentType}
          onTabChange={(tab) => setCurrentType(tab)}
        />
        <div className="flex items-center gap-1.5 md:gap-3">
          <CheckboxWithLabel
            label={t('detail.filters.isRobot')}
            defaultChecked={isRobot}
            isChecked={isRobot}
            onChange={handleOnChangeIsRobot}
          />
          <CheckboxWithLabel
            label={t('detail.filters.basicVersion')}
            defaultChecked={isBasicVersion}
            isChecked={isBasicVersion}
            onChange={handleOnChangeIsBasicVersion}
          />
          <div
            className={cn(
              'rounded-full p-1 pr-2  w-16 h-5 text-[calc(10rem/16)] leading-2.5 flex items-center justify-center cursor-pointer',
              paused ? 'bg-[#00FFF633] text-[#00FFF6]' : 'bg-[#00FFB433] text-[#00FFB4]',
            )}
            onClick={() => setPaused(!paused)}
          >
            {paused ? (
              <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M2.21875 3.85547H9.76431V3.86145H2.21875V3.85547Z" fill="#00FFF6" />
                <path
                  d="M2.88312 3.85521L9.10133 3.86118V3.85521H2.88312ZM1.20898 2.89856L2.88312 2.90454V2.89856H1.20898ZM9.10133 2.89856L10.7755 2.90454V2.89856H9.10133ZM3.83977 2.89856H8.14469V2.89258L3.83977 2.89856Z"
                  fill="currentColor"
                />
                <path
                  d="M3.71115 10.5C3.22227 10.5 2.82227 10.1 2.82227 9.61111V3.38889C2.82227 2.9 3.22227 2.5 3.71115 2.5C4.20004 2.5 4.60004 2.9 4.60004 3.38889V9.61111C4.60004 10.1 4.21115 10.5 3.71115 10.5ZM8.1556 10.5C7.66671 10.5 7.26671 10.1 7.26671 9.61111V3.38889C7.26671 2.9 7.66671 2.5 8.1556 2.5C8.64449 2.5 9.04449 2.9 9.04449 3.38889V9.61111C9.04449 10.1 8.6556 10.5 8.1556 10.5Z"
                  fill="currentColor"
                />
              </svg>
            ) : (
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="currentColor"
                xmlns="http://www.w3.org/2000/svg"
                className="size-3"
              >
                <path
                  d="M7.87 21.28C7.08 21.28 6.33 21.09 5.67 20.71C4.11 19.81 3.25 17.98 3.25 15.57V8.43999C3.25 6.01999 4.11 4.19999 5.67 3.29999C7.23 2.39999 9.24 2.56999 11.34 3.77999L17.51 7.33999C19.6 8.54999 20.76 10.21 20.76 12.01C20.76 13.81 19.61 15.47 17.51 16.68L11.34 20.24C10.13 20.93 8.95 21.28 7.87 21.28ZM7.87 4.21999C7.33 4.21999 6.85 4.33999 6.42 4.58999C5.34 5.20999 4.75 6.57999 4.75 8.43999V15.56C4.75 17.42 5.34 18.78 6.42 19.41C7.5 20.04 8.98 19.86 10.59 18.93L16.76 15.37C18.37 14.44 19.26 13.25 19.26 12C19.26 10.75 18.37 9.55999 16.76 8.62999L10.59 5.06999C9.61 4.50999 8.69 4.21999 7.87 4.21999Z"
                  fill="currentColor"
                />
              </svg>
            )}
            {paused ? t('detail.trade.paused') : t('detail.trade.running')}
          </div>
        </div>
      </div>
      {/* Table */}
      <div className="relative pb-4 z-[3]">
        <TokenDetailDataTable
          loading={isLoading}
          isStickyHeader
          data={tradingTransactions}
          columns={tokenDetailColumns}
          onBottomReached={loadMoreFn}
          containerClassName="border-0 max-h-[calc(100vh-210px)] select-none"
          tableHeaderRowClassName="!border-0 !bg-[#111111]"
          tableHeaderClassName="border-0 text-[#FFFFFF80] text-[calc(1rem*(11/16))] z-10 leading-3 app-font-medium"
          tableHeadClassName="first:px-0"
          tableCellClassName="first:px-0"
        />
      </div>
      <div className="hidden">
        <ModalDateTimePicker
          ref={datePickerRef}
          handleChangeStartTime={(time) => dispatch(setStartDate(time))}
          handleChangeEndTime={(time) => dispatch(setEndDate(time))}
        />
        <FilterTransactionAmount
          ref={amountRef}
          type={FilterTransactionAmountType.USDT}
          defaultMin={minAmount > 0 ? minAmount.toString() : undefined}
          defaultMax={maxAmount > 0 ? maxAmount.toString() : undefined}
          handleMinChange={(value: number) => dispatch(setMinAmount(value))}
          handleMaxChange={(value: number) => dispatch(setMaxAmount(value))}
        />
        <FilterVolume ref={volumeRef} token={symbol ?? ''} />
        <FilterAddress ref={addressRef} onAddressChange={(value) => dispatch(setAddress(value))} />
      </div>
    </div>
  )
}

export default DetailTokenTable
