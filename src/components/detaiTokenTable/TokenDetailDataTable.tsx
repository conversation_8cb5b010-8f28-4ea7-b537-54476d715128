import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  SortingState,
  useReactTable,
  getSortedRowModel,
  Row,
  getFilteredRowModel,
  Cell,
} from '@tanstack/react-table'

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import React, { useState } from 'react'
import { cn } from '@/lib/utils.ts'
import { IconEmpty } from '@components/icon'
import { useTranslation } from 'react-i18next'
import { TokenDetailColumnKeys } from '@/types/enums.ts'
import { TokenDetailRow } from '@/types/tokenDetail.ts'
import { TYPE_TPSL } from '@const/tokenDetail.ts'
import { Skeleton } from '@components/ui/skeleton.tsx'

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  isStickyHeader?: boolean
  isStickyFirstColumn?: boolean
  stickyBg?: string
  containerClassName?: string
  tableClassName?: string
  tableHeaderClassName?: string
  tableHeaderRowClassName?: string
  tableHeadClassName?: string
  tableBodyClassName?: string
  tableBodyRowClassName?: string
  tableCellClassName?: string
  onRowClick?: (data: any) => void
  onBottomReached?: () => void
  loading?: boolean
}

export default function TokenDetailDataTable<TData, TValue>({
  columns,
  data,
  isStickyHeader,
  isStickyFirstColumn,
  stickyBg,
  containerClassName,
  tableClassName,
  tableHeaderClassName,
  tableHeaderRowClassName,
  tableHeadClassName,
  tableBodyClassName,
  tableBodyRowClassName,
  tableCellClassName,
  onRowClick,
  onBottomReached,
  loading,
}: DataTableProps<TData, TValue>) {
  const { t } = useTranslation()
  const [sorting, setSorting] = useState<SortingState>([])

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: { sorting },
  })

  const handleRowClick = (row: Row<TData>) => {
    onRowClick?.(row.original)
  }

  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const target = event.currentTarget

    const isVerticalScrollNearBottom = target.scrollHeight - target.scrollTop - target.clientHeight < 10

    // Only trigger if there's vertical scrolling
    if (isVerticalScrollNearBottom && target.scrollTop > 0) {
      const scrollPercentage = (target.scrollTop + target.clientHeight) / target.scrollHeight

      if (scrollPercentage >= 0.75) {
        onBottomReached?.()
      }
    }
  }

  const renderCell = (cell: Cell<TData, unknown>, index: number) => {
    const commonProps = {
      key: cell.id,
      className: cn('z-0', tableCellClassName, index === 0 && isStickyFirstColumn && 'sticky left-0 z-1'),
      style: isStickyFirstColumn && stickyBg ? { background: stickyBg } : {},
    }

    if ((cell.row.original as TokenDetailRow).type === TYPE_TPSL) {
      if (cell.column.id === TokenDetailColumnKeys.SOLD_PRICE || cell.column.id === TokenDetailColumnKeys.VOLUME)
        return null
      if (cell.column.id === TokenDetailColumnKeys.TRANSACTION_AMOUNT) {
        return (
          <TableCell {...commonProps} key={commonProps.key} colSpan={3}>
            {flexRender(cell.column.columnDef.cell, cell.getContext())}
          </TableCell>
        )
      }
    }

    return (
      <TableCell {...commonProps} key={commonProps.key}>
        {flexRender(cell.column.columnDef.cell, cell.getContext())}
      </TableCell>
    )
  }

  const renderBody = () => {
    if (loading) {
      return (
        <TableRow className={tableBodyRowClassName}>
          <TableCell colSpan={columns.length} className={cn('h-24 text-center', tableCellClassName)}>
            <Skeleton className="w-full h-full" />
          </TableCell>
        </TableRow>
      )
    }

    const rows = table.getRowModel().rows
    if (rows.length === 0) {
      return (
        <TableRow className={tableBodyRowClassName}>
          <TableCell colSpan={columns.length} className={cn('h-24 text-center', tableCellClassName)}>
            <div className="flex flex-col items-center justify-center fixed left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <IconEmpty />
              <span className="text-[#FFFFFF80] text-[0.75rem]">{t('history.nodata')}</span>
            </div>
          </TableCell>
        </TableRow>
      )
    }

    return rows.map((row) => (
      <TableRow
        key={row.id}
        data-state={row.getIsSelected() && 'selected'}
        className={tableBodyRowClassName}
        onClick={() => handleRowClick(row)}
      >
        {row.getVisibleCells().map(renderCell)}
      </TableRow>
    ))
  }

  return (
    <div
      className={cn('rounded-md border overflow-auto no-scrollbar', containerClassName, isStickyHeader && 'relative')}
      onScroll={handleScroll}
    >
      <Table className={cn(tableClassName, isStickyHeader && 'relative w-full h-full')}>
        <TableHeader className={cn(tableHeaderClassName, isStickyHeader && 'sticky top-0 z-[10]')}>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id} className={tableHeaderRowClassName}>
              {headerGroup.headers.map((header, index) => (
                <TableHead
                  key={header.id}
                  className={cn(
                    'z-0',
                    tableHeadClassName,
                    index === 0 && isStickyFirstColumn && 'sticky left-0 z-[10]',
                  )}
                  style={isStickyFirstColumn && stickyBg ? { background: stickyBg } : {}}
                >
                  {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody className={tableBodyClassName}>{renderBody()}</TableBody>
      </Table>
    </div>
  )
}
