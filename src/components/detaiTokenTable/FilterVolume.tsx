import { useTranslation } from 'react-i18next'
import React, { Ref, useImperativeHandle, useState } from 'react'
import { Button } from '@components/ui/button.tsx'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from '@components/ui/drawer.tsx'
import { Loader2, X } from 'lucide-react'
import InputBorderGradient from '@components/orderForm/InputBorderGradient.tsx'
import { useAppDispatch } from '@/redux/store'
import { setData, setMaxVolume, setMinVolume, setPage } from '@/redux/modules/tokenDetail.slice.ts'

export type FilterVolumeHandle = {
  open: () => void
}

type FilterVolumeProps = {
  token: string
  ref?: Ref<FilterVolumeHandle>
}

const FilterVolume = ({ token, ref }: FilterVolumeProps) => {
  const { t } = useTranslation()
  const dispatch = useAppDispatch()

  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [minValue, setMinValue] = useState('')
  const [maxValue, setMaxValue] = useState('')

  useImperativeHandle(ref, () => ({
    open: () => setOpen(true),
  }))

  const handleInputValue = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === '-') e.preventDefault()
  }

  const handleConfirmClick = () => {
    setLoading(true)

    const hasMin = minValue !== ''
    const hasMax = maxValue !== ''

    if (hasMin) dispatch(setMinVolume(Number(minValue)))
    if (hasMax) dispatch(setMaxVolume(Number(maxValue)))

    if (hasMin || hasMax) {
      dispatch(setPage(1))
      dispatch(setData([]))
    }

    setLoading(false)
    setOpen(false)
  }

  const handleResetClick = () => {
    setMinValue('')
    setMaxValue('')
    setOpen(false)
    dispatch(setMinVolume(-1))
    dispatch(setMaxVolume(-1))
  }

  return (
    <>
      <Button size="xs" className="rounded-full bg-transparent p-0" onClick={() => setOpen(true)}>
        <img src="/images/icons/icon-filter.svg" className="w-[11px] h-[11px]" alt="icon filter" />
      </Button>

      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerContent className="w-full bg-[#232329] max-w-[768px] max-h-[80vh] mx-auto">
          <DrawerHeader>
            <DrawerTitle className="mt-1.5">
              <div className="text-[cal c(1rem*(22/16))] leading-[1] app-font-regular text-left">
                {t('detail.tokenDetail.volume')}
              </div>
            </DrawerTitle>

            <DrawerClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
              <X className="size-5" />
            </DrawerClose>
          </DrawerHeader>

          <DrawerDescription className="flex flex-col px-4 gap-3">
            <InputBorderGradient
              unit={token}
              placeHolder={t('detail.tokenDetail.minimumVolume')}
              containerClassName="h-[48px] px-[14px] py-[12px] rounded-[8px]"
              innerBgClassName="rounded-[8px]"
              inputClassName="placeholder:text-[#FFFFFF5C] placeholder:text-[calc(1rem*(14/16))] text-[calc(1rem*(14/16))] max-w-[100%] flex-1"
              unitClassName="min-w-[auto] text-[calc(1rem*(14/16))] text-[#FFFFFF99] leading-[1]"
              inputProps={{
                type: 'number',
                onKeyDown: handleInputValue,
                value: minValue,
                onChange: (e) => setMinValue(e.target.value),
              }}
            />
            <InputBorderGradient
              unit={token}
              placeHolder={t('detail.tokenDetail.maximumVolumeWithToken', { token })}
              containerClassName="h-[48px] px-[14px] py-[12px] rounded-[8px]"
              innerBgClassName="rounded-[8px]"
              inputClassName="placeholder:text-[#FFFFFF5C] placeholder:text-[calc(1rem*(14/16))] text-[calc(1rem*(14/16))] max-w-[100%] flex-1"
              unitClassName="min-w-[auto] text-[calc(1rem*(14/16))] text-[#FFFFFF99] leading-[1]"
              inputProps={{
                type: 'number',
                onKeyDown: handleInputValue,
                value: maxValue,
                onChange: (e) => setMaxValue(e.target.value),
              }}
            />
          </DrawerDescription>

          <DrawerFooter className="pt-0 mt-3 border-t-[0.5px] border-t-[#ECECED0A]">
            <div className="flex justify-center items-center flex-row gap-2.5 pt-4 ">
              <Button
                size="lg"
                disabled={loading}
                variant="borderGradient"
                className="flex-1 rounded-full h-11"
                onClick={handleResetClick}
              >
                {t('orderForm.buySettings.reset')}
              </Button>

              <Button
                size="lg"
                disabled={loading || (minValue === '' && maxValue === '')}
                variant="gradient"
                className="text-[#261236] flex-1 rounded-[50px] h-11"
                onClick={handleConfirmClick}
              >
                {loading && <Loader2 className="animate-spin w-4 h-4 mr-1" />}
                {t('chart.buttons.confirm')}
              </Button>
            </div>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  )
}

export default FilterVolume
