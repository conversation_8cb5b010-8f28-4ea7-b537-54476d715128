import { getClearinghouseState } from '@/api/hyperliquid'
import useDebounceValue from '@/hooks/useDebounceValue'
import { useMultiChainWallet } from '@/hooks/useMultiChainWallet'
import { fixNumber } from '@/lib/utils'
import { Search } from 'lucide-react'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { LoginEvmDrawer } from '../common/LoginEvmDrawer'
import SwitchWalletBottomSheet from '../common/SwitchWalletBottomSheet'
import Text from '../common/Text'
import { TokenSearchDrawer, TokenSearchDrawerType } from '../futuresDetails/tokenSearchDrawer'

const SearchBar = ({ setSearch }: { setSearch: Dispatch<SetStateAction<string>> }) => {
  const { activeWallet, wallets } = useMultiChainWallet({})

  const [openLoginEvmEvm, setOpenLoginEvm] = useState(false)
  const [openSwitchWallet, setOpenSwitchWallet] = useState<boolean>(false)
  const [openTokenSearch, setOpenTokenSearch] = useState(false)
  const [value] = useState<string>('')
  const debounceValue = useDebounceValue(value, 500)
  const [funding, setFunding] = useState(0)

  useEffect(() => {
    setSearch('')
  }, [debounceValue, setSearch])

  const handleConnect = () => {
    setOpenLoginEvm(true)
  }

  const numberOfWallets = () =>
    ['chain', 'telegram'].reduce((sum, key) => sum + Number(wallets?.arb?.[key]?.isConnected), 0)

  const getUserFunding = async () => {
    if (!activeWallet?.isConnected || numberOfWallets() === 0) return 0
    try {
      const data = await getClearinghouseState(activeWallet.walletId)
      const balance = fixNumber(data?.crossMarginSummary?.accountValue, 2)
      setFunding(Number(balance))
    } catch (err: any) {}
  }

  useEffect(() => {
    getUserFunding()
  }, [activeWallet.isConnected, activeWallet.walletId])

  return (
    <div className="py-3 flex gap-2 items-center">
      {/* Search Bar */}
      <div className="relative flex-1" onClick={() => setOpenTokenSearch(true)}>
        <div
          className="w-full 
                  bg-[#ECECED14] 
                  text-[calc(1rem*(14/16))]
                  pl-[14px] 
                  pr-[10px] 
                  h-[36px]
                  rounded-full 
                  border-[0.5px] 
                  border-[#ECECED14] 
                  focus:outline-none 
                  focus:border-[#444444]
                  flex 
                  cursor-pointer 
                  items-center
                  "
        >
          <Text text="搜索币种名称" fontSize={14} fontWeight="regular" color="#FFFFFFB2" className="" />
        </div>
        <Search className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 size-[18px]" />
      </div>
      {activeWallet?.isConnected ? (
        <div
          className="h-[36px] flex gap-2.5 items-center border-[0.5px] border-[#ECECED14] bg-[#ECECED14] rounded-full p-2.5 text-[calc(1rem*(14/16))] leading-[calc(1rem*(14/16))] text-[#FFFFFF] app-font-medium z-10 cursor-pointer"
          onClick={() => setOpenSwitchWallet(true)}
        >
          <div className="flex gap-1 items-center">
            <img src="/images/futuresDiscover/wallets.png" className="w-[14px] h-[14px]" alt="" />
            <span>{numberOfWallets()}</span>
          </div>
          <div className="flex gap-1.5 items-center">
            <img src="/images/futuresDiscover/usdc.png" className="w-[14px] h-[14px]" alt="" />
            <span>{funding}</span>
          </div>
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M10.8185 6.00098H7.85197H5.18067C4.72355 6.00098 4.49499 6.7093 4.81878 7.12452L7.28533 10.2875C7.68055 10.7943 8.32338 10.7943 8.7186 10.2875L9.65665 9.08461L11.1852 7.12452C11.5042 6.7093 11.2756 6.00098 10.8185 6.00098Z"
              fill="white"
            />
          </svg>
        </div>
      ) : (
        <button
          className="bg-[#ECECED14] px-3 py-[5px] rounded-full border-[0.5px] border-[#ECECED14] h-[36px] flex justify-center items-center align-middle z-10"
          onClick={handleConnect}
        >
          <Text text="Connect" fontSize={13} fontWeight="medium" className="mb-1" />
          <img src="/images/futuresDetail/select-down-icon.svg" className="" />
        </button>
      )}
      {openLoginEvmEvm && <LoginEvmDrawer open={openLoginEvmEvm} setOpen={setOpenLoginEvm} />}
      <SwitchWalletBottomSheet open={openSwitchWallet} setOpen={setOpenSwitchWallet} tab="crypto" />
      <TokenSearchDrawer
        open={openTokenSearch}
        setOpen={setOpenTokenSearch}
        type={TokenSearchDrawerType.CRYPTO}
        allowShowList={false}
      />
    </div>
  )
}

export default SearchBar
