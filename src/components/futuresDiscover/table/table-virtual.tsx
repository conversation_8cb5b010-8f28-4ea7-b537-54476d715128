import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  Row,
  SortingState,
  useReactTable,
} from '@tanstack/react-table'
import { useWindowSize } from 'react-use'

import { Skeleton } from '@/components/ui/skeleton'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { cn } from '@/lib/utils.ts'
import { isRealIOSSafari } from '@/utils/helpers'
import { IconEmpty } from '@components/icon'
import { useVirtualizer } from '@tanstack/react-virtual'
import React, { useCallback, useRef, useState, memo, JSX } from 'react'
import { useTranslation } from 'react-i18next'
import SwipeableRow from './swipeable-row'

interface TableVirtualProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  isStickyHeader?: boolean
  isStickyFirstColumn?: boolean
  stickyBg?: string
  containerClassName?: string
  tableClassName?: string
  tableHeaderClassName?: string
  tableHeaderRowClassName?: string
  tableHeadClassName?: string
  tableBodyClassName?: string
  tableBodyRowClassName?: string
  tableCellClassName?: string
  onRowClick?: (data: any) => void
  onBottomReached?: () => void
  isLoading?: boolean
  enableSwipeToDelete?: boolean
  onSwipeDelete?: (data: any) => void
  swipeDeleteText?: string
  rowHeight?: number
  cusTomMaxHeight?: string
  renderFooter?: () => React.ReactNode
  emptyText?: string
  isShowHeader?: boolean
}

const TableVirtualComponent = <TData, TValue>({
  columns,
  data,
  isStickyHeader,
  isStickyFirstColumn,
  stickyBg,
  containerClassName,
  tableClassName,
  tableHeaderClassName,
  tableHeaderRowClassName,
  tableHeadClassName,
  tableBodyClassName,
  tableBodyRowClassName,
  tableCellClassName,
  onRowClick,
  onBottomReached,
  isLoading,
  enableSwipeToDelete = false,
  onSwipeDelete,
  swipeDeleteText = 'Delete',
  rowHeight = 63,
  cusTomMaxHeight,
  renderFooter,
  emptyText,
  isShowHeader = true,
}: TableVirtualProps<TData, TValue>) => {
  const { t } = useTranslation()
  const [sorting, setSorting] = useState<SortingState>([])
  const parentRef = useRef<HTMLDivElement>(null)
  const headerRef = useRef<HTMLTableRowElement>(null)

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
    },
  })

  const handleRowClick = useCallback(
    (row: Row<TData>) => {
      if (onRowClick && row) {
        onRowClick(row.original)
      }
    },
    [onRowClick],
  )

  const handleSwipeDelete = useCallback(
    (row: Row<TData>) => {
      if (onSwipeDelete && row) {
        onSwipeDelete(row.original)
      }
    },
    [onSwipeDelete],
  )

  const handleScroll = useCallback(
    (event: React.UIEvent<HTMLDivElement>) => {
      const target = event.currentTarget
      const scrollPercentage = (target.scrollTop + target.clientHeight) / target.scrollHeight

      if (scrollPercentage >= 0.75) {
        onBottomReached?.()
      }
    },
    [onBottomReached],
  )

  const { rows } = table.getRowModel()
  const virtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => rowHeight,
    overscan: 5,
  })

  const { height } = useWindowSize()

  const maxHeight = height - 180

  return (
    <div
      className={cn('overflow-auto pb-20', containerClassName)}
      onScroll={handleScroll}
      ref={parentRef}
      style={{
        maxHeight: cusTomMaxHeight ? cusTomMaxHeight : isRealIOSSafari() ? maxHeight : 'calc(100vh - 184px)',
      }}
    >
      <div style={{ height: `${virtualizer.getTotalSize()}px`, minHeight: 'calc(100vh - 220px)' }}>
        <Table className={cn(tableClassName)}>
          {isShowHeader && (
            <TableHeader className={cn(tableHeaderClassName, isStickyHeader && 'sticky top-0')}>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id} className={tableHeaderRowClassName} ref={headerRef}>
                  {headerGroup.headers.map((header, index) => (
                    <TableHead
                      key={header.id}
                      className={cn(
                        'z-0',
                        tableHeadClassName,
                        index === 0 && isStickyFirstColumn && 'sticky left-0 z-1',
                      )}
                      style={{
                        ...(isStickyFirstColumn && stickyBg && index === 0 ? { background: stickyBg } : {}),
                        textAlign: index === 0 ? 'left' : 'right',
                      }}
                    >
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
          )}
          <TableBody className={tableBodyClassName}>
            {isLoading &&
              Array.from({ length: 20 }).map((_, i) => (
                <tr key={i}>
                  <td colSpan={columns.length}>
                    <Skeleton key={i} className="h-[63px] mb-1" />
                  </td>
                </tr>
              ))}

            {!isLoading && (
              <>
                {rows.length === 0 ? (
                  <TableRow className={tableBodyRowClassName}>
                    <TableCell colSpan={columns.length} className={cn('h-24 text-center', tableCellClassName)}>
                      <div className="flex flex-col items-center justify-center">
                        <IconEmpty />
                        <span className="text-[#FFFFFF80] text-[0.75rem]">{emptyText || t('history.nodata')}</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  virtualizer.getVirtualItems().map((virtualRow, index) => {
                    const row = rows[virtualRow.index]

                    const rowContent = (
                      <TableRow
                        key={row.id}
                        style={{
                          height: `${virtualRow.size}px`,
                          transform: `translateY(${virtualRow.start - index * virtualRow.size}px)`,
                        }}
                        // Remove onClick when swipe is enabled - it will be handled by SwipeableRow
                        onClick={!enableSwipeToDelete ? () => handleRowClick(row) : undefined}
                        className={cn(
                          tableBodyRowClassName,
                          enableSwipeToDelete ? 'relative' : '',
                          !enableSwipeToDelete ? 'cursor-pointer' : '',
                        )}
                      >
                        {row.getVisibleCells().map((cell, cellIndex) => (
                          <TableCell
                            key={cell.id}
                            className={cn('z-0', tableCellClassName)}
                            style={{
                              textAlign: cellIndex === 0 ? 'left' : 'right',
                            }}
                          >
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        ))}
                      </TableRow>
                    )

                    if (enableSwipeToDelete && onSwipeDelete) {
                      return (
                        <tr
                          key={`swipe-${row.id}_${row.index}`}
                          style={{
                            height: `${virtualRow.size}px`,
                            transform: `translateY(${virtualRow.start - index * virtualRow.size}px)`,
                            display: 'table-row',
                          }}
                        >
                          <td
                            colSpan={columns.length}
                            style={{
                              padding: 0,
                              border: 'none',
                              position: 'relative',
                              height: '100%',
                            }}
                          >
                            <SwipeableRow
                              onSwipeDelete={() => handleSwipeDelete(row)}
                              deleteText={swipeDeleteText}
                              onClick={() => handleRowClick(row)}
                            >
                              <table style={{ width: '100%', tableLayout: 'fixed' }} className="virtual-table-custom">
                                <tbody>
                                  {React.cloneElement(rowContent, {
                                    onClick: undefined, // Remove onClick from the cloned element
                                    className: cn(tableBodyRowClassName, 'cursor-pointer'),
                                    style: {
                                      height: `${virtualRow.size}px`,
                                      transform: 'none',
                                    },
                                  })}
                                </tbody>
                              </table>
                            </SwipeableRow>
                          </td>
                        </tr>
                      )
                    }

                    return rowContent
                  })
                )}
              </>
            )}
          </TableBody>
          <tr>
            <td colSpan={columns.length}>
              {renderFooter && <div className={cn('mt-3 mx-auto pb-10')}>{renderFooter()}</div>}
            </td>
          </tr>
        </Table>
      </div>
    </div>
  )
}

// Custom comparison function for memo
const arePropsEqual = <TData, TValue>(
  prevProps: TableVirtualProps<TData, TValue>,
  nextProps: TableVirtualProps<TData, TValue>
): boolean => {
  // Compare primitive props
  if (
    prevProps.isStickyHeader !== nextProps.isStickyHeader ||
    prevProps.isStickyFirstColumn !== nextProps.isStickyFirstColumn ||
    prevProps.stickyBg !== nextProps.stickyBg ||
    prevProps.containerClassName !== nextProps.containerClassName ||
    prevProps.tableClassName !== nextProps.tableClassName ||
    prevProps.tableHeaderClassName !== nextProps.tableHeaderClassName ||
    prevProps.tableHeaderRowClassName !== nextProps.tableHeaderRowClassName ||
    prevProps.tableHeadClassName !== nextProps.tableHeadClassName ||
    prevProps.tableBodyClassName !== nextProps.tableBodyClassName ||
    prevProps.tableBodyRowClassName !== nextProps.tableBodyRowClassName ||
    prevProps.tableCellClassName !== nextProps.tableCellClassName ||
    prevProps.isLoading !== nextProps.isLoading ||
    prevProps.enableSwipeToDelete !== nextProps.enableSwipeToDelete ||
    prevProps.swipeDeleteText !== nextProps.swipeDeleteText ||
    prevProps.rowHeight !== nextProps.rowHeight ||
    prevProps.cusTomMaxHeight !== nextProps.cusTomMaxHeight ||
    prevProps.emptyText !== nextProps.emptyText ||
    prevProps.isShowHeader !== nextProps.isShowHeader
  ) {
    return false
  }

  // Compare data array length and shallow comparison
  if (prevProps.data.length !== nextProps.data.length) {
    return false
  }

  // Shallow comparison of data array items
  for (let i = 0; i < prevProps.data.length; i++) {
    if (prevProps.data[i] !== nextProps.data[i]) {
      return false
    }
  }

  // Compare columns array length
  if (prevProps.columns.length !== nextProps.columns.length) {
    return false
  }

  // Compare function references (they should be memoized by parent)
  if (
    prevProps.onRowClick !== nextProps.onRowClick ||
    prevProps.onBottomReached !== nextProps.onBottomReached ||
    prevProps.onSwipeDelete !== nextProps.onSwipeDelete ||
    prevProps.renderFooter !== nextProps.renderFooter
  ) {
    return false
  }

  return true
}

// Export the memoized component
export const TableVirtual = memo(TableVirtualComponent, arePropsEqual) as <TData, TValue>(
  props: TableVirtualProps<TData, TValue>
) => JSX.Element