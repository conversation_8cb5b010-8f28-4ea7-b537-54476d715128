import { useTranslation } from 'react-i18next'
import { useState } from 'react'
import { Button } from '@components/ui/button.tsx'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from '@components/ui/drawer.tsx'
import { X } from 'lucide-react'
import { PoolTransactionType } from '@/types/enums.ts'

type FilterPoolTransactionTypeProps = {
  currentType: PoolTransactionType
  updateTransactionType: (type: PoolTransactionType) => void
}

const FilterPoolTransactionType = ({
  updateTransactionType,
  currentType
}: FilterPoolTransactionTypeProps) => {
  const { t } = useTranslation()

  const [open, setOpen] = useState(false)

  const handleClickFilterItem = (type: PoolTransactionType) => {
    updateTransactionType(type)
    setOpen(false)
  }

  return (
    <>
      <Button size="xs" className="rounded-full bg-transparent p-0" onClick={() => setOpen(true)}>
        <img src="/images/icons/icon-filter.svg" className="w-[11px] h-[11px]" alt="icon filter" />
      </Button>

      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerContent className="w-full bg-[#232329] max-w-[768px] max-h-[80vh] mx-auto">
          <DrawerHeader>
            <DrawerTitle className="mt-1.5">
              <div className="text-[cal c(1rem*(22/16))] leading-[1] app-font-regular text-left">
                {t('detail.tokenDetail.finalType')}
              </div>
            </DrawerTitle>

            <DrawerClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
              <X className="size-5" />
            </DrawerClose>
          </DrawerHeader>

          <DrawerDescription></DrawerDescription>
          <DrawerFooter>
            <ul className="flex flex-col">
              <li
                className="flex justify-between items-center py-[18px] cursor-pointer border-b-[0.5px] border-b-[#ECECED14]"
                onClick={() => handleClickFilterItem(PoolTransactionType.All)}
              >
                <span>{t('detail.tabs.all')}</span>
                {currentType === PoolTransactionType.All && <img src="/images/icons/icon-tick-rounded.svg" alt="ic tick" />}
              </li>

              <li
                className="flex justify-between items-center py-[18px] cursor-pointer border-b-[0.5px] border-b-[#ECECED14]"
                onClick={() => handleClickFilterItem(PoolTransactionType.Trading)}
              >
                <span>{t('detail.pool.trading')}</span>
                {currentType === PoolTransactionType.Trading && <img src="/images/icons/icon-tick-rounded.svg" alt="ic tick" />}
              </li>

              <li
                className="flex justify-between items-center py-[18px] cursor-pointer border-b-[0.5px] border-b-[#ECECED14]"
                onClick={() => handleClickFilterItem(PoolTransactionType.Liquidity)}
              >
                <span>{t('detail.pool.liquidity')}</span>
                {currentType === PoolTransactionType.Liquidity && <img src="/images/icons/icon-tick-rounded.svg" alt="ic tick" />}
              </li>

              <li
                className="flex justify-between items-center py-[18px] cursor-pointer border-b-[0.5px] border-b-[#ECECED14]"
                onClick={() => handleClickFilterItem(PoolTransactionType.Buy)}
              >
                <span>{t('history.buy')}</span>
                {currentType === PoolTransactionType.Buy && <img src="/images/icons/icon-tick-rounded.svg" alt="ic tick" />}
              </li>

              <li
                className="flex justify-between items-center py-[18px] cursor-pointer border-b-[0.5px] border-b-[#ECECED14]"
                onClick={() => handleClickFilterItem(PoolTransactionType.Sell)}
              >
                <span>{t('history.sell')}</span>
                {currentType === PoolTransactionType.Sell && <img src="/images/icons/icon-tick-rounded.svg" alt="ic tick" />}
              </li>

              <li
                className="flex justify-between items-center py-[18px] cursor-pointer border-b-[0.5px] border-b-[#ECECED14]"
                onClick={() => handleClickFilterItem(PoolTransactionType.AddLiquidity)}
              >
                <span>{t('history.addLiquidity')}</span>
                {currentType === PoolTransactionType.AddLiquidity && <img src="/images/icons/icon-tick-rounded.svg" alt="ic tick" />}
              </li>

              <li
                className="flex justify-between items-center py-[18px] cursor-pointer border-b-[0.5px] border-b-[#ECECED14]"
                onClick={() => handleClickFilterItem(PoolTransactionType.RemoveLiquidity)}
              >
                <span>{t('history.removeLiquidity')}</span>
                {currentType === PoolTransactionType.RemoveLiquidity && <img src="/images/icons/icon-tick-rounded.svg" alt="ic tick" />}
              </li>
            </ul>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  )
}

export default FilterPoolTransactionType 