import React from 'react'
import { useTranslation } from 'react-i18next'
import AppDrawer from '../common/AppDrawer'
import { PoolTransaction } from '@/@generated/gql/graphql-trading'
import { formatNumber } from '@/utils/numbers'
import { DataTableInfiniteScroll } from '../ui/XTableInfiniteScroll'
import { ColumnDef } from '@tanstack/react-table'
import LiquidityChart from './LiquidityChart'

interface PoolData {
  id: string
  token: string
  pair: string
  currentPrice: number
  initialPrice: number
  totalLiquidity: number
  volume24h: number
  liquidity: string
}

interface LiquidityChartDrawerProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  liquidity?: number
  numberOfPools?: number
  transactions: PoolTransaction[]
}

const LiquidityChartDrawer: React.FC<LiquidityChartDrawerProps> = ({
  isOpen,
  onOpenChange,
  liquidity: _liquidity = 0,
  numberOfPools: _numberOfPools = 0,
  transactions: _transactions,
}) => {
  const { t } = useTranslation()

  const poolData: PoolData[] = [
    {
      id: '1',
      token: 'Trump',
      pair: '20.04K / 432.32',
      currentPrice: 20.04,
      initialPrice: 432.32,
      totalLiquidity: 138.5,
      volume24h: 0,
      liquidity: '$18.5B',
    },
    {
      id: '2',
      token: 'Trump',
      pair: '20.04K / 432.32',
      currentPrice: 20.04,
      initialPrice: 432.32,
      totalLiquidity: 138.5,
      volume24h: 0,
      liquidity: '$18.5B',
    },
    {
      id: '3',
      token: 'Trump',
      pair: '20.04K / 432.32',
      currentPrice: 20.04,
      initialPrice: 432.32,
      totalLiquidity: 138.5,
      volume24h: 0,
      liquidity: '$18.5B',
    },
    {
      id: '4',
      token: 'Trump',
      pair: '20.04K / 432.32',
      currentPrice: 20.04,
      initialPrice: 432.32,
      totalLiquidity: 138.5,
      volume24h: 0,
      liquidity: '$18.5B',
    },
    {
      id: '5',
      token: 'Trump',
      pair: '20.04K / 432.32',
      currentPrice: 20.04,
      initialPrice: 432.32,
      totalLiquidity: 138.5,
      volume24h: 0,
      liquidity: '$18.5B',
    },
  ]

  const poolColumns: ColumnDef<PoolData>[] = [
    {
      accessorKey: 'token',
      header: () => (
        <div className="text-[11px] leading-3 tracking-[0.28px] font-normal text-[#FFFFFF]/50 min-w-[80px]">{t('liquidityChart.tradingPair')}</div>
      ),
      cell: ({ row }) => (
        <div className="flex flex-col justify-center gap-2 min-w-[80px]">
          <div className="flex items-center gap-1.5">
            <img src="/images/tokenDetail/trump.png" className="w-5 h-5 rounded-full" alt="token" />
            <span className="text-white/50 text-sm font-light">{row.original.token}</span>
          </div>
          <div className="flex items-center gap-1.5">
            <img src="/images/tokenDetail/trump.png" className="w-5 h-5 rounded-full" alt="token" />
            <span className="text-white/50 text-sm font-light">{row.original.token}</span>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'pair',
      header: () => (
        <div className="text-[11px] leading-3 tracking-[0.28px] font-normal text-[#FFFFFF]/50 min-w-[120px]">{t('liquidityChart.currentInitial')}</div>
      ),
      cell: ({ row }) => (
        <div className="flex flex-col justify-center gap-2 min-w-[80px]">
          <div className="text-white text-sm min-w-[120px] font-light">{row.original.pair}</div>
          <div className="text-white text-sm min-w-[120px] font-light">{row.original.pair}</div>
        </div>
      ),
    },
    {
      accessorKey: 'totalLiquidity',
      header: () => (
        <div className="text-[11px] leading-3 tracking-[0.28px] font-normal text-[#FFFFFF]/50 min-w-[100px]">{t('liquidityChart.totalLiquidity')}</div>
      ),
      cell: ({ row }) => (
        <div className="flex flex-col justify-center gap-2 min-w-[80px]">
          <div className="text-white text-sm min-w-[100px] font-light">${formatNumber(row.original.totalLiquidity)}M</div>
          <div className="text-white text-sm min-w-[100px] font-light">${formatNumber(row.original.totalLiquidity)}M</div>
        </div>
      ),
    },
    {
      accessorKey: 'liquidity',
      header: () => (
        <div className="text-[11px] leading-3 tracking-[0.28px] font-normal text-[#FFFFFF]/50 min-w-[100px] flex items-center gap-1">
          <img src="/images/cryptoDeposit/solana.svg" className="w-3 h-3" alt="icon solana" />
          {t('liquidityChart.fundPool')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex items-center gap-2 min-w-[100px]">
          <img src="/images/icons/lock.svg" className="w-3 h-3" alt="lock" />
          <span className="text-white text-sm font-light">{row.original.liquidity}</span>
        </div>
      ),
    },
  ]

  return (
    <AppDrawer
      open={isOpen}
      setOpen={(value) => {
        if (typeof value === 'function') {
          onOpenChange(value(isOpen))
        } else {
          onOpenChange(value)
        }
      }}
      title={''}
      maxHeight="85vh"
      drawerClassName="bg-[#17181B] border-[#2A2D33]"
      drawerContentClassName="relative"
      drawerContent={
        <div >
          <div className="absolute inset-0 z-50 bg-[#17181B]/20 backdrop-blur-sm flex items-center justify-center">
            <div className="bg-[#1E1F23] border border-[#2A2D33] rounded-lg px-8 py-6 shadow-xl">
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-4 bg-[#00FFB4]/10 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-[#00FFB4]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-white text-xl font-medium mb-2">{t('liquidityChart.comingSoon')}</h3>
              </div>
            </div>
          </div>
          <div className="pointer-events-none opacity-50">
            <div className="">
              <LiquidityChart />
            </div>

          <div className="mt-6">
            <h4 className="text-white text-lg font-light mb-4">{t('liquidityChart.fundPool')}</h4>
            <DataTableInfiniteScroll
              columns={poolColumns}
              data={poolData}
              isLoading={false}
              fetchMore={() => Promise.resolve()}
              hasMore={false}
              tableProps={{
                isStickyHeader: true,
                stickyBg: 'rgb(30,31,35)',
                tableClassName: '',
                containerClassName: 'border-0 max-h-[387px] select-none',
                tableHeaderRowClassName: '!border-0 whitespace-nowrap',
                tableHeaderClassName:
                  'border-0 text-[#FFFFFF80] text-[calc(1rem*(11/16))] z-10 leading-3 app-font-medium',
                tableBodyRowClassName: 'border-b-[.5px] border-[#ECECED08]',
              }}
            />
          </div>
          </div>
        </div>
      }
    />
  )
}

export default LiquidityChartDrawer
