import React from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, ResponsiveContainer, Tooltip, CartesianGrid } from 'recharts'
import { formatMoney } from '@/utils/helpers'
import { useTranslation } from 'react-i18next'

const liquidityData = [
  { time: '13:00', value: 14000, hour: 13 },
  { time: '13:30', value: 32000, hour: 13.5 },
  { time: '14:00', value: 8500, hour: 14 },
  { time: '14:30', value: 20000, hour: 14.5 },
  { time: '15:00', value: 14500, hour: 15 },
  { time: '15:30', value: 33000, hour: 15.5 },
  { time: '16:00', value: 20500, hour: 16 },
  { time: '16:30', value: 14000, hour: 16.5 },
  { time: '17:00', value: 20000, hour: 17 },
  { time: '17:30', value: 20500, hour: 17.5 },
  { time: '18:00', value: 20000, hour: 18 },
  { time: '18:30', value: 14500, hour: 18.5 },
  { time: '19:00', value: 20000, hour: 19 },
  { time: '19:30', value: 20500, hour: 19.5 },
  { time: '20:00', value: 20000, hour: 20 },
  { time: '20:30', value: 13500, hour: 20.5 },
  { time: '21:00', value: 14500, hour: 21 }
]

interface LiquidityChartProps {
  className?: string
}

const LiquidityChart: React.FC<LiquidityChartProps> = ({ className = '' }) => {
  const { t } = useTranslation()

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-[#1E1F23] border border-[#2A2D33] rounded-lg p-3">
          <p className="text-[#FFFFFF80] text-sm">{t('liquidityChart.time')}: {label}</p>
          <p className="text-[#00FFB4] text-sm font-medium">
            {t('liquidityChart.liquidity')}: ${payload[0].value.toLocaleString()}
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <div className={`w-full rounded-[8px] h-[320px] p-2.5 bg-[#19191E] ${className}`}>
      <div className="mb-4">
        <div className="flex items-center gap-2 mb-2">
          <div className="w-1 h-4 bg-[#00FFB4] rounded-tr-full rounded-br-full "></div>
          <h3 className="text-white text-lg font-medium">{t('liquidityChart.totalLiquidity')}</h3>
        </div>
      </div>
      
      <ResponsiveContainer width="100%" height={271}>
        <BarChart
          data={liquidityData}
          margin={{
            top: 24,
            bottom: 10,
            left:-24
          }}
          barCategoryGap="10%"
        >
          <CartesianGrid 
            stroke="#2A2D33" 
            strokeWidth={1}
            horizontal={true} 
            vertical={false}
          />
          <XAxis 
            dataKey="time"
            axisLine={false}
            tickLine={false}
            tick={{ 
              fill: '#FFFFFF70', 
              fontSize: 9,
            }}
            interval={2} 
          />
          <YAxis 
            axisLine={false}
            tickLine={false}
            tick={{ 
              fill: '#FFFFFF80', 
              fontSize: 10,
              fontWeight:400,
              fontFamily: 'inherit',
            }}
            tickFormatter={(value: number) => formatMoney(value).replace('$', '')}
            domain={[0, 35000]}
            ticks={[0, 5000, 10000, 15000, 20000, 25000, 30000, 35000]}
            label={{ 
              value: '$', 
              position: 'insideTopLeft', 
              angle: 0, 
              textAnchor: 'start',
              offset: 10,
              style: { fill: '#FFFFFF80', fontSize: '12px' }
            }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar 
            dataKey="value" 
            fill="#00FFF6" 
            maxBarSize={10}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}

export default LiquidityChart
