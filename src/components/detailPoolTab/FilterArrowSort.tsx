import { SortByCreateAtType } from '@/types/enums.ts'
import { IconSortDown, IconSortUp } from '@components/icon'

interface FilterArrowSortProps {
  onSort: (sort: string) => void
  currentSort: string
}

const FilterArrowSort = ({ onSort, currentSort }: FilterArrowSortProps) => {
  const handleOnclickSort = () => {
    onSort(currentSort === SortByCreateAtType.DESC ? SortByCreateAtType.ASC : SortByCreateAtType.DESC)
  }

  return (
    <div
      className="flex flex-col ml-1 cursor-pointer"
      onClick={handleOnclickSort}
    >
      <IconSortUp currentColor={currentSort === SortByCreateAtType.ASC ? '#FFFFFF' : '#FFFFFF80'} />
      <IconSortDown currentColor={currentSort === SortByCreateAtType.DESC ? '#FFFFFF' : '#FFFFFF80'} />
    </div>
  )
}

export default FilterArrowSort 