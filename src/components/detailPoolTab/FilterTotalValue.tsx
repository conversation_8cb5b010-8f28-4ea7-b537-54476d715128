import { Button } from '@components/ui/button.tsx'
import { useTranslation } from 'react-i18next'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from '@components/ui/drawer.tsx'
import { Loader2, X } from 'lucide-react'
import React, { useState } from 'react'
import { cn } from '@/lib/utils.ts'
import GradientBordered from '@components/common/GradientBordered.tsx'
import InputBorderGradient from '@components/orderForm/InputBorderGradient.tsx'

type FilterTotalValueProps = {
  onValueChange: (min: number | undefined, max: number | undefined) => void
}

const listFilterValues = [100, 500, 1000, 2500, 5000, 10000]

const FilterTotalValue = ({ onValueChange }: FilterTotalValueProps) => {
  const { t } = useTranslation()

  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [optionValue, setOptionValue] = useState<number>(0)
  const [minValue, setMinValue] = useState("")
  const [maxValue, setMaxValue] = useState("")
  const [errorText, setErrorText] = useState("")

  const handleInputValue = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "-") e.preventDefault()
  }

  const handleOnClickOptionItem = (value: number) => {
    setOptionValue(value)
    setMinValue(`${value}`)
  }

  const handleConfirmClick = () => {
    setErrorText("")
    setLoading(true)

    const min = Number(minValue)
    const max = Number(maxValue)

    const hasMin = minValue !== ""
    const hasMax = maxValue !== ""

    if (hasMin && hasMax && min > max) {
      setErrorText(t("detail.tokenDetail.errorInputTransactionFilter"))
      setLoading(false)
      return
    }

    onValueChange(hasMin ? min : undefined, hasMax ? max : undefined)

    setLoading(false)
    setOpen(false)
  }

  const handleResetClick = () => {
    setMinValue("")
    setMaxValue("")
    setOptionValue(0)
    setOpen(false)
    onValueChange(undefined, undefined)
  }

  return (
    <>
      <Button size="xs" className="rounded-full bg-transparent p-0" onClick={() => setOpen(true)}>
        <img src="/images/icons/icon-filter.svg" className="w-[11px] h-[11px]" alt="icon filter" />
      </Button>

      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerContent className="w-full bg-[#232329] max-w-[768px] max-h-[80vh] mx-auto">
          <DrawerHeader>
            <DrawerTitle className="mt-1.5">
              <div className="text-[calc(1rem*(22/16))] leading-[1] app-font-regular text-left">
                {t('detail.pool.totalValue')}
              </div>
            </DrawerTitle>

            <DrawerClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
              <X className="size-5" />
            </DrawerClose>
          </DrawerHeader>

          <div className="flex flex-col px-4 gap-3">
            <div className="grid grid-cols-3 gap-x-2 gap-y-3">
              {listFilterValues.map((item) => (
                <GradientBordered
                  key={item}
                  containerClassName={cn(
                    'cursor-pointer flex-1 text-center h-[34px] rounded-[6.67px] text-[calc(1rem*(14/16))] text-white leading-[1] p-[0.5px] hover-scale',
                    optionValue !== item && '!bg-[#ECECED14] !bg-none',
                  )}
                  innerBgClassName={cn(
                    'p-[8px] rounded-[6.5px] !bg-[transparent] app-font-regular',
                    optionValue === item && '!bg-[rgba(36,36,36,0.7)] text-[#00FFB4]',
                  )}
                  onClick={() => handleOnClickOptionItem(item)}
                >
                  {`>$${item}`}
                </GradientBordered>
              ))}
            </div>
            <InputBorderGradient
              unit="$"
              placeHolder={t('filter.minimum')}
              containerClassName="h-[48px] px-[14px] py-[12px] rounded-[8px]"
              innerBgClassName="rounded-[8px]"
              inputClassName="placeholder:text-[#FFFFFF5C] placeholder:text-[calc(1rem*(14/16))] text-[calc(1rem*(14/16))] max-w-[100%] flex-1"
              unitClassName="min-w-[auto] text-[calc(1rem*(14/16))] text-[#FFFFFF99] leading-[1]"
              inputProps={{
                type: 'number',
                onKeyDown: handleInputValue,
                value: minValue,
                onChange: (e) => setMinValue(e.target.value)
              }}
            />
            <InputBorderGradient
              unit="$"
              placeHolder={t('filter.maximum')}
              containerClassName="h-[48px] px-[14px] py-[12px] rounded-[8px]"
              innerBgClassName="rounded-[8px]"
              inputClassName="placeholder:text-[#FFFFFF5C] placeholder:text-[calc(1rem*(14/16))] text-[calc(1rem*(14/16))] max-w-[100%] flex-1"
              unitClassName="min-w-[auto] text-[calc(1rem*(14/16))] text-[#FFFFFF99] leading-[1]"
              inputProps={{
                type: 'number',
                onKeyDown: handleInputValue,
                value: maxValue,
                onChange: (e) => setMaxValue(e.target.value)
              }}
            />
          </div>
          <DrawerDescription></DrawerDescription>

          <DrawerFooter className="pt-0 mt-3 border-t-[0.5px] border-t-[#ECECED0A]">
            {
              errorText && errorText !== "" && <div className={"pt-4 text-[11px] leading-[1] text-[#FF353C]"}>
                {errorText}
              </div>
            }
            <div className="flex justify-center items-center flex-row gap-2.5 pt-4 ">
              <Button
                size="lg"
                disabled={loading}
                variant="borderGradient"
                className="flex-1 rounded-full h-11"
                onClick={handleResetClick}
              >
                {t('orderForm.buySettings.reset')}
              </Button>

              <Button
                size="lg"
                disabled={loading || (minValue === "" && maxValue === "")}
                variant="gradient"
                className="text-[#261236] flex-1 rounded-[50px] h-11"
                onClick={handleConfirmClick}
              >
                {loading && <Loader2 className="animate-spin w-4 h-4 mr-1" />}
                {t('chart.buttons.confirm')}
              </Button>
            </div>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  )
}

export default FilterTotalValue 