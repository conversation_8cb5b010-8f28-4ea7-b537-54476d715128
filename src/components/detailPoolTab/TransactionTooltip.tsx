import { FC, useState } from 'react'
import * as TooltipPrimitive from '@radix-ui/react-tooltip'

interface TransactionTooltipProps {
  children: React.ReactNode
  data: {
    value: number
    change: number
  }
}

export const TransactionTooltip: FC<TransactionTooltipProps> = ({ children, data }) => {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <TooltipPrimitive.Provider>
      <TooltipPrimitive.Root open={isOpen} onOpenChange={setIsOpen}>
        <TooltipPrimitive.Trigger asChild onClick={() => setIsOpen(!isOpen)}>
          {children}
        </TooltipPrimitive.Trigger>
        <TooltipPrimitive.Portal>
          <TooltipPrimitive.Content className="bg-[#1C1D20] rounded-lg p-3 z-50 shadow-lg" sideOffset={5}>
            <div className="flex flex-col gap-2 min-w-[150px]">
              <div className="flex items-center justify-between">
                <span className="text-white/50 text-xs inline-flex items-center gap-1">
                  <span className="bg-[#00FFB4] w-1.5 h-1.5 rounded-full inline-block"></span> 单边减池子
                </span>
                <span className="text-[#00FFA7] text-xs">${data.value}</span>
              </div>
            </div>
            <TooltipPrimitive.Arrow className="fill-[#1C1D20]" />
          </TooltipPrimitive.Content>
        </TooltipPrimitive.Portal>
      </TooltipPrimitive.Root>
    </TooltipPrimitive.Provider>
  )
}
