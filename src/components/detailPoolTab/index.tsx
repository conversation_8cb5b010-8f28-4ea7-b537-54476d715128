import { useTranslation } from 'react-i18next'
import { useEffect, useRef, useState } from 'react'

import { ColumnDef } from '@tanstack/react-table'
import { PoolColumnKeys, PoolTransactionType, TransactionClassification } from '@/types/enums.ts'
import MovingBgGridFilterTags from '../common/MovingBgGridFilterTags'
import { cn } from '@/lib/utils'
import { DataTableInfiniteScroll } from '../ui/XTableInfiniteScroll'
import { TransactionTooltip } from './TransactionTooltip'
import { useGetPoolTransactions } from '@/hooks/useGetPoolTransactions'
import { SkeletonList } from '@/components/ui/skeleton'
import { PoolTransaction } from '@/@generated/gql/graphql-trading'
import { timeFromNow, formatMoney } from '@/utils/helpers'
import { formatNumber } from '@/utils/numbers'
import { gqlClient } from '@/lib/gql/apollo-client'
import { getPoolTransactions } from '@/services/tokens.service'
import { SortByCreateAtType } from '@/types/enums'
import FilterArrowSort from './FilterArrowSort'
import FilterAddress from './FilterAddress'
import ModalDateTimePicker from '../detaiTokenTable/ModalDateTimePicker'
import FilterPoolTransactionType from './FilterPoolTransactionType'
import FilterQuantity from './FilterQuantity'
import FilterTotalValue from './FilterTotalValue'
import { TimeWheelDateType } from '@/redux/modules/tokenDetail.slice'
import { Button } from '../ui/button'
import LiquidityChartDrawer from './LiquidityChartDrawer'

interface DetailPoolTabProps {
  token?: string
  chainId?: number
}

interface FilterState {
  transactionType: PoolTransactionType
  holder: string
  sortBy: string
  timestampFrom?: number
  timestampTo?: number
  minQuantity?: number
  maxQuantity?: number
  minTotalValue?: number
  maxTotalValue?: number
  classification: TransactionClassification
}

const DetailPoolTab = ({ token, chainId }: DetailPoolTabProps) => {
  const { t } = useTranslation()

  const [transactions, setTransactions] = useState<PoolTransaction[]>([])
  const [lastTimestamp, setLastTimestamp] = useState<number | undefined>(undefined)
  const [hasMore, setHasMore] = useState(true)
  const [loadingMore, setLoadingMore] = useState(false)
  const [numberOfPools, setNumberOfPools] = useState<number | undefined>(undefined)
  const [liquidity, setLiquidity] = useState<number | undefined>(0)
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)

  const [filters, setFilters] = useState<FilterState>({
    transactionType: PoolTransactionType.All,
    holder: '',
    sortBy: SortByCreateAtType.DESC,
    timestampFrom: undefined,
    timestampTo: undefined,
    minQuantity: undefined,
    maxQuantity: undefined,
    minTotalValue: undefined,
    maxTotalValue: undefined,
    classification: TransactionClassification.All,
  })

  useEffect(() => {
    setTransactions([])
    setLastTimestamp(undefined)
    setHasMore(true)
    setLoadingMore(false)
  }, [filters])

  const { data, loading } = useGetPoolTransactions({
    filter: {
      token: token ?? '',
      chainId: chainId ?? 0,
      type: filters.transactionType,
      classification: filters.classification,
      lastTimestamp: undefined,
      timestampFrom: filters.timestampFrom,
      timestampTo: filters.timestampTo,
      sortBy: filters.sortBy === SortByCreateAtType.ASC ? 'timestamp' : '-timestamp',
      address: filters.holder,
      transactionVolumeFrom: filters.minQuantity,
      transactionVolumeTo: filters.maxQuantity,
      transactionUsdAmountFrom: filters.minTotalValue,
      transactionUsdAmountTo: filters.maxTotalValue,
    },
    skipCondition: !token || !chainId,
  })

  useEffect(() => {
    if (data?.getPoolTransactions?.data) {
      setTransactions(data.getPoolTransactions.data)
      setLastTimestamp(Number(data.getPoolTransactions.fromTimestamp))
      setHasMore(!!data.getPoolTransactions.data.length)
      setNumberOfPools(Number(data.getPoolTransactions.numberOfPools))
      setLiquidity(Number(data.getPoolTransactions.liquidity))
    }
  }, [data])

  async function fetchPoolTransactions(filter: any) {
    return gqlClient.query({
      query: getPoolTransactions,
      variables: { input: filter },
      fetchPolicy: 'network-only',
    })
  }

  const loadMore = async () => {
    if (loadingMore || !hasMore) return
    setLoadingMore(true)
    const res = await fetchPoolTransactions({
      token: token ?? '',
      chainId: chainId ?? 0,
      type: filters.transactionType,
      classification: filters.classification,
      lastTimestamp,
      timestampFrom: filters.timestampFrom,
      timestampTo: filters.timestampTo,
      sortBy: filters.sortBy === SortByCreateAtType.ASC ? 'timestamp' : '-timestamp',
      address: filters.holder,
      transactionVolumeFrom: filters.minQuantity,
      transactionVolumeTo: filters.maxQuantity,
      transactionUsdAmountFrom: filters.minTotalValue,
      transactionUsdAmountTo: filters.maxTotalValue,
    })
    if (res?.data?.getPoolTransactions?.data) {
      setTransactions((prev) => [...prev, ...res.data.getPoolTransactions.data])
      setLastTimestamp(Number(res.data.getPoolTransactions.fromTimestamp))
      setHasMore(!!res.data.getPoolTransactions.data.length)
      setNumberOfPools(Number(res.data.getPoolTransactions.numberOfPools))
      setLiquidity(Number(res.data.getPoolTransactions.liquidity))
    } else {
      setHasMore(false)
    }
    setLoadingMore(false)
  }

  const containerRef = useRef<HTMLDivElement>(null)
  useEffect(() => {
    const handleScroll = () => {
      const el = containerRef.current
      if (!el || loadingMore || !hasMore) return
      if (el.scrollHeight - el.scrollTop - el.clientHeight < 100) {
        loadMore().catch(console.error)
      }
    }
    const el = containerRef.current
    if (el) el.addEventListener('scroll', handleScroll)
    return () => {
      if (el) el.removeEventListener('scroll', handleScroll)
    }
  }, [loadingMore, hasMore, lastTimestamp])

  const tagFilters: string[] = [
    t('detail.filters.all'),
    t('detail.filters.smartMoney'),
    t('detail.filters.whale'),
    t('detail.filters.sniper'),
    t('detail.filters.sniper1'),
    t('detail.filters.projectParty'),
    t('detail.filters.ratWarehouse'),
    t('detail.filters.newWallet'),
    t('detail.filters.kol'),
  ]
  const [currentTab, setCurrentTab] = useState<string>(tagFilters[0])

  const getClassificationFromLabel = (label: string): TransactionClassification => {
    switch (label) {
      case t('detail.filters.all'):
        return TransactionClassification.All
      case t('detail.filters.smartMoney'):
        return TransactionClassification.SmartMoney
      case t('detail.filters.whale'):
        return TransactionClassification.Whale
      case t('detail.filters.sniper'):
      case t('detail.filters.sniper1'):
        return TransactionClassification.Sniper
      case t('detail.filters.projectParty'):
        return TransactionClassification.ProjectParty
      case t('detail.filters.ratWarehouse'):
        return TransactionClassification.Followed
      case t('detail.filters.newWallet'):
        return TransactionClassification.Fresh
      case t('detail.filters.kol'):
        return TransactionClassification.KOL
      default:
        return TransactionClassification.All
    }
  }

  const handleUpdateHolder = (address: string) => {
    setFilters((prev) => ({ ...prev, holder: address }))
  }

  const handleUpdateSort = (sort: string) => {
    setFilters((prev) => ({ ...prev, sortBy: sort }))
  }

  const handleTimeChange = (start: TimeWheelDateType | undefined, end: TimeWheelDateType | undefined) => {
    if (start) {
      const startTimestamp = new Date(
        Number(start.year),
        Number(start.month) - 1,
        Number(start.day),
        Number(start.hour),
        Number(start.minute),
      ).getTime()
      setFilters((prev) => ({ ...prev, timestampFrom: startTimestamp }))
    } else {
      setFilters((prev) => ({ ...prev, timestampFrom: undefined }))
    }
    if (end) {
      const endTimestamp = new Date(
        Number(end.year),
        Number(end.month) - 1,
        Number(end.day),
        Number(end.hour),
        Number(end.minute),
      ).getTime()
      setFilters((prev) => ({ ...prev, timestampTo: endTimestamp }))
    } else {
      setFilters((prev) => ({ ...prev, timestampTo: undefined }))
    }
  }

  const handleQuantityChange = (min: number | undefined, max: number | undefined) => {
    setFilters((prev) => ({
      ...prev,
      minQuantity: min,
      maxQuantity: max,
    }))
  }

  const handleTotalValueChange = (min: number | undefined, max: number | undefined) => {
    setFilters((prev) => ({
      ...prev,
      minTotalValue: min,
      maxTotalValue: max,
    }))
  }

  const handleTransactionTypeChange = (type: PoolTransactionType) => {
    setFilters((prev) => ({ ...prev, transactionType: type }))
  }

  const handleFilterByAddress = (address: string) => {
    setFilters((prev) => ({ ...prev, holder: address }))
  }

  const handleTabChange = (tab: string) => {
    setCurrentTab(tab)
    const classification = getClassificationFromLabel(tab)
    setFilters((prev) => ({ ...prev, classification }))
  }

  const columns: ColumnDef<PoolTransaction>[] = [
    {
      accessorKey: PoolColumnKeys.TYPE,
      minSize: 200,
      header: () => (
        <div className="flex items-center gap-0.5 min-w-[88px]">
          <div className="text-[11px] leading-3 tracking-[0.28px] text-[#FFFFFF]/50">{t('detail.pool.type')}</div>
          <FilterPoolTransactionType
            currentType={filters.transactionType}
            updateTransactionType={handleTransactionTypeChange}
          />
        </div>
      ),
      cell: ({ row }) => {
        const getTransactionTypeLabel = (type: string) => {
          switch (type) {
            case PoolTransactionType.All:
              return t('detail.tabs.all')
            case PoolTransactionType.Trading:
              return t('detail.pool.trading')
            case PoolTransactionType.Liquidity:
              return t('detail.pool.liquidity')
            case PoolTransactionType.Buy:
              return t('history.buy')
            case PoolTransactionType.Sell:
              return t('history.sell')
            case PoolTransactionType.AddLiquidity:
            case PoolTransactionType.Add:
              return t('history.addLiquidity')
            case PoolTransactionType.Remove:
            case PoolTransactionType.RemoveLiquidity:
              return t('history.removeLiquidity')
            default:
              return type
          }
        }

        return (
          <div className="flex items-center gap-1 min-w-[88px]">
            <img src="/images/tokenDetail/whale.png" alt="icon whale"/>
            {getTransactionTypeLabel(row.original.type)}
          </div>
        )
      },
    },
    {
      accessorKey: PoolColumnKeys.TIME,
      size: 88,
      header: () => {
        return (
          <div className="flex items-center gap-0.5 min-w-[64px]">
            <div className="text-[11px] leading-3 tracking-[0.28px] text-[#FFFFFF]/50">{t('detail.pool.time')}</div>
            <FilterArrowSort onSort={handleUpdateSort} currentSort={filters.sortBy} />
            <ModalDateTimePicker
              handleChangeStartTime={(time) => handleTimeChange(time, undefined)}
              handleChangeEndTime={(time) => handleTimeChange(undefined, time)}
            />
          </div>
        )
      },
      cell: ({ row }) => <div className="min-w-[64px]">{timeFromNow(Number(row.original.timestamp))}</div>,
    },
    {
      accessorKey: PoolColumnKeys.QUANTITY,
      header: () => {
        return (
          <div className="flex items-center gap-0.5 min-w-[88px]">
            <div className="text-[11px] leading-3 tracking-[0.28px] text-[#FFFFFF]/50">{t('detail.pool.quantity')}</div>
            <FilterQuantity token={'Trump'} onQuantityChange={handleQuantityChange} />
          </div>
        )
      },
      cell: ({ row }) => {
        const baseAmount = Number(row.original.baseAmount || 0)
        const quoteAmount = Number(row.original.quoteAmount || 0)
        if (baseAmount == 0 && quoteAmount == 0) {
          return <div className="flex gap-0.5 min-w-[74px] flex-col text-ce"> -</div>
        }
        return (
          <div className="flex gap-0.5 min-w-[74px] flex-col">
            {baseAmount != 0 && (
              <div className={`text-xs leading-3 tracking-[0.28px] flex gap-1 text-[#AB57FF]`}>
                <img src="/images/cryptoDeposit/solana.svg" className="w-3 h-3"  alt="icon solana"/>
                {baseAmount < 0 ? '-' : '+'}
                {formatNumber(Math.abs(baseAmount))}
              </div>
            )}
            {quoteAmount != 0 && (
              <div className={`text-xs leading-3 tracking-[0.28px] mt-1.5 flex gap-1 text-[#00FFB4]`}>
                <img src="/images/tokenDetail/trump.png" className="w-3 h-3 rounded-full " alt="icon trump" />
                {quoteAmount > 0 ? '+' : ''}
                {formatNumber(Math.abs(quoteAmount))}
              </div>
            )}
          </div>
        )
      },
    },
    {
      accessorKey: PoolColumnKeys.TOTAL_VALUE,
      header: () => {
        return (
          <div className="flex items-center gap-0.5 min-w-[74px]">
            <div className="text-[11px] leading-3 tracking-[0.28px] text-[#FFFFFF]/50">
              {t('detail.pool.totalValue')}
            </div>
            <FilterTotalValue onValueChange={handleTotalValueChange} />
          </div>
        )
      },
      cell: ({ row }) => {
        const usdAmount = Number(row.original.usdAmount || 0)
        return (
          <TransactionTooltip
            data={{
              value: usdAmount,
              change: 0,
            }}
          >
            <div
              className={`text-xs font-medium inline-flex items-center gap-1 cursor-pointer ${usdAmount < 0 ? 'text-[#FF5B5B]' : 'text-[#00FFB4]'}`}
            >
              <span
                className={`w-1.5 h-1.5 rounded-full inline-block ${usdAmount < 0 ? 'bg-[#FF5B5B]' : 'bg-[#00FFB4]'}`}
              ></span>
              {usdAmount < 0 ? '-' : ''}${formatNumber(Math.abs(usdAmount))}
            </div>
          </TransactionTooltip>
        )
      },
    },
    {
      accessorKey: PoolColumnKeys.ADDRESS,
      header: () => {
        return (
          <div className="flex items-center gap-0.5 min-w-[74px]">
            <div className="text-[11px] leading-3 tracking-[0.28px] text-[#FFFFFF]/50">
              {t('detail.pool.address')}
              <FilterAddress onAddressChange={handleUpdateHolder} currentAddress={filters.holder} />
            </div>
          </div>
        )
      },
      cell: ({ row }) => {
        const address = row.original.maker
        const shortAddress = address ? `${address.slice(0, 5)}...${address.slice(-4)}` : ''
        return (
          <div className="text-white flex items-center gap-1">
            <span className="w-[100px]">{shortAddress}</span>
            <Button
              size="xs"
              className="rounded-full bg-transparent p-0 hover:bg-white/10"
              onClick={() => handleFilterByAddress(address)}
            >
              <img src="/images/icons/icon-filter.svg" className="w-[11px] h-[11px] min-w-[11px]" alt="icon filter" />
            </Button>
          </div>
        )
      },
    },
  ]

  return (
    <div
      ref={containerRef}
      className="sticky top-[30px] translate-y-[-30px] mt-8 z-[1] px-2.5"
      style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 210px)' }}
    >
      <div className="mt-2 text-white/50 text-[0.813rem] flex items-center justify-between">
        <div className=" flex gap-3">
          <span>
            {t('detail.pool.totalLiquidity')}:{' '}
            <span className="text-white/60 font-medium">{formatMoney(liquidity)}</span>
          </span>
          <span>
            {t('detail.pool.poolCount')}: <span className="text-white/60 font-medium">{numberOfPools}</span>
          </span>
        </div>
        <button 
          className="inline-flex items-center gap-1 hover:text-white/80 transition-colors cursor-pointer"
          onClick={() => setIsDrawerOpen(true)}
        >
          <img src="/images/tokenDetail/activity.svg" alt="icon activity"/>
          {t('detail.pool.chart')}
        </button>
        <LiquidityChartDrawer
          isOpen={isDrawerOpen}
          onOpenChange={setIsDrawerOpen}
          liquidity={liquidity}
          numberOfPools={numberOfPools}
          transactions={transactions}
        />
      </div>
      <div className={cn('relative pt-[1px] mt-[5px]')}>
        <MovingBgGridFilterTags
          tabs={tagFilters}
          defaultTab={tagFilters[0]}
          activeTab={currentTab}
          onTabChange={handleTabChange}
          containerId="token-detail-pairs"
          containerClassName="mt-2.5 w-full overflow-x-auto no-scrollbar"
        />
      </div>
      <div className="relative pb-4 z-[3]">
        <DataTableInfiniteScroll
          columns={columns}
          data={transactions}
          isLoading={loading && !transactions.length}
          fetchMore={loadMore}
          hasMore={hasMore}
          tableProps={{
            isStickyHeader: true,
            stickyBg: 'rgb(23,24,27)',
            tableClassName: '',
            containerClassName: 'border-0 max-h-[calc(100vh-210px)] select-none',
            tableHeaderRowClassName: '!border-0 bg-black whitespace-nowrap',
            tableHeaderClassName: 'border-0 text-[#FFFFFF80] text-[calc(1rem*(11/16))] z-10 leading-3 app-font-medium',
            tableBodyRowClassName: '!border-0',
            skeletonComponent: <SkeletonList className="w-full" count={10} />,
          }}
        />
      </div>
    </div>
  )
}

export default DetailPoolTab
