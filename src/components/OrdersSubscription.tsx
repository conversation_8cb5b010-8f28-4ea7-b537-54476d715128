import { useSubscription } from '@/lib/mqtt'
import { useAppDispatch, useAppSelector } from '@/redux/store'
import { selectToken } from '@/redux/modules/auth.slice'
import React, { useCallback, useEffect } from 'react'
import { Order } from '@/@generated/gql/graphql-trading'
import {
  updateFillWeb3OrderFailed,
  updateOrderSubmitFailed,
  updateOrderUpdated,
} from '@/redux/modules/ordersSubscription.slice.ts'
import useShowToastOrder from '@/hooks/useShowToastOrder'

const OrdersSubscription: React.FC = () => {
  const dispatch = useAppDispatch()
  const tokenSelected = useAppSelector(selectToken)
  const activeAccount = useAppSelector((state) => state.wallet.activeAccount)
  const userId = tokenSelected[activeAccount]?.user_id
  const { showToastSubmittedSuccessOrder, showToastSubmittedFailOrder, newHiddenToastProcessOrder } =
    useShowToastOrder()
  const { message: _orderMessage } = useSubscription(
    [`users/${userId}/order_updated`, `users/${userId}/order_submit_failed`, `users/${userId}/fill_web3_order_failed`],
    {
      shouldSkip: !userId,
    },
  )

  const handleMessage = useCallback(() => {
    if (!_orderMessage || !userId) return

    const _messageTopic = _orderMessage?.topic
    const isOrderUpdated = _messageTopic?.includes('/order_updated')
    const isOrderSubmitFailed = _messageTopic?.includes('/order_submit_failed')
    const isFillWeb3OrderFailed = _messageTopic?.includes('/fill_web3_order_failed')

    try {
      const message = _orderMessage?.message
      const data = JSON.parse(message?.toString() || '')

      if (isOrderUpdated) {
        dispatch(updateOrderUpdated(data))
        newHiddenToastProcessOrder(data)
        if (data?.status === 'Completed') {
          showToastSubmittedSuccessOrder(data as Order)
        }
        return
      }

      if (isOrderSubmitFailed) {
        dispatch(updateOrderSubmitFailed(data))
        newHiddenToastProcessOrder(data)
        showToastSubmittedFailOrder(data?.code, data?.order)
        return
      }

      if (isFillWeb3OrderFailed) {
        dispatch(updateFillWeb3OrderFailed(data))
        return
      }
    } catch (error) {
      console.warn('_orderMessage error: ', error)
    }
  }, [_orderMessage, userId])

  useEffect(() => {
    console.log({ _orderMessage })
    handleMessage()
  }, [_orderMessage, userId])

  return <></>
}

export default OrdersSubscription
