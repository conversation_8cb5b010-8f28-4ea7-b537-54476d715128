import { useState } from 'react'
import { useMultiChainWallet } from '@/hooks/useMultiChainWallet'
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle, DrawerTrigger } from '@/components/ui/drawer'
import clsx from 'clsx'
import { f } from 'fintech-number'
import { useAppDispatch, useAppSelector } from '@/redux/store'
import { priceChain } from '@/redux/modules/price.slice'
import { getImgFromNameWallet, TYPE_ACCOUNT, TYPE_CHAIN } from '@/lib/blockchain'
import { CopyButton } from '../common/copy-button'
import { formatAddressWallet } from '@/lib/string'
import { cn } from '@/lib/utils'
import { Button } from '../ui/button'
import TooltipTag from '../common/TooltipTag'
import { useConnectModal } from '@rainbow-me/rainbowkit'
import { EVENT_MESSAGE_MODAL_WALLET_CONNECT } from '../header/wallet-connect'
import eventBus from '@/lib/eventBus'
import { walletActions } from '@/redux/modules/wallet.slice'
import { useTranslation } from 'react-i18next'
import useSignWallet from '@/hooks/useSignWallet'
import LogoWithChain from '../common/LogoWithChain'
import { getBlockChainLogo } from '@/utils/helpers'
import { PortfolioDTO } from '@/types/holding'
import { getPortfolio } from '@/services/tokens.service'
import ChooseAccountPopup from '@components/assets/deposit/ChooseAccountPopup.tsx'
import ChooseTokenPopup from '@components/assets/deposit/ChooseTokenPopup.tsx'
import { formatBalanceWallet, fShortenNumber } from '@/lib/number'
import { useQuery } from '@apollo/client'
import { selectToken } from '@/redux/modules/auth.slice'
import { Maybe } from '@/@generated/gql/graphql-core'

export const getImgIconChain = (chain: TYPE_CHAIN) => {
  switch (chain) {
    case TYPE_CHAIN.ETH:
      return '/images/icons/ic-ethereum.png'
    case TYPE_CHAIN.SOLANA:
      return '/images/icons/ic-solana.png'
    case TYPE_CHAIN.ARB:
      return '/images/icons/ic-ethereum.png'
    default:
      return '/images/icons/ic-solana.png'
  }
}

const FormDeposit = ({
  token,
  logoUrl,
  symbol,
}: {
  token: Maybe<string> | undefined
  logoUrl: Maybe<string> | undefined
  symbol: Maybe<string> | undefined
}) => {
  const { t, i18n } = useTranslation()
  const { wallets, activeChain, activeWallet, activeAccount, connectTelegramForChain } = useMultiChainWallet({})
  const [open, setOpen] = useState<boolean>(false)
  const priceETH = useAppSelector(priceChain('ETH'))
  const priceSol = useAppSelector(priceChain('SOL'))
  const { openConnectModal } = useConnectModal()
  const [openChooseAccountPopup, setOpenChooseAccountPopup] = useState(false)
  const [openChooseTokenPopup, setOpenChooseTokenPopup] = useState(false)
  const [walletToDeposit, setWalletToDeposit] = useState<string | undefined>(undefined)
  const currentLang = i18n.language

  useSignWallet({
    isAutoConnect: false,
  })

  const calculatePrice = (chain: TYPE_CHAIN) => {
    switch (chain) {
      case TYPE_CHAIN.ETH:
        return f(activeWallet?.balance?.formatted * priceETH, {
          decimal: 2,
          round: 'down',
        })
      case TYPE_CHAIN.SOLANA:
        return f(activeWallet?.balance?.formatted * priceSol, {
          decimal: 2,
          round: 'down',
        })
      default:
        return 0
    }
  }

  // eslint-disable-next-line no-unsafe-optional-chaining
  const { telegram: telegramWallet, chain: chainWallet } = wallets?.[activeChain]

  const onClickLoginByTG = () => {
    if (telegramWallet?.isConnected) return
    connectTelegramForChain()
    setOpen(false)
  }

  const onClickConnectWallet = () => {
    if (chainWallet?.isConnected) return
    if (activeChain === TYPE_CHAIN.ETH && openConnectModal) {
      openConnectModal()
    }
    if (activeChain === TYPE_CHAIN.SOLANA) {
      eventBus.dispatch(EVENT_MESSAGE_MODAL_WALLET_CONNECT, {
        data: {
          isOpen: true,
        },
      })
    }
    setOpen(false)
  }

  const iconWalletWeb3 = activeWallet?.walletInfo?.icon ? activeWallet?.walletInfo?.icon : getImgIconChain(activeChain)

  const tokenSelected = useAppSelector(selectToken)
  const getToken = (type: TYPE_ACCOUNT) => {
    return tokenSelected?.[type]?.access_token
  }
  const { data: dataPortfolioTelegram } = useQuery(getPortfolio, {
    variables: {
      input: { userAddress: telegramWallet?.walletId, token: token },
    },
    skip: !telegramWallet?.isConnected || !getToken(TYPE_ACCOUNT.TELEGRAM) || !token,
    context: { headers: { authorization: `Bearer ${getToken(TYPE_ACCOUNT.TELEGRAM)}` } },
    pollInterval: open ? 60000 : 0,
  })

  const dataPortfolioTelegramByToken = dataPortfolioTelegram?.getPortfolio?.data ?? []

  const { data: dataPortfolioWallet } = useQuery(getPortfolio, {
    variables: {
      input: { userAddress: chainWallet?.walletId, token: token },
    },
    skip: !chainWallet?.isConnected || !getToken(activeChain) || !token,
    context: { headers: { authorization: `Bearer ${getToken(activeChain)}` } },
    pollInterval: open ? 60000 : 0,
  })
  const dataPortfolioWalletByToken = dataPortfolioWallet?.getPortfolio?.data ?? []

  const listWalletsConnected = [
    ...(telegramWallet?.isConnected
      ? [
          {
            wallet: telegramWallet,
            typeAccount: TYPE_ACCOUNT.TELEGRAM,
            isHoldToken: dataPortfolioTelegramByToken?.length > 0,
            tokenHoding: dataPortfolioTelegramByToken,
          },
        ]
      : []),
    ...(chainWallet?.isConnected
      ? [
          {
            wallet: chainWallet,
            typeAccount: TYPE_ACCOUNT.CHAIN,
            isHoldToken: dataPortfolioWalletByToken?.length > 0,
            tokenHoding: dataPortfolioWalletByToken,
          },
        ]
      : []),
  ]

  const listWalletHodingToken = listWalletsConnected.filter((item) => item?.isHoldToken)
  const listWalletNotHodingToken = listWalletsConnected.filter((item) => !item?.isHoldToken)

  return (
    <div className="flex items-center mb-3.5">
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerTrigger asChild className="cursor-pointer">
          <div className="flex items-center gap-0.5">
            <img
              src={activeAccount === TYPE_ACCOUNT.TELEGRAM ? '/images/icons/logo-tele-1.svg' : iconWalletWeb3}
              className={clsx('w-3.5 h-3.5', {
                'mt-[1px]': activeAccount === TYPE_ACCOUNT.CHAIN,
              })}
              alt=""
            />
            {currentLang === 'zh' && (
              <p className="text-xs font-medium leading-none text-[#ffffff99]">{t('orderForm.form.available')}</p>
            )}
            <img className={clsx('w-3 h-3', open && 'rotate-180')} src="/images/icons/ic-down.svg" alt="" />
          </div>
        </DrawerTrigger>
        <DrawerContent className="bg-[#232329] max-w-[768px] mx-auto">
          <DrawerHeader className="flex items-center justify-between">
            <DrawerTitle>{t('orderForm.form.availableAssets')}</DrawerTitle>
            <img
              src="/images/icons/icon-x.svg"
              className="w-6 h-6 cursor-pointer"
              onClick={() => setOpen(false)}
              alt=""
            />
          </DrawerHeader>
          <div className="w-full px-3 mt-2">
            {listWalletHodingToken?.length > 0 && (
              <div>
                <p>
                  {t('orderForm.form.hodingtoken', {
                    token: symbol,
                  })}
                </p>
                {listWalletHodingToken?.map((item, index) => {
                  return (
                    <AccordionWallet
                      key={index}
                      walletInfo={item}
                      open={open}
                      logoUrl={logoUrl}
                      index={index + 1}
                    ></AccordionWallet>
                  )
                })}
              </div>
            )}
            {listWalletNotHodingToken?.length > 0 && (
              <div
                className={cn('', {
                  'mt-9': listWalletHodingToken?.length > 0,
                })}
              >
                <p>{t('orderForm.form.otherToken')}</p>
                {listWalletNotHodingToken?.map((item, index) => {
                  return (
                    <AccordionWallet
                      key={index}
                      walletInfo={item}
                      open={open}
                      logoUrl={logoUrl}
                      index={index + 1 + listWalletHodingToken?.length}
                    ></AccordionWallet>
                  )
                })}
              </div>
            )}
            <div className="flex items-center gap-2 mx-auto py-3 mt-10">
              <Button
                className={cn('flex-1 max-w-full rounded-[50px]', {
                  'cursor-not-allowed': telegramWallet?.isConnected,
                  'btn-gradient': telegramWallet?.isConnected,
                  'text-[#141414]': telegramWallet?.isConnected,
                  'text-[#fff]': !telegramWallet?.isConnected,
                  'bg-[#ECECED1F]': !telegramWallet?.isConnected,
                })}
                disabled={telegramWallet?.isConnected}
                onClick={onClickLoginByTG}
              >
                {t('orderForm.form.connectTelegramWallet')}
                {telegramWallet?.isConnected && (
                  <TooltipTag variant="gradient">{t('orderForm.form.linked')}</TooltipTag>
                )}
              </Button>
              <Button
                className={cn('flex-1 rounded-[50px]', {
                  'cursor-not-allowed': chainWallet?.isConnected,
                  'btn-gradient': chainWallet?.isConnected,
                  'text-[#141414]': chainWallet?.isConnected,
                  'text-[#fff]': !chainWallet?.isConnected,
                  'bg-[#ECECED1F]': !chainWallet?.isConnected,
                })}
                disabled={chainWallet?.isConnected}
                onClick={onClickConnectWallet}
              >
                {t('orderForm.form.connectExternalWallet')}
                {chainWallet?.isConnected && <TooltipTag variant="gradient">{t('orderForm.form.linked')}</TooltipTag>}
              </Button>
            </div>
          </div>
        </DrawerContent>
      </Drawer>
      <div className="ml-auto mr-1">
        <p className="text-[11px] font-medium leading-none text-white">
          {f(activeWallet?.balance?.formatted, {
            decimal: 6,
            round: 'down',
          })}{' '}
          {activeWallet?.chainType?.toUpperCase()}{' '}
          <span className="text-[#ffffff99]">≈ ${calculatePrice(activeWallet?.chainType)}</span>
        </p>
      </div>
      <img src="/images/icons/ic-add-circle.svg" className="w-3 h-3 cursor-pointer" alt=""
        onClick={() => {
          setOpenChooseAccountPopup(true)
        }}
      />
      <ChooseAccountPopup
        open={openChooseAccountPopup}
        setOpen={setOpenChooseAccountPopup}
        setOpenChooseTokenPopup={(open: boolean, setWalletAccountSelect) => {
          setOpenChooseTokenPopup(open)
          if (setWalletAccountSelect) {
            setWalletToDeposit(setWalletAccountSelect)
          }
        }}
      />
      <ChooseTokenPopup
        open={openChooseTokenPopup}
        setOpen={setOpenChooseTokenPopup}
        walletToDeposit={walletToDeposit}
      />
    </div>
  )
}

export default FormDeposit

const AccordionWallet = ({
  walletInfo,
  open,
  logoUrl,
  index,
}: {
  walletInfo: any
  open: boolean
  logoUrl: Maybe<string> | undefined
  index: number
}) => {
  const { wallet, typeAccount, tokenHoding, isHoldToken } = walletInfo
  const activeChain = useAppSelector((state) => state.wallet.activeChain)
  const activeAccount = useAppSelector((state) => state.wallet.activeAccount)
  const dispatch = useAppDispatch()
  const { t } = useTranslation()

  const tokenSelected = useAppSelector(selectToken)
  const getToken = (type: TYPE_ACCOUNT) => {
    return tokenSelected?.[type]?.access_token
  }

  const { data: dataLists } = useQuery(getPortfolio, {
    variables: {
      input: { userAddress: wallet?.walletId, hideSmallBalance: false, hideSmallLiquidity: false, limit: 5 },
    },
    skip:
      !wallet?.isConnected ||
      !getToken(typeAccount === TYPE_ACCOUNT.TELEGRAM ? TYPE_ACCOUNT.TELEGRAM : activeChain) ||
      isHoldToken,
    pollInterval: open ? 60000 : 0,
    context: {
      headers: {
        authorization: `Bearer ${getToken(typeAccount === TYPE_ACCOUNT.TELEGRAM ? TYPE_ACCOUNT.TELEGRAM : activeChain)}`,
      },
    },
  })

  const listPortfolios = dataLists?.getPortfolio?.data ?? []
  const totalHoldingTokens = dataLists?.getPortfolio?.totalHoldingTokens ?? 0

  const onSwitchAccount = (account: TYPE_ACCOUNT) => {
    dispatch(walletActions.setActiveAccount(account))
  }

  return (
    <div
      className={cn(
        'mt-3.5 bg-[url(/images/icons/img-bg-list-wallets-1.svg)] bg-center bg-cover bg-no-repeat rounded-md mx-auto overflow-hidden',
        {
          'gradient-border': activeAccount === typeAccount,
        },
      )}
    >
      <div
        className="p-3 border-b-[0.5px] border-b-[#ececed14] gap-0 rounded-4x overflow-hidden"
        onClick={() => onSwitchAccount(typeAccount)}
      >
        <div className="flex items-center gap-3">
          <img
            src={wallet?.walletInfo?.icon ? wallet?.walletInfo?.icon : getImgFromNameWallet(wallet?.walletInfo?.name)}
            className="w-10 h-10"
            alt=""
          />
          <div>
            <div className="flex items-center">
              <p className="text-sm font-medium mr-2.5">
                {t('detail.tokenDetail.wallet')} {index}
              </p>
              <p className="text-xs leading-none text-white/50 mr-1.5">{formatAddressWallet(wallet?.walletId)}</p>
              <CopyButton text={wallet?.walletId} />
            </div>
            <div className="flex items-center gap-1.5 mt-2">
              <img src={getImgIconChain(activeChain)} className="w-4.5 h-4.5" alt="" />
              <p className="text-sm text-white/70">
                {formatBalanceWallet({
                  balance: wallet?.balance?.formatted,
                })}
              </p>
            </div>
          </div>
          <div className="ml-auto mt-auto">
            {tokenHoding?.length > 0 &&
              tokenHoding?.map((item: PortfolioDTO, index: number) => {
                // const chainUrl = getBlockchainLogo2(item?.chainId ?? chainSymbolToId['sol'])
                const tokenLogo = logoUrl ?? getBlockChainLogo(item?.chainId, item?.token ?? '')
                return (
                  <div className="flex items-center gap-1.5 mt-2" key={index}>
                    <div className="w-4 h-4">
                      <LogoWithChain
                        logo={tokenLogo}
                        name={item?.symbol}
                        logoClassName="border-[0.5px] border-[#2E0066] w-[16px] min-w-[16px] h-[16px] !rounded-[6px]"
                      />
                    </div>
                    <p className="text-sm text-white/70">
                      {fShortenNumber(item?.totalBaseAmount, 6, {
                        rounded: 'auto',
                      })}
                    </p>
                  </div>
                )
              })}
            {listPortfolios?.length > 0 && (
              <div className="flex items-center">
                {listPortfolios?.map((item: PortfolioDTO, index: number) => {
                  const tokenLogo = item?.logoUrl ?? getBlockChainLogo(item?.chainId, item?.token ?? '')
                  return (
                    <div className="flex items-center gap-1.5 mt-2 ml-[-8px]" key={index}>
                      <LogoWithChain
                        logo={tokenLogo}
                        name={item?.symbol}
                        logoClassName="border-[0.5px] border-[#2E0066] w-[16px] min-w-[16px] h-[16px] !rounded-[6px]"
                      />
                    </div>
                  )
                })}
                {listPortfolios?.length > 3 && (
                  <p className="text-sm text-white mt-2 ml-1.5">{fShortenNumber(totalHoldingTokens - 3)}</p>
                )}
              </div>
            )}
          </div>
        </div>
        {activeAccount === typeAccount && (
          <img src="/images/icons/ic-wallet-checked.svg" className="absolute right-0 top-0 z-10" alt="" />
        )}
      </div>
    </div>
  )
}
