// components/TokenPageTitle.tsx
import { useTokenPriceChange2 } from '@hooks/useTokenPriceChange'
import { usePageTitle } from '@hooks/usePageTitle'
import { formatTokenPrice } from '@/utils/helpers'
import { f } from 'fintech-number'

const subscriptMap: Record<string, string> = {
  '0': '₀',
  '1': '₁',
  '2': '₂',
  '3': '₃',
  '4': '₄',
  '5': '₅',
  '6': '₆',
  '7': '₇',
  '8': '₈',
  '9': '₉',
}

function toSubscript(input: number | string): string {
  return String(input)
    .split('')
    .map((char) => subscriptMap[char] ?? char)
    .join('')
}

function formatPriceAsTitle(tokenPrice: string | null | undefined) {
  if (!tokenPrice) return ''
  const formattedPrice = formatTokenPrice(+tokenPrice, { roundType: 'round' })
  const { integerPart, decimalPart, zeroCount } = formattedPrice

  const integerStr = f(+integerPart)
  const decimalStr = decimalPart
    ? `.${zeroCount > 0 ? '0' + toSubscript(zeroCount) : ''}${decimalPart.replace(/0+$/, '')}`
    : ''

  return `$${integerStr}${decimalStr}`
}

type TokenPageTitleProps = {
  address?: string
  symbol?: string
  defaultPrice?: string
}

const TokenPageTitle = ({ address, symbol, defaultPrice }: TokenPageTitleProps) => {
  const tokenPrice = useTokenPriceChange2({
    address,
    defaultValue: defaultPrice,
  })

  const title = symbol && tokenPrice ? `${symbol} ${formatPriceAsTitle(tokenPrice)} | XBIT Platform` : 'XBIT Platform'

  usePageTitle(title) // runs on render

  return null
}

export default TokenPageTitle
