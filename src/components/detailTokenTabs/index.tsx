import { UITab } from '@/types/uiTabs.ts'
import { useState } from 'react'
import Container from '@components/common/Container.tsx'
import { useTranslation } from 'react-i18next'
import FilterFollowedTabs from '@components/detailTokenTabs/FilterFollowedTabs.tsx'
import DetailLatestTab from '@components/detailLatestTab'
import { ChainIds, FilterTransactionAmountType } from '@/types/enums.ts'
import ChangeCurrencyFollowed from '@components/detailTokenTabs/ChangeCurrencyFollowed.tsx'
import DetailFollowedHolderTab from '@components/detailFollowedHolderTab'
import DetailFollowedPoolTab from '@components/detailFollowedPoolTab'
import { RootState, useAppSelector } from '@/redux/store'
import { HoldingState } from '@/redux/modules/holding.slice.ts'

const DetailTokenTabs = () => {
  const { t } = useTranslation()
  const currentListTabs: UITab[] = [
    {
      value: 'latest',
      label: t('detail.tabs.latest'),
    },
    {
      value: 'holders',
      label: t('detail.tabs.holdersWithCount'),
    },
    {
      value: 'pool',
      label: t('detail.tabs.pool'),
    },
  ]

  const { currentToken } = useAppSelector((state: RootState) => state.holding as HoldingState)

  const [currentTab, setCurrentTab] = useState<string>(currentListTabs[0].value)
  const [currency, setCurrency] = useState<FilterTransactionAmountType>(FilterTransactionAmountType.USDT)

  const handleChangeTab = (tab: string) => {
    setCurrentTab(tab)
  }

  const handleRenderTabs = (tab: string) => {
    switch (tab) {
      case currentListTabs[0].value:
        return <DetailLatestTab currency={currency} />
      case currentListTabs[1].value:
        return <DetailFollowedHolderTab />
      default:
        return <DetailFollowedPoolTab token={currentToken ?? ""} chainId={ChainIds.Solana} />
    }
  }

  return (
    <Container className="relative mt-2.5 z-[2]">
      <div className="flex items-center justify-between">
        <FilterFollowedTabs
          containerId={'tabs-followed'}
          tabs={currentListTabs}
          onTabChange={handleChangeTab}
          defaultTab={currentListTabs[0].value}
        />
        {
          currentTab === currentListTabs[0].value
          && <ChangeCurrencyFollowed currency={currency} setCurrency={setCurrency} />
        }
      </div>

      {handleRenderTabs(currentTab)}
    </Container>
  )
}

export default DetailTokenTabs