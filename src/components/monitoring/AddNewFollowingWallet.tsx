import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import BottomSheet from '@/components/common/BottomSheet'
import { Button } from '@components/ui/button.tsx'
import { toast } from 'sonner'
import { isValidSolAddress } from '@/lib/blockchain'
import { cn } from '@/lib/utils'
import { useMutation } from '@apollo/client'
import { followWallet } from '@/services/wallet.service'
import { gqlClient } from '@/lib/gql/apollo-client'

type Props = {
  open: boolean
  setOpen: (open: boolean) => void
}

const AddNewFollowingWallet = ({ open, setOpen }: Props) => {
  const { t } = useTranslation()
  const [walletAddress, setWalletAddress] = useState<string>('')
  const [isValidAddress, setIsValidAddress] = useState<boolean>(true)
  const [walletName, setWalletName] = useState<string>('')
  const [focusField, setFocusField] = useState<'address' | 'name' | undefined>(undefined)
  const [followWalletMutation] = useMutation(followWallet, {
    client: gqlClient,
  })



  const handleAddWallet = () => {
    if (!walletAddress) {
      setIsValidAddress(false)
      return
    }
    if (!isValidSolAddress(walletAddress)) {
      setIsValidAddress(false)
      return
    }
    setIsValidAddress(true)
    followWalletMutation({
      variables: {
        input: {
          chain: 'SOLANA',
          walletAddress: walletAddress,
          // name: walletName || walletAddress,
        },
      },
    })
      .then(() => {
        setOpen(false)
        setWalletAddress('')
        setWalletName('')
        setFocusField(undefined)
        toast.success(t('followingWallet.addWalletSuccess'))
      })
      .catch((error) => {
        setOpen(false)
        setWalletAddress('')
        setWalletName('')
        setFocusField(undefined)
        toast.error(t('toast.addFavoriteFailed'))
      })
  }

  return (
    <BottomSheet open={open} setOpen={setOpen} title={t('followingWallet.addWallet')}>
      <div className="overflow-y-auto no-scrollbar max-h-[calc(80vh-150px)]">
        <div className="font-normal text-[14px] leading-[18px]">
          {t('followingWallet.walletAddress')} <span className="text-[#FF353C]"> *</span>
        </div>
        <div
          className={cn(
            'rounded-lg mt-3',
            focusField === 'address' ? 'border-gradient style2' : 'border border-[#ECECED1F]',
          )}
        >
          <input
            type="text"
            className="w-full px-3 py-4 rounded-lg bg-[#141414] text-[14px] text-white focus:outline-none"
            placeholder={t('followingWallet.inputWalletAddress')}
            value={walletAddress}
            onChange={(e) => {
              setWalletAddress(e.target.value)
            }}
            onFocus={() => {
              setFocusField('address')
              setIsValidAddress(true)
            }}
          />
        </div>
        {!isValidAddress && (
          <div className="mt-2 font-normal text-[12px] text-[#FF353C] leading-none">
            {t('followingWallet.inputWalletAddressError')}
          </div>
        )}
        <div className="mt-5 font-normal text-[14px] leading-[18px]">{t('followingWallet.walletName')}</div>
        <div
          className={cn(
            'rounded-lg mt-3',
            focusField === 'name' ? 'border-gradient style2' : 'border border-[#ECECED1F]',
          )}
        >
          <input
            type="text"
            className="w-full px-3 py-4 rounded-lg bg-[#141414] text-[14px] text-white focus:outline-none"
            placeholder={t('followingWallet.optional')}
            value={walletName}
            onChange={(e) => setWalletName(e.target.value)}
            onFocus={() => {
              setFocusField('name')
            }}
          />
        </div>
      </div>
      <div className="w-full mt-3 pt-3 border-t border-[#ECECED0A] flex gap-4 items-center">
        <Button variant="borderGradient" className="rounded-full flex-1 h-11" onClick={() => setOpen(false)}>
          {t('button.cancel')}
        </Button>
        <Button
          variant="gradient"
          className="rounded-full flex-1 h-11 text-black"
          onClick={() => handleAddWallet()}
          disabled={!walletAddress || !isValidAddress}
        >
          {t('button.add')}
        </Button>
      </div>
    </BottomSheet>
  )
}

export default AddNewFollowingWallet
