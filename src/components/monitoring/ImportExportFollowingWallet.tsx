import BottomSheet from '@/components/common/BottomSheet'
import { useTranslation } from 'react-i18next'
import { Button } from '@components/ui/button.tsx'
import { useState } from 'react'
import { toast } from 'sonner'
import { isValidSolAddress } from '@/lib/blockchain'
import { cn } from '@/lib/utils'

type Props = {
  open: boolean
  setOpen: (open: boolean) => void
  listFollowing: string[]
}

const ImportExportFollowingWallet = ({ open, setOpen, listFollowing }: Props) => {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState<'import' | 'export'>('import')
  const [importedData, setImportedData] = useState<string>('')
  const [isValidData, setIsValidData] = useState<boolean>(true)
  const [isFocus, setIsFocus] = useState<boolean>(false)

  const handleImport = () => {
    const addresses = importedData
      .split(',')
      .map((item) => item.trim())
      .filter((item) => item !== '')

    const output = addresses.map((address) => {
      const parts = address.split(':')
      return {
        address: parts[0].trim(),
        name: parts[1] ? parts[1].trim() : parts[0].trim(),
      }
    })
    const invalidAddresses = output.filter((item) => !isValidSolAddress(item.address))
    if (invalidAddresses.length > 0) {
      setIsValidData(false)
      return
    }
    setIsValidData(true)
    // TODO: Call the followWallet mutation with the output data
    setOpen(false)
    toast.success(t('followingWallet.importSuccess'))
  }

  const handleExport = () => {
    if (listFollowing.length > 0) {
      const dataToCopy = listFollowing.map((item) => {
        return {
          address: item,
          name: item,
        }
      })
      // Convert to JSON string
      const jsonData = JSON.stringify(dataToCopy, null, 2)
      const blob = new Blob([jsonData], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = 'following_wallets.json'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      navigator.clipboard
        .writeText(jsonData)
        .then(() => {
          setOpen(false)
        })
        .catch((err) => {
          console.error('Failed to copy data to clipboard:', err)
        })
      setOpen(false)
      toast.success(t('followingWallet.exportSuccess'))
    } else {
      setIsValidData(false)
    }
  }

  return (
    <BottomSheet open={open} setOpen={setOpen} title={t('followingWallet.importAndExportWallets')}>
      <div className="overflow-y-auto no-scrollbar max-h-[calc(80vh-150px)]">
        <div className="bg-[#ECECED0A] rounded-[200px] p-0.5 border-[0.5px] border-[#ECECED0A] flex items-center justify-between">
          <button
            className={`flex-1 text-center py-2 rounded-full font-medium text-[16px] ${activeTab === 'import' ? 'bg-gradient-to-r from-[#d83bfc] via-[#ffffff] to-[#19f5b1] text-[#141414]' : 'text-[#FFFFFFB2]'}`}
            onClick={() => setActiveTab('import')}
          >
            {t('followingWallet.import')}
          </button>
          <button
            className={`flex-1 text-center py-2 rounded-full font-medium text-[16px] ${activeTab === 'export' ? 'bg-gradient-to-r from-[#d83bfc] via-[#ffffff] to-[#19f5b1] text-[#141414]' : 'text-[#FFFFFFB2]'}`}
            onClick={() => setActiveTab('export')}
          >
            {t('followingWallet.export')}
          </button>
        </div>
        {activeTab === 'import' ? (
          <>
            <div className="mt-4 text-white/70 font-normal text-[13px] leading-1.4">
              {t('followingWallet.importNote')}
            </div>
            <div className="mt-3 text-white/70 font-normal text-[13px] leading-1.4">
              {t('followingWallet.importExample')}
            </div>
            <div
              className={cn(
                'rounded-lg mt-3 bg-[#141414]',
                isFocus ? 'border-gradient style2' : 'border border-[#ECECED1F]',
              )}
            >
              <textarea
                className="w-full h-full px-3 py-4 rounded-lg bg-[#141414] text-[14px] text-white focus:outline-none"
                placeholder={t('followingWallet.inputWalletAddress2')}
                rows={5}
                value={importedData}
                onChange={(e) => setImportedData(e.target.value)}
                onFocus={() => {
                  setIsFocus(true)
                  setIsValidData(true)
                }}
              />
            </div>
            {!isValidData && (
              <div className="mt-2 font-normal text-[12px] text-[#FF353C] leading-none">
                {t('followingWallet.inputWalletAddressError2')}
              </div>
            )}
          </>
        ) : (
          <div className="mt-4 py-6 flex flex-col items-center justify-center">
            <div className="font-nomal text-[16px] leading-1.4 text-white">
              {t('followingWallet.batchCopyToClipboard')}
            </div>
            <img
              src="/images/export-following-wallet.png"
              alt="export-following-wallet"
              className="mt-4 w-[160px] h-[120px]"
            />
          </div>
        )}
      </div>
      <div className="w-full mt-3 pt-3 border-t border-[#ECECED0A] flex gap-4 items-center">
        <Button variant="borderGradient" className="rounded-full flex-1 h-11" onClick={() => setOpen(false)}>
          {t('button.cancel')}
        </Button>
        <Button
          variant="gradient"
          className="rounded-full flex-1 h-11 text-black"
          onClick={() => {
            if (activeTab === 'import') {
              handleImport()
            } else {
              handleExport()
            }
          }}
          disabled={activeTab === 'import' ? !importedData || !isValidData : false}
        >
          {activeTab === 'import' ? t('button.confirm') : t('followingWallet.copyToClipboard')}
        </Button>
      </div>
    </BottomSheet>
  )
}

export default ImportExportFollowingWallet
