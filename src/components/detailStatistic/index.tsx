import Container from '@components/common/Container.tsx'
import { useTranslation } from 'react-i18next'
import { useGetPortfolio } from '@hooks/useGetPortfolio.ts'
import { useMultiChainWallet } from '@hooks/useMultiChainWallet.ts'
import { RootState, useAppSelector } from '@/redux/store'
import { HoldingState } from '@/redux/modules/holding.slice.ts'
import useTokenPrice from '@hooks/useTokenPrice.ts'
import { formatLongValue } from '@/utils/helpers.ts'
import React, { useState } from 'react'
import { cn } from '@/lib/utils.ts'
import { FilterTransactionAmountType } from '@/types/enums.ts'
import { priceChain } from '@/redux/modules/price.slice.ts'

type StatsColumn = {
  value: string
  title: string | React.ReactNode
  content: string | React.ReactNode
}

const DetailStatistic = () => {
  const { t } = useTranslation()

  const [currency, setCurrency] = useState<FilterTransactionAmountType>(FilterTransactionAmountType.USDT)

  const { activeWallet } = useMultiChainWallet({})
  const address = activeWallet?.walletId
  const { currentToken } = useAppSelector((state: RootState) => state.holding as HoldingState)
  const priceData = useAppSelector(priceChain('SOL'))

  const { data } = useGetPortfolio({
    limit: 1,
    userAddress: address,
    token: currentToken,
    skipCondition: !currentToken || !address,
  })

  const portfolioData = data?.getPortfolio?.data?.filter((item) => item.token === currentToken)?.[0]

  const price = useTokenPrice(currentToken ?? '', (portfolioData?.price ?? 0)?.toString())

  const realized = portfolioData?.realizedPnL ?? 0
  const unrealized = portfolioData
    ? (Number(price ?? 0) - Number(portfolioData?.avgPriceUsd)) * Number(portfolioData?.totalBaseAmount)
    : 0

  const holdingValue = portfolioData
    ? Number(portfolioData?.totalBaseAmount) * Number(price ?? '0')
    : 0

  const PnL = Number(realized) + Number(unrealized)
  const PnLPercent = (PnL && portfolioData?.maxHoldingQty)
    ? (PnL / (Number(portfolioData?.maxHoldingQty) * price)) * 100
    : 0

  const buyValue = (Number(portfolioData?.avgPriceUsd) * Number(portfolioData?.totalBuyQty))
  const buyQuoteValue = (priceData && buyValue) ? Number(buyValue) / priceData : 0

  const sellValue = (Number(portfolioData?.avgPriceUsd) * Number(portfolioData?.totalSellQty))
  const sellQuoteValue = (priceData && sellValue) ? Number(sellValue) / priceData : 0

  const handleColumnContentTextColor = (value: string) => {
    const strValue = value.replace('$', '')
    if (strValue === '--' || strValue === '0' || strValue === '0.00' || !isFinite(parseFloat(strValue))) {
      return 'text-white'
    }
    return parseFloat(strValue) > 0 ? 'text-[#00FFB4]' : 'text-[#AB57FF]'
  }

  const handleChangeCurrency = () => {
    setCurrency(
      currency === FilterTransactionAmountType.USDT
        ? FilterTransactionAmountType.SOL
        : FilterTransactionAmountType.USDT,
    )
  }

  const statsColumns: StatsColumn[] = [
    {
      value: 'buyValue',
      title: t('detailStatistic.buyValue'),
      content: (
        <span className="flex items-center justify-center gap-0.5 text-[#00FFB4]">
          {currency === FilterTransactionAmountType.SOL ? (
            <img src="/images/orderBook/icon-sol.png" alt="icon sol" />
          ) : (
            '$'
          )}
          {formatLongValue(currency === FilterTransactionAmountType.SOL ? buyQuoteValue : buyValue)}
        </span>
      ),
    },
    {
      value: 'sellValue',
      title: t('detailStatistic.sellValue'),
      content: (
        <span className="flex items-center justify-center gap-0.5 text-[#AB57FF]">
          {currency === FilterTransactionAmountType.SOL ? (
            <img src="/images/orderBook/icon-sol.png" alt="icon sol" />
          ) : (
            '$'
          )}
          {formatLongValue(currency === FilterTransactionAmountType.SOL ? sellQuoteValue : sellValue)}
        </span>
      ),
    },
    {
      value: 'holdingValue',
      title: t('detail.statistics.holdingValue'),
      content: (
        <span className="flex items-center justify-center gap-0.5">
          {currency === FilterTransactionAmountType.SOL ? (
            <img src="/images/orderBook/icon-sol.png" alt="icon sol" />
          ) : (
            '$'
          )}
          {formatLongValue(holdingValue)}
        </span>
      ),
    },
    {
      value: 'pnl',
      title: t('detailStatistic.pnl'),
      content: (
        <span className={cn(
          "flex items-center justify-center gap-0.5",
          PnL > 0 ? 'text-[#00FFB4]' : 'text-[#AB57FF]'
        )}>
          {currency === FilterTransactionAmountType.SOL ? (
            <img src="/images/orderBook/icon-sol.png" alt="icon sol" />
          ) : (
            '$'
          )}
          {formatLongValue(PnL)}
          <span className="ml-1">({formatLongValue(PnLPercent, true)}%)</span>
        </span>
      ),
    },
  ]

  return (
    <Container>
      <div className="px-[8px] py-[7.5px] rounded-[6px] bg-[#FFFFFF0A] flex items-center justify-between text-nowrap overflow-x-auto">
        {/*btn change currency*/}
        <div
          onClick={handleChangeCurrency}
          className="flex flex-col items-center gap-1 px-1 py-[4.5px] bg-[#ECECED0A] rounded-xs cursor-pointer select-none"
        >
          <img src="/images/orderBook/icon-refund.svg" className="w-3 min-w-3" alt="change currency" />
          <span className="w-5 text-center text-[9px] leading-[1] text-[#FFFFFFB2] aap-font-regular">
            {currency === FilterTransactionAmountType.USDT ? 'USD' : 'SOL'}
          </span>
        </div>
        {statsColumns.map((column: StatsColumn) => (
          <div
            key={column.value}
            className="leading-[1] px-[14.5px] flex-1 flex flex-col justify-between text-center gap-[6px] relative first:pl-0 last:pr-0 last:after:content-none after:content-[''] after:absolute after:w-[1px] after:h-[24px] after:bg-[#ECECED14] after:right-0 after:top-[50%] after:transform after:-translate-y-1/2"
          >
            <div className="text-[calc(1rem*(11/16))] text-[#FFFFFF99]">{column.title}</div>
            <div
              className={cn('text-[calc(1rem*(10/16))] text-white', handleColumnContentTextColor(`${column.content}`))}
            >
              {column.content}
            </div>
          </div>
        ))}
      </div>
    </Container>
  )
}

export default DetailStatistic
