import InfluentialFollowers from './InfluentialFollowers'
import OfficialDataAnalysis from './OfficialDataAnalysis'
import OnChainAnalytics from './OnChainAnalytics'
import OnChainTextAnalysis from './OnChainTextAnalysis'

export interface AIAnalyticsProps {
  address: string | undefined
}

const AIAnalytics = (props: AIAnalyticsProps) => {
  const { address } = props
  return (
    <div className="mt-4">
      <div className="px-[10px]">
        <OnChainAnalytics address={address} />
        <OnChainTextAnalysis tokenAddress={address} />
      </div>
      <OfficialDataAnalysis />
      <InfluentialFollowers />
    </div>
  )
}

export default AIAnalytics
