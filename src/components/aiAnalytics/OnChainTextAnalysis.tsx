import { useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useGetAiAnalyzedInfo } from '@hooks/useGetAiAnalyzedInfo.ts'
import { cn } from '@/lib/utils.ts'
import { Skeleton } from '@components/ui/skeleton.tsx'
import { AiAnalyzedInfoLang } from '@/@generated/gql/graphql-core.ts'

const languageMap: Record<string, AiAnalyzedInfoLang> = {
  en: AiAnalyzedInfoLang.En,
  zh: AiAnalyzedInfoLang.ZhHans,
  ja: AiAnalyzedInfoLang.Ja,
  hi: AiAnalyzedInfoLang.Hi,
}

export interface OnChainTextAnalysisProps {
  tokenAddress: string | undefined
}

const OnChainTextAnalysis = (props: OnChainTextAnalysisProps) => {
  const { tokenAddress } = props
  const { t, i18n } = useTranslation()

  const tabs = {
    narrative: t('ai.narrative'),
    website: t('ai.websiteAnalysis'),
    avatar: t('ai.avatarAnalysis'),
  }

  const [activeTab, setActiveTab] = useState(tabs.narrative)
  const [isExpanded, setIsExpanded] = useState(false)
  const pRef = useRef<HTMLParagraphElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)

  const language = useMemo(() => {
    const lang = i18n.language
    return languageMap[lang] || AiAnalyzedInfoLang.En
  }, [i18n.language])

  const { data, loading } = useGetAiAnalyzedInfo({ tokenAddress: tokenAddress, language: language })

  const getTabContent = (tab: string | undefined): string => {
    if (!data) return ''
    switch (tab) {
      case tabs.narrative:
        return data.themeNarrativeAnalytic ?? ''
      case tabs.website:
        return data.socialWebsiteAnalytic ?? ''
      case tabs.avatar:
        return data.avatarAnalytic ?? ''
      default:
        return data.themeNarrativeAnalytic ?? ''
    }
  }

  const getTabStyle = (tab: string) => ({
    background:
      activeTab === tab
        ? 'linear-gradient(43.83deg, #E843FE 0%, #FFFFFF 47.32%, #FFFFFF 63.89%, #00FFCD 103.57%)'
        : '#ECECED14',
    color: activeTab === tab ? '#141414' : '#FFFFFFB2',
  })

  const content = getTabContent(activeTab)

  useEffect(() => {
    const p = pRef.current
    const button = buttonRef.current
    if (!p || !button) return
    // Check if the content is longer than 3 lines
    const lineHeight = parseFloat(getComputedStyle(p).lineHeight)
    const maxLines = 5
    const maxHeight = Math.round(maxLines * lineHeight)
    const isLong = p.scrollHeight > maxHeight
    if (!isLong) {
      button.classList.add('hidden')
    } else {
      button.classList.remove('hidden')
    }
  }, [pRef.current, buttonRef.current, content, isExpanded])

  return (
    <div className="mt-5 rounded-xl w-full max-w-3xl text-white">
      <h2 className="text-sm app-font-medium">{t('ai.onchainTextTitle')}</h2>

      <div className="mt-4 mb-3 flex gap-3">
        {Object.values(tabs).map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className="px-3.5 py-1.5 text-sm app-font-medium rounded-full border transition-all duration-200"
            style={getTabStyle(tab)}
          >
            {tab}
          </button>
        ))}
      </div>

      {loading ? (
        <Skeleton className="w-full h-28" />
      ) : (
        <div
          className={cn(
            'text-[calc(1rem*(13/16))] leading-[calc(1rem*(19.5/16))] text-[#FFFFFFCC] bg-[#ECECED0A] border border-[#ECECED1F] rounded-[8px] p-2',
            !content && 'hidden',
          )}
        >
          <p
            ref={pRef}
            className={cn(
              'text-[calc(1rem*(13/16))]leading-[calc(1rem*(19.5/16))] transition-all text-ellipsis',
              isExpanded ? '' : 'line-clamp-5',
            )}
          >
            {content}
          </p>
          <button ref={buttonRef} onClick={() => setIsExpanded(!isExpanded)} className="text-[#1890FF]">
            {isExpanded ? t('ai.collapse') : t('ai.expand')}
          </button>
        </div>
      )}

      <div className="mt-2 text-[calc(1rem*(11/16))] leading-[calc(1rem*(11/16))] text-[#FFFFFF80] font-[350] flex items-center gap-1.5">
        <span>{t('ai.onchainTextNote')}</span>
      </div>
    </div>
  )
}

export default OnChainTextAnalysis
