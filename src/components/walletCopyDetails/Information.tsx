import { getB<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, getBlockchainLogo2 } from '@/utils/helpers.ts'
import { IconChevronRight, IconEdit } from '@components/icon'
import { cn } from '@/lib/utils.ts'
import { Button } from '@components/ui/button.tsx'
import { useTranslation } from 'react-i18next'
import ModifyWalletName from './ModifyWalletName'
import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { APP_PATH } from '@/lib/constant'
import { CopyButton } from '../common/copy-button'
import { useMutation } from '@apollo/client'
import { tradingClient } from '@/lib/gql/apollo-client'
import { updateCopyTradeConfig } from '@/services/copytrade.service'
import { toast } from 'sonner'
import { ChainIds } from '@/types/enums'
import { listCoinHelper } from '@/utils/list-coin-helper'
import WalletTypeBadge, { WalletType } from '../common/WalletTypeBadge'
import IconBtn from '../common/IconBtn'

const mockData = {
  token: {
    chainId: 501424,
    token: '******************************************',
  },
  walletName: 'Test Wallet',
  tags: ['new', 'kol', 'smart-money', 'sniper', 'pump'],
}

const classNames: Record<string, string> = {
  new: 'text-[#20D3EE] border-[0.5px] border-[#20D3EE]',
  kol: 'text-[#FACC14] border-[0.5px] border-[#FACC14]',
  'smart-money': 'text-[#00FFB4] border-[0.5px] border-[#00FFB4]',
  sniper: 'text-[#F471B5] border-[0.5px] border-[#F471B5]',
}

const labels: Record<string, string> = {
  new: '新',
  kol: 'K',
  'smart-money': '智',
  sniper: '狙',
}

function renderTag(tag: string) {
  if (tag === 'pump') {
    return (
      <div key={tag} className="rounded-xs p-0.5 h-3 min-w-3">
        <img src="/images/icons/icon-pump.webp" alt="" className="w-full h-full" />
      </div>
    )
  }
  return (
    <div key={tag} className={cn('rounded-xs p-0.5 size-3 flex justify-center items-center', classNames[tag])}>
      <span className="text-[0.5rem] leading-[0.5rem] text-center">{labels[tag]}</span>
    </div>
  )
}

type IProps = {
  tradeConfig: Record<string, any>
  address?: string
}

export default function Information(props: IProps) {
  const { tradeConfig, address = '' } = props
  const { status = 'Active', leaderTags = [], userAddress, leaderNickname, leaderAddress } = tradeConfig

  const { t } = useTranslation()
  const navigate = useNavigate()
  //TODO: fix this, hardcode for now
  const chainLogo = getBlockchainLogo2(ChainIds.Solana)
  const tokenLogo = getBlockChainLogo(ChainIds.Solana, userAddress)
  const [openEditTokenName, setOpenEditTokenName] = useState(false)
  const [displayWalletName, setDisplayWalletName] = useState<string>(leaderNickname || leaderAddress)
  const [mutate] = useMutation(updateCopyTradeConfig, {
    client: tradingClient,
  })

  const onEditParamsSetting = () => {
    navigate(APP_PATH.COPY_TRADING_WALLET_SETTINGS + `/${address}`)
  }

  const handleOnChangeName = async (name: string) => {
    try {
      const response = await mutate({
        variables: {
          input: {
            id: address || '',
            leaderNickname: name,
          },
        },
      })

      toast.success(t('walletCopy.toast.modifyNameSuccess'))
      setDisplayWalletName(name || leaderAddress)
      return response.data
    } catch (err) {
      toast.error(t('walletCopy.toast.modifyNameError'));
      throw err
    }
  }

  return (
    <div className="flex items-center py-3">
      <div>
        <div className="relative size-9">
          <img
            className="size-9 rounded-sm"
            src={tokenLogo}
            alt=""
            onError={(e) => {
              e.currentTarget.src = '/images/logo-pair-fallback.webp'
              e.currentTarget.onerror = null
            }}
          />
          <img className="size-3.5 rounded-full absolute right-0 bottom-0" src={chainLogo} alt="" />
        </div>
      </div>
      <div className="flex-1 ml-2">
        <div className="text-[calc(13rem/16)] app-font-medium leading-[calc(13rem/16)] text-white mb-2 flex items-center gap-1">
          <span>{displayWalletName.length <= 15 ? displayWalletName : listCoinHelper.formatWalletNameCustom(displayWalletName)}</span>
          {/* <IconChevronRight className="size-3 text-[#B9B9B9]" /> */}
          <IconBtn 
            icon={<IconChevronRight className="size-3 text-[#B9B9B9]" />}
            onClick={() => navigate(`${APP_PATH.MEME_ASSETS_WALLET}/${leaderAddress}`)}
          />
        </div>
        <div className="flex gap-2">
          <ModifyWalletName
            walletName={leaderNickname}
            walletAddress={leaderAddress}
            disabled={status != 'Active'}
            open={openEditTokenName}
            toggle={() => setOpenEditTokenName(!openEditTokenName)}
            onChangeName={handleOnChangeName}
          />
          <CopyButton
            icon="/images/tokenDetail/icon-copy.webp"
            className="w-[10px] min-w-[10px] h-[10px]"
            text={leaderAddress}
          />
          {/* <div className="flex gap-1">{leaderTags.map((tag) => renderTag(tag))}</div> */}
          <div className="flex items-center gap-1">
            <div className="flex gap-[calc(1rem*(4/16))]">
              {leaderTags?.map((type: WalletType) => <WalletTypeBadge type={type} />)}{' '}
            </div>
            {/* <img src="/images/icons/pump-icon.svg" className="w-[14px] h-[14px]" alt="" /> */}
          </div>
        </div>
      </div>

      <div className="flex">
        {/* <Button
          variant="secondary"
          className="flex items-center gap-1 bg-[#ECECED14] text-[#FFFFFFB2] px-2.5 text-[calc(11rem/16)] rounded-full h-7"
          onClick={onEditParamsSetting}
        >
          <IconWallet className="size-3" />
          <span>{t('walletCopy.walletFollow')}</span>
        </Button> */}
        <Button
          variant="secondary"
          className="flex items-center gap-1 bg-[#ECECED14] text-[#FFFFFFB2] px-2.5 text-[calc(11rem/16)] rounded-full h-7"
          onClick={onEditParamsSetting}
        >
          <IconEdit className="size-3" />
          <span>{t('walletCopy.modifyParameters')}</span>
        </Button>
      </div>
    </div>
  )
}
