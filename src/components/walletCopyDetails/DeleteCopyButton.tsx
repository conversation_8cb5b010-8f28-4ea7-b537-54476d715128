import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { IconTrash } from '../icon'
import { XModal } from '../ui'
import { useMutation } from '@apollo/client'
import { updateCopyTradeConfigStatus } from '@/services/copytrade.service'
import { tradingClient } from '@/lib/gql/apollo-client'
import { toast } from 'sonner'
import { ConfigStatus } from '@/@generated/gql/graphql-trading'

interface DeleteCopyButtonProps {
  id: string
}

const DeleteCopyButton = ({ id }: DeleteCopyButtonProps) => {
  const [showModal, setShowModal] = useState(false)
  const { t } = useTranslation()
  const [mutate] = useMutation(updateCopyTradeConfigStatus, {
    client: tradingClient
  })

  async function handleConfirm() {
    try {
      await mutate({
        variables: {
          input: {
            id,
            status: ConfigStatus.Canceled,
          },
        },
      });
      toast.success(t('walletCopy.cancelCopy'), { position: 'bottom-center' })
    }
    catch (error) {
      toast.error(t('walletCopy.errorUpdatingStatus'), { position: 'bottom-center' })
    }
    finally {
      setShowModal(false)
    }
  }
  return (
    <>
      <button className="text-[#B9B9B9]" onClick={() => setShowModal(true)}>
        <span className='text-sm text-white'>{t('listCoin.copyTrade.copyTradeDelete')}</span>
      </button>
      <XModal.Confirmation
        showModal={showModal}
        onConfirm={handleConfirm}
        setShowModal={setShowModal}
        title={t('walletCopy.confirmDelete')}
        description={`${id}${t('walletCopy.deleteMessage')}`}
      />
    </>
  )
}

export default DeleteCopyButton
