import { cn } from '@/lib/utils.ts'
import { UITab } from '@/types/uiTabs.ts'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import TabTransfers from './TabTransfers'

type IProps = {
  id: string
}

function Tabs(props: { activeTab?: string; onTabChange?: (tab: string) => any; tabs: UITab[] }) {
  const { activeTab, onTabChange, tabs } = props
  return (
    <div className="w-[240px] h-8 flex bg-[#ECECED14] rounded-[8px] relative p-0.5">
      {tabs.map((tab: UITab) => (
        <button
          key={tab.value}
          className={cn(
            'flex-1 h-full flex justify-center items-center text-[calc(14rem/16)] leading-[calc(14rem/16)] z-10',
            activeTab === tab.value ? 'text-[#FFFFFF]' : 'text-[#FFFFFFB2]',
          )}
          onClick={() => onTabChange?.(tab.value)}
        >
          {tab.label}
        </button>
      ))}
      <div
        className={cn(
          'absolute w-1/2 left-0 top-0 bottom-0 p-0.5 z-0 transition duration-300',
          activeTab === 'success' ? 'translate-x-0' : 'translate-x-full',
        )}
      >
        <div
          className="bg-[#141414] w-full h-full rounded-[6px] border"
          style={{
            borderImageSource:
              'linear-gradient(37.15deg, rgba(225, 73, 248, 0.1) 13.23%, rgba(153, 69, 255, 0.1) 37.52%, rgba(0, 243, 171, 0.1) 93.06%)',
          }}
        />
      </div>
    </div>
  )
}

export default function OrderTrackingList(props: IProps) {
  const { id } = props
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState<'success' | 'failed'>('success')

  const tabs: UITab[] = [
    {
      value: 'success',
      label: t('walletCopy.successList'),
    },
    {
      value: 'failed',
      label: t('walletCopy.failedList'),
    },
  ]
  /**
   * 
   * {
    recentFollowUp: '1m',
    type: 'buy',
    asset: 'Trump',
    profit: null,
    volume: 10659.65,
    soldPrice: 0.00170,
    amount: 10650,
    copyType: 'maxFollowBuy',
    parameters: {
      tp: '10%',
      sl: '50%',
    },
    hash: 'sdeAWQ'
  }
   */

  return (
    <div>
      <div className="mb-3 flex items-center justify-between">
        <Tabs activeTab={activeTab} onTabChange={setActiveTab} tabs={tabs} />
        {/* <Button
          variant="ghost"
          className="p-[5px] bg-[#232329] rounded-[200px] gap-[6px] h-[26px] focus-visible:shadow-[none]"
        >
          <img src="/images/icons/sol-rounded-icon.svg" alt="" className="w-4 h-4" />
          <span className="block text-xs text-[#ffffff] leading-[1] tracking-[0px] font-normal">SOL</span>
          <img src="/images/icons/refund-icon.svg" alt="" className="w-4 h-4" />
        </Button> */}
      </div>
      <TabTransfers status={activeTab} id={id} />
      {/* {activeTab === 'success' ? <TabSuccessList data={listOrders} /> : <TabFailedList data={listOrders} />} */}
    </div>
  )
}
