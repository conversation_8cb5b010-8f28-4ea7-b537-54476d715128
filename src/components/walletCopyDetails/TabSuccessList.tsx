import { ChainIds } from "@/types/enums";
import { getAllValuesOfKey } from "@/utils/array";
import { formatSmallLongNumber, getLinkExplorer } from "@/utils/helpers.ts";
import { getTimeAgo } from "@/utils/time";
import AssetFilterHead from "@components/walletCopyDetails/AssetFilterHead.tsx";
import CopyTypeCell from "@components/walletCopyDetails/CopyTypeCell.tsx";
import ParametersCell from "@components/walletCopyDetails/ParametersCell.tsx";
import ProfitCell from "@components/walletCopyDetails/ProfitCell.tsx";
import RecentFollowUpTableHead from "@components/walletCopyDetails/RecentFollowUpTableHead.tsx";
import TranslationHead from "@components/walletCopyDetails/TranslationHead.tsx";
import TypeFilterHead from "@components/walletCopyDetails/TypeFilterHead.tsx";
import VolumeFilterHead from "@components/walletCopyDetails/VolumeFilterHead.tsx";
import { DataTable } from "@pages/home/<USER>";
import { ColumnDef } from "@tanstack/react-table";
import { get } from "lodash-es";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";


// const data = [
//   {
//     recentFollowUp: '1m',
//     type: 'buy',
//     asset: 'Trump',
//     profit: null,
//     volume: 10659.65,
//     soldPrice: 0.00170,
//     amount: 10650,
//     copyType: 'maxFollowBuy',
//     parameters: {
//       tp: '10%',
//       sl: '50%',
//     },
//     hash: 'sdeAWQ'
//   },
//   {
//     recentFollowUp: '2m',
//     type: 'sell',
//     asset: 'Trump',
//     profit: 1000,
//     volume: 10659.65,
//     soldPrice: 0.00007,
//     amount: 10650,
//     copyType: 'fixedBuy',
//     parameters: {
//       tp: '10%',
//       sl: '50%',
//     },
//     hash: 'sdeAWQ'
//   },
//   {
//     recentFollowUp: '1m',
//     type: 'buy',
//     asset: 'Trump',
//     profit: null,
//     volume: 10659.65,
//     soldPrice: 0.00170,
//     amount: 10650,
//     copyType: 'autoFollowSell',
//     parameters: {
//       tp: '10%',
//       sl: '50%',
//     },
//     hash: 'sdeAWQ'
//   },
//   {
//     recentFollowUp: '2m',
//     type: 'buy',
//     asset: 'Trump',
//     profit: null,
//     volume: 10659.65,
//     soldPrice: 0.00170,
//     amount: 10650,
//     copyType: 'customSingle',
//     parameters: {
//       tp: '10%',
//       sl: '50%',
//     },
//     hash: 'sdeAWQ'
//   },
//   {
//     recentFollowUp: '3m',
//     type: 'buy',
//     asset: 'Trump',
//     profit: null,
//     volume: 10659.65,
//     soldPrice: 0.00170,
//     amount: 10650,
//     copyType: 'customBatch',
//     parameters: {
//       tp: '10%',
//       sl: '50%',
//     },
//     hash: 'sdeAWQ'
//   }
// ]

type IProps = {
  data: {
    recentFollowUp: string
    type: string
    asset: string
    profit: number | null
    volume: number
    soldPrice: number
    amount: number
    copyType: string
    parameters: {
      tp: string
      sl: string
    }
    hash: string
  }[]
}


export default function TabSuccessList(props: IProps) {
  const { data } = props
  const listCoints = useMemo(() => {
    return getAllValuesOfKey(data, 'asset')
  }, [data]);

  // Cache columns definition to avoid unnecessary re-renders
  const columns = useMemo<ColumnDef<any, any>[]>(() => [
    {
      accessorKey: 'recentFollowUp',
      header: RecentFollowUpTableHead,
      sortingFn: (rowA, rowB, columnId) => {
        // Your custom sorting logic here
        const valueA = rowA.getValue(columnId);
        const valueB = rowB.getValue(columnId);
        // For timestamp comparison:
        return new Date(valueA as string).getTime() - new Date(valueB as string).getTime();
      },
      cell: (props) => {
        const value = props.getValue()
        return (
          <span className="text-[calc(13rem/16)]">
            {getTimeAgo(value)}
          </span>
        )
      }
    },
    {
      accessorKey: 'type',
      enableColumnFilter: true,
      header: (props) => <TypeFilterHead key={'type'} {...props} />,
      filterFn: (row, columnId, filterValue) => String(row.getValue(columnId)).toLocaleLowerCase() === filterValue || filterValue === 'all',
      cell: (props) => {
        const { t } = useTranslation()
        const isBuy = `${props.getValue()}`.toLocaleLowerCase() === 'buy';
        return (
          <ProfitCell
            state={isBuy ? 'profit' : 'loss'}
            value={isBuy ? t('walletCopy.buy') : t('walletCopy.sell')}
          />
        )
      },
    },
    {
      accessorKey: 'asset',
      header: (props) => <AssetFilterHead list={listCoints} key={'asset'} {...props} />,
      filterFn: (row, columnId, filterValue) => {
        const value = row.getValue(columnId) as string
        // String(row.getValue(columnId)).toLocaleLowerCase() === filterValue || filterValue === 'all'
        return value.toLocaleLowerCase() === filterValue.toLocaleLowerCase() || filterValue === 'all'
      }
    },
    {
      accessorKey: 'profit',
      header: () => <TranslationHead key={'profit'} tKey="profit" />,
      cell: (props) => {
        const isBuy = props.row.original.type.toLocaleLowerCase() === 'buy';
        const value = props.getValue()
        const state = isBuy ? 'profit' : 'loss'
        return (
          <ProfitCell
            state={state}
            value={value}
          />
        )
      }
    },
    {
      accessorKey: 'amount',
      header: () => <TranslationHead key={'amount'} tKey="amount" />,
      cell: (props) => (
        <span className="text-[calc(13rem/16)]">
          {formatSmallLongNumber(props.getValue())}
        </span>
      )
    },
    {
      accessorKey: 'soldPrice',
      header: () => <TranslationHead key={'soldPrice'} tKey="price" />,
      cell: (props) => {
        const { original } = props.row;
        const isBuy = original.type.toLocaleLowerCase() === 'buy';
        return (
          <ProfitCell
            state={isBuy ? 'profit' : 'loss'}
            value={isBuy ? '--' : get(original, 'closePriceUsd', 0)}
          />
        )
      }
    },
    {
      accessorKey: 'volume',
      enableColumnFilter: true,
      header: (props) => <VolumeFilterHead key={'volume'} {...props} />,
      filterFn: (row, columnId, filterValue) => {
        const { minVolume, maxVolume } = filterValue
        const value = row.getValue(columnId) as number
        return (minVolume === undefined || value >= minVolume) && (maxVolume === undefined || value <= maxVolume)
      },
      cell: (props) => (
        <ProfitCell
          state={props.getValue() > 0 ? 'profit' : 'loss'}
          // value={formatSmallLongNumber(props.getValue())}
          value={props.getValue()}
        />
      )
    },
    {
      accessorKey: 'copyType',
      header: () => <TranslationHead key={'copyType'} tKey="copyType" />,
      cell: (props) => <CopyTypeCell {...props} />
    },
    {
      accessorKey: 'parameters',
      header: () => <TranslationHead key={'parameters'} tKey="sellParameters" />,
      cell: (props) => (
        <ParametersCell
          tp={props.getValue().tp}
          sl={props.getValue().sl}
          type={props.row.original.type}
          row={props.row.original}
        />
      )
    },
    {
      accessorKey: 'hash',
      header: () => <TranslationHead tKey="transactionHash" />,
      cell: (props) => <a href={getLinkExplorer(ChainIds.Solana, props.getValue())} target="_blank">{props.getValue().slice(0, 6)}</a>
    }
  ], []);
  return (
    <div className="break-keep">
      <DataTable
        columns={columns}
        data={data}
        containerClassName="border-none overflow-x-auto no-scrollbar"
        tableHeadClassName="text-[calc(11rem/16)] leading-[0.75rem] text-[#FFFFFF80] cursor-pointer"
        tableCellClassName="text-[calc(13rem/16)] leading-[0.75rem] break-keep"
      />
    </div>
  )
}