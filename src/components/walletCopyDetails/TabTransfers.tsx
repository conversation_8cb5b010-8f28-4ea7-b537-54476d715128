import { formatMarketValue } from "@/lib/format";
import { tradingClient } from "@/lib/gql/apollo-client";
import { fShortenNumber } from "@/lib/number";
import { cn } from "@/lib/utils";
import { getCopyTradeOrders } from "@/services/copytrade.service";
import { ChainIds } from "@/types/enums";
import { formatNumber, formatPrice, formatSmallLongNumber, getLinkExplorer } from "@/utils/helpers.ts";
import { getTimeAgo } from "@/utils/time";
import { useQuery } from "@apollo/client";
import AssetFilterHead from "@components/walletCopyDetails/AssetFilterHead.tsx";
import CopyTypeCell from "@components/walletCopyDetails/CopyTypeCell.tsx";
import ParametersCell from "@components/walletCopyDetails/ParametersCell.tsx";
import ProfitCell from "@components/walletCopyDetails/ProfitCell.tsx";
import RecentFollowUpTableHead from "@components/walletCopyDetails/RecentFollowUpTableHead.tsx";
import TranslationHead from "@components/walletCopyDetails/TranslationHead.tsx";
import TypeFilterHead from "@components/walletCopyDetails/TypeFilterHead.tsx";
import VolumeFilterHead from "@components/walletCopyDetails/VolumeFilterHead.tsx";
import { ColumnDef } from "@tanstack/react-table";
import { get } from "lodash-es";
import { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { DataTableInfiniteScroll } from "../ui/XTableInfiniteScroll";
import { SkeletonList } from "../ui/skeleton";
import { formatPercent } from "@/utils/numbers";

type IProps = {
  status: 'success' | 'failed'
  id: string
  // data: {
  //   recentFollowUp: string
  //   type: string
  //   asset: string
  //   profit: number | null
  //   volume: number
  //   soldPrice: number
  //   amount: number
  //   copyType: string
  //   parameters: {
  //     tp: string
  //     sl: string
  //   }
  //   hash: string
  // }[]
}

type OrderItem = {
  recentFollowUp: string;
  type: string;
  asset: string;
  profit: string | number | null;
  volume: number;
  soldPrice: string | number;
  amount: string | number;
  copyType: string;
  parameters: {
    tp: string;
    sl: string;
  };
  pnl: any;
  closePriceUsd: string | number;
  hash: string;
  copyConfig: any;
  failedReason: string;
};

const pageSize = 20;

export default function TabTransfers(props: IProps) {
  const { status = 'success', id } = props
  const { t } = useTranslation()
  const [listOrders, setListOrders] = useState<OrderItem[]>([]);
  const [listCoints, setListCoints] = useState<string[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const { data, loading } = useQuery(getCopyTradeOrders, {
    variables: {
      input: {
        copyTradeConfigId: id,
        status: status === 'success' ? 'Completed' : 'Canceled',
        chainId: ChainIds.Solana,
        pageSize,
        page,
      }
    },
    client: tradingClient,
    onCompleted: (res) => {
      const _getCopyTradeOrders = get(res, 'copyTradeOrders', []);
      if (_getCopyTradeOrders.length < pageSize) setHasMore(false);
      if (page === 1) {
        setListOrders(_getCopyTradeOrders.map(mapOrderItem));
      } else {
        setListOrders(prev => {
          // If no new orders, return previous state
          if (_getCopyTradeOrders.length) {
            const newOrders = _getCopyTradeOrders.map(mapOrderItem);
            return [...prev, ...newOrders];
          }
          return prev;
        });
      }
      setListCoints(_getCopyTradeOrders.map((item: any) => item.baseSymbol));
    }
  })

  function mapOrderItem(item: any): OrderItem {
    const isBuy = item.transactionType.toLocaleLowerCase() === 'buy';
    return {
      recentFollowUp: item.createdAt,
      type: item.transactionType,
      asset: item.baseSymbol,
      profit: isBuy ? '--' : item.pnl,
      volume: parseFloat(item.closePriceUsd) * parseFloat(item.baseAmount),
      soldPrice: item.closePriceUsd,
      amount: item.baseAmount,
      copyType: item.transactionType,
      parameters: {
        tp: get(item.copyConfig, 'tp', 0),
        sl: get(item.copyConfig, 'sl', 0)
      },
      pnl: item.pnl,
      closePriceUsd: item.closePriceUsd,
      hash: item.txid,
      copyConfig: item.copyConfig,
      failedReason: t(`orderForm.status.${item.submitCode}`),
    };
  }

  const handleBottomReached = () => {
    if (!loading && hasMore) {
      setPage(p => p + 1);
    }
  };

  // useEffect(() => {
  //   const _result: OrderItem[] = [];
  //   const _listCoints: string[] = [];
  //   if (data) {
  //     const _getCopyTradeOrders = get(data, 'copyTradeOrders', []);
  //     if (_getCopyTradeOrders.length > 0) {
  //       for (let i = 0; i < _getCopyTradeOrders.length; i++) {
  //         const item = _getCopyTradeOrders[i];
  //         const isBuy = item.transactionType.toLocaleLowerCase() === 'buy';
  //         const _item: OrderItem = {
  //           recentFollowUp: item.createdAt,
  //           type: item.transactionType,
  //           asset: item.baseSymbol,
  //           profit: isBuy ? '--' : item.pnl,
  //           volume: parseFloat(item.closePriceUsd) * parseFloat(item.baseAmount),
  //           soldPrice: item.closePriceUsd,
  //           amount: item.baseAmount,
  //           copyType: item.transactionType,
  //           parameters: {
  //             tp: formatNumber(item.tp) + '%',
  //             sl: formatNumber(item.sl) + '%'
  //           },
  //           pnl: item.pnl,
  //           closePriceUsd: item.closePriceUsd,
  //           hash: item.txid,
  //           copyConfig: item.copyConfig,
  //           failedReason: t(`orderForm.status.${item.submitCode}`),
  //         };
  //         _result.push(_item);
  //         _listCoints.push(item.baseSymbol);
  //       }
  //       setListOrders(_result);
  //     } else {
  //       setListOrders([]);
  //     }
  //   } else {
  //     setListOrders([]);
  //   }
  //   setListCoints(_listCoints);
  // }, [data]);

  // Cache columns definition to avoid unnecessary re-renders
  const columns = useMemo<ColumnDef<any, any>[]>(() =>
    status === 'success' ?
      [{
        accessorKey: 'recentFollowUp',
        header: RecentFollowUpTableHead,
        sortingFn: (rowA, rowB, columnId) => {
          // Your custom sorting logic here
          const valueA = rowA.getValue(columnId);
          const valueB = rowB.getValue(columnId);
          // For timestamp comparison:
          return new Date(valueA as string).getTime() - new Date(valueB as string).getTime();
        },
        cell: (props) => {
          const value = props.getValue()
          return (
            <span className="text-[calc(13rem/16)]">
              {getTimeAgo(value)}
            </span>
          )
        }
      },
      {
        accessorKey: 'type',
        enableColumnFilter: true,
        header: (props) => <TypeFilterHead key={'type'} {...props} />,
        filterFn: (row, columnId, filterValue) => String(row.getValue(columnId)).toLocaleLowerCase() === filterValue || filterValue === 'all',
        cell: (props) => {
          const { t } = useTranslation()
          const isBuy = `${props.getValue()}`.toLocaleLowerCase() === 'buy';
          return (
            <ProfitCell
              state={isBuy ? 'profit' : 'loss'}
              value={isBuy ? t('walletCopy.buy') : t('walletCopy.sell')}
            />
          )
        },
      },
      {
        accessorKey: 'asset',
        header: (props) => <AssetFilterHead list={listCoints} key={'asset'} {...props} />,
        filterFn: (row, columnId, filterValue) => {
          console.log("🚀 ~ TabSuccessList ~ filterValue:", filterValue)
          const value = row.getValue(columnId) as string
          console.log("🚀 ~ TabSuccessList ~ value123:", value)
          // String(row.getValue(columnId)).toLocaleLowerCase() === filterValue || filterValue === 'all'
          return value.toLocaleLowerCase() === filterValue.toLocaleLowerCase() || filterValue === 'all'
        }
      },
      {
        accessorKey: 'profit',
        header: () => <TranslationHead key={'profit'} tKey="profit" />,
        cell: (props) => {
          const isBuy = props.row.original.type.toLocaleLowerCase() === 'buy';
          const value = formatNumber(Math.abs(props.getValue()));
          const state = isBuy ? 'profit' : 'loss'
          return (
            <ProfitCell
              state={state}
              value={`${props.getValue() == 0 ? ' ' : props.getValue() > 0 ? '+' : '-'}${value.replace('-', '')}`}
            />
          )
        }
      },
      {
        accessorKey: 'amount',
        header: () => <TranslationHead key={'amount'} tKey="amount" />,
        cell: (props) => {
          const isBuy = props.row.original.type.toLocaleLowerCase() === 'buy';
          return (
            <span className={cn('text-[calc(13rem/16)]', isBuy ? 'text-[#00FFB4]' : 'text-[#AB57FF]')}>
              {formatNumber(props.getValue())}
            </span>
          )
        }
      },
      {
        accessorKey: 'soldPrice',
        header: () => <TranslationHead key={'soldPrice'} tKey="price" />,
        cell: (props) => {
          const { original } = props.row;
          const isBuy = original.type.toLocaleLowerCase() === 'buy';
          return (
            <ProfitCell
              state={isBuy ? 'profit' : 'loss'}
              value={isBuy ? '--' : formatPrice(Number(get(original, 'closePriceUsd', 0)))}
            />
          )
        }
      },
      {
        accessorKey: 'volume',
        enableColumnFilter: true,
        header: (props) => <VolumeFilterHead key={'volume'} {...props} />,
        filterFn: (row, columnId, filterValue) => {
          const { minVolume, maxVolume } = filterValue
          const value = row.getValue(columnId) as number
          return (minVolume === undefined || value >= minVolume) && (maxVolume === undefined || value <= maxVolume)
        },
        cell: (props) => (
          <ProfitCell
            // state={props.getValue() > 0 ? 'profit' : 'loss'}
            state={'none'}
            // value={formatSmallLongNumber(props.getValue())}
            value={formatPrice(props.getValue())}
          />
        )
      },
      {
        accessorKey: 'copyType',
        header: () => <TranslationHead key={'copyType'} tKey="copyType" />,
        cell: (props) => <CopyTypeCell {...props} />
      },
      {
        accessorKey: 'parameters',
        header: () => <TranslationHead key={'parameters'} tKey="sellParameters" />,
        cell: (props) => {
          return (
            <ParametersCell
              tp={props.getValue().tp}
              sl={props.getValue().sl}
              type={props.row.original.type}
              row={props.row.original}
            />
          )
        }
      },
      {
        accessorKey: 'hash',
        header: () => <TranslationHead tKey="transactionHash" />,
        cell: (props) => <a href={getLinkExplorer(ChainIds.Solana, props.getValue())} target="_blank">{props.getValue().slice(0, 6)}</a>
      }
      ] : [
        {
          accessorKey: 'recentFollowUp',
          header: RecentFollowUpTableHead,
          sortingFn: (rowA, rowB, columnId) => {
            // Your custom sorting logic here
            const valueA = rowA.getValue(columnId);
            const valueB = rowB.getValue(columnId);
            // For timestamp comparison:
            return new Date(valueA as string).getTime() - new Date(valueB as string).getTime();
          },
          cell: (props) => {
            const value = props.getValue()
            return (
              <span className="text-[calc(13rem/16)]">
                {getTimeAgo(value)}
              </span>
            )
          }
        },
        {
          accessorKey: 'type',
          enableColumnFilter: true,
          header: (props) => <TypeFilterHead {...props} />,
          filterFn: (row, columnId, filterValue) => String(row.getValue(columnId)).toLocaleLowerCase() === filterValue || filterValue === 'all',
          cell: (props) => {
            const { t } = useTranslation()
            const isBuy = `${props.getValue()}`.toLocaleLowerCase() === 'buy';
            return (
              <ProfitCell
                state={isBuy ? 'profit' : 'loss'}
                value={isBuy ? t('walletCopy.buy') : t('walletCopy.sell')}
              />
            )
          },
        },
        {
          accessorKey: 'failedReason',
          accessorFn: (row) => row.failedReason || '--',
          header: () => <TranslationHead tKey="failedReason" />,
        },
        {
          accessorKey: 'asset',
          enableColumnFilter: true,
          header: (props) => <AssetFilterHead list={listCoints} {...props} />,
          filterFn: (row, columnId, filterValue) => row.getValue(columnId) === filterValue || filterValue === 'all'
        },
        {
          accessorKey: 'profit',
          header: () => <TranslationHead tKey="profit" />,
          cell: (props) => {
            const value = props.getValue()
            const displayValue = value === null ? '--' : `${(value > 0 ? '+' : '')}$${formatSmallLongNumber(value)}`
            const state = value === null ? 'none' : (value > 0 ? 'profit' : 'loss')
            return (
              <ProfitCell
                state={state}
                value={displayValue}
              />
            )
          }
        },
        {
          accessorKey: 'volume',
          header: (props) => <VolumeFilterHead {...props} />,
          cell: (props) => (
            <ProfitCell
              state="none"
              value={fShortenNumber(props.getValue())}
            />
          )
        },
        {
          accessorKey: 'soldPrice',
          header: () => <TranslationHead tKey="soldPrice" />,
          cell: (props) => (
            <ProfitCell
              state="none"
              value={formatSmallLongNumber(props.getValue())}
            />
          )
        },
        {
          accessorKey: 'amount',
          header: () => <TranslationHead tKey="amount" />,
          cell: (props) => (
            <span className="text-[calc(13rem/16)]">
              {formatMarketValue(props.getValue())}
            </span>
          )
        },
        {
          accessorKey: 'copyType',
          header: () => <TranslationHead tKey="copyType" />,
          cell: (props) => <CopyTypeCell {...props} />
        },
        {
          accessorKey: 'parameters',
          header: () => <TranslationHead tKey="sellParameters" />,
          cell: (props) => (
            <ParametersCell
              tp={props.getValue().tp}
              sl={props.getValue().sl}
              type={props.row.original.type}
              row={props.row.original}
            />
          )
        },
        {
          accessorKey: 'hash',
          header: () => <TranslationHead tKey="transactionHash" />,
          cell: (props) => <a href={getLinkExplorer(ChainIds.Solana, props.getValue())} target="_blank">{props.getValue().slice(0, 6)}</a>
        }
      ], []);
  return (
    <div className="break-keep">
      <DataTableInfiniteScroll
        columns={columns}
        data={listOrders}
        fetchMore={handleBottomReached}
        hasMore={hasMore}
        tableProps={{
          containerClassName: "border-none overflow-x-auto no-scrollbar max-h-[calc(100vh-200px)]",
          tableHeadClassName: "text-[calc(11rem/16)] leading-[0.75rem] text-[#FFFFFF80] cursor-pointer",
          tableCellClassName: "text-[calc(13rem/16)] leading-[0.75rem] break-keep",
          skeletonComponent: <SkeletonList className="w-[100vw]" count={10} />
        }}
      />
    </div>
  )
}