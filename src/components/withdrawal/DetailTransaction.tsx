import BottomSheet from '@/components/common/BottomSheet'
import { useAppSelector } from '@/redux/store'
import MoneyFormatted from '@components/common/MoneyFormatted.tsx'

type Props = {
  open: boolean
  setOpen: (open: boolean) => void
  transaction: any
}

const DetailTransaction = ({ open, setOpen, transaction }: Props) => {
  const { amount, createdAt, status, toAddress } = transaction || {}
  const price = useAppSelector((state) => state.price.list)
  return (
    <BottomSheet open={open} setOpen={setOpen} title={'Transaction Details'}>
      <div className="text-center text-[26px] text-[#AB57FF] font-semibold">
        - <MoneyFormatted value={amount} unit={'SOL'} />
      </div>
      <div className="text-center ">
        <MoneyFormatted value={amount * price.SOL} />
      </div>
      <div className="mt-5 px-[14px] py-3 bg-[#ECECED14] rounded-[8px]">
        <div className="flex items-center justify-between">
          <span>Date</span>
          <span>{new Date(createdAt).toLocaleString()}</span>
        </div>
        <div className="flex items-center justify-between mt-2">
          <span>Status</span>
          <span>{status}</span>
        </div>
        <div className="flex items-center justify-between mt-2">
          <span>Recipient</span>
          <span>{toAddress?.length > 10 ? `${toAddress.slice(0, 10)}...${toAddress.slice(-5)}` : toAddress}</span>
        </div>
      </div>
    </BottomSheet>
  )
}

export default DetailTransaction
