import Header from './header'
import Footer from '@components/footer'
import { _changeTokenAccount, LOGIN_SUCCESS, LOGOUT } from '@/redux/modules/auth.slice'
import { useEffect } from 'react'
import { useMultiChainWallet } from '@/hooks/useMultiChainWallet'
import { Toaster } from '@/components/ui/sonner'
import { Connector } from '@/lib/mqtt'
import ls from '@/lib/local-storage'
import { useLocation, useNavigate } from 'react-router-dom'
import PriceToUSDSubscription from './mqtt/PriceToUSDSubscription'
import BalanceWalletSubcription from './mqtt/BalanceWalletSubcription'
import ErrorWrapper from './error-wapper'
import { APP_PATH } from '@/lib/constant'
import { cn } from '@/lib/utils'
import OrdersSubscription from '@components/OrdersSubscription.tsx'
import AuthHandlerComponent from './auth/AuthHandlerComponent'
import { RootState, useAppSelector, useAppDispatch } from '@/redux/store'
import { ConnectorDex } from '@/lib/mqtt-dex'
import { userGqlClient } from '@/lib/gql/apollo-client'
import { userSettings } from '@/services/settings.service'
import { updateUserSettings } from '@/redux/modules/userSettings.slice'

export type PageWrapperProps = {
  children?: React.ReactNode
  showHeader?: boolean
  isWebview?: boolean
  isHorizontalFlip?: boolean
  isShowFooter?: boolean
  isDex?: boolean
  showNotifications?: boolean
  isScrollable?: boolean
  footerWithNonePadding?: boolean
  isShowBackgroundImage?: boolean
}

const PageWrapper = ({
  children,
  showHeader = true,
  isWebview = false,
  isHorizontalFlip = false,
  isShowFooter = true,
  isShowBackgroundImage = true,
  isDex = false,
  isScrollable = true,
  footerWithNonePadding = false,
}: PageWrapperProps) => {
  //Tracking
  // usePageTracking()

  const dispatch = useAppDispatch()
  const urlParams = new URLSearchParams(window.location.search)
  const code = urlParams.get('code')
  const userId = urlParams.get('user_id')
  const ref = urlParams.get('r')
  const navigate = useNavigate()
  const location = useLocation()
  const { pathname } = location
  const color = useAppSelector((state: RootState) => state.preference.priceChangeColor)
  const { activeWallet } = useMultiChainWallet({})

  const getUserSettings = async () => {
    try {
      const { data } = await userGqlClient.query({
        query: userSettings,
      })
      const settings = data.userSettings
      if (settings) {
        dispatch(updateUserSettings(settings))
      }
    } catch (error) {
      console.error('Error fetching user settings:', error)
      return null
    }
  }

  useEffect(() => {
    if (code && userId) {
      const code_fr_ls = ls.get('code_telegram')
      if (code_fr_ls !== code) {
        ls.set('code_telegram', code)
        navigate(`${APP_PATH.MEME_TELEGRAM_AUTH}?code=${code}&user_id=${userId}`)
      }
    }
    if (activeWallet && activeWallet.isConnected) {
      getUserSettings()
    }
  }, [])

  useEffect(() => {
    const root = window.document.documentElement
    if (color === 'inverse') {
      // check if the root element has the class 'inverse'
      if (!root.classList.contains('inverse')) {
        root.classList.add('inverse')
      }
    } else {
      // remove the 'inverse' class from the root element
      if (root.classList.contains('inverse')) {
        root.classList.remove('inverse')
      }
    }
  }, [color])

  useEffect(() => {
    if (ref) {
      localStorage.setItem('REFERRER_CODE', ref)
    }
  }, [ref])

  useEffect(() => {
    const mainContent = document.getElementById('main-content')
    if (mainContent) {
      mainContent.scrollTo({ top: 0, behavior: 'smooth' })
    }
  }, [pathname])

  if (isWebview) {
    return <Connector>{children}</Connector>
  }

  return (
    <div
      id="main-content"
      className={cn(
        'relative max-w-[768px] mx-auto max-h-[100dvh]',
        isShowFooter && 'pb-[95px]',
        isScrollable ? 'overflow-y-auto max-h-[100dvh] no-scrollbar' : 'overflow-y-hidden',
        footerWithNonePadding && 'pb-0',
      )}
    >
      <img
        src={pathname.includes('futures') ? '' : '/images/home-bg.webp'}
        className={cn(
          'w-full max-h-[50vh] opacity-[20%] absolute top-0 left-0 z-[-1] pointer-events-none transition-opacity duration-700',
          pathname === APP_PATH.FUTURES_DISCOVER && 'opacity-20 duration-500',
          isHorizontalFlip && 'scale-x-[-1]',
          isShowBackgroundImage ? 'block' : 'hidden',
        )}
        alt=""
      />
      <Connector>
        <ConnectorDex>
          <OrdersSubscription />
          <div className="z-1">
            {showHeader && <Header showNotifications />}
            {children}
            {isShowFooter && <Footer isDex={isDex} />}
          </div>
          <>
            <PriceToUSDSubscription />
            <BalanceWalletSubcription />
          </>
        </ConnectorDex>
      </Connector>
      {/* <BoardCastListener /> */}
      <Toaster />
      <ErrorWrapper />
      <AuthHandlerComponent />
    </div>
  )
}

export function wrapper({ children, ...props }: PageWrapperProps) {
  return <PageWrapper {...props}>{children}</PageWrapper>
}

const AuthHandler = (children: any) => {
  return <>{children}</>
}

export function useAuth(children: any) {
  return <AuthHandler>{children}</AuthHandler>
}

const BoardCastListener = () => {
  const { disconnectActiveWallet } = useMultiChainWallet({})
  useEffect(() => {
    const authChannel = new BroadcastChannel('auth_sync_channel')
    let isCheckLogout = 0
    let isCheckLogin = 0
    const handleMessage = (event: MessageEvent) => {
      const { type, timestamp } = event.data

      if (Date.now() - timestamp < 5000) {
        switch (type) {
          case LOGIN_SUCCESS:
            if (isCheckLogin === 0) {
              isCheckLogin = 1
              isCheckLogout = 0
            }
            break
          case LOGOUT:
            if (isCheckLogout === 0) {
              disconnectActiveWallet()
              isCheckLogin = 0
              isCheckLogout = 1
            }
            break
          default:
            break
        }
      }
    }
    authChannel.addEventListener('message', handleMessage)

    return () => {
      authChannel.removeEventListener('message', handleMessage)
      authChannel.close()
    }
  }, [])

  return null
}
