import { getB<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, getBlockchainLogo2, getLinkExplorer } from '@/utils/helpers.ts'
import { DataTable } from '@pages/home/<USER>'
import { ColumnDef } from '@tanstack/react-table'
import { IconSortDown, IconSortUp } from '@components/icon'
import dayjs from 'dayjs'
import { useTranslation } from 'react-i18next'
import { useNavigate, useParams } from 'react-router-dom'
import { useAppSelector } from '@/redux/store'
import FilterByType from './FilterByType'
import FilterByToken from './FilterByToken'
import LogoWithChain from '@components/common/LogoWithChain.tsx'
import MoneyFormatted from '@components/common/MoneyFormatted.tsx'
import { Order, SortDirection, TransactionType, TransactionToken } from '@/@generated/gql/graphql-trading.ts'
import { ChainIds } from '@/types/enums.ts'
import { useMemo, useState } from 'react'
import { APP_PATH } from '@/lib/constant'
import { getPath } from '@/lib/utils.ts'
import { TYPE_CHAIN } from '@/lib/blockchain'

type TransactionHistoryTableProps = {
  transactions: Order[]
  transactionTokens: TransactionToken[]
  dataUnit: 'USD' | 'SOL' | 'ETH'
  tokenFilter: string | undefined
  sortByCreatedAt: SortDirection
  typeFilter?: TransactionType
  onFilterByTokenChange: (value: string | undefined) => void
  onSortByCreatedAtChange: (value: SortDirection) => void
  onFilterByTypeChange: (value?: TransactionType) => void
  onBottomReached: () => void
  ref?: React.Ref<HTMLDivElement>
}

const TransactionHistoryTable = ({
  transactions,
  transactionTokens,
  dataUnit,
  tokenFilter,
  sortByCreatedAt,
  typeFilter,
  onFilterByTokenChange,
  onSortByCreatedAtChange,
  onFilterByTypeChange,
  onBottomReached,
  ref,
}: TransactionHistoryTableProps) => {
  const { t } = useTranslation()
  const { address } = useParams()
  const navigate = useNavigate()
  const price = useAppSelector((state) => state.price.list)
  const activeChain = useAppSelector((state) => state.wallet.activeChain)

  const priceUnit = useMemo(() => {
    if (activeChain === TYPE_CHAIN.SOLANA) {
      return price.SOL
    }
    if (activeChain === TYPE_CHAIN.ETH) {
      return price.ETH
    }
    return 1
  }, [activeChain, price.SOL, price.ETH])

  const [openModalFilterByToken, setOpenModalFilterByToken] = useState(false)
  const [openModalFilterByType, setOpenModalFilterByType] = useState(false)

  const columns: ColumnDef<Order>[] = [
    {
      accessorKey: 'txHash',
      header: () => (
        <div className="flex items-center gap-[2px] min-w-[140px]">
          <div>{t('history.token')}</div>
          <div
            className="flex items-center cursor-pointer justify-center w-[14px] h-[14px]"
            onClick={() => {
              setOpenModalFilterByToken(true)
            }}
          >
            <img src="/images/icons/icon-filter.svg" className="w-[10px] h-[10px]" alt="" />
          </div>
          <div>/{t('history.time')}</div>
          <div
            className="flex items-center cursor-pointer justify-center"
            onClick={() => {
              if (sortByCreatedAt === SortDirection.Desc) {
                onSortByCreatedAtChange(SortDirection.Asc)
              } else {
                onSortByCreatedAtChange(SortDirection.Desc)
              }
            }}
          >
            <div className="flex flex-col ml-1">
              <IconSortUp currentColor={sortByCreatedAt === 'asc' ? '#FFFFFF' : '#FFFFFF80'} />
              <IconSortDown currentColor={sortByCreatedAt === 'desc' ? '#FFFFFF' : '#FFFFFF80'} />
            </div>
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const baseSymbol = row.original?.baseSymbol
        const baseToken = row.original?.baseAddress
        const chainId = Number(row.original?.chainId) as ChainIds
        const createdAt = row.original?.createdAt
        return (
          <div className="flex items-center gap-[5px]">
            <div className="relative">
              <LogoWithChain
                logo={getBlockChainLogo(chainId, baseToken)}
                logoClassName="w-[30px] h-[30px]"
                chainLogo={getBlockchainLogo2(chainId)}
                name={baseSymbol}
              />
            </div>
            <div>
              <div className="font-[500] text-[13px]">{baseSymbol}</div>
              <div className="flex items-center gap-[5px] text-[rgba(255,255,255,0.7)] font-[400] text-[10px]">
                {dayjs(createdAt).format('YYYY/MM/DD HH:mm:ss')}
              </div>
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: 'type',
      header: () => (
        <div className="flex items-center gap-[2px]">
          <div>{t('history.type')}</div>
          <div
            className="flex items-center cursor-pointer justify-center w-[14px] h-[14px]"
            onClick={() => {
              setOpenModalFilterByType(true)
            }}
          >
            <img src="/images/icons/icon-filter.svg" className="w-[10px] h-[10px]" alt="" />
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const type = row.original?.transactionType
        return (
          <div
            className={`${type === TransactionType.Buy ? 'text-[#00FFB4]' : type === TransactionType.Sell ? 'text-[#AB57FF]' : ''}`}
          >
            {(() => {
              switch (type) {
                case TransactionType.Buy:
                  return t('history.buy')
                case TransactionType.Sell:
                  return t('history.sell')
                // case 'add':
                //   return t('history.addLiquidity')
                // case 'remove':
                //   return t('history.removeLiquidity')
                default:
                  return type
              }
            })()}
          </div>
        )
      },
    },
    {
      header: `${t('history.amount')}`,
      cell: ({ row }) => {
        const quoteAmount = Number(row.original?.quoteAmount) // SOL or ETH amount
        if (dataUnit === 'USD') {
          return <MoneyFormatted value={quoteAmount * Number(priceUnit)} roundType="floor" />
        }
        return <MoneyFormatted value={quoteAmount} unit={dataUnit} roundType="floor" />
      },
    },
    {
      header: `${t('history.pnl')}`,
      cell: ({ row }) => {
        const pnl = Number(row.original?.pnl)
        return (
          <div className={`${pnl > 0 ? 'text-[#00FFB4]' : pnl < 0 ? 'text-[#AB57FF]' : ''}`}>
            {dataUnit === 'USD' ? (
              <>
                {pnl < 0 ? '-' : ''}
                <MoneyFormatted value={Math.abs(pnl)} roundType="floor" />
              </>
            ) : (
              <MoneyFormatted value={pnl / Number(priceUnit)} unit={dataUnit} roundType="floor" />
            )}
          </div>
        )
      },
    },
    {
      header: `${t('history.price')}`,
      cell: ({ row }) => {
        const closePriceQuote = Number(row.original?.closePriceQuote)
        const closePriceUsd = Number(row.original?.closePriceUsd)
        const type = row.original?.transactionType
        return (
          <div
            className={`${type === TransactionType.Buy ? 'text-[#00FFB4]' : type === TransactionType.Sell ? 'text-[#AB57FF]' : ''}`}
          >
            {dataUnit === 'USD' ? (
              <MoneyFormatted value={closePriceUsd} roundType="ceil" />
            ) : (
              <MoneyFormatted value={closePriceQuote} unit={dataUnit} roundType="ceil" />
            )}
          </div>
        )
      },
    },
    {
      header: `${t('history.tradeVolume')}`,
      cell: ({ row }) => {
        const amount = Number(row.original?.baseAmount)
        return <MoneyFormatted value={amount} showUnit={false} roundType="floor" />
      },
    },
    {
      header: `${t('history.gasFee')}`,
      cell: ({ row }) => {
        const gasFee = Number(row.original?.gasFeeAmount)
        return (
          <>
            {dataUnit === 'USD' ? (
              <MoneyFormatted value={gasFee * Number(priceUnit)} roundType="ceil" />
            ) : (
              <MoneyFormatted value={gasFee} unit={dataUnit} roundType="ceil" />
            )}
          </>
        )
      },
    },
    {
      header: `${t('history.slippageLoss')}`,
      cell: ({ row }) => {
        const slippageLoss = Number(row.original?.slippageLossAmount)
        return (
          <>
            {dataUnit === 'USD' ? (
              <MoneyFormatted value={slippageLoss * Number(priceUnit)} roundType="ceil" />
            ) : (
              <MoneyFormatted value={slippageLoss} unit={dataUnit} roundType="ceil" />
            )}
          </>
        )
      },
    },
    {
      header: `${t('history.facilitationPayment')}`,
      cell: ({ row }) => {
        const facilitationPayment = Number(row.original?.antiMevFeeAmount)
        return (
          <>
            {dataUnit === 'USD' ? (
              <MoneyFormatted value={facilitationPayment * Number(priceUnit)} roundType="ceil" />
            ) : (
              <MoneyFormatted value={facilitationPayment} unit={dataUnit} roundType="ceil" />
            )}
          </>
        )
      },
    },
    {
      header: `${t('history.priorityFee')}`,
      cell: ({ row }) => {
        const priorityFee = Number(row.original?.priorityFeeAmount)
        return (
          <>
            {dataUnit === 'USD' ? (
              <MoneyFormatted value={priorityFee * Number(priceUnit)} roundType="ceil" />
            ) : (
              <MoneyFormatted value={priorityFee} unit={dataUnit} roundType="ceil" />
            )}
          </>
        )
      },
    },
    {
      header: `${t('history.pump')}`,
      cell: ({ row }) => {
        const pumpFee = Number(row.original?.pumpFeeAmount)
        return (
          <>
            {dataUnit === 'USD' ? (
              <MoneyFormatted value={pumpFee * Number(priceUnit)} roundType="ceil" />
            ) : (
              <MoneyFormatted value={pumpFee} unit={dataUnit} roundType="ceil" />
            )}
          </>
        )
      },
    },
    {
      header: `${t('history.xbitFee')}`,
      cell: ({ row }) => {
        const xbitFee = Number(row.original?.platformFeeAmount)
        return (
          <>
            {dataUnit === 'USD' ? (
              <MoneyFormatted value={xbitFee * Number(priceUnit)} roundType="ceil" />
            ) : (
              <MoneyFormatted value={xbitFee} unit={dataUnit} roundType="ceil" />
            )}
          </>
        )
      },
    },
    {
      header: `${t('history.txHash')}`,
      cell: ({ row }) => {
        const txid = row.original?.txid
        const chanId = Number(row.original?.chainId) as ChainIds
        return (
          <a
            href={getLinkExplorer(chanId, txid)}
            target="_blank"
            rel="noopener noreferrer"
            className="font-[400] text-[12px] hover:text-[#AB57FF] hover:underline"
          >
            {txid?.length > 10 ? `${txid?.slice(0, 10)}...${txid?.slice(-5)}` : txid}
          </a>
        )
      },
    },
  ]

  return (
    <div className="relative pb-4">
      <FilterByToken
        open={openModalFilterByToken}
        setOpen={setOpenModalFilterByToken}
        transactionTokens={transactionTokens}
        tokenFilter={tokenFilter}
        onFilterByTokenChange={onFilterByTokenChange}
      />
      <FilterByType
        open={openModalFilterByType}
        setOpen={setOpenModalFilterByType}
        typeFilter={typeFilter}
        onFilterByTypeChange={onFilterByTypeChange}
      />
      <DataTable
        columns={columns}
        data={transactions}
        isStickyHeader
        isStickyFirstColumn
        stickyBg="rgb(23,24,27)"
        containerClassName="max-h-[400px]"
        tableClassName=""
        tableHeaderClassName="text-[rgba(255,255,255,0.48) bg-[#27272a]"
        tableHeaderRowClassName="border-b-[0.5px] border-[rgba(35,35,41,1)] text-[11px] text-[rgba(255, 255, 255, 0.48)] sticky top-0 whitespace-nowrap"
        tableHeadClassName="app-font-light px-2 py-1 items-center justify-center"
        tableBodyClassName="max-h-[300px]"
        tableBodyRowClassName="group whitespace-nowrap"
        tableCellClassName="group-hover:!bg-[#27272a] cursor-pointer"
        onRowClick={(rowData) => {
          const baseAddress = rowData?.baseAddress
          const mainContent = document.getElementById('main-content')
          if (mainContent) {
            mainContent.scrollTo({ top: 0, behavior: 'smooth' })
          }
          if (baseAddress && baseAddress !== address) {
            navigate(getPath(APP_PATH.MEME_TOKEN_DETAIL, { address: baseAddress, chain: activeChain }))
          }
        }}
        onBottomReached={onBottomReached}
        ref={ref}
      />
    </div>
  )
}
export default TransactionHistoryTable
