import {
  ChainType,
  OrderSortField,
  SearchOrderInput,
  SortDirection,
  TransactionType,
  TransactionTokenInput,
} from '@/@generated/gql/graphql-trading.ts'
import { useMultiChainWallet } from '@/hooks/useMultiChainWallet'
import { TYPE_CHAIN } from '@/lib/blockchain'
import { tradingClient } from '@/lib/gql/apollo-client'
import { useAppSelector } from '@/redux/store'
import { getHistoryStatistic, getTransactionHistory, getTransactionTokens } from '@/services/order.service'
import { ChainIds } from '@/types/enums'
import { getBlockchainLogo2 } from '@/utils/helpers.ts'
import Container from '@components/common/Container.tsx'
import { useEffect, useState, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router-dom'
import TransactionHistoryTable from './TransactionHistoryTable'
import MoneyFormatted from '@components/common/MoneyFormatted.tsx'
import { ServiceConfig } from '@/lib/gql/service-config'
import { AnimatePresence, motion } from 'framer-motion'

const TransactionHistory = () => {
  const { address } = useParams()
  const { t } = useTranslation()
  const activeChain = useAppSelector((state) => state.wallet.activeChain)
  const price = useAppSelector((state) => state.price.list)
  const { activeWallet } = useMultiChainWallet({})
  const userAddress = activeWallet?.walletId
  const [tokenFilter, setTokenFilter] = useState<string | undefined>(undefined)
  const [sortByCreatedAt, setSortByCreatedAt] = useState<SortDirection>(SortDirection.Desc)
  const [typeFilter, setTypeFilter] = useState<TransactionType>()
  const [totalBuyAmount, setTotalBuyAmount] = useState<number | string>(0)
  const [totalSellAmount, setTotalSellAmount] = useState<number | string>(0)

  const refTable = useRef<HTMLDivElement>(null)

  const [transactions, setTransactions] = useState<any[]>([])
  const [transactionTokens, setTransactionTokens] = useState<any[]>([])
  const [dataUnit, setDataUnit] = useState<'USD' | 'SOL' | 'ETH'>('USD')
  const [offset, setOffset] = useState(0)

  const fetchHistoryStatistic = async () => {
    if (!userAddress || !ServiceConfig.token) return
    const queryInput = {
      baseAddress: address,
      userAddress: userAddress,
    }

    const response = await tradingClient.query({
      query: getHistoryStatistic,
      variables: {
        input: queryInput,
      },
    })
    setTotalBuyAmount(response?.data?.historyStatistic.totalBuyQuote)
    setTotalSellAmount(response?.data?.historyStatistic.totalSellQuote)
  }

  const fetchTransactionHistory = async () => {
    if (!userAddress || !ServiceConfig.token) return
    let chain
    if (activeChain === TYPE_CHAIN.SOLANA) {
      chain = ChainType.Solana
    } else if (activeChain === TYPE_CHAIN.ETH) {
      chain = ChainType.Evm
    }
    const queryInput: SearchOrderInput = {
      chain: chain,
      transactionType: typeFilter,
      baseAddress: tokenFilter,
      userAddress: userAddress,
      sortField: OrderSortField.CreatedAt,
      sortDir: sortByCreatedAt,
      limit: 20,
      offset: offset,
    }
    const response = await tradingClient.query({
      query: getTransactionHistory,
      variables: {
        input: queryInput,
      },
    })
    if (offset > 0) {
      setTransactions((prev) => [...prev, ...response.data.getTransactions])
    } else {
      setTransactions(response.data.getTransactions)
    }
  }

  const fetchTransactionTokens = async () => {
    if (!userAddress || !ServiceConfig.token) return
    const queryInput: TransactionTokenInput = {
      userAddress: userAddress,
    }
    const response = await tradingClient.query({
      query: getTransactionTokens,
      variables: {
        input: queryInput,
      },
    })
    setTransactionTokens(response.data.transactionTokens)
  }

  useEffect(() => {
    if (userAddress) {
      fetchTransactionTokens()
    }
  }, [userAddress])

  useEffect(() => {
    if (tokenFilter === address) {
      fetchHistoryStatistic()
    }
  }, [tokenFilter])

  useEffect(() => {
    if (offset === 0) {
      fetchTransactionHistory()
      if (refTable.current) {
        refTable.current.scrollTo({ top: 0 })
      }
    }
    setOffset(0)
  }, [sortByCreatedAt, tokenFilter, typeFilter])

  useEffect(() => {
    if (offset === 0) {
      if (refTable.current) {
        refTable.current.scrollTo({ top: 0 })
      }
    }
    fetchTransactionHistory()
  }, [userAddress, offset])

  return (
    <Container className="mt-[10px]">
      <div className="flex justify-between items-center">
        <div
          className="flex items-center gap-1 cursor-pointer"
          onClick={() => {
            if (tokenFilter === address) {
              setTokenFilter(undefined)
            } else {
              setTokenFilter(address)
            }
          }}
        >
          <div
            className={`peer h-3.5 w-3.5 shrink-0 rounded-sm ${tokenFilter === address ? 'border-none' : 'border border-[#b8b4ad]'}`}
          >
            <div className={`flex items-center justify-center ${tokenFilter === address ? 'block' : 'hidden'}`}>
              <img src="/images/icons/ic-tick-square.svg" className="w-3.5 h-3.5" alt="" />
            </div>
          </div>
          <span className="block text-[calc(1rem*(11/16))] text-[#FFFFFF99] leading-[1] relative top-[-0.5px]">
            {t('history.showCurrentCoinOnly')}
          </span>
        </div>
        <div
          className="px-[6px] py-1 bg-[#ECECED14] rounded-[200px] flex flex-row items-center gap-1 cursor-pointer hover:bg-[#ECECED1A] transition-all duration-200 ease-in-out"
          onClick={() => {
            if (dataUnit === 'USD') {
              setDataUnit(activeChain === TYPE_CHAIN.SOLANA ? 'SOL' : activeChain === TYPE_CHAIN.ETH ? 'ETH' : 'USD')
            } else {
              setDataUnit('USD')
            }
          }}
        >
          {dataUnit !== 'USD' && (
            <img
              src={getBlockchainLogo2(activeChain === TYPE_CHAIN.SOLANA ? ChainIds.Solana : ChainIds.Ethereum)}
              alt=""
              className="w-[16px] h-[16px]
              rounded-full"
            />
          )}
          <span
            className="text-[12px] font-[400] 
            uppercase 
          "
          >
            {dataUnit}
          </span>
          <img src="/images/icons/fund-icon.svg" alt="" className="w-[16px] h-[16px]" />
        </div>
      </div>
      <AnimatePresence>
        {tokenFilter === address && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
          >
            <div className="mt-[10px] flex items-center justify-between gap-3">
              <div className="p-4 bg-[url('/images/bg-total-buy-amount.png')] bg-cover bg-center border-[0.5px] border-[rgba(35,35,41,1)] rounded-[6px] flex-1 text-center">
                <div className="font-[400] text-[12px]">{t('history.buyAmount')}</div>
                <div className="mt-2 font-[600] text-[12px] text-[#00FFB4]">
                  {dataUnit === 'USD' ? (
                    <MoneyFormatted
                      value={Number(totalBuyAmount) * Number(price[activeChain.toUpperCase()])}
                      showUnit={true}
                      roundType="floor"
                    />
                  ) : (
                    <MoneyFormatted value={Number(totalBuyAmount)} unit={dataUnit} roundType="floor" />
                  )}
                </div>
              </div>
              <div className="p-4 bg-[url('/images/bg-total-sell-amount.png')] bg-cover bg-center border-[0.5px] border-[rgba(35,35,41,1)] rounded-[6px] flex-1 text-center">
                <div className="font-[400] text-[12px]">{t('history.sellAmount')}</div>
                <div className="mt-2 font-[600] text-[12px] text-[#AB57FF]">
                  {dataUnit === 'SOL' ? (
                    <MoneyFormatted
                      value={Number(totalSellAmount) / Number(price.SOL)}
                      unit={dataUnit}
                      roundType="floor"
                    />
                  ) : dataUnit === 'ETH' ? (
                    <MoneyFormatted
                      value={Number(totalSellAmount) / Number(price.ETH)}
                      unit={dataUnit}
                      roundType="floor"
                    />
                  ) : (
                    <MoneyFormatted value={Number(totalSellAmount)} showUnit={true} roundType="floor" />
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      <div className="mt-[10px]">
        <TransactionHistoryTable
          transactions={transactions}
          transactionTokens={transactionTokens}
          dataUnit={dataUnit}
          tokenFilter={tokenFilter}
          sortByCreatedAt={sortByCreatedAt}
          typeFilter={typeFilter}
          onFilterByTokenChange={setTokenFilter}
          onSortByCreatedAtChange={setSortByCreatedAt}
          onFilterByTypeChange={setTypeFilter}
          onBottomReached={() => {
            if (transactions.length < offset + 20) return
            setOffset((prev) => prev + 20)
          }}
          ref={refTable}
        />
      </div>
    </Container>
  )
}

export default TransactionHistory
