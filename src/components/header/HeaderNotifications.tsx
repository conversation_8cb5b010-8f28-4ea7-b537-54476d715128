import { IconNotifications } from '@components/icon/stroke/IconNotifications.tsx'
import { useNavigate } from 'react-router-dom'
import { APP_PATH } from '@/lib/constant.ts'
import { useQuery } from '@tanstack/react-query'
import { getUnreadNotificationCount } from '@services/notifications.service.ts'
import { notificationClient } from '@/lib/gql/apollo-client.ts'
import { ServiceConfig } from '@/lib/gql/service-config.ts'

const useNotificationsCount = () => {
  const { data } = useQuery({
    queryKey: ['notificationsCount'],
    queryFn: async () => {
      const { data } = await notificationClient.mutate({
        mutation: getUnreadNotificationCount,
      })
      return data?.getUnreadNotificationCount ?? 0
    },
    refetchInterval: 60000, // Refetch every minute
    enabled: !!ServiceConfig.token,
  })
  return data
}

export const HeaderNotifications = () => {
  const navigate = useNavigate()
  const unreadCount = useNotificationsCount()
  return (
    <div className="relative pr-1 cursor-pointer" onClick={() => navigate(APP_PATH.MEME_NOTIFICATIONS)}>
      <IconNotifications />
      {!!unreadCount && unreadCount > 0 && (
        <div className="border boder-[#141414] min-w-5 rounded-full text-center text-white bg-[#FF3526] py-0.5 px-1 text-[calc(10rem/16)] leading-[calc(10rem/16)] absolute -top-1 -right-1.5">
          {unreadCount > 99 ? '99+' : unreadCount}
        </div>
      )}
    </div>
  )
}
