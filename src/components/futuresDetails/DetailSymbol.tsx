import { useAppSelector, useAppDispatch } from '@/redux/store'
import { baseCoinSelector, quoteCoinSelector } from '@/redux/modules/futuresCurrentSymbol.slice'
import { useActiveAssetCtx } from '@/hooks/hyperliquid/useActiveAssetCtx'
import {
  selectFuturesTradePreferences,
  futuresTradePreferencesActions,
} from '@/redux/modules/futuresTradePreferences.slice'
import { setSymbolInfo } from '@/redux/modules/futuresCurrentSymbol.slice'
import { cn, showRate } from '@/lib/utils'
import { useEffect, useState } from 'react'
import Container from '@components/common/Container.tsx'
import { Button } from '@/components/ui/button'
import { TokenSearchDrawer, TokenSearchDrawerType } from './tokenSearchDrawer'

const DetailSymbol = () => {
  const dispatch = useAppDispatch()
  const baseCoin = useAppSelector(baseCoinSelector)
  const quoteCoin = useAppSelector(quoteCoinSelector)
  const { price, markPrice, change } = useActiveAssetCtx(baseCoin)
  const { isExpandKline } = useAppSelector(selectFuturesTradePreferences)
  const [openTokenSearch, setOpenTokenSearch] = useState(false)

  useEffect(() => {
    dispatch(setSymbolInfo({ price, markPrice, change }))
  }, [price, markPrice, change, dispatch])

  const handleClickCandle = () => {
    dispatch(
      futuresTradePreferencesActions.updateTradePreferences({
        isExpandKline: !isExpandKline,
      }),
    )
  }

  return (
    <Container className="mb-2.5">
      <div className="flex items-center justify-between">
        <div className="flex items-center cursor-pointer" onClick={() => setOpenTokenSearch(true)}>
          <img src="/images/futuresDetail/symbol-down-arrow.svg" alt="" />
          <div className="flex items-center text-[calc(16rem/16)] leading-[calc(16rem/16)] mr-1 font-bold">
            {`${baseCoin}${quoteCoin}`}
          </div>

          <div
            className={cn(
              'text-[calc(12rem/16)] py-0.5 px-1.5  text-[#141414] font-bold rounded-[4px] leading-[calc(12rem/16)]',
              parseFloat(change) >= 0 ? 'bg-[#00FFB4]' : 'bg-[#AB57FF]',
            )}
          >
            <span>{showRate(change)}</span>
          </div>
        </div>

        <Button
          variant={'ghost'}
          className="p-0 h-[calc(22rem/16)]"
          onClick={() => {
            handleClickCandle()
          }}
        >
          <div className="flex items-center">
            <img
              src={
                isExpandKline
                  ? '/images/futuresDetail/chart-candle-active-icon.svg'
                  : '/images/futuresDetail/chart-candle-icon.svg'
              }
              alt=""
            />

            <span
              className={cn(
                'transition-transform duration-300',
                isExpandKline ? 'rotate-180 text-rise' : 'text-[#B9B9B9]',
              )}
            >
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M10.8175 6.00098H7.851H5.17969C4.72257 6.00098 4.49401 6.7093 4.8178 7.12452L7.28436 10.2875C7.67958 10.7943 8.3224 10.7943 8.71762 10.2875L9.65567 9.08461L11.1842 7.12452C11.5032 6.7093 11.2746 6.00098 10.8175 6.00098Z"
                  fill="currentColor"
                />
              </svg>
            </span>
          </div>
        </Button>
      </div>
      <TokenSearchDrawer
        open={openTokenSearch}
        setOpen={setOpenTokenSearch}
        type={TokenSearchDrawerType.CRYPTO}
        allowShowList
      />
    </Container>
  )
}

export default DetailSymbol
