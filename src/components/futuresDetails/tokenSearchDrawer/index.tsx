import AppDrawer from '@/components/common/AppDrawer'
import MovingLineTabs from '@/components/common/MovingLineTabs'
import { cn } from '@/lib/utils'
import useHandleGetData from '@/pages/futures-market/hooks/useHandleGetData'
import { getFavoriteTokens } from '@/services/tokens.service'
import { useQuery } from '@apollo/client'
import { Dispatch, memo, RefObject, SetStateAction, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import ContractSection from './ContractSection'
import useHandleLogic from './hooks/useHandleLogic'
import OptionalList from './OptionalList'
import { SearchHistory } from './SearchHistory'
import { TokenTrending } from '@/types/token'
import { SearchSidebar } from './SearchSidebar'
import { TokenSearch } from './TokenSearch'
import { ISymbolList } from '@/pages/futures-market/type'
import { ToastProvider } from './CustomToast'
import MemeList from './MemeList'

interface TokenSearchDrawerProps {
  open: boolean
  type: TokenSearchDrawerType
  allowShowList: boolean

  setOpen: Dispatch<SetStateAction<boolean>>
}

export enum TokenSearchDrawerType {
  MEME = 'meme',
  CRYPTO = 'crypto',
}

export const headerTabs = [
  {
    value: '自选',
    label: '自选',
  },
  {
    value: '合约',
    label: '合约',
  },
  {
    value: 'Meme',
    label: 'Meme',
  },
]

export const TokenSearchDrawer = memo((props: TokenSearchDrawerProps) => {
  const { open, setOpen, type, allowShowList } = props

  const { symbolsFavorite, getFavoriteSymbols, isLoadingFavorite } = useHandleGetData({
    condition: 'volume',
    isFavorite: open,
    isDisabledNomalList: true,

  })
  const { data: favoriteTokensData, refetch: refetchFavoriteTokens } = useQuery(getFavoriteTokens, {
    variables: {
      input: {
        page: 1,
        limit: 20,
        chain: 'ALL',
        dex: 'All',
        timeRange: 'h24',
      },
    },
  })

  const favoriteTokens = useMemo(() => {
    return favoriteTokensData?.getFavoriteToken.data || []
  }, [favoriteTokensData])

  const { searchHistory, setSearchHistory, saveToHistory, handleClearHistory } = useHandleLogic()
  const [currentTab, setCurrentTab] = useState<string>(
    type === TokenSearchDrawerType.CRYPTO ? headerTabs[1].value : headerTabs[2].value,
  )
  const [search, setSearch] = useState('')
  const stickyRef = useRef<HTMLDivElement>(null)
  const drawerContentRef = useRef<HTMLDivElement>(null)
  const listRef = useRef<HTMLDivElement>(null)
  const [isShowList, setIsShowList] = useState(allowShowList)

  const handleTabChange = (tab: string) => {
    setCurrentTab(tab)
  }

  const handleRenderTab = useCallback(
    (
      tab: string,
      symbolsFavorite: ISymbolList[],
      favoriteTokens: TokenTrending[],
      type: TokenSearchDrawerType,
      allowShowList?: boolean,
      isLoadingFavorite?: boolean,
    ) => {
      switch (tab) {
        case headerTabs[0].value:
          return (
            <OptionalList
              search={search}
              symbolsFavorite={symbolsFavorite.map((item) => ({
                ...item,
                isFavorite: true,
              }))}
              favoriteTokens={favoriteTokens.map((item) => ({
                ...item,
                isFavorite: true,
              }))}
              type={type}
              allowShowList={allowShowList}
              isLoadingFavorite={isLoadingFavorite}
            />
          )
        case headerTabs[2].value:
          return <MemeList debounceValue={search} allowShowList={allowShowList} favoriteTokens={favoriteTokens} />
        default:
          return <ContractSection search={search} allowShowList={allowShowList} symbolsFavorite={symbolsFavorite} />
      }
    },
    [search, symbolsFavorite, favoriteTokens, type, allowShowList, isLoadingFavorite],
  )

  useEffect(() => {
    setSearchHistory(JSON.parse(localStorage.getItem('searchHistoryv2') || '[]'))
  }, [open])

  useEffect(() => {
    const timer = setTimeout(() => {
      const sticky = stickyRef.current
      const list = listRef.current
      const container = drawerContentRef.current

      if (!sticky || !container || !list) return

      const handleScroll = () => {
        const stickyBottom = sticky.getBoundingClientRect().bottom
        const listTop = list.getBoundingClientRect().top
        const delta = stickyBottom - listTop
        list.style.clipPath = `inset(${delta}px 0px 0px 0px)`
      }

      container.addEventListener('scroll', handleScroll)
      handleScroll()

      return () => {
        container.removeEventListener('scroll', handleScroll)
      }
    }, 0)

    return () => clearTimeout(timer)
  }, [open])

  const [tabs, setTabs] = useState(headerTabs)

  useEffect(() => {
    if (!allowShowList) {
      setTabs(headerTabs.filter((tab) => tab.value !== headerTabs[0].value))
    } else {
      if (symbolsFavorite.length > 0 || favoriteTokens.length > 0) {
        setTabs(headerTabs)
      } else {
        setTabs(headerTabs.filter((tab) => tab.value !== headerTabs[0].value))
      }
    }
  }, [symbolsFavorite, search, allowShowList])

  useEffect(() => {
    if (currentTab === headerTabs[0].value) {
      getFavoriteSymbols()
      refetchFavoriteTokens()
    }
  }, [currentTab])

  useEffect(() => {
    if (search) {
      setIsShowList(true)
    }
  }, [search])

  useEffect(() => {
    if (open) {
      getFavoriteSymbols()
    }
  }, [open])

  return (
    <ToastProvider>
      <AppDrawer
        open={open}
        setOpen={setOpen}
        rightIcon={<div></div>}
        drawerHeaderClassName="py-1.5"
        drawerContentRef={drawerContentRef as RefObject<HTMLDivElement>}
        drawerContentClassName="_hidescrollbar max-w-[768px] px-2.5 h-dvh"
        // drawerClassName="!h-dvh !max-h-dvh rounded-t-none"
        // isShowControlIcon={false}
        drawerContent={
          <div className="relative">
            <TokenSearch
              setSearch={setSearch}
              currentTab={currentTab}
              closeDialog={() => setOpen(false)}
              setIsShowList={setIsShowList}
            />

            <div
              className={`transition-all duration-300 overflow-hidden ${
                !isShowList ? 'opacity-100 max-h-[1000px] translate-y-0' : 'opacity-0 max-h-0 -translate-y-4'
              }`}
            >
              <SearchHistory
                searchHistory={searchHistory}
                handleClearHistory={handleClearHistory}
                saveToHistory={saveToHistory}
              />
              <SearchSidebar />
            </div>

            <div className={cn('mt-2.5', isShowList ? 'block' : 'hidden')}>
              <div className="sticky top-12 z-10" ref={stickyRef}>
                <MovingLineTabs
                  tabs={tabs}
                  defaultTab={currentTab}
                  onTabChange={handleTabChange}
                  containerClassName="after:hidden w-full gradient-border-buttom rounded-t-[8px] p-1 bg-inherit z-1 relative"
                  tabsClassName="w-full"
                  wrapperClassName="z-1"
                  forceUpdate={isShowList}
                />
              </div>
              <div className="w-full" ref={listRef}>
                <div className="z-1 relative">
                  {handleRenderTab(currentTab, symbolsFavorite, favoriteTokens, type, allowShowList, isLoadingFavorite)}
                </div>
              </div>
            </div>
          </div>
        }
      />
    </ToastProvider>
  )
})
