import Text from '@/components/common/Text'
import { IconSortDown, IconSortUp } from '@/components/icon'
import CategoryTabs from '@/pages/futures-market/components/category-tabs'
import useHandleGetData from '@/pages/futures-market/hooks/useHandleGetData'
import { memo, useCallback, useEffect, useState } from 'react'
import ContractList from './ContractList'
import { ISymbolList } from '@/pages/futures-market/type'

const SortHeader = memo(
  ({
    text,
    onSort,
    sortIndicator,
  }: {
    text: string
    onSort: () => void
    sortIndicator: { upColor: string; downColor: string }
  }) => (
    <div className="flex cursor-pointer" onClick={onSort}>
      <Text text={text} fontSize={11} fontWeight="light" color="#FFFFFF80" className="cursor-pointer" />
      <div className="flex flex-col ml-1 cursor-pointer">
        <IconSortUp currentColor={sortIndicator.upColor} />
        <IconSortDown currentColor={sortIndicator.downColor} />
      </div>
    </div>
  ),
)

SortHeader.displayName = 'SortHeader'

const useCategoryManagement = (categoriesData: string[], handleGetSymbolList: (category: string) => void) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('')

  useEffect(() => {
    if (categoriesData?.length > 0 && !selectedCategory) {
      const firstCategory = categoriesData[0]
      setSelectedCategory(firstCategory)
      handleGetSymbolList(firstCategory)
    }
  }, [categoriesData, selectedCategory, handleGetSymbolList])

  const handleCategoryChange = useCallback(
    (categoryValue: string) => {
      setSelectedCategory(categoryValue)
      handleGetSymbolList(categoryValue)
    },
    [handleGetSymbolList],
  )

  return { selectedCategory, handleCategoryChange }
}

const ContractSection = ({
  search,
  allowShowList,
  symbolsFavorite,
}: {
  search: string
  allowShowList?: boolean
  symbolsFavorite: ISymbolList[]
}) => {
  const {
    isLoading,
    symbolList,
    isLoadingCategory,
    categoryList: categoriesData,
    handleGetSymbolList,
  } = useHandleGetData({ condition: 'category', isCategory: true })

  const { selectedCategory, handleCategoryChange } = useCategoryManagement(categoriesData, handleGetSymbolList)
  const [listTokenDex, setListTokenDex] = useState<ISymbolList[]>([])

  useEffect(() => {
    setListTokenDex(
      symbolList.map((item) => {
        const symbolFavorite = symbolsFavorite.find((symbol) => symbol.symbol === item.symbol)
        if (symbolFavorite) {
          return {
            ...item,
            isFavorite: true,
          }
        }
        return {
          ...item,
        }
      }),
    )
  }, [symbolsFavorite, symbolList])

  return (
    <div className="relative">
      {!search && allowShowList && (
        <CategoryTabs
          tabs={categoriesData}
          activeTab={selectedCategory}
          isLoadingCategory={isLoadingCategory}
          onTabChange={handleCategoryChange}
        />
      )}

      <ContractList
        symbolDataInitial={listTokenDex.slice(0, 20)}
        search={search}
        isLoading={isLoading}
        allowShowList={allowShowList}
        favoriteTokens={symbolsFavorite}
      />
    </div>
  )
}

export default memo(ContractSection)
