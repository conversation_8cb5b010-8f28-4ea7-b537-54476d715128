import ChainCurrencyIcon from '@/components/common/ChainCurrencyIcon'
import Text from '@/components/common/Text'
import { CopyButton } from '@/components/common/copy-button'
import { PriceChange } from '@/components/futuresDiscover/table/crypto-table'
import { TableVirtual } from '@/components/futuresDiscover/table/table-virtual'
import { IconSortDown, IconSortUp } from '@/components/icon'
import { formatAddressWallet } from '@/lib/string'
import { useSearchFilter } from '@/pages/futures-market/hooks/useHandleGetData'
import useSortableTable from '@/pages/futures-market/hooks/useSortableTable'
import { formatMoney, formatPercentage, getBlockChainLogo, getBlockchainLogo2 } from '@/utils/helpers'
import { createColumnHelper } from '@tanstack/react-table'
import { Dispatch, memo, SetStateAction, useEffect, useMemo, useState } from 'react'

import { cn } from '@/lib/utils'
import { searchTokens } from '@/services/tokens.service'
import { SearchTokenResponse } from '@/types/responses'
import { TokenTrending } from '@/types/token'
import { useQuery } from '@apollo/client'
import ConfirmCollectTokenMeme from './ConfirmCollectTokenMeme'
import useHandleLogic from './hooks/useHandleLogic'

interface SearchResultsListProps {
  debounceValue: string
  favoriteTokens: TokenTrending[]
  allowShowList?: boolean
  isFavorite?: boolean
}

const useSortHandlers = (handleSort: (field: string) => void, getSortIndicator: (field: string) => any) => {
  const sortHandlers = useMemo(
    () => ({
      symbol: () => handleSort('symbol'),
      marketcap: () => handleSort('marketcap'),
      price24hChange: () => handleSort('price24hChange'),
    }),
    [handleSort],
  )

  const sortIndicators = useMemo(
    () => ({
      symbol: getSortIndicator('symbol'),
      marketcap: getSortIndicator('marketcap'),
      price24hChange: getSortIndicator('price24hChange'),
    }),
    [getSortIndicator],
  )

  return { sortHandlers, sortIndicators }
}

const SortHeader = memo(
  ({
    text,
    onSort,
    sortIndicator,
  }: {
    text: string
    onSort: () => void
    sortIndicator: { upColor: string; downColor: string }
  }) => (
    <div className="flex cursor-pointer" onClick={onSort}>
      <Text text={text} fontSize={11} fontWeight="light" color="#FFFFFF80" className="cursor-pointer" />
      <div className="flex flex-col ml-1 cursor-pointer">
        <IconSortUp currentColor={sortIndicator.upColor} />
        <IconSortDown currentColor={sortIndicator.downColor} />
      </div>
    </div>
  ),
)

SortHeader.displayName = 'SortHeader'

const useTableColumns = (
  sortHandlers: any,
  sortIndicators: any,
  setFavoriteTokensInitial: Dispatch<SetStateAction<TokenTrending[]>>,
  isFavorite?: boolean,
  debounceValue?: string,
) => {
  const columnHelper = createColumnHelper<any>()

  return useMemo(
    () => [
      columnHelper.accessor('symbol', {
        header: () => (
          <div className="flex items-center">
            <SortHeader text="币种" onSort={sortHandlers.symbol} sortIndicator={sortIndicators.symbol} />
          </div>
        ),
        cell: (info) => {
          const { chainId, token, image, symbol } = info.row.original
          const chainLogo = getBlockchainLogo2(chainId)
          const tokenLogo = image ?? getBlockChainLogo(chainId, token)

          return (
            <div className="flex items-center gap-2 flex-3">
              <ConfirmCollectTokenMeme
                token={token}
                defaultCollect={isFavorite ?? false}
                tokenSymbol={symbol}
                triggerClassName="p-0"
                onRemoveSuccess={() => {
                  setFavoriteTokensInitial((prev) => prev.filter((item) => item.symbol !== symbol))
                }}
              />
              <ChainCurrencyIcon
                chainIcon={chainLogo}
                currencyIcon={tokenLogo}
                name={symbol}
                fallbackClassName="bg-secondary"
              />
              <div className="">
                <div className="mb-1 flex items-center gap-1 text-title align-baseline font-bold text-[calc(1rem*(13/16))] leading-[calc(1rem*(13/16))]">
                  <Text
                    text={symbol}
                    fontSize={13}
                    fontWeight="medium"
                    className="leading-[calc(1rem*(13/16))]"
                    highLightText={debounceValue?.split(' ').join('').toUpperCase()}
                  />
                  <img src="/images/icons/icon-pump.webp" alt="" className="w-2.5 h-2.5" />
                </div>
                <div className="flex items-center">
                  <div className="text-[calc(1rem*(10/16))] text-[#FFFFFF99] leading-[calc(1rem*(10/16))] mr-1">
                    {formatAddressWallet(token, 5, 4)}
                  </div>
                  <div className="cursor-copy mr-2">
                    <CopyButton
                      icon="/images/tokenDetail/icon-copy.webp"
                      className="w-[10px] min-w-[10px] h-[10px]"
                      text={token}
                    />
                  </div>
                </div>
              </div>
            </div>
          )
        },
      }),

      columnHelper.accessor('marketcap', {
        header: () => (
          <div className="flex justify-end items-end">
            <SortHeader text="市值" onSort={sortHandlers.marketcap} sortIndicator={sortIndicators.marketcap} />
          </div>
        ),
        cell: (info) => {
          const currentPrice = info.getValue()
          const price = info.row.original.price
          return (
            <div className="flex items-end gap-1 flex-col relative">
              <Text text={formatMoney(price)} fontSize={15} fontWeight="medium" className="lining-nums" />
              <Text
                text={formatMoney(Number(currentPrice))}
                fontSize={11}
                fontWeight="regular"
                color="#FFFFFFB2"
                className="lining-nums"
              />
            </div>
          )
        },
      }),

      columnHelper.accessor('price24hChange', {
        header: () => (
          <div className="flex justify-end">
            <SortHeader
              text="涨跌幅"
              onSort={sortHandlers.price24hChange}
              sortIndicator={sortIndicators.price24hChange}
            />
          </div>
        ),
        cell: (info) => {
          const changePercent = info.getValue()
          return <PriceChange value={formatPercentage(changePercent)} isPositive={Number(changePercent) > 0} />
        },
      }),
    ],
    [columnHelper, sortHandlers, sortIndicators],
  )
}

export const MemeWatchlist = (props: SearchResultsListProps) => {
  const { debounceValue, favoriteTokens, isFavorite } = props
  const { loading } = useQuery<SearchTokenResponse>(searchTokens, {
    variables: {
      input: debounceValue,
    },
    skip: !debounceValue,
  })
  const { handleRowClick } = useHandleLogic()
  const [favoriteTokensInitial, setFavoriteTokensInitial] = useState<TokenTrending[]>(favoriteTokens)

  const { sortedData, handleSort, getSortIndicator } = useSortableTable<any>(favoriteTokensInitial || [])
  const filteredData = useSearchFilter(sortedData || [], debounceValue)
  const { sortHandlers, sortIndicators } = useSortHandlers(handleSort, getSortIndicator)
  const columns = useTableColumns(sortHandlers, sortIndicators, setFavoriteTokensInitial, isFavorite, debounceValue)

  useEffect(() => {
    setFavoriteTokensInitial(favoriteTokens)
  }, [favoriteTokens])

  return (
    <div>
      <div className={cn('relative')}>
        <TableVirtual<any, any>
          columns={columns}
          isLoading={loading}
          data={filteredData}
          onRowClick={handleRowClick}
          isStickyHeader={false}
          containerClassName="!border-none _hidescrollbar !max-h-none"
          tableHeaderClassName="text-[#FFFFFF80] text-[calc(1rem*(12/16))] font-[400]"
          tableHeaderRowClassName="!border-none"
          tableCellClassName="group-hover:!bg-[#27272a] cursor-pointer !border-none !py-2.5 justify-end"
        />
      </div>
    </div>
  )
}
