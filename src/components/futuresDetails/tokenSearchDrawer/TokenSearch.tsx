import { IconHeaderSearch } from '@/components/icon'
import useDebounceValue from '@/hooks/useDebounceValue'
import { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react'

export const TokenSearch = ({
  setSearch,
  closeDialog,
  setIsShowList,
}: {
  setSearch: Dispatch<SetStateAction<string>>
  closeDialog: () => void
  currentTab: string
  setIsShowList: Dispatch<SetStateAction<boolean>>

}) => {
  const ref = useRef<HTMLInputElement>(null)
  const [value, setValue] = useState<string>('')
  const debounceValue = useDebounceValue(value, 700)

  useEffect(() => {
    setSearch(debounceValue)
  }, [debounceValue, setSearch])

  return (
    <div className="flex justify-center -mx-3 px-3 py-1 sticky top-0 z-10 bg-[#232329]">
      <div className="group w-full flex items-center">
        <div
          className={`p-[1px] rounded-full transition-all duration-300 bg-[#FFFFFF14] group-focus-within:bg-[linear-gradient(90deg,#9945FF_20%,#00F3AB_100%)] flex-1 mr-2.5`}
        >
          <div className="bg-[#1C1C1E] rounded-full py-2.5 px-[15px] inline-flex items-center w-full transition-all duration-300">
            <IconHeaderSearch className="mr-1.5 text-white" />
            <input
              ref={ref}
              type="text"
              value={value}
              onChange={(e) => setValue(e.target.value)}
              // onFocus={() => setIsShowList(true)}
              className="w-full h-full bg-transparent outline-none text-[14px] text-white placeholder:text-[#FFFFFF80]"
              placeholder="搜索币种"
            />
            {value.length > 0 && (
              <img
                className="size-[calc(1rem*(14/16))] cursor-pointer"
                src="/images/icons/circle-cancel.svg"
                alt=""
                onClick={() => {
                  setValue('')
                }}
              />
            )}
          </div>
        </div>

        <button
          onMouseDown={(e) => {
            e.preventDefault()
            closeDialog()
            ref.current?.blur()
          }}
          className={`text-[#FFFFFFB2] text-sm font-[350] transition-all duration-300 overflow-hidden opacity-100 w-auto translate-x-0`}
        >
          取消
        </button>
      </div>
    </div>
  )
}
