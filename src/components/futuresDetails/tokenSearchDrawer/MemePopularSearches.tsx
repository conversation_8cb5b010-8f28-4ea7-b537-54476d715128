import { APP_PATH, CHAIN_SYMBOLS } from '@/lib/constant'
import { getPath } from '@/lib/utils'
import { PopularToken } from '@/types/popularToken.ts'
import { GetPopularTokenResponse } from '@/types/responses.ts'
import { useQuery } from '@apollo/client'
import { Avatar, AvatarFallback, AvatarImage } from '@components/ui/avatar.tsx'
import { getPopularTokens } from '@services/tokens.service.ts'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import useHandleLogic from './hooks/useHandleLogic'
import SkeletonList from './SkeletonList'

const MemePopularSearches = () => {
  const navigate = useNavigate()
  const { saveToHistory } = useHandleLogic()
  const [popularSearches, setPopularSearches] = useState<PopularToken[]>([])

  const { data, loading } = useQuery<GetPopularTokenResponse>(getPopularTokens, {
    skip: popularSearches.length > 0,
  })

  useEffect(() => {
    if (popularSearches.length === 0) {
      setPopularSearches(data?.getPopularTokens.slice(0, 8) ?? [])
    }
  }, [data])

  const handlePopularSearchClick = (item: PopularToken) => {
    saveToHistory({ address: item.token, name: item.symbol, chainId: item.chainId, logo: item.logoUrl }, 'meme')
    navigate(getPath(APP_PATH.MEME_TOKEN_DETAIL, { address: item.token, chain: CHAIN_SYMBOLS[item.chainId] }))
  }

  return (
    <div className="flex items-start gap-1.5 overflow-x-auto no-scrollbar pt-4 -mt-1.5">
      {loading ? (
        <SkeletonList />
      ) : (
        popularSearches.map((item) => (
          <div
            key={item.token}
            className="relative px-6 py-2 bg-[#ECECED14] hover:bg-[#FFFFFF30] border border-gradient rounded-[6px] flex flex-col justify-center items-center cursor-pointer"
            onClick={() => handlePopularSearchClick(item)}
          >
            {item.hot && (
              <img src="/images/icons/icon-hot.svg" className="size-4 mr-1 absolute -top-1.5 -right-2" alt="" />
            )}
            <div className="flex items-center gap-1">
              <Avatar className="size-[14px] bg-[#111111]">
                <AvatarFallback className="capitalize text-[calc(9rem/16)] bg-[#111111]">
                  {item.symbol.slice(0, 2).toLowerCase()}
                </AvatarFallback>
                <AvatarImage src={item.logoUrl} />
              </Avatar>
              <div className="text-[calc(14rem/16)] app-font-medium text-[#FFFFFF]">{item.symbol}</div>
            </div>
            <div className="text-[calc(9rem/16)] text-[#FFFFFF80]">Meme</div>
          </div>
        ))
      )}
    </div>
  )
}

export default MemePopularSearches
