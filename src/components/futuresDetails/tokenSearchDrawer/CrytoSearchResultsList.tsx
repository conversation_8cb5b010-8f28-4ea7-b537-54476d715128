import Text from '@/components/common/Text'
import { LeverageBadge, PriceChange } from '@/components/futuresDiscover/table/crypto-table'
import { TableVirtual } from '@/components/futuresDiscover/table/table-virtual'
import { symbolDexClient } from '@/lib/gql/apollo-client'
import { ISymbolList } from '@/pages/futures-market/type'
import { SEARCH_SYMBOL_ENDPOINT } from '@/services/symbol.dex.service'
import { formatMoney, formatPercentage } from '@/utils/helpers'
import { createColumnHelper } from '@tanstack/react-table'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import useHandleLogic from './hooks/useHandleLogic'
import ConfirmCollectToken from './ConfirmCollectToken'

const CrytoSearchResultsList = ({ search, favoriteTokens }: { search: string; favoriteTokens?: ISymbolList[] }) => {
  const columnHelper = createColumnHelper<ISymbolList>()
  const navigate = useNavigate()
  const { saveToHistory } = useHandleLogic()
  const [loading, setLoading] = useState(false)
  const [popularSymbols, setPopularSymbols] = useState<ISymbolList[]>([])

  const columns = useMemo(
    () => [
      columnHelper.accessor('symbol', {
        header: () => <></>,
        cell: (info) => {
          const { symbol, maxLeverage, marketCap, isFavorite } = info.row.original
          return (
            <div className="flex items-center space-x-2">
              <ConfirmCollectToken
                defaultCollect={isFavorite ?? false}
                token={symbol}
                // onRemoveSuccess={() => {
                //   setPopularSymbols((prev) => prev.filter((item) => item.symbol !== symbol))
                // }}
                tokenSymbol={symbol}
                triggerClassName="p-0"
              />
              <div className="flex flex-col gap-1">
                <div className="flex items-end gap-1">
                  <Text
                    text={symbol}
                    fontSize={15}
                    fontWeight="medium"
                    className="leading-[calc(1rem*(15/16))]"
                    highLightText={search.split(' ').join('').toUpperCase()}
                  />
                  <Text
                    text="/"
                    fontSize={9}
                    fontWeight="light"
                    color="#FFFFFF80"
                    className="leading-[calc(1rem*(12/16))]"
                    highLightText={search.split(' ').join('').toUpperCase()}
                  />
                  <Text
                    text="USDC"
                    fontSize={11}
                    fontWeight="light"
                    color="#FFFFFF80"
                    className="leading-[calc(1rem*(11/16))]"
                  />
                  <LeverageBadge value={maxLeverage as unknown as string} />
                </div>
                <div className="flex gap-1 items-end">
                  <div className="text-[calc(1rem*(12/16))] tex-[#FFFFFFB2] lining-nums">{formatMoney(marketCap)}</div>
                </div>
              </div>
            </div>
          )
        },
      }),

      columnHelper.accessor('currentPrice', {
        header: () => <></>,
        cell: (info: any) => {
          const currentPrice = info.getValue()
          const volume = info.row.original.volume
          return (
            <div className="flex items-end gap-1 flex-col relative">
              <Text text={formatMoney(currentPrice)} fontSize={15} fontWeight="medium" className="lining-nums" />
              <Text
                text={formatMoney(volume)}
                fontSize={11}
                fontWeight="regular"
                color="#FFFFFFB2"
                className="lining-nums"
              />
            </div>
          )
        },
      }),

      columnHelper.accessor('changPxPercent', {
        header: () => <></>,
        cell: (info) => {
          const changePercent = info.getValue()
          return <PriceChange value={formatPercentage(changePercent)} isPositive={Number(changePercent) > 0} />
        },
      }),
    ],
    [columnHelper, search],
  )

  const handleSearchResult = async () => {
    try {
      setLoading(true)
      const { data } = await symbolDexClient.query({
        query: SEARCH_SYMBOL_ENDPOINT,
        variables: {
          input: {
            filter: search.split(' ').join('').toUpperCase(),
          },
        },
      })
      setPopularSymbols(
        data?.searchSymbol?.list.map((token: ISymbolList) => {
          const symbolFavorite = favoriteTokens?.find((symbol) => symbol.symbol === token.symbol)
          if (symbolFavorite) {
            return {
              ...token,
              isFavorite: true,
            }
          }
          return {
            ...token,
          }
        }),
      )
    } catch (error) {
      console.error('Error fetching list:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (search) {
      handleSearchResult()
    }
  }, [search])

  const handleRowClick = useCallback(
    (row: any) => {
      saveToHistory({ address: row.symbol, name: row.symbol, logo: row.image, chainId: row.chainId }, 'dex')
      navigate(`/futures/${row.symbol}`)
    },
    [navigate],
  )

  return (
    <div>
      <TableVirtual<ISymbolList, any>
        isLoading={loading}
        columns={columns}
        data={popularSymbols}
        isStickyHeader={false}
        isShowHeader={false}
        containerClassName="!border-none _hidescrollbar"
        tableHeaderClassName="text-[#FFFFFF80] text-[calc(1rem*(12/16))] font-[400]"
        tableHeaderRowClassName="!border-none"
        tableCellClassName="group-hover:!bg-[#27272a] cursor-pointer !border-none !py-2.5 justify-end"
        onRowClick={handleRowClick}
        cusTomMaxHeight="none"
        emptyText="暂无数据"
      />
    </div>
  )
}

export default CrytoSearchResultsList
