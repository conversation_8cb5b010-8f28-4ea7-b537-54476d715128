import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogOverlay,
  DialogTitle,
} from '@/components/ui/dialog'
import { ServiceConfig } from '@/lib/gql/service-config.ts'
import { cn } from '@/lib/utils.ts'
import { useMutation } from '@apollo/client'
import useSignWallet from '@hooks/useSignWallet.ts'
import { addTokenToFavorite, removeTokenFromFavorite } from '@services/tokens.service.ts'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useToast } from './CustomToast'
import { IconSpinner } from '@/components/icon'

const ConfirmCollectTokenMeme = (props: {
  defaultCollect: boolean
  token: string
  showDialog?: boolean
  onRemove?: () => void
  onRemoveSuccess?: () => void
  onRemoveFailed?: () => void
  onAdded?: () => void
  tokenSymbol: string
  triggerClassName?: string
}) => {
  const {
    token,
    defaultCollect,
    onRemove,
    onRemoveSuccess,
    onRemoveFailed,
    showDialog = false,
    onAdded,
    tokenSymbol,
    triggerClassName,
  } = props
  const [open, setOpen] = useState(false)
  const [isCollect, setCollect] = useState<boolean>(defaultCollect)
  const { t } = useTranslation()
  const [addToFavoritesMutation] = useMutation(addTokenToFavorite)
  const [removeFromFavoritesMutation] = useMutation(removeTokenFromFavorite)
  const { handleSignMessage } = useSignWallet({ isAutoConnect: false })
  const { showToast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  const addToFavorites = () => {
    setIsLoading(true)
    setCollect(true)
    addToFavoritesMutation({ variables: { token: token } })
      .then((e) => {
        showToast({
          type: 'success',
          title: t('toast.addFavoriteSuccess'),
          duration: 4000,
        })
        onAdded?.()
      })
      .catch((e) => {
        setCollect(false)
        showToast({
          type: 'error', 
          title: t('toast.addFavoriteFailed'),
          description: e[0].message,
        })
      })
      .finally(() => {
        setIsLoading(false)
      })
  }

  const removeFromFavorites = () => {
    setIsLoading(true)
    setCollect(false)
    setOpen(false)
    onRemove?.()
    removeFromFavoritesMutation({ variables: { token: token } })
      .then(() => {
        showToast({
          type: 'success',
          title: t('toast.removeFavoriteSuccess'),
        })
        onRemoveSuccess?.()
      })
      .catch((e) => {
        showToast({
          type: 'error',
          title: t('toast.removeFavoriteFailed'),
          description: e[0].message,
        })
        setCollect(true)
        onRemoveFailed?.()
      })
      .finally(() => {
        setIsLoading(false)
      })
  }

  const performAddOrRemove = () => {
    if (!isCollect) {
      // add to favorites
      addToFavorites()
    } else if (!showDialog) {
      // not show dialog
      removeFromFavorites()
    } else {
      // show dialog
      setOpen(true)
    }
  }

  const handleCollectChange = (event: React.MouseEvent) => {
    event.stopPropagation()

    if (!ServiceConfig.token) {
      handleSignMessage().then(() => {
        setTimeout(() => {
          if (ServiceConfig.token) {
            // sign successfully, perform add/remove action
            performAddOrRemove()
          } else {
            showToast({
              type: 'error',
              title: t('listCoin.requireLoginToFavorite'),
            })
          }
        }, 500)
      })
      return
    }
    performAddOrRemove()
  }

  useEffect(() => {
    if (!!ServiceConfig.token) {
      setCollect(defaultCollect)
    } else {
      setCollect(false)
    }
  }, [ServiceConfig.token])

  const handleCancelCollect = (event: React.MouseEvent) => {
    event.stopPropagation()
    removeFromFavorites()
  }

  const handleOverlayClick = (event: React.MouseEvent) => {
    event.stopPropagation()
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <div className={cn('p-2 pr-0 cursor-pointer', triggerClassName)}>
        {isLoading ? (
          <div className="flex items-center justify-center">
            <IconSpinner className="size-4.5 animate-spin" />
          </div>
        ) : (
          <img
            onClick={handleCollectChange}
            className="transition-all duration-100 hover:scale-[1.2]"
            src={isCollect ? '/images/icons/star-active-icon.svg' : '/images/icons/star-icon.svg'}
            alt=""
          />
        )}
      </div>
      <DialogOverlay
        onPointerDown={(event) => handleOverlayClick(event)}
        onClick={(event) => handleOverlayClick(event)}
      >
        <DialogContent
          onPointerDown={(event) => event.stopPropagation()}
          onClick={(event) => event.stopPropagation()}
          className="w-[335px] bg-[#232329] rounded-2xl p-5"
        >
          <DialogHeader>
            <DialogTitle className="text-center">
              <p className="text-[18px] py-3">{t('listCoin.removeWatchlist', { coinName: tokenSymbol })}</p>
            </DialogTitle>
            <div className="flex justify-center items-center flex-row gap-2.5 mt-4">
              <Button variant="close" className="flex-1 rounded-[50px]" onClick={() => setOpen(false)}>
                {t('toast.cancel')}
              </Button>
              <Button variant="gradient" className="text-[#261236] flex-1 rounded-[50px]" onClick={handleCancelCollect}>
                {t('toast.confirm')}
              </Button>
            </div>
          </DialogHeader>
          <DialogDescription />
        </DialogContent>
      </DialogOverlay>
    </Dialog>
  )
}

export default ConfirmCollectTokenMeme
