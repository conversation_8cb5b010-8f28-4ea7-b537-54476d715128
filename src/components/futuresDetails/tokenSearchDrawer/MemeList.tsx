import ChainCurrencyIcon from '@/components/common/ChainCurrencyIcon'
import Text from '@/components/common/Text'
import { CopyButton } from '@/components/common/copy-button'
import { PriceChange } from '@/components/futuresDiscover/table/crypto-table'
import { TableVirtual } from '@/components/futuresDiscover/table/table-virtual'
import { IconSortDown, IconSortUp } from '@/components/icon'
import { formatAddressWallet } from '@/lib/string'
import useSortableTable from '@/pages/futures-market/hooks/useSortableTable'
import { useAppSelector } from '@/redux/store'
import { getTrending24hTokens, searchTokens } from '@/services/tokens.service'
import { GetTokenTrendingResponse, SearchTokenResponse } from '@/types/responses'
import { formatMoney, formatPercentage, getBlockChainLogo, getBlockchainLogo2 } from '@/utils/helpers'
import { useQuery } from '@apollo/client'
import { createColumnHelper } from '@tanstack/react-table'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'

import { TokenTrending } from '@/types/token'
import ConfirmCollectTokenMeme from './ConfirmCollectTokenMeme'
import MemeSearchResultsList from './MemeSearchResultsList'
import useHandleLogic from './hooks/useHandleLogic'

interface SearchResultsListProps {
  debounceValue: string
  allowShowList?: boolean
  favoriteTokens: TokenTrending[]
}

const SortHeader = memo(
  ({
    text,
    onSort,
    sortIndicator,
  }: {
    text: string
    onSort: () => void
    sortIndicator: { upColor: string; downColor: string }
  }) => (
    <div className="flex cursor-pointer" onClick={onSort}>
      <Text text={text} fontSize={11} fontWeight="light" color="#FFFFFF80" className="cursor-pointer" />
      <div className="flex flex-col ml-1 cursor-pointer">
        <IconSortUp currentColor={sortIndicator.upColor} />
        <IconSortDown currentColor={sortIndicator.downColor} />
      </div>
    </div>
  ),
)

SortHeader.displayName = 'SortHeader'

// Memoized cell components to prevent unnecessary re-renders
const SymbolCell = memo(({ info, debounceValue }: { info: any; debounceValue?: string }) => {
  const { chainId, token, image, symbol, isFavorite } = info.row.original
  const chainLogo = getBlockchainLogo2(chainId)
  const tokenLogo = image ?? getBlockChainLogo(chainId, token)

  return (
    <div className="flex items-center gap-2 flex-3">
      <ConfirmCollectTokenMeme
        token={token}
        defaultCollect={isFavorite}
        tokenSymbol={symbol}
        triggerClassName="p-0"
      />
      <ChainCurrencyIcon
        chainIcon={chainLogo}
        currencyIcon={tokenLogo}
        name={symbol}
        fallbackClassName="bg-secondary"
      />
      <div className="">
        <div className="mb-1 flex items-center gap-1 text-title align-baseline font-bold text-[calc(1rem*(13/16))] leading-[calc(1rem*(13/16))]">
          <Text
            text={symbol}
            fontSize={13}
            fontWeight="medium"
            className="leading-[calc(1rem*(13/16))]"
            highLightText={debounceValue?.split(' ').join('').toUpperCase()}
          />
          <img src="/images/icons/icon-pump.webp" alt="" className="w-2.5 h-2.5" />
        </div>
        <div className="flex items-center">
          <div className="text-[calc(1rem*(10/16))] text-[#FFFFFF99] leading-[calc(1rem*(10/16))] mr-1">
            {formatAddressWallet(token, 5, 4)}
          </div>
          <div className="cursor-copy mr-2">
            <CopyButton
              icon="/images/tokenDetail/icon-copy.webp"
              className="w-[10px] min-w-[10px] h-[10px]"
              text={token}
            />
          </div>
        </div>
      </div>
    </div>
  )
})

SymbolCell.displayName = 'SymbolCell'

const MarketCapCell = memo(({ info }: { info: any }) => {
  const currentPrice = info.getValue()
  const price = info.row.original.price
  
  return (
    <div className="flex items-end gap-1 flex-col relative">
      <Text text={formatMoney(price)} fontSize={15} fontWeight="medium" className="lining-nums" />
      <Text
        text={formatMoney(Number(currentPrice))}
        fontSize={11}
        fontWeight="regular"
        color="#FFFFFFB2"
        className="lining-nums"
      />
    </div>
  )
})

MarketCapCell.displayName = 'MarketCapCell'

const PriceChangeCell = memo(({ info }: { info: any }) => {
  const changePercent = info.getValue()
  return <PriceChange value={formatPercentage(changePercent)} isPositive={Number(changePercent) > 0} />
})

PriceChangeCell.displayName = 'PriceChangeCell'

// Memoized sort handlers hook
const useSortHandlers = (handleSort: (field: string) => void, getSortIndicator: (field: string) => any) => {
  const sortHandlers = useMemo(
    () => ({
      symbol: () => handleSort('symbol'),
      marketcap: () => handleSort('marketcap'),
      price24hChange: () => handleSort('price24hChange'),
    }),
    [handleSort],
  )

  const sortIndicators = useMemo(
    () => ({
      symbol: getSortIndicator('symbol'),
      marketcap: getSortIndicator('marketcap'),
      price24hChange: getSortIndicator('price24hChange'),
    }),
    [getSortIndicator],
  )

  return { sortHandlers, sortIndicators }
}

const useTableColumns = (sortHandlers: any, sortIndicators: any, debounceValue?: string) => {
  const columnHelper = createColumnHelper<any>()

  return useMemo(
    () => [
      columnHelper.accessor('symbol', {
        header: () => (
          <div className="flex items-center">
            <SortHeader text="币种" onSort={sortHandlers.symbol} sortIndicator={sortIndicators.symbol} />
          </div>
        ),
        cell: (info) => <SymbolCell info={info} debounceValue={debounceValue} />,
      }),

      columnHelper.accessor('marketcap', {
        header: () => (
          <div className="flex justify-end items-end">
            <SortHeader text="市值" onSort={sortHandlers.marketcap} sortIndicator={sortIndicators.marketcap} />
          </div>
        ),
        cell: (info) => <MarketCapCell info={info} />,
      }),

      columnHelper.accessor('price24hChange', {
        header: () => (
          <div className="flex justify-end">
            <SortHeader
              text="涨跌幅"
              onSort={sortHandlers.price24hChange}
              sortIndicator={sortIndicators.price24hChange}
            />
          </div>
        ),
        cell: (info) => <PriceChangeCell info={info} />,
      }),
    ],
    [sortHandlers, sortIndicators, debounceValue],
  )
}

const MemeList = (props: SearchResultsListProps) => {
  const { debounceValue, allowShowList, favoriteTokens } = props
  const { handleRowClick } = useHandleLogic()
  const { activeChain } = useAppSelector((state) => state.wallet)
  const [listTokenMeme, setListTokenMeme] = useState<TokenTrending[]>([])

  const searchVariables = useMemo(
    () => ({
      input: debounceValue,
    }),
    [debounceValue]
  )

  const trendingVariables = useMemo(
    () => ({
      input: {
        page: 1,
        limit: 20,
        timeRange: 'h24',
        dex: 'All',
        direction: 'Popular',
        chain: 'ALL',
      },
    }),
    [activeChain]
  )

  const { loading: loadingSearch, data: dataSearch } = useQuery<SearchTokenResponse>(searchTokens, {
    variables: searchVariables,
    skip: !debounceValue,
  })

  const { data, loading } = useQuery<GetTokenTrendingResponse>(getTrending24hTokens, {
    variables: trendingVariables,
  })

  const tokens = useMemo(() => {
    if (!data?.getTokenTrending?.data) return []
    
    return data.getTokenTrending.data.map((token) => ({
      ...token,
      price24hChange: Number(token?.price24hChange),
      marketcap: Number(token?.marketcap),
    }))
  }, [data])

  const tableData = useMemo(() => {
    return debounceValue ? (dataSearch?.searchToken || []) : tokens
  }, [debounceValue, dataSearch?.searchToken, tokens])

  const { sortedData, handleSort, getSortIndicator } = useSortableTable<any>(tableData)
  const { sortHandlers, sortIndicators } = useSortHandlers(handleSort, getSortIndicator)
  const columns = useTableColumns(sortHandlers, sortIndicators, debounceValue)

  const favoriteTokensMap = useMemo(() => {
    return new Map(favoriteTokens.map(token => [token.token, token]))
  }, [favoriteTokens])

  useEffect(() => {
    const updatedTokens = sortedData.map((item) => {
      const tokenFavorite = favoriteTokensMap.get(item.token)
      return {
        ...item,
        isFavorite: !!tokenFavorite,
      }
    })
    
    setListTokenMeme(updatedTokens)
  }, [sortedData, favoriteTokensMap])

  const tableProps = useMemo(
    () => ({
      isLoading: loading || loadingSearch,
      columns,
      data: listTokenMeme,
      onRowClick: handleRowClick,
      isStickyHeader: false,
      containerClassName: "!border-none _hidescrollbar !max-h-none",
      tableHeaderClassName: "text-[#FFFFFF80] text-[calc(1rem*(12/16))] font-[400]",
      tableHeaderRowClassName: "!border-none",
      tableCellClassName: "group-hover:!bg-[#27272a] cursor-pointer !border-none !py-2.5 justify-end",
      emptyText: "暂无数据",
    }),
    [loading, loadingSearch, columns, listTokenMeme, handleRowClick]
  )

  const renderTable = useCallback(() => {
    if (!allowShowList) {
      return <MemeSearchResultsList debounceValue={debounceValue} favoriteTokens={favoriteTokens} />
    }
    return <TableVirtual {...tableProps} />
  }, [allowShowList, debounceValue, favoriteTokens, tableProps])

  return <div>{renderTable()}</div>
}

export default memo(MemeList) 