import { SearchSymbolResponse } from '@/@generated/gql/graphql-symbolDex'
import Text from '@/components/common/Text'
import { LeverageBadge, PriceChange } from '@/components/futuresDiscover/table/crypto-table'
import { TableVirtual } from '@/components/futuresDiscover/table/table-virtual'
import { IconSortDown, IconSortUp } from '@/components/icon'
import { symbolDexClient } from '@/lib/gql/apollo-client'
import { useSearchFilter } from '@/pages/futures-market/hooks/useHandleGetData'
import useSortableTable from '@/pages/futures-market/hooks/useSortableTable'
import { ISymbolList } from '@/pages/futures-market/type'
import { SEARCH_SYMBOL_ENDPOINT } from '@/services/symbol.dex.service'
import { formatMoney, formatPercentage } from '@/utils/helpers'
import { useQuery } from '@apollo/client'
import { createColumnHelper } from '@tanstack/react-table'
import { Dispatch, memo, SetStateAction, useCallback, useEffect, useMemo, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import ConfirmCollectToken from './ConfirmCollectToken'
import CrytoSearchResultsList from './CrytoSearchResultsList'
import useHandleLogic from './hooks/useHandleLogic'

const SortHeader = memo(
  ({
    text,
    onSort,
    sortIndicator,
  }: {
    text: string
    onSort: () => void
    sortIndicator: { upColor: string; downColor: string }
  }) => (
    <div className="flex cursor-pointer" onClick={onSort}>
      <Text text={text} fontSize={11} fontWeight="light" color="#FFFFFF80" className="cursor-pointer" />
      <div className="flex flex-col ml-1 cursor-pointer">
        <IconSortUp currentColor={sortIndicator.upColor} />
        <IconSortDown currentColor={sortIndicator.downColor} />
      </div>
    </div>
  ),
)

SortHeader.displayName = 'SortHeader'

const useSortHandlers = (handleSort: (field: string) => void, getSortIndicator: (field: string) => any) => {
  const sortHandlers = useMemo(
    () => ({
      symbol: () => handleSort('symbol'),
      marketCap: () => handleSort('marketCap'),
      currentPrice: () => handleSort('currentPrice'),
      volume: () => handleSort('volume'),
      changPxPercent: () => handleSort('changPxPercent'),
    }),
    [handleSort],
  )

  const sortIndicators = useMemo(
    () => ({
      symbol: getSortIndicator('symbol'),
      marketCap: getSortIndicator('marketCap'),
      currentPrice: getSortIndicator('currentPrice'),
      volume: getSortIndicator('volume'),
      changPxPercent: getSortIndicator('changPxPercent'),
    }),
    [getSortIndicator],
  )

  return { sortHandlers, sortIndicators }
}

const useTableColumnsWithSort = (
  sortHandlers: any,
  sortIndicators: any,
  setSymbolList: Dispatch<SetStateAction<ISymbolList[]>>,
  search?: string,
) => {
  const columnHelper = createColumnHelper<ISymbolList>()

  return useMemo(
    () => [
      columnHelper.accessor('symbol', {
        header: () => (
          <div className="flex items-center">
            <div className="flex items-center gap-1.5">
              <SortHeader text="币种" onSort={sortHandlers.symbol} sortIndicator={sortIndicators.symbol} />
              <SortHeader text="市值" onSort={sortHandlers.marketCap} sortIndicator={sortIndicators.marketCap} />
            </div>
          </div>
        ),
        cell: (info) => {
          const { symbol, maxLeverage, marketCap, isFavorite } = info.row.original
          return (
            <div className="flex items-center space-x-2">
              <ConfirmCollectToken
                defaultCollect={isFavorite ?? false}
                token={symbol}
                onRemoveSuccess={() => {
                  setSymbolList((prev) => prev.filter((item) => item.symbol !== symbol))
                }}
                tokenSymbol={symbol}
                triggerClassName="p-0"
              />
              <div className="flex flex-col gap-1">
                <div className="flex items-end gap-1">
                  <Text
                    text={symbol}
                    fontSize={15}
                    fontWeight="medium"
                    className="leading-[calc(1rem*(15/16))]"
                    highLightText={search?.split(' ').join('').toUpperCase()}
                  />
                  <Text
                    text="/"
                    fontSize={9}
                    fontWeight="light"
                    color="#FFFFFF80"
                    className="leading-[calc(1rem*(12/16))]"
                  />
                  <Text
                    text="USDC"
                    fontSize={11}
                    fontWeight="light"
                    color="#FFFFFF80"
                    className="leading-[calc(1rem*(11/16))]"
                  />
                  <LeverageBadge value={maxLeverage as unknown as string} />
                </div>
                <div className="flex gap-1 items-end">
                  <div className="text-[calc(1rem*(12/16))] tex-[#FFFFFFB2] lining-nums">{formatMoney(marketCap)}</div>
                </div>
              </div>
            </div>
          )
        },
      }),

      columnHelper.accessor('currentPrice', {
        header: () => (
          <div className="flex items-center gap-2 justify-end">
            <div className="flex items-center gap-1.5">
              <SortHeader text="价格" onSort={sortHandlers.currentPrice} sortIndicator={sortIndicators.currentPrice} />
              <SortHeader text="成交额" onSort={sortHandlers.volume} sortIndicator={sortIndicators.volume} />
            </div>
          </div>
        ),
        cell: (info: any) => {
          const currentPrice = info.getValue()
          const volume = info.row.original.volume
          return (
            <div className="flex items-end gap-1 flex-col relative">
              <Text text={formatMoney(currentPrice)} fontSize={15} fontWeight="medium" className="lining-nums" />
              <Text
                text={formatMoney(volume)}
                fontSize={11}
                fontWeight="regular"
                color="#FFFFFFB2"
                className="lining-nums"
              />
            </div>
          )
        },
      }),

      columnHelper.accessor('changPxPercent', {
        header: () => (
          <div className="flex justify-end gap-2">
            <div className="flex items-center text-right">
              <SortHeader
                text="涨跌幅"
                onSort={sortHandlers.changPxPercent}
                sortIndicator={sortIndicators.changPxPercent}
              />
            </div>
          </div>
        ),
        cell: (info) => {
          const changePercent = info.getValue()
          return <PriceChange value={formatPercentage(changePercent)} isPositive={Number(changePercent) > 0} />
        },
      }),
    ],
    [columnHelper, sortHandlers, sortIndicators],
  )
}

const ContractList = ({
  symbolDataInitial,
  search,
  isLoading,
  allowShowList,
  isFavorite,
  favoriteTokens,
}: {
  symbolDataInitial: ISymbolList[]
  search: string
  isLoading?: boolean
  allowShowList?: boolean
  isFavorite?: boolean
  favoriteTokens?: ISymbolList[]
}) => {
  const { saveToHistory } = useHandleLogic()
  const navigate = useNavigate()
  const [symbolList, setSymbolList] = useState<ISymbolList[]>(symbolDataInitial)
  const [symbolInitial, setSymbolInitial] = useState<ISymbolList[]>(symbolDataInitial)

  const filteredData = useSearchFilter(symbolInitial || [], search)
  const { sortedData, handleSort, getSortIndicator } = useSortableTable<any>(!isFavorite ? symbolList : filteredData)
  const { sortHandlers, sortIndicators } = useSortHandlers(handleSort, getSortIndicator)
  const columns = useTableColumnsWithSort(sortHandlers, sortIndicators, setSymbolInitial, search)
  const { data, loading, refetch } = useQuery<SearchSymbolResponse>(SEARCH_SYMBOL_ENDPOINT, {
    skip: !search,
    variables: {
      input: {
        filter: search.split(' ').join('').toUpperCase(),
      },
    },
    client: symbolDexClient,
  })  

  const handleRowClick = useCallback(
    (row: any) => {
      saveToHistory({ address: row.symbol, name: row.symbol, logo: row.image, chainId: row.chainId }, 'dex')
      navigate(`/futures/${row.symbol}`)
    },
    [navigate],
  )

  useEffect(() => {
    if (search) {
      refetch()
    }
  }, [search])

  useEffect(() => {
    if (!search) {
      setSymbolList(symbolDataInitial)
    }
  }, [symbolDataInitial, search])

  useEffect(() => {
    setSymbolInitial(symbolDataInitial)
  }, [symbolDataInitial])

  useEffect(() => {
    if (data) {
      setSymbolList(data?.searchSymbol?.list || [])
    }
  }, [data])

  const renderTable = useCallback(() => {
    if (!allowShowList) {
      return <CrytoSearchResultsList search={search} favoriteTokens={favoriteTokens} />
    }
    return (
      <TableVirtual<ISymbolList, any>
        isLoading={isLoading || loading}
        columns={columns}
        data={sortedData}
        isStickyHeader={false}
        containerClassName="!border-none _hidescrollbar"
        tableHeaderClassName="text-[#FFFFFF80] text-[calc(1rem*(12/16))] font-[400]"
        tableHeaderRowClassName="!border-none"
        tableCellClassName="group-hover:!bg-[#27272a] cursor-pointer !border-none !py-2.5 justify-end"
        onRowClick={handleRowClick}
        cusTomMaxHeight="none"
        emptyText="暂无数据"
      />
    )
  }, [allowShowList, columns, favoriteTokens, handleRowClick, isLoading, loading, search, sortedData])

  return <>{renderTable()}</>
}

export default memo(ContractList)
