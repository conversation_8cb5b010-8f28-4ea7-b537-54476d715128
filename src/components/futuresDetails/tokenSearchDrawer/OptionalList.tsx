import Text from '@/components/common/Text'
import { IconSortDown, IconSortUp } from '@/components/icon'
import { cn } from '@/lib/utils'
import { ISymbolList } from '@/pages/futures-market/type'
import { TokenTrending } from '@/types/token'
import { memo, useCallback, useEffect, useState } from 'react'
import { TokenSearchDrawerType } from '.'
import ContractList from './ContractList'
import { MemeWatchlist } from './MemeWatchlist'

const SortHeader = memo(
  ({
    text,
    onSort,
    sortIndicator,
  }: {
    text: string
    onSort: () => void
    sortIndicator: { upColor: string; downColor: string }
  }) => (
    <div className="flex cursor-pointer" onClick={onSort}>
      <Text text={text} fontSize={11} fontWeight="light" color="#FFFFFF80" className="cursor-pointer" />
      <div className="flex flex-col ml-1 cursor-pointer">
        <IconSortUp currentColor={sortIndicator.upColor} />
        <IconSortDown currentColor={sortIndicator.downColor} />
      </div>
    </div>
  ),
)

SortHeader.displayName = 'SortHeader'

const headerTabs = [
  {
    value: '合约',
    label: '合约',
  },
  {
    value: 'Meme',
    label: 'Meme',
  },
]

const OptionalList = ({
  search,
  symbolsFavorite,
  favoriteTokens,
  type,
  allowShowList,
  isLoadingFavorite,
}: {
  search: string
  symbolsFavorite: ISymbolList[]
  favoriteTokens: TokenTrending[]
  type: TokenSearchDrawerType
  allowShowList?: boolean
  isLoadingFavorite?: boolean
}) => {
  const [activeHeader, setActiveHeader] = useState(headerTabs[0].value)
  const [count, setCount] = useState(0)

  useEffect(() => {
    // chặn lại số lần render không cho setActiveHeader lặp lại
    if (count >= 4) {
      return
    }

    if (symbolsFavorite.length === 0 || favoriteTokens.length === 0) {
      if (symbolsFavorite.length === 0) {
        setActiveHeader(headerTabs[1].value)
      } else {
        setActiveHeader(headerTabs[0].value)
      }
    } else {
      if (type === TokenSearchDrawerType.MEME) {
        setActiveHeader(headerTabs[1].value)
      } else {
        setActiveHeader(headerTabs[0].value)
      }
    }
  }, [symbolsFavorite, favoriteTokens, type, count])

  useEffect(() => {
    setCount((prev) => prev + 1)
  }, [symbolsFavorite, favoriteTokens, type])

  const handleRenderTab = useCallback(
    (tab: string) => {
      switch (tab) {
        case headerTabs[0].value:
          return (
            <ContractList
              symbolDataInitial={symbolsFavorite}
              search={search}
              allowShowList={allowShowList}
              isFavorite={true}
              isLoading={isLoadingFavorite}
            />
          )
        default:
          return (
            <MemeWatchlist
              debounceValue={search}
              favoriteTokens={favoriteTokens}
              allowShowList={allowShowList}
              isFavorite={true}
            />
          )
      }
    },
    [symbolsFavorite, favoriteTokens, search, allowShowList, isLoadingFavorite],
  )

  return (
    <div className="relative">
      <div className="mt-2 flex gap-1.5 items-center">
        {headerTabs.map(({ value, label }) => (
          <div
            className={cn(
              'text-[calc(1rem*(12/16))] px-3 py-1 border-[0.5px] border-[#ECECED14] rounded-full cursor-pointer',
              value === activeHeader ? 'text-[#FFFFFF] bg-[#ECECED14]' : 'text-[#FFFFFF80]',
              value === headerTabs[1].value && favoriteTokens.length === 0 && 'hidden',
              value === headerTabs[0].value && symbolsFavorite.length === 0 && 'hidden',
            )}
            onClick={() => setActiveHeader(value)}
            key={value}
          >
            {label}
          </div>
        ))}
      </div>
      <div className="w-full overflow-hidden">{handleRenderTab(activeHeader)}</div>
    </div>
  )
}

export default memo(OptionalList)
