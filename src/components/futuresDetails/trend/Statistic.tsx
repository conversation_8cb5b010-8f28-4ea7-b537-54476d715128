import { useWebSocketChannel } from '@/hooks/hyperliquid/useWebSocketChannel'
import { symbolDexClient } from '@/lib/gql/apollo-client'
import { cn } from '@/lib/utils.ts'
import { symbolInfoSelector } from '@/redux/modules/futuresCurrentSymbol.slice'
import { useAppSelector } from '@/redux/store'
import { SEARCH_SYMBOL_ENDPOINT } from '@/services/symbol.dex.service'
import { formatMoney, formatNumberWithCommas } from '@/utils/helpers'
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
type DetailStatisticProps = {
  containerClassName?: string
}

interface Candle {
  t: number // open millis
  T: number // close millis
  s: string // coin
  i: string // interval
  o: number // open price
  c: number // close price
  h: number // high price
  l: number // low price
  v: number // volume (base unit)
  n: number // number of trades
}
export interface ISymbolList {
  volume: string
  symbol: string
}


const StatisticItem = memo<{
  label: string
  value: string
  className?: string
}>(({ label, value, className }) => (
  <p className={cn("flex items-center justify-between mb-1.5", className)}>
    <span className="text-[#FFFFFF80]">{label}</span>
    <span className="text-[#FFFFFF]">{value}</span>
  </p>
))

StatisticItem.displayName = 'StatisticItem'

const PriceDisplay = memo<{
  price: string
  label?: string
  priceClassName?: string
  labelClassName?: string
}>(({ price, label, priceClassName, labelClassName }) => (
  <div>
    {label && (
      <p className={cn("text-[#FFFFFF80] text-[calc(11rem/16)] leading-[calc(11rem/16)] mb-0.5", labelClassName)}>
        {label}
      </p>
    )}
    <p className={cn("text-rise text-[calc(22rem/16)] leading-[calc(22rem/16)] mb-1.5", priceClassName)}>
      {price}
    </p>
  </div>
))

PriceDisplay.displayName = 'PriceDisplay'

const DetailStatistic = memo<DetailStatisticProps>(({ containerClassName }) => {
  const { baseCoin, markPrice } = useAppSelector(symbolInfoSelector)
  const [candleData, setCandleData] = useState<Candle | undefined>(undefined)
  const [symbolList, setSymbolList] = useState<ISymbolList[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const lastFetchTimeRef = useRef<number>(0)
  
  const fetchSymbolList = useCallback(async () => {
    // 防止频繁请求，设置最小间隔为5秒
    const now = Date.now()
    if (now - lastFetchTimeRef.current < 5000) {
      return
    }
    lastFetchTimeRef.current = now
    if (!baseCoin) return
    try {
      setIsLoading(true)
      const { data } = await symbolDexClient.query({
        query: SEARCH_SYMBOL_ENDPOINT,
        variables: {
          input: {
            filter: baseCoin.toUpperCase(),
          },
        },
      })
      setSymbolList(data?.searchSymbol?.list || [])
    } catch (error) {
    } finally {
      setIsLoading(false)
    }
  }, [baseCoin])

  // 初始加载和baseCoin变化时获取数据
  useEffect(() => {
    fetchSymbolList()
  }, [baseCoin, fetchSymbolList])
  useEffect(() => {
    // 清除之前的定时器
    if (timerRef.current) {
      clearInterval(timerRef.current)
    }
    // 设置新的定时器，每10秒获取一次数据
    timerRef.current = setInterval(() => {
      fetchSymbolList()
    }, 10000)
    // 组件卸载时清除定时器
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }
  }, [fetchSymbolList])

  const wsParams = useMemo(() => {
    if (!baseCoin) return null
    return {
      type: 'candle',
      coin: baseCoin,
      interval: '1d',
    }
  }, [baseCoin])

  const handleWebSocketData = useCallback((data: Candle) => {
    setCandleData(data)
  }, [])

  useWebSocketChannel('candle', wsParams, handleWebSocketData)
  
  // 获取当前币种的数据
  const currentCoinData = useMemo(() => {
    return symbolList.find(item => item.symbol === baseCoin)
  }, [symbolList, baseCoin])
  
  const formattedValues = useMemo(() => {
    if (!candleData) {
      return {
        currentPrice: '--',
        markPriceValue: formatNumberWithCommas(`${markPrice || ''}`),
        highPrice: '--',
        lowPrice: '--',
        volume: '--'
      }
    }
    return {
      currentPrice: formatNumberWithCommas(`${candleData?.c}`),
      markPriceValue: formatNumberWithCommas(`${markPrice || ''}`),
      highPrice: formatNumberWithCommas(`${candleData?.h}`),
      lowPrice: formatNumberWithCommas(`${candleData?.l}`),
      volume: formatNumberWithCommas(`${candleData?.v}`)
    }
  }, [candleData, markPrice, currentCoinData])

  // Memoize container className
  const containerCls = useMemo(() => 
    cn('flex items-center justify-between mb-[12px]', containerClassName),
    [containerClassName]
  )

  return (
    <div className={containerCls}>
      <div>
        <PriceDisplay 
          label="最新成交价"
          price={formattedValues?.currentPrice ?? '--'}
        />
        
        <p className="text-rise text-[calc(11rem/16)] leading-[calc(11rem/16)] mb-1.5">
          <span className="text-[#FFFFFF80] mr-1.5">标记价</span>
          <span className="text-[#FFFFFF]">{formattedValues.markPriceValue}</span>
        </p>
      </div>

      <div className="w-[157px] pl-[13px] text-[calc(11rem/16)] leading-[calc(11rem/16)]">
        <StatisticItem 
          label="24H 最高" 
          value={formattedValues.highPrice}
        />
        
        <StatisticItem 
          label="24H 最低" 
          value={formattedValues.lowPrice}
        />
        
        <StatisticItem 
          label="24H 成交额" 
          value={formatMoney(Number(symbolList[0]?.volume), true)}
          className="mb-0" // Remove margin for last item
        />
      </div>
    </div>
  )
})

DetailStatistic.displayName = 'DetailStatistic'

export default DetailStatistic