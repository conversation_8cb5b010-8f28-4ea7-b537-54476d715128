import { cn } from '@/lib/utils'
import { RootState, useAppSelector } from '@/redux/store'
import useHandleChangeValue from '../hooks/useHandleChangeValue'
import { OrderContractState, OrderSide } from './type.order'

const TradingButton = () => {
  const { orderInfo } = useAppSelector<RootState, OrderContractState>((state) => state.orderContract)

  const { handleOrderInfoChange } = useHandleChangeValue()

  return (
    <>
      <div className="absolute inset-0 flex">
        <div
          className="w-1/2 rounded-l-full bg-[#ECECED1F]"
          style={{
            clipPath: 'polygon(0 0, 100% 0, 97% 100%, 0 100%)',
          }}
        />
        <div
          className="w-1/2 rounded-r-full bg-[#ECECED1F]"
          style={{
            clipPath: 'polygon(3% 0, 100% 0, 100% 100%, 0% 100%)',
          }}
        />
      </div>

      {/* Active background highlight with animation */}
      <div
        className={cn(
          `absolute h-full bg-[#36D399] transition-all duration-300 ease-in-out`,
          orderInfo.side === OrderSide.buy ? 'rounded-l-full' : 'rounded-r-full',
        )}
        style={{
          width: '50%',
          clipPath:
            orderInfo.side === OrderSide.buy
              ? 'polygon(0 0, 100% 0, 97% 100%, 0 100%)'
              : 'polygon(3% 0, 100% 0, 100% 100%, 0% 100%)',
          left: orderInfo.side === OrderSide.buy ? '0' : '50%',
        }}
      />

      {/* Button text elements */}
      <div
        className={`relative w-1/2 rounded-l-full py-2 font-medium text-sm leading-none cursor-pointer z-10 transition-all duration-300 ease-in-out ${
          orderInfo.side === OrderSide.buy ? 'text-[#ffffff]' : 'text-[#d7d7d7] hover:text-white'
        }`}
        onClick={() => {
          if (orderInfo.side !== OrderSide.buy) {
            handleOrderInfoChange('side', OrderSide.buy)
          }
        }}
      >
        <div className="text-center relative">
          <span className={`relative`}>做多</span>
        </div>
      </div>

      <div
        className={`relative w-1/2 rounded-r-full py-2 font-medium text-sm leading-none cursor-pointer z-10 transition-all duration-300 ease-in-out ${
          orderInfo.side === OrderSide.sell ? 'text-[#ffffff]' : 'text-[#d7d7d7] hover:text-white'
        }`}
        onClick={() => {
          if (orderInfo.side !== OrderSide.sell) {
            handleOrderInfoChange('side', OrderSide.sell)
          }
        }}
      >
        <div className="text-center relative">
          <span className={`relative`}>做空</span>
        </div>
      </div>
    </>
  )
}

export default TradingButton
