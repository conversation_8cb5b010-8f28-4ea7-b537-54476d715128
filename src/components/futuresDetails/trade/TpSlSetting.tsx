import { formatPrice } from '@/components/futuresDetails/trade/tools'
import { MathFun } from '@/lib/utils.ts'
import { symbolInfoSelector } from '@/redux/modules/futuresCurrentSymbol.slice.ts'
import { selectSzMap } from '@/redux/modules/futuresMeta.slice'
import { futuresTradeConfigSelector } from '@/redux/modules/futuresTradeConfigs.slice'
import { RootState, useAppSelector } from '@/redux/store.ts'
import { UITab } from '@/types/uiTabs.ts'
import DrawerCheckSelect from '@components/common/DrawerCheckSelect.tsx'
import { OrderSide, OrderTypeEnum } from '@components/futuresDetails/trade/type.order.ts'
import { useEffect, useState } from 'react'
import useHandleChangeValue from '../hooks/useHandleChangeValue.tsx'
import InputRightBorderGradient from './InputRightBorderGradient.tsx'
import { OrderContractState } from './type.order.ts'

// Hash order price

const TpSlSetting = () => {
  const {
    orderInfo: { tpPrice, slPrice, size, side, currency, price: limitPrice, type },
  } = useAppSelector<RootState, OrderContractState>((state) => state.orderContract)
  const { handleOrderInfoChange } = useHandleChangeValue()
  const { baseCoin, price: marketPrice } = useAppSelector(symbolInfoSelector)

  const szMap = useAppSelector(selectSzMap)

  const entryPrice = type === OrderTypeEnum.market ? Number(marketPrice) : Number(limitPrice)


  const tradeConfigs = useAppSelector(futuresTradeConfigSelector(baseCoin))
  const leverage = Number(tradeConfigs.leverage)

  const unitList: UITab[] = [
    {
      value: '%',
      label: '收益率(%)',
      // desc: '根据预估投资回报率设置止盈止损触发价',
    },
    {
      value: '$',
      label: '盈亏(USD)',
      // desc: '根据预估盈亏设置止盈止损触发价',
    },
  ]

  const [unit, setUnit] = useState<string>(unitList[0].value)
  const [pricePercent, setPricePercent] = useState({
    slPricePercent: '',
    tpPricePercent: '',
  })

  const handleUnitChange = (unit: string) => {
    setUnit(unit)
    handleOrderInfoChange('tpslUnit', unit)
  }

  const handleChangeTsSl = (name: 'slPrice' | 'tpPrice', value: string) => {
    handleOrderInfoChange(name, value)

    const priceNumber = Number(value)
    if (isNaN(priceNumber) || priceNumber <= 0) return

    let resultStr = ''

    if (entryPrice && leverage && priceNumber) {
      const priceDiff = side === OrderSide.buy ? priceNumber - entryPrice : entryPrice - priceNumber

      if (unit === '%') {
        const percentage = MathFun.mul(MathFun.div(priceDiff, entryPrice), leverage * 100)
        resultStr = percentage.toFixed(2)
      } else if (unit === '$' && Number(size) > 0) {
        let pnlUsd = 0
        if (currency === 'USD') {
          // USD 本位：size 是 USD 金额，不再乘 leverage，priceDiff × size / entryPrice
          pnlUsd = MathFun.mul(MathFun.div(priceDiff, entryPrice), size)
        } else {
          // 币本位：size 是币数量，盈亏 = priceDiff  × size
          pnlUsd = MathFun.mul(priceDiff, size)
        }

        resultStr = pnlUsd.toFixed(2)
      }
    }
    if (name === 'tpPrice') {
      setPricePercent((prev) => ({
        ...prev,
        tpPricePercent: resultStr,
      }))
    } else {
      const priceDiff = side === OrderSide.buy ? priceNumber - entryPrice : entryPrice - priceNumber
      if (priceDiff < 0) {
        resultStr =  Math.abs(parseFloat(resultStr)).toString();
      } else {
        resultStr = (-Math.abs(parseFloat(resultStr))).toString();
      }
      

      setPricePercent((prev) => ({
        ...prev,
        slPricePercent:  resultStr
      }))
    }
  }

  const handleChanglePricePercent = (name: 'slPricePercent' | 'tpPricePercent', value: string) => {
    const sanitizedValue = value
      // .replace(/[^\d.]/g, '') // 移除非数字和小数点字符
      .replace(/^0+(\d)/, '$1') // 去除前导0（保留一个）
      .replace(/^\./, '0.') // 开头不能是点
      .replace(/(\.\d{0,2}).*$/, '$1') // 小数最多保留2位

    setPricePercent({
      ...pricePercent,
      [name]: sanitizedValue,
    })

    const numericInput = Number(value)
    let newPrice = 0

    if (!entryPrice || !leverage || !numericInput) return

    if (unit === '%') {
      // === 按收益率计算 ===
      const priceOffset = MathFun.div(MathFun.mul(numericInput, entryPrice), leverage * 100)

      const isPositive =
        (name === 'tpPricePercent' && side === OrderSide.buy) || (name === 'slPricePercent' && side === OrderSide.sell)

      newPrice = isPositive ? MathFun.add(entryPrice, priceOffset) : MathFun.sub(entryPrice, priceOffset)
    } else if (unit === '$') {
      let priceOffset = 0
      if (currency === 'USD') {
        // USD 本位，目标盈亏 = size * Δp / entryPrice
        // 推导 Δp = PnL * entryPrice / size
        priceOffset = MathFun.div(MathFun.mul(numericInput, entryPrice), size)
      } else {
        // 币本位，目标盈亏 = Δp * size * leverage
        // 推导 Δp = PnL / (size * leverage)
        priceOffset = MathFun.div(numericInput, MathFun.mul(size, leverage))
      }

      const isProfit =
        (name === 'tpPricePercent' && side === OrderSide.buy) || (name === 'slPricePercent' && side === OrderSide.sell)

      newPrice = isProfit ? MathFun.add(entryPrice, priceOffset) : MathFun.sub(entryPrice, priceOffset)
    }
    const formatNewPrice = formatPrice(newPrice, szMap[baseCoin])

    // 更新价格
    if (name === 'tpPricePercent') {
      handleOrderInfoChange('tpPrice', formatNewPrice)
    } else {
      handleOrderInfoChange('slPrice', formatNewPrice)
    }
  }

  const handleCheckDisable = () => {
    if (Number(size) === 0) {
      return true
    }
    return false
  }

  useEffect(() => {
    const intervalId = setInterval(() => {
      handleChangeTsSl('tpPrice', tpPrice)
      handleChangeTsSl('slPrice', slPrice)
    }, 10000);

    return () => clearInterval(intervalId)
  }, [entryPrice, tpPrice, slPrice])

  useEffect(() => {
    handleChangeTsSl('tpPrice', tpPrice)
    handleChangeTsSl('slPrice', slPrice)
  }, [unit, type, side])

  return (
    <div className="mb-2.5">
      <div className="flex items-center mb-1">
        <InputRightBorderGradient
          label="止盈价"
          containerClassName="w-full"
          inputClassName="flex-1"
          inputWrapperClassName="flex items-center justify-between"
          onChange={(e) => handleChangeTsSl('tpPrice', e)}
          value={`${tpPrice}`}
          inputProps={{
            min: 0,
            // type: 'number',
            disabled: handleCheckDisable(),
          }}
          formatThousands={true}
          floatingLabel={true}
        />

        <InputRightBorderGradient
          unit={
            <div
              className="text-[#FFFFFF] cursor-pointer"
              onClick={() => {
                handleUnitChange(unit === unitList[0].value ? unitList[1].value : unitList[0].value)
              }}
            >
              <div className="flex items-center">
                <span>{unit}</span>
                <img className="ml-0.5" src="/images/futuresDetail/arrow-down.svg" />
              </div>
            </div>
          }
          inputProps={{
            // type: 'number',
            disabled: handleCheckDisable(),
          }}
          onChange={(e) => handleChanglePricePercent('tpPricePercent', e)}
          value={pricePercent.tpPricePercent}
          label="收益"
          containerClassName="w-full"
          inputClassName="flex-1"
          inputWrapperClassName="flex items-center justify-between"
          formatThousands={true}
          floatingLabel={true}
        />
      </div>

      <div className="flex items-center">
        <InputRightBorderGradient
          label="止损价"
          containerClassName="w-full"
          inputClassName="flex-1"
          inputWrapperClassName="flex items-center justify-between"
          onChange={(e) => handleChangeTsSl('slPrice', e)}
          value={`${slPrice}`}
          inputProps={{
            min: 0,
            // type: 'number',
            disabled: handleCheckDisable(),
          }}
          formatThousands={true}
          floatingLabel={true}
        />

        <InputRightBorderGradient
        unit={
            <div
              className="text-[#FFFFFF] cursor-pointer"
              onClick={() => {
                handleUnitChange(unit === unitList[0].value ? unitList[1].value : unitList[0].value)
              }}
            >
              <div className="flex items-center">
                <span>{unit}</span>
                <img className="ml-0.5" src="/images/futuresDetail/arrow-down.svg" />
              </div>
            </div>
          }
          inputProps={{
            // type: 'number',
            disabled: handleCheckDisable(),
          }}
          onChange={(e) => handleChanglePricePercent('slPricePercent', e)}
          value={pricePercent.slPricePercent}
          label="亏损"
          containerClassName="w-full"
          inputClassName="flex-1"
          inputWrapperClassName="flex items-center justify-between"
          formatThousands={true}
          floatingLabel={true}
        />
      </div>
    </div>
  )
}
export default TpSlSetting
