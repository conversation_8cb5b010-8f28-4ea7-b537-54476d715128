export * from '@/types/hyperliquid'
export type PositionModeValue = 'cross' | 'isolated'
export type OrderType = 'Limit' | 'Market'
export type OrderSide = 'B' | 'A';     // 买入 或 卖出

export type DelegateType = 
'Take Profit Limit' | 'Stop Limit' | 'Limit' | 'Market' | 
'Take Profit Market' | 'Stop Market'

export type TpslOrderType = 
'Take Profit Limit' | 'Stop Limit' |
'Take Profit Market' | 'Stop Market'


export type All = 'All'

export interface FilterTypeOption {
  label: string;
  value: OrderType | All;
}

export interface FilterSideOption {
  label: string;
  value: OrderSide | All;
}


export interface xOpenOrders {
  "coin": string,
  "side": 'A' | 'B',
  "limitPx": string,
  "sz": string,
  "oid": number,
  "timestamp": number,
  "triggerCondition": string,
  "isTrigger": boolean,
  "triggerPx": string,
  "children": any[],
  "isPositionTpsl": boolean,
  "reduceOnly": boolean,
  "orderType": OrderType,
  "origSz": string,
  "tif": string,
  "cloid": null
}

export interface xPositions {
  coin: string;
  cumFunding: {
    allTime: string;
    sinceChange: string;
    sinceOpen: string;
  };
  entryPx: string;
  leverage: {
    rawUsd: string;
    type: string;
    value: number;
  };
  side: 'A' | 'B';
  liquidationPx: string;
  marginUsed: string;
  maxLeverage: number;
  positionValue: string;
  returnOnEquity: string;
  markPrice: string
  szi: string;
  unrealizedPnl: string;
  tpPrice: string;
  slPrice: string;
  midPrice: string
  timestamp?: number
}


export interface xHistoryOrders {
  coin: string;
  side: 'A' | 'B';
  limitPx: string;
  sz: string;
  oid: number;
  timestamp: number;
  triggerCondition: 'Triggered' | 'NotTriggered';
  isTrigger: boolean;
  triggerPx: string;
  children: any[];
  isPositionTpsl: boolean;
  reduceOnly: boolean;
  orderType: 'Take Profit Market' | string;
  origSz: string;
  tif: 'Gtc' | 'Ioc' | 'Fok' | string;
  cloid: string | null;
  status: 'filled' | 'open' | 'cancelled' | string;
  statusTimestamp: number;
}

export interface xHistoryTrade {
  coin: string;
  px: string;
  sz: string;
  side: 'A' | 'B';
  time: number;
  startPosition: string;
  dir: 'Close Long' | 'Close Short' | 'Open Long' | 'Open Short' | string;
  closedPnl: string;
  hash: string;
  oid: number;
  crossed: boolean;
  fee: string;
  tid: number;
  feeToken: string;
}

export interface xBuilderInfo {
  b: string
  f: number
}



