import { Button } from '@/components/ui/button'
import { useState, useEffect, useRef, useMemo } from 'react'
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle, DrawerTrigger } from '@/components/ui/drawer'
import { cn } from '@/lib/utils.ts'
import InputControl from './InputControl'
import LeverageSlider from './LeverageSlider'
import { useAppSelector } from '@/redux/store'
import { selectMaxLeverageBySymbol } from '@/redux/modules/futuresMeta.slice'
import { symbolInfoSelector } from '@/redux/modules/futuresCurrentSymbol.slice'

type PositionLeverProps = {
  isFullWidth?: boolean
  leverage?: string
  onChange?: (value: string) => void
}

const PositionLever = ({ isFullWidth, leverage = '20', onChange }: PositionLeverProps) => {
  const { baseCoin } = useAppSelector(symbolInfoSelector)
  const maxLeverageFromStore = useAppSelector(selectMaxLeverageBySymbol(baseCoin))

  const maxLeverage = useMemo(() => {
    return typeof maxLeverageFromStore === 'number' && maxLeverageFromStore > 0
      ? maxLeverageFromStore
      : 40
  }, [maxLeverageFromStore])

  const [open, setOpen] = useState(false)
  const [lever, setLever] = useState<string>(leverage)
  const [sliderValue, setSliderValue] = useState([Number(leverage)])
  const [isLoading, setIsLoading] = useState(false)
  const originalLeverageRef = useRef(leverage)

  useEffect(() => {
    const numeric = Number(leverage)
    if (!isNaN(numeric)) {
      setLever(numeric > maxLeverage ? `${maxLeverage}` : leverage)
      originalLeverageRef.current = numeric > maxLeverage ? `${maxLeverage}` : leverage
    }
  }, [leverage, maxLeverage])

  useEffect(() => {
    if (open) {
      // 打开抽屉时，保存原始杠杆值并设置当前值
      const numeric = Number(leverage)
      if (!isNaN(numeric)) {
        const validLeverage = numeric > maxLeverage ? `${maxLeverage}` : leverage
        setLever(validLeverage)
        originalLeverageRef.current = validLeverage
      }
    }
  }, [open, leverage, maxLeverage])

  // 抽屉关闭时重置为原始值
  useEffect(() => {
    if (!open) {
      setLever(originalLeverageRef.current)
      setSliderValue([Number(originalLeverageRef.current)])
      setIsLoading(false)
    }
  }, [open])

  const milestones = useMemo(() => {
    return [0, maxLeverage]
  }, [maxLeverage])

  const onSliderValueChange = (value: number[]) => {
    const newLever = Math.min(value[0], maxLeverage)
    setSliderValue([newLever])
    setLever(`${newLever}`)
  }

  const handleSureBtn = () => {
    if (onChange) {
      onChange(lever)
    }
    setOpen(false)
  }

  useEffect(() => {
    const numeric = Number(lever)
    if (!isNaN(numeric)) {
      setSliderValue([Math.min(numeric, maxLeverage)])
    }
  }, [lever, maxLeverage])

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <div
          className={cn(
            `flex items-center py-2 px-2.5 rounded-[6px] bg-[#ECECED14] text-rise border border-solid border-[#ECECED14] 
            text-[calc(1rem*(13/16))] leading-[calc(1rem*(13/16))] cursor-pointer`,
            isFullWidth && 'w-full justify-between',
          )}
        >
          {`${leverage}x`}
          <img className="ml-2" src="/images/futuresDetail/select-down-icon.svg" />
        </div>
      </DrawerTrigger>
      <DrawerContent className="w-full bg-[#232329] max-w-[768px] mx-auto">
        <DrawerHeader className="py-5 px-3.5  flex w-full items-center justify-between">
          <DrawerTitle className="flex items-center">
            <div className="text-[calc(1rem*(18/16))] leading-[calc(1rem*(18/16))]">调整杠杆</div>
          </DrawerTitle>
          <img
            src="/images/icons/icon-x.svg"
            className="w-6 h-6 cursor-pointer"
            onClick={() => setOpen(false)}
            alt=""
          />
        </DrawerHeader>
        <div className="px-3 pb-8">
          <InputControl
            value={lever}
            onChange={(v) => setLever(v)}
            inputProps={{ type: 'number' }}
            onMinus={() => setLever((prev) => `${Math.max(+prev - 1, 0)}`)}
            onPlus={() => setLever((prev) => `${Math.min(+prev + 1, maxLeverage)}`)}
          />

          <div className="mb-[30px] mt-[12px]">
            <LeverageSlider
              sliderValue={sliderValue}
              setSliderValue={setSliderValue}
              onSliderValueChange={onSliderValueChange}
              maxValue={maxLeverage}
              milestones={milestones}
            />
          </div>

          <div className="mb-8 flex items-center text-[#FF353C] text-[calc(1rem*(12/16))] leading-[calc(1rem*(12/16))]">
            <img src="/images/futuresDetail/danger-icon.svg" className="mr-1" />
            请注意，设置更高的杠杆倍数会增加清算的风险。
          </div>

          <Button
            variant="gradient"
            className=" text-tertiary w-full rounded-[50px] h-[calc(1rem*(44/16))]"
            onClick={handleSureBtn}
          >
            确定
          </Button>
        </div>
      </DrawerContent>
    </Drawer>
  )
}

export default PositionLever
