import { useTranslation } from 'react-i18next'
import CheckboxWithLabel from '@components/common/CheckboxWithLabel.tsx'
import { OrdersListFilter } from '.'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogOverlay,
} from '@/components/ui/dialog'
import { FilterTypeOption, FilterSideOption, OrderType, OrderSide, All } from '../types'
import DrawerCheckSelect from '@components/common/DrawerCheckSelect.tsx'
import { Button } from '@/components/ui/button'
import { useMemo, useState } from 'react'


interface CurrentOrdersFilterProps {
  filter: OrdersListFilter
  setFilter: (filter: OrdersListFilter) => void,
  onClickCancelAll: () => void
}


const CurrentOrdersFilter = ({ filter, setFilter, onClickCancelAll }: CurrentOrdersFilterProps) => {
  const { t } = useTranslation()

  const entrustmentOptions: FilterTypeOption[] = [
    {
      label: '全部委托',
      value: 'All'
    },
    {
      label: '市价交易',
      value: 'Market'
    },

    {
      label: '限价委托',
      value: 'Limit'
    },
   /*  {
      label: '止盈止损',
      value: '止盈止损'
    },
    {
      label: '分段委托',
      value: '分段委托'
    },
    {
      label: '分时委托',
      value: '分时委托'
    }, */
  ]

  const directionsOptions: FilterSideOption[] = [
    {
      label: '全部方向',
      value: 'All',
    },
    {
      label: '做多',
      value: 'B',
    },
    {
      label: '做空',
      value: 'A',
    },
  ]


  const [entrustment, setEntrustment] = useState<string>(entrustmentOptions[0].value)
  const [direction, setDirection] = useState<string>(directionsOptions[0].value)

  
  const handleEntrustmentChange = (entrustment: string) => {
    setEntrustment(entrustment)
    if (setFilter) {
      setFilter({
        ...filter,
        type: entrustment as OrderType | All
      })
    }
  }

  const handleDirectionChange = (direction: string) => {
    setDirection(direction)
    if (setFilter) {
      setFilter({
        ...filter,
        side: direction as OrderSide | All
      })
    }
  }

  const entrustmentText = useMemo(() => {
    return entrustmentOptions.find(item => {
      return item.value === entrustment
    })?.label
  }, [entrustmentOptions, entrustment]) 


  const directionText = useMemo(() => {
    return directionsOptions.find(item => {
      return item.value === direction
    })?.label
  }, [directionsOptions, direction]) 

  

  return (
    <div className="mb-[8px]">
      <div className="flex items-center justify-between gap-[8px] mb-[12px]">
        <div className="flex gap-[8px]">

        <DrawerCheckSelect 
          childrenTrigger={
            <div className='flex items-center cursor-pointer rounded-full px-[10px] py-[5px] min-w-[92px]  bg-[#ECECED1F] h-[26px]  text-[#FFFFFF] text-[13px] leading-[1] app-font-regular'>
              {entrustmentText}
              <img className='ml-1' src="/images/futuresDetail/order-arrow-down.svg" />
            </div>
          }
          options={entrustmentOptions}
          value={entrustment}
          onChange={handleEntrustmentChange}
        />

        <DrawerCheckSelect 
          childrenTrigger={
            <div className='flex items-center bg-[#ECECED1F] rounded-full px-[10px] py-[5px] min-w-[92px]  h-[26px]  text-[#FFFFFF] text-[13px] leading-[1] app-font-regular'>
            <div className="cursor-pointer text-[#FFFFFF] text-[13px] leading-[1] app-font-regular w-[calc(100%_-_10px)]">
              {directionText}
            </div>
            <img className="ml-1" src="/images/futuresDetail/order-arrow-down.svg" />
          </div>
          }
          options={directionsOptions}
          value={direction}
          onChange={handleDirectionChange}
        />

        </div>

        <CancelAllOrder onClickCancelAll={onClickCancelAll}/>

      </div>


      <CheckboxWithLabel
        label={t('currentOrdersList.showCurrentCoinOnly')}
        isChecked={filter?.showOnlyBaseCoin}
        onChange={() => {
          setFilter({
            ...filter,
            showOnlyBaseCoin: !filter?.showOnlyBaseCoin
          })
        }}
      />



    </div>
  )
}

interface CancelAllOrderProps {
  onClickCancelAll: () => void
}


const CancelAllOrder = ({onClickCancelAll}: CancelAllOrderProps) => {

  const [open, setOpen] = useState(false)

	const handleConfirm = () => {
    onClickCancelAll()
		setOpen(false)
	}

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant={'ghost'}
          className="p-0  text-[#FFFFFF] text-[calc(12rem/16)] h-[calc(12rem/16)]"
          > 撤销全部委托
        </Button>
      </DialogTrigger>
      <DialogContent
        onPointerDown={(event) => event.stopPropagation()}
        onClick={(event) => event.stopPropagation()}
        className="w-[335px] bg-[#232329] rounded-2xl p-5"
      >
        <DialogHeader>
          <DialogTitle className="text-center">
            <p className="text-[calc(18rem/16)] py-3">
            确定撤销全部委托订单？
            </p>
          </DialogTitle>
          <div className="flex justify-center items-center flex-row gap-2.5 mt-4">
            <Button  variant="borderGradient" className="flex-1 rounded-[50px]" onClick={() => setOpen(false)}>
              取消
            </Button>
            <Button variant="gradient" className="text-[#261236] flex-1 rounded-[50px]" onClick={handleConfirm}>
              确定
            </Button>
          </div>
        </DialogHeader>
        <DialogDescription />
      </DialogContent>
    </Dialog>
  )
}

export default CurrentOrdersFilter


