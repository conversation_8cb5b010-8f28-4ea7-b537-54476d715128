import CardWithGradient from '@/components/common/CardWithGradient'
import Tag from '@/components/common/Tag'
import ToastCenterScreen from '@/components/futuresDetails/ToastCenterScreen'
import { Button } from '@/components/ui/button'
import React, { useMemo, useState } from 'react'
import CircularProgressBar from './CircularProgressBar'
import { xOpenOrders } from '../types'
import { getOrderTypeDescription, getOrderSideDescription, isHyperOrderSuccess, getTpOrSlChild } from '../tools'
import isEqual from 'lodash/isEqual'
import ImgWithFallback from '@/components/common/ImgWithFallback'
import dayjs from 'dayjs'
import { Configs } from '@/const/configs'
import { userGqlClient } from '@/lib/gql/apollo-client'
import { useAppSelector, useAppDispatch } from '@/redux/store'
import { selectAllPerpMeta } from '@/redux/modules/futuresMeta.slice'
import { signHyperLiquidCancelOrderMutation } from '@services/auth.service.ts'
import { toast } from 'sonner'
import { formatNumberWithCommas } from '@/utils/helpers'

interface CurrentOrderCardProps {
  orderInfo: xOpenOrders
}
const CurrentOrderCard = ({ orderInfo }: CurrentOrderCardProps) => {
  const [showToast, setShowToast] = useState(false)

  const typeDesc = getOrderTypeDescription(orderInfo.orderType)
  const sideDesc = getOrderSideDescription(orderInfo.side, orderInfo)

  const origSz = parseFloat(orderInfo.origSz || '0')
  const sz = parseFloat(orderInfo.sz || '0')
  const completedSz = origSz - sz
  const allMeta = useAppSelector(selectAllPerpMeta)

  const percentage = useMemo(() => {
    return origSz === 0 ? 0 : (completedSz / origSz) * 100
  }, [origSz, sz, completedSz])

  const bgColor = orderInfo.side === 'B' ? 'green' : 'purple'

  const formattedTime = useMemo(() => dayjs(orderInfo.timestamp).format('MM-DD HH:mm:ss'), [orderInfo.timestamp])

  const tpOpenOrder = getTpOrSlChild(orderInfo, 'tp')[0]
  const slOpenOrder = getTpOrSlChild(orderInfo, 'sl')[0]

  const cancelOrder = async () => {
    console.log('cancelOrder')
    const nonce = Date.now()
    const { data } = await userGqlClient.mutate({
      mutation: signHyperLiquidCancelOrderMutation,
      variables: {
        input: {
          action: {
            type: 'cancel',
            cancels: [
              {
                a: allMeta.findIndex((item: any) => orderInfo.coin === item.name),
                o: orderInfo.oid,
              },
            ],
          },
          nonce: nonce,
          vaultAddress: '',
        },
      },
    })

    if (!data?.signHyperLiquidCancelOrder?.signature) {
      toast('获取代理钱包签名失败')
    }

    const response = await fetch(`${Configs.getHyperliquidConfig().apiUrl}/exchange`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: {
          type: 'cancel',
          cancels: [
            {
              a: allMeta.findIndex((item: any) => orderInfo.coin === item.name),
              o: orderInfo.oid,
            },
          ],
        },
        nonce: nonce,
        signature: data.signHyperLiquidCancelOrder.signature,
        vaultAddress: null,
      }),
    })

    const result = isHyperOrderSuccess(await response.json())
    if (!result.ok) {
      toast.error(result.error)
      return
    }
    toast.success('撤单成功')
  }

  return (
    <>
      <CardWithGradient
        bgColor={bgColor as 'green' | 'purple'}
        header={
          <div className="flex items-start justify-between w-full">
            <div className="flex items-start w-full">
              <ImgWithFallback
                src={`${Configs.getHyperliquidConfig().imgUrl}/${orderInfo.coin}.svg`}
                srcFallback="/images/logo-pair-fallback.webp"
                sharedClassName="size-7 mr-2"
              />
              <div className="mr-2">
                <p className="mb-1 text-[calc(14rem/16)] leading-[calc(14rem/16)] app-font-medium">
                  {orderInfo.coin}USD永续
                </p>
                <p className="text-[#FFFFFF80] text-[calc(11rem/16)] leading-[calc(11rem/16)]">{formattedTime}</p>
              </div>

              <div className="flex gap-1.5">
                <Tag
                  label={sideDesc}
                  color={orderInfo.side === 'B' ? '#00FFB4' : '#ab57ff'}
                  containerClassName="rounded-[4px] px-1 py-0.75 app-font-medium"
                />
                <Tag label={typeDesc} color="#00FFF6" containerClassName="rounded-[4px] px-1 py-0.75" />

                {/*  <Tag
                  label="全仓 100x"
                  color="#00FFF6"
                  containerClassName="!border-transparent bg-[#00FFF633] rounded-[4px] px-1 py-0.75"
                /> */}
              </div>
            </div>
            <Button
              variant={'ghost'}
              className="p-0 text-[#B9B9B9] cursor-pointer text-[calc(13rem/16)] h-[calc(13rem/16)]"
              onClick={() => cancelOrder()}
            >
              {' '}
              撤单
            </Button>
          </div>
        }
        classNameHeader="px-3 py-2.5"
        content={
          <div className="p-3 flex items-center relative z-1">
            <div className="flex-1 flex items-center justify-between gap-2">
              <div className="flex items-center gap-1">
                <CircularProgressBar
                  initialPercentage={percentage}
                  color={bgColor === 'green' ? '#00FFB4' : '#AB57FF'}
                  size={68}
                />
              </div>

              <table className="w-full text-[calc(12rem/16)] leading-[calc(12rem/16)] text-white">
                <tbody>
                  <tr className="">
                    <td className="pr-2 align-top">
                      <p className="text-[#FFFFFF80] text-[calc(11rem/16)] leading-[calc(11rem/16)] mb-1.5">委托数量 ({orderInfo.coin})</p>
                      <p className="app-font-medium">{origSz}</p>
                    </td>

                    <td className="pr-2 align-top">
                      <p className="text-[#FFFFFF80] text-[calc(11rem/16)] leading-[calc(11rem/16)] mb-1.5">已成交 (BTC)</p>
                      <p>{completedSz}</p>
                    </td>

                    <td className="pr-0 text-right align-top">
                      <p className="text-[#FFFFFF80] text-[calc(11rem/16)] leading-[calc(11rem/16)] mb-1.5">委托价格</p>
                      <p>{orderInfo.orderType.indexOf('Market') ? '市价' : formatNumberWithCommas(orderInfo.limitPx)}</p>
                    </td>
                  </tr>

                  {orderInfo.isTrigger || orderInfo.children.length ? (
                    <tr>
                      <td className="pt-3.5 pr-2 align-top">
                        {!orderInfo.children.length ? (
                          <>
                            <p className="text-[#FFFFFF80] mb-1.5">触发价格</p>
                            <p>{formatNumberWithCommas(orderInfo.triggerPx)}</p>
                          </>
                        ) : (
                          <>
                            <p className="text-[#FFFFFF80] text-[calc(11rem/16)] leading-[calc(11rem/16)] mb-1.5">止盈止损</p>
                            <p className="text-[calc(14rem/16)] leading-[calc(14rem/16)] font-bold">
                              <span className="text-rise font-semibold">{tpOpenOrder?.triggerPx || '-'}</span>
                              <span className="text-[#FFFFFF80] mx-0.5">/</span>
                              <span className="text-fall font-semibold">{slOpenOrder?.triggerPx || '-'}</span>
                            </p>
                          </>
                        )}
                      </td>
                    </tr>
                  ) : (
                    <></>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        }
      />
      <ToastCenterScreen showModal={showToast} setShowModal={setShowToast} text="撤单成功" />
    </>
  )
}

export default React.memo(CurrentOrderCard, (prevProps, nextProps) => isEqual(prevProps.orderInfo, nextProps.orderInfo))
