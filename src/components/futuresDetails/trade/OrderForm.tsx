import { formatPrice } from '@/components/futuresDetails/trade/tools'
import { useIsLoggedInOnArb } from '@/hooks/hyperliquid/useIsLoggedInOnArb'
import { cn } from '@/lib/utils'
import { symbolInfoSelector } from '@/redux/modules/futuresCurrentSymbol.slice'
import { selectSzMap } from '@/redux/modules/futuresMeta.slice'
import { futuresTradeConfigSelector } from '@/redux/modules/futuresTradeConfigs.slice'
import { fundingSelector } from '@/redux/modules/futuresUserInfo.slice'
import { resetOrderInfo, setOrderInfo } from '@/redux/modules/orderContract.slice'
import { RootState, useAppDispatch, useAppSelector } from '@/redux/store'
import { formatNumberWithCommas } from '@/utils/helpers'
import FilterSelect from '@components/common/FilterSelect'
import { Signature } from 'ethers'
import { memo, SetStateAction, useEffect, useMemo, useState } from 'react'
import ConfirmPlaceOrder from './ConfirmPlaceOrder'
import { orderTypeOptions } from './Constans'
import DepositDrawer from './DepositDrawer'
import FormLimitPrice from './FormLimitPrice'
import FormMarketPrice from './FormMarketPrice'
import FormPhased from './FormPhased'
import FormTpSl from './FormTpSl'
import FormTwap from './FormTwap'
import SwitchWalletDrawer from './SwitchWalletDrawer'
import TradingButton from './TradingButton'
import { OrderContractState, OrderSide, OrderTypeEnum } from './type.order'
import WalletAuthorizationDrawer from './WalletAuthorizationDrawer'

export interface IPOrderForm {
  signature?: Signature
  baseCoin: string
  containerClassName?: string
}


const OrderForm = memo(({ baseCoin, containerClassName }: IPOrderForm) => {
  const dispatch = useAppDispatch()
  const { price: symbolPrice, szDecimals } = useAppSelector(symbolInfoSelector)
  
  // const tradeConfigs = useAppSelector(futuresTradeConfigSelector(baseCoin))

  const { orderInfo } = useAppSelector<RootState, OrderContractState>((state) => state.orderContract)
  const { available } = useAppSelector(fundingSelector)

  const szMap = useAppSelector(selectSzMap)
  const { leverage } = useAppSelector(futuresTradeConfigSelector(baseCoin))

  const isLogin = useIsLoggedInOnArb()

  const [openWalletDrawer, setOpenWalletDrawer] = useState(false)
  const [openDepositDrawer, setOpenDepositDrawer] = useState(false)

  // 计算订单价值（USD）
  const calculatePositionValue = useMemo(() => {
    const entryPrice = Number(symbolPrice)
    const positionSize = Number(orderInfo.size)
    if (!entryPrice || !positionSize) return 0
    
    return orderInfo.currency === 'USD'
    ? positionSize
    : positionSize * entryPrice


  }, [symbolPrice, orderInfo.size, orderInfo.currency])

  // 计算所需保证金（USD）
  const calculateMarginRequired = useMemo(() => {
      if (!calculatePositionValue || !leverage) return 0
      return calculatePositionValue / Number(leverage)
  }, [calculatePositionValue, leverage])

  // 计算强平价格（USD）
  const calculateLiquidationPrice = useMemo(() => {

    const entryPrice =  orderInfo.type === OrderTypeEnum.market ? Number(symbolPrice) : orderInfo.price;
    let positionSize 
    if (orderInfo.currency === 'USD') {
      positionSize = Number(orderInfo.size);
    } else {
      positionSize =  Number(orderInfo.size) * Number(symbolPrice);
    }
    if (!entryPrice || !positionSize || !leverage) return '-';

    const MAINTENANCE_MARGIN_RATE =  0.005;
    let newPrice = 0
    // USD 本位
    if (orderInfo.side === OrderSide.buy) {
      newPrice = entryPrice * (1 - (1 / Number(leverage)) + MAINTENANCE_MARGIN_RATE);
    } else {
      newPrice = entryPrice * (1 + (1 / Number(leverage)) - MAINTENANCE_MARGIN_RATE);
    }

    return parseFloat(formatPrice(newPrice, szDecimals))
  }, [symbolPrice, orderInfo, leverage])

  const calcList = useMemo(() => {
    return [
      /* {
        label: '强平价格',
        value: `${calculateLiquidationPrice}`,
      }, */
      {
        label: '订单价值',
        value: `$${formatNumberWithCommas(calculatePositionValue.toFixed(2))}`,
      },

      {
        label: '保证金',
        value: `$${formatNumberWithCommas(calculateMarginRequired.toFixed(2))}`,
      },
    ]

  },[calculateLiquidationPrice, calculatePositionValue, calculateMarginRequired])

  useEffect(() => {
    if (!baseCoin) return
    if (orderInfo.currency === 'USD') {
      dispatch(resetOrderInfo({ currency: 'USD' }))
    } else {
      dispatch(resetOrderInfo({ currency: baseCoin }))
    }
    dispatch(
      setOrderInfo({
        ...orderInfo,
        type: OrderTypeEnum.market,
        price: Number(symbolPrice),
        size:0
      }),
    )
  }, [dispatch, baseCoin])

  return (
    <div className={cn("py-2.5 px-2  rounded-[6px]", containerClassName)}>
      <div className="flex justify-between items-center mb-3">
        <div className="text-[calc(12rem/16)] leading-[calc(12rem/16)]">账户可用</div>
        <div className="flex items-center text-[calc(11rem/16)] leading-[calc(11rem/16)]">
          {formatNumberWithCommas(available.toString())} USDC
          {isLogin ? (
            <img
              className="ml-1 cursor-pointer"
              src="/images/futuresDetail/add-icon.svg"
              onClick={() => {
                setOpenDepositDrawer(true)
              }}
            />
          ) : (
            <img className="ml-1" src="/images/futuresDetail/add-icon.svg" />
          )}
        </div>
      </div>

      <div className="flex w-full mb-3 text-center relative">
        <TradingButton />
      </div>

      <FilterSelect
        options={orderTypeOptions}
        triggerIcon="/images/tokenDetail/icon-chevron-down.svg"
        triggerIconClassname="w-[8.42px] h-[5.14px] absolute top-[50%] translate-y-[-50%] right-[7.69px]"
        defaultValue={orderTypeOptions[0].value}
        selectTriggerProps={{
          className:
            'w-full max-w-auto justify-center bg-[#ECECED14] rounded-[4px] border-[#ECECED1F] relative p-[7px] app-font-medium text-[calc(1rem*(14/16))] leading-[1rem])] text-white mb-[12px]',
        }}
        onValueChange={(e) => {
          dispatch(
            setOrderInfo({
              ...orderInfo,
              type: e as OrderTypeEnum,
              price: Number(symbolPrice),
            }),
          )
        }}
        childrenTrigger={
          <>
            {(orderInfo.type === 'limit' || orderInfo.type === 'tpsl' || orderInfo.type === 'phased') && (
              <img
                className="absolute top-1/2 left-1 translate-y-[-50%] w-[16px] min-w-[16px]"
                src="/images/orderSetting/icon-info.svg"
                alt=""
              />
            )}
          </>
        }
      />

      {orderInfo.type === 'market' && <FormMarketPrice calcInfo={calcList} />}
      {orderInfo.type === 'limit' && <FormLimitPrice calcInfo={calcList} />}
      {orderInfo.type === 'tpsl' && <FormTpSl />}
      {orderInfo.type === 'phased' && <FormPhased />}
      {orderInfo.type === 'twap' && <FormTwap />}
      <ConfirmPlaceOrder
        orderMargin={calculateMarginRequired}
        orderValue={calculatePositionValue }
        available={available}
        orderInfo={orderInfo}
        orderSide={orderInfo.side}
        baseCoin={baseCoin}
        price={symbolPrice.toString()}
        liqPrice={calculateLiquidationPrice.toString()}
      />
      <SwitchWalletDrawer open={openWalletDrawer} setOpenWalletDrawer={setOpenWalletDrawer} />
      <DepositDrawer open={openDepositDrawer} setOpenDrawer={setOpenDepositDrawer} />
      <WalletAuthorizationDrawer
        open={false}
        setOpen={function (value: SetStateAction<boolean>): void {
          throw new Error('Function not implemented.')
        }}
        initialCompletedSteps={[1]}
      />
    </div>
  )
})

export default OrderForm
