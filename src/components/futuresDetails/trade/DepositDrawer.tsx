import AppDrawer from '@/components/common/AppDrawer'
import ButtonShadowGradient from '@/components/common/buttons/ButtonShadowGradient'
import LogoWith<PERSON>hain from '@/components/common/LogoWithChain'
import { HYPERLIQUID_BRIDGE_ADDRESS, USDC_ABI, USDC_ADDRESS_ARBITRUM } from '@/components/transfer/constants'
import { BrowserProvider, Contract, ethers, parseUnits } from 'ethers'
import { FC, SetStateAction, useEffect, useState } from 'react'
import { createPublicClient, http, formatUnits } from 'viem'
import { arbitrum } from 'viem/chains'
import { useAccount } from 'wagmi'

interface DepositDrawerProps {
  open: boolean
  setOpenDrawer: React.Dispatch<SetStateAction<boolean>>
}

const MIN_AMOUNT: number = 10 // todo: should be set to 10

const DepositDrawer: FC<DepositDrawerProps> = ({ open, setOpenDrawer }) => {
  const { connector: activeConnector, address } = useAccount()

  const [balance, setBalance] = useState<string>('0')
  const [amount, setAmount] = useState<string | null>(null)
  const [provider, setProvider] = useState<ethers.BrowserProvider | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const [walletLogo, setWalletLogo] = useState<string>()
  // Clear input when drawer opens
  useEffect(() => {
    if (open) {
      setAmount(null)
      setIsLoading(false) // Reset loading state when drawer opens
    }
  }, [open])

  useEffect(() => {
    if (activeConnector) retrieveBalance()
  }, [address])

  useEffect(() => {
    if (activeConnector) {
      switch (activeConnector.name) {
        case 'MetaMask':
          setWalletLogo('/images/wallets/meta-mark-connect.png')
          break;
        case 'Bitget':
          setWalletLogo('/images/wallets/ic-bitget-wallet.svg')
          break;
        default:
          setWalletLogo('/images/wallets/boss-wallet.png')
          break;
      }
    }
  }, [address])

  const handleMaxIn = () => {
    setAmount(balance)
  }

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setAmount(Number(value) > Number(balance) ? balance : value)
  }

  const handleDeposit = async () => {
    if (!amount || Number(amount) <= 0) return

    setIsLoading(true)

    try {
      // switch to arbitrum
      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: '0xa4b1' }],
      })

      const newProvider = new BrowserProvider(window.ethereum, 'any')
      setProvider(newProvider) // Update the state with the new provider
      const network = await newProvider.getNetwork()

      if (network.chainId !== BigInt(42161)) {
        throw new Error('Failed to switch to Arbitrum')
      }

      const signer = await newProvider.getSigner()

      const usdc = new Contract(USDC_ADDRESS_ARBITRUM, USDC_ABI, signer)
      const decimals = 6

      const parsedAmount = parseUnits(amount + '', decimals)

      const tx = await usdc.transfer(HYPERLIQUID_BRIDGE_ADDRESS, parsedAmount)
      const receipt = await tx.wait()

      if (receipt.hash) {
        setAmount(null)
        setOpenDrawer(false)
        // Refresh balance after successful deposit
        await retrieveBalance()
      }
    } catch (error) {
      console.error('Deposit failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const retrieveBalance = async () => {
    // Create a public client for Arbitrum
    const arbitrumClient = createPublicClient({
      chain: arbitrum,
      transport: http(
        'https://summer-distinguished-breeze.arbitrum-mainnet.quiknode.pro/6681c4b31fa02ee860081babc885d918c856a012/',
      ),
    })

    // Read USDC balance using the public client
    const balance = (await arbitrumClient.readContract({
      address: USDC_ADDRESS_ARBITRUM,
      abi: [
        {
          constant: true,
          inputs: [{ name: 'account', type: 'address' }],
          name: 'balanceOf',
          outputs: [{ name: '', type: 'uint256' }],
          type: 'function',
        },
      ],
      functionName: 'balanceOf',
      args: [address],
    })) as bigint

    // Format balance with 6 decimals (USDC standard)
    const formattedBalance = formatUnits(balance, 6)
    setBalance(formattedBalance)
  }

  // Check if deposit button should be disabled
  const isDepositDisabled = Number(balance) < MIN_AMOUNT || !amount || Number(amount) <= 0 || isLoading

  return (
    <AppDrawer
      open={open}
      setOpen={setOpenDrawer}
      title="存入USDC至合约账户"
      drawerClassName="bg-[url('/images/tokenDetail/bg_top_100.png')] bg-no-repeat bg-center bg-cover"
      drawerHeaderClassName="py-[14px]"
      drawerContent={
        <div className="mt-2">
          <div className="rounded-[8px] px-3 py-[14px] bg-[#ECECED0A] border-[0.5px] border-[#ECECED14]">
            <div className="flex text-[#FFFFFFB2] items-center gap-2 text-sm">
              <img src={walletLogo} alt="" className="w-4 h-4" /> 存入地址
            </div>
            <p className="mt-3 break-all">{address}</p>
          </div>
          <div
            className="mt-4 px-[14px] py-3 rounded-[10px] relative z-[1] overflow-hidden"
            style={{ background: 'linear-gradient(145deg, #9945FF08,#00F3AB08)' }}
          >
            <svg
              className="absolute inset-0 w-full h-full z-[-1] pointer-events-none"
              xmlns="http://www.w3.org/2000/svg"
              preserveAspectRatio="none"
            >
              <rect
                x="0.25"
                y="0.25"
                width="99.5%"
                height="99.5%"
                rx="10"
                ry="10"
                fill="none"
                stroke="url(#gradient)"
                strokeWidth="0.5"
              />
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#9945FF" />
                  <stop offset="100%" stopColor="#00F3AB" />
                </linearGradient>
              </defs>
            </svg>
            <h5 className="text-sm text-[#FFFFFFB2]">存入数量</h5>
            <div className="px-3 py-[16.5px] bg-[#141414] mt-2 rounded-[8px] flex gap-2 justify-between">
              <div className="flex items-center justify-between gap-1">
                <LogoWithChain
                  logo="/images/withdrawal/usdc.png"
                  logoClassName="w-8"
                  name={'USDC'}
                  chainLogo="/images/arbitrum.svg"
                  chainContainerClassName="!bg-none right-0"
                  chainInnerClassName="w-[14px] h-[14px]"
                />
                <div className="flex flex-col gap-1">
                  <div className="text-sm text-white leading-3">USDC</div>
                  <div className="text-sm text-white/70 leading-3">Arbitrum</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <input
                  type="number"
                  placeholder="0.0"
                  value={amount}
                  onChange={(e) => {
                    const value = e.target.value
                    if (/^\d*\.?\d*$/.test(value)) {
                      handleAmountChange(e)
                    }
                  }}
                  className="w-full text-sm font-medium outline-none text-[calc(14rem/16)] text-end h-full"
                />
                <button className="text-sm text-[#00FFB4] font-medium min-w-[56px] ml-2" onClick={handleMaxIn}>
                  全部存入
                </button>
              </div>
            </div>
            <div className="mt-[8px]">
              <div className="flex justify-between">
                <span className="text-[#FFFFFFB2] text-[12px]">可用余额</span>
                <span className="text-[#FFFFFF] text-[13px]">{Number(balance)}</span>
              </div>
              {Number(balance) < MIN_AMOUNT ? <span className="text-[#FF353C] text-[12px]">余额不足</span> : ''}
            </div>
          </div>
          <div className="mt-3 text-xs text-[#FFFFFF80]">
            最低存入
            <span className="text-white ml-1">{MIN_AMOUNT} USDC</span>
          </div>
          <div className="flex justify-between mt-6 py-2.5">
            <div className="text-[#FFFFFF80] text-xs">
              {/* <span>到账数量</span>
              <div className="flex flex-col gap-1">
                <span>
                  <span className="text-white text-base">0.00</span>
                  <span className="text-[13px] ml-0.5">USDC</span>
                </span>
                <span>
                  手续费
                  <span className="text-white ml-0.5">0.00 USDC</span>
                </span>
              </div> */}
            </div>

            {Number(balance) < MIN_AMOUNT ? (
              <ButtonShadowGradient className=" rounded-[200px] w-36 max-w-36 h-11" disabled>
                金额过低
              </ButtonShadowGradient>
            ) : (
              <ButtonShadowGradient
                className="rounded-[200px] w-36 max-w-36 h-11"
                onClick={handleDeposit}
                disabled={isDepositDisabled}
              >
                {isLoading ? '处理中...' : '确认存入'}
              </ButtonShadowGradient>
            )}
          </div>
        </div>
      }
    />
  )
}

export default DepositDrawer
