import { cn } from '@/lib/utils.ts'
import { formatNumberWithCommas, removeFormatting } from '@/utils/helpers'
import {
  DetailedHTMLProps,
  InputHTMLAttributes,
  ReactNode,
  useState,
} from 'react'

type InputBorderGradientProps = {
  placeholder?: string
  unit?: string | ReactNode
  containerClassName?: string
  innerBgClassName?: string
  unitClassName?: string
  inputClassName?: string
  inputWrapperClassName?: string
  readonly?: boolean
  readonlyLabel?: string
  value?: string
  inputProps?: DetailedHTMLProps<InputHTMLAttributes<HTMLInputElement>, HTMLInputElement>
  onFocus?: () => void
  onBlur?: () => void
  onChange?: (value: string) => void
  formatThousands?: boolean
}

const isValidDecimalInput = (value: string) => {
  if (
    value === '' ||
    value === '0' ||
    value === '.' ||
    value === '0.' ||
    /^[0-9]*\.?[0-9]*$/.test(value)
  ) {
    return value.split('.').length <= 2
  }
  return false
}

const InputBorderGradient = ({
  placeholder,
  unit,
  containerClassName,
  innerBgClassName,
  inputClassName,
  inputWrapperClassName,
  unitClassName,
  readonly = false,
  readonlyLabel,
  value,
  inputProps = {},
  onFocus,
  onBlur,
  onChange,
  formatThousands = false,
}: InputBorderGradientProps) => {
  const [isFocus, setFocus] = useState(false)

  const handleFocus = () => {
    setFocus(true)
    onFocus?.()
  }

  const handleBlur = () => {
    setFocus(false)
    onBlur?.()
  }

  const handleValueChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputVal = event.target.value
    const cleanValue = removeFormatting(inputVal)
    
    if (isValidDecimalInput(cleanValue)) {
      onChange?.(cleanValue)
    }
  }

  const showFloatingLabel = !readonly && (isFocus || value)
  const showReadonly = readonly && readonlyLabel !== undefined
  
  const displayValue = formatThousands && value ? formatNumberWithCommas(value) : value

  return (
    <div
      className={cn(
        'relative h-[40px] py-1 px-2 flex items-center bg-[#ECECED14] rounded-[4px] after:content-[""] after:absolute after:inset-[1px] after:rounded-[4px] after:z-0 after:pointer-events-none',
        isFocus && 'bg-[linear-gradient(90deg,#9945FF,#00F3AB)]',
        containerClassName,
      )}
    >
      <div
        className={cn(
          'absolute inset-[1px] bg-[#141414] rounded-[4px] z-0',
          innerBgClassName,
        )}
      />

      <div className="flex items-center justify-between gap-[10px] relative z-1 w-full">
        <div className={cn('flex-1 max-w-[calc(100%-50px)]', inputWrapperClassName)}>
          {showFloatingLabel && (
            <div className="text-[10px] leading-[10px] text-[#FFFFFF5C]">
              {placeholder}
            </div>
          )}
          {showReadonly ? (
            <div className="text-[14px] leading-[14px] text-white">
              {readonlyLabel}
            </div>
          ) : (
            <input
              {...inputProps}
              value={displayValue}
              placeholder={placeholder}
              readOnly={readonly}
              className={cn(
                'app-font-regular text-[#00FFB4] focus:placeholder-transparent placeholder:text-[14px] placeholder:text-[#FFFFFF80] text-[14px] w-full h-[14px] leading-[1] outline-0 bg-transparent',
                inputClassName,
              )}
              onChange={handleValueChange}
              onFocus={handleFocus}
              onBlur={handleBlur}
            />
          )}
        </div>

        {unit && (
          <span
            className={cn(
              'app-font-regular text-[12px] flex items-center text-center',
              isFocus ? 'text-white' : 'text-[#FFFFFF5C]',
              unitClassName,
            )}
          >
            {unit}
          </span>
        )}
      </div>
    </div>
  )
}

export default InputBorderGradient