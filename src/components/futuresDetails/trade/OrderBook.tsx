import { useOrderBookData } from '@/hooks/hyperliquid/useOrderBookData'
import { useOrderTradeData } from '@/hooks/hyperliquid/useOrderTradeData'
import { cn } from '@/lib/utils'
import { coinOptionsSelector, setSymbolInfo, symbolInfoSelector } from '@/redux/modules/futuresCurrentSymbol.slice'
import { selectTiersBySymbol } from '@/redux/modules/futuresMeta.slice'
import { futuresTradeConfigSelector } from '@/redux/modules/futuresTradeConfigs.slice'
import {
  futuresTradePreferencesActions,
  selectFuturesTradePreferences,
} from '@/redux/modules/futuresTradePreferences.slice'
import { useAppDispatch, useAppSelector } from '@/redux/store'
import { formatNumberWithCommas } from '@/utils/helpers'
import {useEffect, useMemo, useRef, useState } from 'react'
import { setOrderInfo } from '@/redux/modules/orderContract.slice'
import { OrderContractState} from './type.order'
interface DepthItem {
  price: number
  quantity: number
}

interface ProcessedDepthItem extends DepthItem {
  percent: number
  quoteQuantity: number
}

const OrderBook = () => {
  const dispatch = useAppDispatch()
  const containerRef = useRef<HTMLDivElement>(null)
  const [recordCount, setRecordCount] = useState(5)
  const { baseCoin, quoteCoin, price, markPrice, change, szDecimals, lastTradePrice} = useAppSelector(symbolInfoSelector)
  const coinOptions = useAppSelector(coinOptionsSelector)
  const { depthUnit, depthLayout } = useAppSelector(selectFuturesTradePreferences)
  const { orderInfo } = useAppSelector<RootState, OrderContractState>((state) => state.orderContract)
  const tradeConfigs = useAppSelector(futuresTradeConfigSelector(baseCoin))
  const depthTick = Number(tradeConfigs.depthTick)

  const tiers = useAppSelector(selectTiersBySymbol(baseCoin)) || []

  const tierParams = useMemo(() => {
    const tier = tiers.find((item: any) => item.tick === depthTick)
    return {
      nSigFigs: tier?.nSigFigs,
      mantissa: tier?.mantissa,
    }
  }, [tiers, depthTick])
  
  // 缓存上一次价格
  const lastPriceRef = useRef<string | null>(null);
  
  // 使用 useOrderTradeData 获取最新交易数据
  const trades = useOrderTradeData(baseCoin);
  
  // 当交易数据更新时，更新最新成交价
  useEffect(() => {
    if (trades.length > 0) {
      const latestTrade = trades[0];
      const newPrice = String(latestTrade.price);
      
      // 只有价格发生变化时才更新
      if (newPrice !== lastPriceRef.current) {
        lastPriceRef.current = newPrice;
        dispatch(setSymbolInfo({ lastTradePrice: newPrice }));
      }
    }
  }, [trades, dispatch]);

  const { bids, asks } = useOrderBookData(baseCoin, tierParams.nSigFigs, tierParams.mantissa)


  const showDepthUnit = useMemo(() => {
    return depthUnit === 'base' ? baseCoin : quoteCoin
  }, [depthUnit, baseCoin, quoteCoin])

  const sumDepth = (array: DepthItem[], isReversed: boolean): ProcessedDepthItem[] => {
    const ordered = isReversed ? [...array].reverse() : array

    let cumulative = 0
    const maxQuantity = ordered.reduce((max, item) => {
      cumulative += item.quantity
      return Math.max(max, cumulative)
    }, 0)

    cumulative = 0
    const processed = ordered.map((item) => {
      cumulative += item.quantity
      const fix = depthUnit === 'base' ? szDecimals : 0
      
      /* const quantity = Number(cumulative.toFixed(fix))
      const quoteQuantity = Number((item.price * cumulative).toFixed(fix)) */

      
      const percent = maxQuantity > 0
        ? Number(((cumulative / maxQuantity) * 100).toFixed(2))
        : 0

      return {
        ...item,
        quantity: Number(item.quantity),
        quoteQuantity: Number((item.price * item.quantity)),
        percent,
      }
    })

    return isReversed ? processed.reverse() : processed
  }

  useEffect(() => {
    if (!containerRef.current) return

    const calculateRecordCount = (containerHeight: number) => {
      // 基础高度计算
      const topHeight = 12
      const middleHeight = 44
      const rowHeight = 19
      
      // 获取可用高度
      let availableHeight = containerHeight - topHeight - middleHeight
      
      // 根据布局模式计算记录数
      const count = depthLayout === 'showAll'
        ? Math.max(3, Math.floor(availableHeight / 2 / rowHeight))
        : Math.max(6, Math.floor(availableHeight / rowHeight))
      
      // 限制最大记录数，防止过长
      return Math.min(count, 10) // 设置一个合理的最大值
    }

    const updateRecordCount = () => {
      if (!containerRef.current) return
      const containerHeight = containerRef.current.getBoundingClientRect().height
      const count = calculateRecordCount(containerHeight)
      setRecordCount(count)
    }

    updateRecordCount()

    const resizeObserver = new window.ResizeObserver(() => {
      updateRecordCount()
    })
    resizeObserver.observe(containerRef.current)

    return () => {
      resizeObserver.disconnect()
    }
  }, [depthLayout])

  const handlePriceClick = (price: number) => {
    console.log('price', price)
    dispatch(setOrderInfo({ ...orderInfo, price: price }))
  }

  const topAsks = useMemo(
    () => sumDepth(asks.reverse().slice(-(depthLayout !== 'showAll' ? recordCount * 2 : recordCount)), true),
    [asks, recordCount, depthLayout],
  )
  const topBids = useMemo(
    () => sumDepth(bids.slice(0, depthLayout !== 'showAll' ? recordCount * 2 : recordCount), false),
    [bids, recordCount, depthLayout],
  )

  const renderOrderRecords = (items: ProcessedDepthItem[], isDown = false, fix: number) => (
    <div className={cn('flex-1', isDown ? 'flex flex-col justify-end' : '')}>
      {items.map((item, index) => (
        <OrderRecord
          key={index}
          className={cn("relative mb-0.5 py-1", isDown ? "orderbook-sell" : "orderbook-buy")}
          style={{ '--after-width': `${item.percent}%` } as React.CSSProperties}
          price={item.price}
          quantity={depthUnit === 'base' ? item.quantity.toFixed(fix) : item.quoteQuantity.toFixed(fix)}
          isDown={isDown}
          onClick={() => handlePriceClick(item.price)}
        />
      ))}
    </div>
  )

  const fix = depthUnit === 'base' ? szDecimals : 0

  // const handleDepthUnitChange = (value: string) => {
  //   if (value === showDepthUnit) return
  //   dispatch(
  //     futuresTradePreferencesActions.updateTradePreferences({
  //       depthUnit: baseCoin === value ? 'base' : 'quote',
  //     }),
  //   )
  // }

  return (
    <div ref={containerRef} className="flex-1 flex flex-col overflow-hidden">
      <div className="flex items-center justify-between text-[#FFFFFFB2] mb-1 h-[calc(1rem*(12/16))]">
        <div className="text-[calc(1rem*(10/16))] leading-1">
          价格
          <span className="text-[calc(1rem*(9/16))] leading-1">(USD)</span>
        </div>

        {/* <DrawerCheckSelect
          childrenTrigger={
            <div className="cursor-pointer text-[calc(1rem*(10/16))] leading-1 flex items-center">
              数量
              <span className="text-[calc(1rem*(9/16))] leading-1">({showDepthUnit})</span>
              <img src="/images/futuresDetail/select-down-icon.svg" />
            </div>
          }
          options={coinOptions}
          value={showDepthUnit}
          onChange={(value: string) => handleDepthUnitChange(value)}
        /> */}
        <div
          className="cursor-pointer text-[calc(1rem*(10/16))] leading-1 flex items-center cursor-pointer"
          onClick={() => {
            dispatch(
              futuresTradePreferencesActions.updateTradePreferences({
                depthUnit: depthUnit === 'base' ? 'quote' : 'base',
              }),
            )
          }}
        >
          数量
          <span className="text-[calc(1rem*(9/16))] leading-1">({showDepthUnit})</span>
          <img src="/images/futuresDetail/select-down-icon.svg" />
        </div>
      </div>

      {(depthLayout === 'showAsks' || depthLayout === 'showAll') && renderOrderRecords(topAsks, true, fix)}

      <div className="py-2 h-[44px]">
        <div className={cn('text-[1rem] leading-[1rem] mb-1 text-center',
          parseFloat(change) >= 0 ? 'text-rise' : 'text-fall')}>
          {lastTradePrice && lastTradePrice !== '0' 
            ? formatNumberWithCommas(lastTradePrice) 
            : formatNumberWithCommas(price)}
        </div>
        <div className="flex justify-center items-center text-[calc(11rem/16)] leading-[calc(11rem/16)]">
          <p className="flex items-center">
            <img className="mr-1" src="/images/futuresDetail/maker-icon.svg" />
            {markPrice}
          </p>
          {/* <p className="flex items-center text-[#FFFFFFB2]">
            更多
            <img src="/images/futuresDetail/arrow-right.svg" />
          </p> */}
        </div>
      </div>

      {(depthLayout === 'showBids' || depthLayout === 'showAll') && renderOrderRecords(topBids, false ,fix)}
    </div>
  )
}

type OrderRecordProps = {
  price: number
  quantity: number | string
  isDown?: boolean
  className?: string
  style?: React.CSSProperties
  onClick?: () => void
}

const OrderRecord = ({ price, quantity, isDown, className, style, onClick }: OrderRecordProps) => {
  return (
    <div
      className={cn(
        'flex items-center justify-between gap-[10px] app-font-medium text-[calc(1rem*(11/16))] leading-[1] mb-[11.55px] last:mb-0',
        className,
      )}
      style={style}
      onClick={onClick}
    >
      <div className={cn('text-[#36D399]', isDown && 'text-[#AB57FF]')}>{formatNumberWithCommas(`${price}`)}</div>
      <div className="text-[#FFFFFFCC]">{formatNumberWithCommas(`${quantity}`)}</div>
    </div>
  )
}

export default OrderBook
