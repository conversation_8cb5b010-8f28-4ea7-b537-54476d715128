import { coinOptionsSelector } from '@/redux/modules/futuresCurrentSymbol.slice.ts'
import { RootState, useAppSelector } from '@/redux/store.ts'
import { UITab } from '@/types/uiTabs'
import SliderGradient from '@components/orderForm/SliderGradient.tsx'
import useHandleChangeValue from '../hooks/useHandleChangeValue.tsx'
import CalcOrderInfo from './CalcOrderInfo.tsx'
import GroupOption from './GroupOption.tsx'
import InputBorderGradient from './InputBorderGradient.tsx'
import { OrderContractState } from './type.order.ts'
import { useEffect } from 'react'

interface FormMarketPriceProps {
  calcInfo: UITab[]
}

const FormMarketPrice = ({ calcInfo }: FormMarketPriceProps) => {
  const {
    orderInfo: { size, currency, sliderValue },
  } = useAppSelector<RootState, OrderContractState>((state) => state.orderContract)
  const coinOptions = useAppSelector(coinOptionsSelector)
  // console.log(size,'ssss')
  const { handleSizeChange, onSliderValueChange, handleOrderInfoChange } = useHandleChangeValue()

  return (
    <div>
      <InputBorderGradient
        unit={
          <div
            className="text-[#FFFFFF] cursor-pointer"
            onClick={() => {
              handleOrderInfoChange('currency', currency === coinOptions[0].value ? coinOptions[1].value : coinOptions[0].value)
            }}
          >
            <div className="flex items-center">
              <span>{currency}</span>
              <img className="ml-1" src="/images/futuresDetail/arrow-swap-icon.svg" />
            </div>
          </div>
        }
        placeholder="数量"
        value={`${size || ''}`}
        onChange={handleSizeChange}
        innerBgClassName="!bg-[#14141480]"
        containerClassName="mb-[16px] w-full"
        inputClassName="flex-1"
        formatThousands={true}
      />

      <SliderGradient
        containerClassName="mb-[35px]"
        sliderValue={sliderValue}
        onSliderValueChange={onSliderValueChange}
      />

      <GroupOption />

      <CalcOrderInfo calcList={calcInfo} />
    </div>
  )
}

export default FormMarketPrice
