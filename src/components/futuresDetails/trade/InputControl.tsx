import { cn } from '@/lib/utils.ts'
import { formatNumberWithCommas, removeFormatting } from '@/utils/helpers'
import { Button } from '@components/ui/button.tsx'
import React, { DetailedHTMLProps, InputHTMLAttributes, useEffect, useRef, useState } from 'react'

interface InputControl {
  placeholder?: string
  value: string
  unit?: string
  inputClassName?: string
  onChange?: (value: string) => void
  colorBg?: string
  inputProps?: DetailedHTMLProps<InputHTMLAttributes<HTMLInputElement>, HTMLInputElement>
  onMinus?: () => void
  onPlus?: () => void
  formatThousands?: boolean
}

const InputControl = ({
  placeholder,
  inputClassName,
  unit,
  value,
  onChange,
  inputProps,
  colorBg,
  onMinus,
  onPlus,
  formatThousands = false,
}: InputControl) => {
  const [isFocus, setFocus] = useState<boolean>(false)
  const [isAnimating, setIsAnimating] = useState<boolean>(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const prevValueRef = useRef<string>(value)

  // 检测值变化并触发动画
  useEffect(() => {
    if (value !== prevValueRef.current) {
      setIsAnimating(true);
      const timer = setTimeout(() => {
        setIsAnimating(false);
      }, 150); // 减少动画持续时间
      prevValueRef.current = value;
      return () => clearTimeout(timer);
    }
  }, [value]);

  const handleFocus = () => {
    inputRef?.current?.focus()
    setFocus(true)
  }

  const handleBlur = () => {
    setFocus(false)
  }

  const handleMinus = () => {
    if (!isFocus) {
      handleFocus()
    }
    setIsAnimating(true)
    onMinus?.()
    setTimeout(() => setIsAnimating(false), 150)
  }

  const handlePlus = () => {
    if (!isFocus) {
      handleFocus()
    }
    setIsAnimating(true)
    onPlus?.()
    setTimeout(() => setIsAnimating(false), 150)
  }
  
  const handleValueChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value
    const cleanValue = removeFormatting(value)
    if (onChange) {
      onChange(cleanValue)
    }
  }

  const displayValue = formatThousands && value ? formatNumberWithCommas(value) : value

  return (
    <div
      className={cn(
        'rounded-[4px] relative h-[40px] px-[1.5px] py-[1px] mb-3 bg-[#ECECED14]',
        isFocus && 'bg-[linear-gradient(90deg,#9945FF,#00F3AB)]',
      )}
    >
      <div className={cn('absolute inset-[1px] bg-[#141414] rounded-[4px] z-0', colorBg)} />
      <div className="flex items-center justify-between gap-0 relative z-1 h-full">
        <Button
          onClick={handleMinus}
          className="w-[22px] min-w-[22px] p-0 bg-none rounded-[4px] bg-[#ECECED0A] flex items-center justify-center z-10"
        >
          <img src="/images/orderForm/icon-minus.svg" className="w-[14px] min-w-[14px]" alt="" />
        </Button>
        <div className="max-w-[calc(100%-102px)] min-w-[auto] flex-1 flex justify-center">
          <div>
            {isFocus && (
              <div className="text-[calc(1rem*(10/16))] text-center leading-[calc(1rem*(10/16))] text-[#FFFFFF5C]">
                {placeholder}
              </div>
            )}
            <input
              ref={inputRef}
              {...inputProps}
              value={displayValue}
              onChange={(event) => handleValueChange(event)}
              placeholder={placeholder}
              className={cn(
                'placeholder:text-[calc(14rem/16)] focus:placeholder-transparent app-font-regular text-[#00FFB4] text-center placeholder:text-[#FFFFFFCC] text-[calc(1rem*(15/16))] leading-[1] outline-0',
                isAnimating && 'scale-103 transition-transform duration-150',
                // isAnimating && 'number-change-animation',
                inputClassName,
              )}
           

              onFocus={handleFocus}
              onBlur={handleBlur}
            />
          </div>
        </div>

        <Button
          onClick={handlePlus}
          className="w-[22px] min-w-[22px] p-0 bg-none rounded-[4px] bg-[#ECECED0A] flex items-center justify-center"
        >
          <img src="/images/orderForm/icon-plus.svg" className="w-[14px] min-w-[14px]" alt="" />
        </Button>
      </div>
    </div>
  )
}

export default InputControl
