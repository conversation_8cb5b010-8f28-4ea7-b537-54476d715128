import { useTranslation } from 'react-i18next'
import FilterSelect, { FilterSelectOption } from '@components/common/FilterSelect'
import CheckboxWithLabel from '@components/common/CheckboxWithLabel.tsx'
import { OrdersListFilter } from '.'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogOverlay,
} from '@/components/ui/dialog'
import DrawerCheckSelect from '@components/common/DrawerCheckSelect.tsx'
import { Button } from '@/components/ui/button'
import { useState, useMemo } from 'react'
import { FilterTypeOption, FilterSideOption, OrderType, OrderSide, All } from '../types'

interface CurrentOrdersFilterProps {
  filter: OrdersListFilter
  setFilter: (filter: OrdersListFilter) => void
  onClickCloseAll: () => void
}

const CurrentOrdersFilter = ({ filter, setFilter, onClickCloseAll }: CurrentOrdersFilterProps) => {
  const { t } = useTranslation()

  const directionsOptions: FilterSideOption[] = [
    {
      label: '全部方向',
      value: 'All',
    },
    {
      label: '做多',
      value: 'B',
    },
    {
      label: '做空',
      value: 'A',
    },
  ]

  const [direction, setDirection] = useState<string>(directionsOptions[0].value)

  const handleDirectionChange = (direction: string) => {
    setDirection(direction)

    if (setFilter) {
      setFilter({
        ...filter,
        side: direction as OrderSide | All,
      })
    }
  }

  const directionText = useMemo(() => {
    return directionsOptions.find((item) => {
      return item.value === direction
    })?.label
  }, [directionsOptions, direction])

  return (
    <div className="mb-[8px]">
      <div className="flex items-center justify-between gap-[8px] mb-[12px]">
        <div className="flex gap-[8px]">
          <DrawerCheckSelect
            childrenTrigger={
              <div className='flex items-center bg-[#ECECED1F] rounded-full px-[10px] py-[5px] min-w-[92px]  h-[26px]  text-[#FFFFFF] text-[13px] leading-[1] app-font-regular'>
                <div className="cursor-pointer text-[#FFFFFF] text-[13px] leading-[1] app-font-regular w-[calc(100%_-_10px)]">
                  {directionText}
                </div>
                <img className="ml-1" src="/images/futuresDetail/order-arrow-down.svg" />
              </div>
              
            }
            options={directionsOptions}
            value={direction}
            onChange={handleDirectionChange}
          />
        </div>

        <CloseAllPosition onClickCloseAll={onClickCloseAll} />
      </div>

      <CheckboxWithLabel
        label={t('currentOrdersList.showCurrentCoinOnly')}
        isChecked={filter?.showOnlyBaseCoin}
        onChange={() => {
          setFilter({
            ...filter,
            showOnlyBaseCoin: !filter?.showOnlyBaseCoin,
          })
        }}
      />
    </div>
  )
}

interface CloseAllPositioinProps {
  onClickCloseAll: () => void
}

const CloseAllPosition = ({ onClickCloseAll }: CloseAllPositioinProps) => {
  const [open, setOpen] = useState(false)

  const handleConfirm = () => {
    onClickCloseAll()
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button 
          variant={'ghost'} 
          className="p-0  text-[#FFFFFF] text-[calc(12rem/16)] h-[calc(12rem/16)]"
        >
          {' '}
          一键全部平仓
        </Button>
      </DialogTrigger>
      <DialogContent
        onPointerDown={(event) => event.stopPropagation()}
        onClick={(event) => event.stopPropagation()}
        className="w-[335px] bg-[#232329] rounded-2xl p-5"
      >
        <DialogHeader>
          <DialogTitle className="text-center">
            <p className="text-[calc(18rem/16)] py-3">确定以市价平仓全部订单？</p>
          </DialogTitle>
          <div className="flex justify-center items-center flex-row gap-2.5 mt-4">
            <Button variant="borderGradient" className="flex-1 rounded-[50px]" onClick={() => setOpen(false)}>
              取消
            </Button>
            <Button variant="gradient" className="text-[#261236] flex-1 rounded-[50px]" onClick={handleConfirm}>
              确定
            </Button>
          </div>
        </DialogHeader>
        <DialogDescription />
      </DialogContent>
    </Dialog>
  )
}

export default CurrentOrdersFilter
