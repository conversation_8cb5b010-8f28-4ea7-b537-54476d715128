import CardWithGradient from '@/components/common/CardWithGradient'
import Tag from '@/components/common/Tag'
import ToastCenterScreen from '@/components/futuresDetails/ToastCenterScreen'
import { Button } from '@/components/ui/button'
import ImgWithFallback from '@/components/common/ImgWithFallback'
import { useMemo, useRef, useState } from 'react'
import { xPositions, PositionModeValue, xOpenOrders } from '../types'
import { getPositionDescription } from '../tools'
import { showRate, cn } from '@/lib/utils'
import { z } from 'zod'
import MarketPriceCloseButton from './MarketPriceCloseButton'
import TpslButton from './TpslButton'
import { selectSzMap } from '@/redux/modules/futuresMeta.slice'
import { useAppSelector } from '@/redux/store.ts'
import { formatPrice } from '@/components/futuresDetails/trade/tools'
import { Configs } from '@/const/configs'
import dayjs from 'dayjs'
import { formatNumberWithCommas } from '@/utils/helpers'

interface MyPositionCardProps {
  positionInfo: xPositions
  onSetTpSl: (orders: xOpenOrders) => void
}

const MyPositionCard = ({ positionInfo, onSetTpSl }: MyPositionCardProps) => {
  const bgColor = positionInfo.side === 'B' ? 'green' : 'purple'
  const [showToast, setShowToast] = useState(false)

  const szMap = useAppSelector(selectSzMap)

  const positionDesc = getPositionDescription(
    positionInfo.leverage.type as PositionModeValue,
    positionInfo.leverage.value,
  )
  const unrealizedPnlText =
    parseFloat(positionInfo.unrealizedPnl) >= 0 ? `+${positionInfo.unrealizedPnl}` : positionInfo.unrealizedPnl

  const roeText = showRate(positionInfo?.returnOnEquity * 100)

  const liqPx = useMemo(() => {
    return parseFloat(formatPrice(positionInfo.liquidationPx, szMap[positionInfo.coin]))
  }, [positionInfo.liquidationPx, szMap])

  const tpPx = useMemo(() => {
    if (!positionInfo.tpPrice) return ''
    return parseFloat(formatPrice(positionInfo.tpPrice, szMap[positionInfo.coin])).toString()
  }, [positionInfo.tpPrice, szMap])

  const slPx = useMemo(() => {
    if (!positionInfo.slPrice) return ''
    return parseFloat(formatPrice(positionInfo.slPrice, szMap[positionInfo.coin])).toString()
  }, [positionInfo.slPrice, szMap])

  return (
    <>
      <CardWithGradient
        isHoverScaleCard={false}
        bgColor={bgColor as 'green' | 'purple'}
        header={
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <ImgWithFallback
                src={`${Configs.getHyperliquidConfig().imgUrl}/${positionInfo.coin}.svg`}
                srcFallback="/images/logo-pair-fallback.webp"
                sharedClassName="size-6 mr-2"
              />
              <div className="mr-1 text-[calc(14rem/16)] leading-[calc(14rem/16)] app-font-medium">
                {positionInfo.coin}USD永续
              </div>

              <div className="flex gap-1">
                <Tag
                  label={positionInfo.side === 'B' ? '做多' : '做空'}
                  color={positionInfo.side === 'B' ? '#00FFB4' : '#ab57ff'}
                  containerClassName="rounded-[4px] px-1 py-0.75"
                />

                <Tag
                  label={positionDesc}
                  color="#00FFF6"
                  containerClassName="!border-transparent bg-[#00FFF633] rounded-[4px] px-1 py-0.75"
                />
              </div>
            </div>

            {positionInfo?.timestamp && (
              <span className="text-xs leading-[calc(12rem/16)] text-[#FFFFFF80]">
                {dayjs(positionInfo?.timestamp).format('MM-DD HH:mm:ss')}
              </span>
            )}
          </div>
        }
        classNameHeader="px-3 py-2.5"
        content={
          <>
            <div className="p-3">
              <div className="flex items-center justify-between gap-1 mb-[14px]">
                <div>
                  <p className="text-[#FFFFFF80] mb-1.5 text-[calc(11rem/16)] leading-[calc(12rem/16)]">盈亏(USDT)</p>
                  <p
                    className={cn(
                      'flex items-center t leading-[calc(14rem/16)]',
                      parseFloat(positionInfo.unrealizedPnl) >= 0 ? 'text-rise' : 'text-fall',
                    )}
                  >
                    <span className="text-[calc(15rem/16)] font-semibold mr-1">{unrealizedPnlText}</span>
                    <span className="text-[calc(12rem/16)]">({roeText})</span>
                  </p>
                </div>

                <div className="text-right">
                  <p className="text-[#FFFFFF80] mb-1.5 text-[calc(11rem/16)] leading-[calc(12rem/16)]">
                    持仓量 ({positionInfo.coin})
                  </p>
                  <p className="text-[#FFFFFF] text-[calc(14rem/16)] leading-[calc(14rem/16)] app-font-medium">
                    {Math.abs(parseFloat(positionInfo.szi))}
                  </p>
                </div>
              </div>

              <table className="w-full ">
                <tbody>
                  <tr>
                    <td className="pb-3">
                      <div className="flex flex-col gap-3 col-span-2">
                        <div className="">
                          <p className="text-[#FFFFFF80] mb-1.5 text-[calc(11rem/16)] leading-[calc(12rem/16)]">
                            仓位价值 (USDT)
                          </p>
                          <p className="text-[#FFFFFF] text-[calc(13rem/16)] leading-[calc(14rem/16)] app-font-medium">
                            {positionInfo.positionValue}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="pb-3">
                      <div className="flex justify-center">
                        <div className="min-w-18">
                          <p className="text-[#FFFFFF80] mb-1.5 text-[calc(11rem/16)] leading-[calc(12rem/16)]">
                            保证金 (USDT)
                          </p>
                          <p className="text-[#FFFFFF] text-[calc(14rem/16)] leading-[calc(14rem/16)] app-font-medium">
                            {positionInfo.marginUsed}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="pb-3">
                      <div className="flex justify-end">
                        <div className="text-right">
                          <p className="text-[#FFFFFF80] mb-1.5 text-[calc(11rem/16)] leading-[calc(12rem/16)]">
                            强平价格
                          </p>
                          <p className="text-[#FFFFFF] text-[calc(14rem/16)] leading-[calc(14rem/16)] font-bold">
                            {formatNumberWithCommas(liqPx.toString())}
                          </p>

                          {/*  <p className={cn("text-[calc(14rem/16)] leading-[calc(14rem/16)] font-bold",
                            parseFloat(positionInfo?.cumFunding?.sinceOpen) >= 0 ? 'text-rise' : 'text-fall'
                          )}>
                          {positionInfo?.cumFunding?.sinceOpen}
                          </p> */}
                        </div>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <td className="">
                      <div>
                        <p className="text-[#FFFFFF80] mb-1.5 text-[calc(11rem/16)] leading-[calc(12rem/16)]">
                          开仓价格
                        </p>
                        <p className="text-[#FFFFFF] text-[calc(13rem/16)] leading-[calc(14rem/16)] font-bold">
                          {formatNumberWithCommas(positionInfo.entryPx)}
                        </p>
                      </div>
                    </td>
                    <td className="">
                      <div className="flex justify-center">
                        <div className="min-w-18">
                          <p className="text-[#FFFFFF80] mb-1.5 text-[calc(11rem/16)] leading-[calc(12rem/16)]">
                            标记价格
                          </p>
                          <p className="text-[#FFFFFF] text-[calc(14rem/16)] leading-[calc(14rem/16)] font-bold">
                            {formatNumberWithCommas(positionInfo.markPrice)}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="flex justify-end">
                      <TpslButton
                        info={{
                          ...positionInfo,
                          tpPrice: tpPx,
                          slPrice: slPx,
                        }}
                        onSetTpSl={onSetTpSl}
                        childrenTrigger={
                          <Button variant={'ghost'} className="p-0 h-[calc(14rem/16)] h-[44px]">
                            <div className="flex justify-end flex-col items-end">
                              <p className="text-[#FFFFFF80] mb-1.5 text-[calc(11rem/16)] leading-[calc(12rem/16)] flex justify-end items-center">
                                止盈 / 止损
                                <img className="ml-0.5" src="/images/futuresDetail/edit-icon.svg" />
                              </p>
                              <p className="text-[calc(14rem/16)] leading-[calc(14rem/16)] font-bold">
                                <span className="text-rise">{tpPx ? formatNumberWithCommas(tpPx) : '-'}</span>
                                <span className="text-[#FFFFFF80] mx-0.5">/</span>
                                <span className="text-fall">{slPx ? formatNumberWithCommas(slPx) : '-'}</span>
                              </p>
                            </div>
                          </Button>
                        }
                      />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div className="p-2.5 flex justify-between items-center border-t-[0.5px] border-solid border-[#ECECED14]">
              <div className="flex items-center gap-3">
                <TpslButton
                  info={{
                    ...positionInfo,
                    tpPrice: tpPx,
                    slPrice: slPx,
                  }}
                  onSetTpSl={onSetTpSl}
                  childrenTrigger={
                    <Button
                      variant={'borderGradient'}
                      className="w-[130px] rounded-[200px] text-[calc(12rem/16)] text-rise h-[calc(32rem/16)]"
                    >
                      止盈止损
                    </Button>
                  }
                />
                <MarketPriceCloseButton info={positionInfo} />
              </div>
              <Button variant={'ghost'} className="p-0 text-[#FFFFFF80]  h-[calc(20rem/16)]">
                <div className="flex items-center">
                  <img className="mr-1" src="/images/futuresDetail/share-icon.svg" />
                  <span className="text-[calc(13rem/16)]">分享</span>
                </div>
              </Button>
            </div>
          </>
        }
      />

      <ToastCenterScreen showModal={showToast} setShowModal={setShowToast} text="撤单成功" />
    </>
  )
}

export default MyPositionCard
