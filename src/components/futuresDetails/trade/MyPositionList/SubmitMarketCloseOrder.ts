import { signHyperLiquidCreateOrderMutation } from '@services/auth.service.ts'
import { userGqlClient } from '@/lib/gql/apollo-client'
import { Configs } from '@/const/configs'
import { xBuilderInfo } from '../types'




export const submitMarketCloseOrder = async ({
  coinIndex,
  isBuy,
  price,
  size,
  builder,
}: {
  coinIndex: number
  isBuy: boolean
  price: string
  size: string
  builder: xBuilderInfo
}) => {

  const order = {
    a: coinIndex,
    b: isBuy,
    p: price,
    r: true,
    s: size,
    t: { limit: { tif: 'FrontendMarket' } },
  }

  const action = {
    type: 'order',
    orders: [order],
    grouping: 'na',
    builder
  }

  const nonce = Date.now()

  const { data } = await userGqlClient.mutate({
    mutation: signHyperLiquidCreateOrderMutation,
    variables: {
      input: {
        action,
        nonce,
        vaultAddress: '',
      },
    },
  })

  const signature = data?.signHyperLiquidCreateOrder?.signature
  if (!signature) {
    throw new Error('获取代理钱包签名失败')
  }

  const res = await fetch(`${Configs.getHyperliquidConfig().apiUrl}/exchange`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      action,
      nonce,
      signature,
      vaultAddress: null,
    }),
  })

  const result = await res.json()

  return result
}
