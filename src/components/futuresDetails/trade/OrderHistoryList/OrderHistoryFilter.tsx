import { useTranslation } from 'react-i18next'
import FilterSelect, { FilterSelectOption } from '@components/common/FilterSelect'
import CheckboxWithLabel from '@components/common/CheckboxWithLabel.tsx'
import { OrdersListFilter } from '.'
import DrawerCheckSelect from '@components/common/DrawerCheckSelect.tsx'
import { useState, useMemo } from 'react'
import { OrderSide, All } from '../types'



interface CurrentOrdersFilterProps {
  filter: OrdersListFilter
  setFilter: (filter: OrdersListFilter) => void
  currentToken?: string
}

const CurrentOrdersFilter = ({ filter, setFilter }: CurrentOrdersFilterProps) => {
  const { t } = useTranslation()



  const directionsOptions: FilterSelectOption[] = [
    {
      label: '全部方向',
      value: 'All',
    },
    {
      label: '做多',
      value: 'Open Long',
    },
    {
      label: '做空',
      value: 'Open Short'
    },
    {
      label: '平多',
      value: 'Close Long'
    },
    {
      label: '平空',
      value: 'Close Short'
    },
  ]


  const [direction, setDirection] = useState<string>(directionsOptions[0].value)
  
  const handleDirectionChange = (direction: string) => {
    setDirection(direction)
    if(setFilter) {
      setFilter({
        ...filter,
        side:  direction as OrderSide | All
      })
    }
  }


  const directionText = useMemo(() => {
      return directionsOptions.find((item) => {
        return item.value === direction
      })?.label
  }, [directionsOptions, direction])

  

  return (
    <div className="mb-[8px]">
      <div className="flex items-center justify-between gap-[8px] mb-[12px]">
        <div className="flex gap-[8px]">


        <DrawerCheckSelect 
          childrenTrigger={
             <div className='flex items-center bg-[#ECECED1F] rounded-full px-[10px] py-[5px] min-w-[92px]  h-[26px]  text-[#FFFFFF] text-[13px] leading-[1] app-font-regular'>
             <div className="cursor-pointer text-[#FFFFFF] text-[13px] leading-[1] app-font-regular w-[calc(100%_-_10px)]">
               {directionText}
             </div>
             <img className="ml-1" src="/images/futuresDetail/order-arrow-down.svg" />
           </div>
          }
          options={directionsOptions}
          value={direction}
          onChange={handleDirectionChange}
        />

        </div>

        <CheckboxWithLabel
        label={t('currentOrdersList.showCurrentCoinOnly')}
        defaultChecked={filter?.showOnlyBaseCoin}
        onChange={() => {
          setFilter({
            ...filter,
            showOnlyBaseCoin: !filter?.showOnlyBaseCoin
          })
        }}
      />


      </div>


    



    </div>
  )
}



export default CurrentOrdersFilter


