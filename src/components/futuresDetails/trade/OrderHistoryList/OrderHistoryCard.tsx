import Tag from '@/components/common/Tag'
import { cn, showMathSymbol } from '@/lib/utils'
import ToastCenterScreen from '@/components/futuresDetails/ToastCenterScreen'
import { useState, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import CardWithGradient from '@/components/common/CardWithGradient'
import { xHistoryTrade } from '../types'
import dayjs from 'dayjs'
import ImgWithFallback from '@/components/common/ImgWithFallback'
import { Configs } from '@/const/configs'
import { formatNumberWithCommas } from '@/utils/helpers'



interface OrderHistoryCardProps {
  orderInfo: xHistoryTrade
}
const OrderHistoryCard = ({ orderInfo }: OrderHistoryCardProps) => {
  const [showToast, setShowToast] = useState(false)

  const bgColor = orderInfo.side === 'B' ? 'green' : 'purple'
  
  const formattedTime = useMemo(() => dayjs(orderInfo.time).format('MM-DD HH:mm:ss'), [orderInfo.time])
  
  const orderValue = useMemo(() => {
    return (parseFloat(orderInfo.px) * parseFloat(orderInfo.sz)).toFixed(2)
  }, [orderInfo.px, orderInfo.sz])

  const dirText = useMemo(() => {
    switch (orderInfo.dir) {
      case 'Close Long':
        return '平多'
      case 'Close Short':
        return '平空'
      case 'Open Long':
        return '开多'
      case 'Open Short':
        return '开空'
      default:
        return ''
    }
  },[orderInfo.dir])

  /* const notional = parseFloat(orderInfo.px) * parseFloat(orderInfo.sz)
  const adjusted_pnl = parseFloat(orderInfo.closedPnl) - parseFloat(orderInfo.fee) */

   
  


  return (
    <>
      <CardWithGradient
        bgColor={bgColor as 'green' | 'purple'}
        header={
          <div className="flex items-start justify-between">
            <div className="flex items-start">

              <ImgWithFallback
                src={`${Configs.getHyperliquidConfig().imgUrl}/${orderInfo.coin}.svg`}
                srcFallback="/images/logo-pair-fallback.webp"
                sharedClassName="size-7 mr-2"
              />
              <div className="mr-3">
                <p className="mb-1 text-[calc(14rem/16)] leading-[calc(14rem/16)]">{orderInfo.coin}USD永续</p>
                <p className="text-[#FFFFFF80] text-[calc(11rem/16)] leading-[calc(11rem/16)]">{formattedTime}</p>
              </div>

              <div className="flex gap-1.5">
                <Tag label={dirText} color="#FFFFFFCC" containerClassName="rounded-[4px] px-1 py-0.75" />

                {/* <Tag
                  label="全仓 100x"
                  color="#00FFF6"
                  containerClassName="!border-transparent bg-[#00FFF633] rounded-[4px] px-1 py-0.75"
                /> */}
              </div>
            </div>
            <Button
              variant={'ghost'}
              className="p-0 text-[#B9B9B9] cursor-pointer text-[calc(13rem/16)] h-[calc(13rem/16)]"
              onClick={() => setShowToast(true)}
            >
              <img src="/images/futuresDetail/share-icon.svg" alt="" />
            </Button>
          </div>
        }
        content={
          <div className="px-3 py-3.5">
            <div className="flex items-center justify-between gap-2 mb-[16px]">
              <div>
                <p className="text-[#FFFFFF80] mb-1.5 text-[calc(11rem/16)] leading-[calc(12rem/16)]">
                  已实现盈亏 (USDT)
                </p>
                <p className="flex  leading-[calc(14rem/16)]">
                  <span className={cn("text-[calc(15rem/16)] font-bold mr-1",
                    parseFloat(orderInfo.closedPnl) > 0 ? 'text-[#00FFB4]' : 
                    parseFloat(orderInfo.closedPnl) < 0 ? 'text-[#AB57FF]' : 
                    'text-[#FFFFFF]'
                  )}>{orderInfo.closedPnl}</span>
                  {/* <span className="text-[calc(12rem/16)]">(+0.55%)</span> */}
                </p>
              </div>

              <div className="text-end">
                <p className="text-[#FFFFFF80] mb-1.5 text-[calc(11rem/16)] leading-[calc(12rem/16)]">持仓量 ({orderInfo.coin})</p>
                <p className="text-[#FFFFFF] text-[calc(14rem/16)] leading-[calc(14rem/16)] font-bold">{orderInfo.sz}</p>
              </div>
            </div>

            <div className="flex items-center justify-between gap-2">
              <div>
                <p className="text-[#FFFFFF80] mb-1.5 text-[calc(11rem/16)] leading-[calc(12rem/16)]">成交价格</p>
                <p className="text-[#FFFFFF] text-[calc(12rem/16)] leading-[calc(12rem/16)]">{formatNumberWithCommas(orderInfo.px)}</p>
              </div>

              <div>
                <p className="text-[#FFFFFF80] mb-1.5 text-[calc(11rem/16)] leading-[calc(12rem/16)]">成交价值</p>
                <p className="text-[#FFFFFF] text-[calc(12rem/16)] leading-[calc(12rem/16)]">{formatNumberWithCommas(orderValue)}</p>
              </div>

              <div className="text-end">
                <p className="text-[#FFFFFF80] mb-1.5 text-[calc(11rem/16)] leading-[calc(12rem/16)]">手续费 (USDT)</p>
                <p className="text-[#FFFFFF] text-[calc(12rem/16)] leading-[calc(12rem/16)]">{orderInfo.fee}</p>
              </div>
            </div>
          </div>
        }
      />
      <ToastCenterScreen showModal={showToast} setShowModal={setShowToast} text="分享成功" />
    </>
  )
}

export default OrderHistoryCard
