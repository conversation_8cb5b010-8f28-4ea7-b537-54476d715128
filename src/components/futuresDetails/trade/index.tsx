import { useCallback, useEffect, useMemo, useState, useRef } from 'react'
import HeaderSetting from './HeaderSetting'
import Chart from '../chart/index'
import Container from '@components/common/Container.tsx'
import OrderBook from './OrderBook'
import LastestTarde from './LastestTarde'
import OrderForm from './OrderForm'
import LeftSetting from './LeftSetting'
import { UITab } from '@/types/uiTabs.ts'
import MovingLineTabs from '@components/common/MovingLineTabs.tsx'
import CurrentOrderList from './CurrentOrderList'
import MyPositionList from './MyPositionList'
import OrderHistoryList from './OrderHistoryList'
import { Button } from '@/components/ui/button'
import { getPredictedFundings, getPerpMetadata } from '@/api/hyperliquid'
import { updateUserFunding } from '@/redux/modules/futuresUserInfo.slice'
import { PredictedFunding } from '@/types/hyperliquid.ts'
import { useMultiChainWallet } from '@hooks/useMultiChainWallet.ts'
import FeeCountdown from './FeeCountdown'
import { RootState, useAppSelector, useAppDispatch } from '@/redux/store'
import { useWebData2 } from '@/hooks/hyperliquid/useWebData2'
import DetailSymbol from '../DetailSymbol'
import { selectAllPerpMeta, setSymbolListCtxs  } from '@/redux/modules/futuresMeta.slice'
import { OpenOrdersContext } from './OpenOrdersContext'







interface TradePageProps {
  baseCoin: string
}



const TradePage = ({ baseCoin }: TradePageProps) => {

  const { openOrders, positions, availableFund, symbolListCtxs } = useWebData2()
  const allMeta = useAppSelector(selectAllPerpMeta)


  const dispatch = useAppDispatch();

 
  const currentListTabs: UITab[] = useMemo((): UITab[] => ([
    {
      value: '当前委托',
      label: `当前委托(${openOrders.length})`,
    },
    {
      value: '我的持仓',
      label: `我的持仓(${positions.length})`,
    },
    {
      value: '成交历史',
      label: '成交历史',
    },
  ]), [openOrders.length, positions.length])

  const [currentTab, setCurrentTab] = useState<string>(currentListTabs[1].value)

  const [nextFunding, setNextFunding] = useState<PredictedFunding>({
    fundingRate: '',
    nextFundingTime: 0,
  })





  const handleChangeTab = (tab: string) => {
    setCurrentTab(tab)
  }

  const handleRenderTab = (tab: string) => {
    switch (tab) {
      case currentListTabs[1].value:
        return <MyPositionList baseCoin={baseCoin} positions={positions} />
      case currentListTabs[2].value:
        return <OrderHistoryList />
      default:
        return <CurrentOrderList baseCoin={baseCoin} orders={openOrders} />
    }
  }

  const fetchNextPredictedFunding = async () => {
    try {
      const data = await getPredictedFundings()

      if (data?.length) {
        const item = data.find((item: any) => {
          return item[0] === baseCoin
        })
        const hourTs = 60 * 60 * 1000
        const fundingTimeItem = item?.[1]?.find((item: any) => {
          return item[0] === "HlPerp"
        })
        fundingTimeItem[1].nextFundingTime = fundingTimeItem[1].nextFundingTime + hourTs
        setNextFunding(fundingTimeItem[1])
      }
    } catch (err: any) {
    }
  }

  useEffect(() => {
      dispatch(setSymbolListCtxs(symbolListCtxs))
  }, [symbolListCtxs, dispatch])

 useEffect(() => {
    dispatch(updateUserFunding({ available: availableFund }))
  }, [availableFund, dispatch])



  useEffect(() => {
    fetchNextPredictedFunding()
  }, [baseCoin])

  return (
    <OpenOrdersContext.Provider value={openOrders}>
      <DetailSymbol />
      <Chart baseCoin={baseCoin} disabledExpand={true} />
      <Container
        className="pt-[14px] rounded-tl-[8px] rounded-tr-[8px] pb-3
          border-t border-solid"
      >
        <HeaderSetting fundingRate={{
          fundingRate: nextFunding?.fundingRate,
          nextFundingTimeStr: <FeeCountdown targetTime={nextFunding?.nextFundingTime} />
        }} />
        <div className="flex gap-2">
          <div className="w-[37%] flex flex-col">
            <OrderBook />
            {/* <LastestTarde/> */}
            <LeftSetting />
          </div>
          <OrderForm baseCoin={baseCoin} containerClassName="w-[63%]" />
        </div>
      </Container>
      <div className="relative">
        <MovingLineTabs
          tabs={currentListTabs}
          onTabChange={handleChangeTab}
          defaultTab={currentListTabs[1].value}
          containerClassName="bg-[none] w-full mb-[10px]"
          tabsClassName="w-full"
        />

        {/* <Button
          variant={'ghost'}
          className="absolute top-[50%] transform -translate-y-1/2 right-[10px] py-1 px-2.5 rounded-[200px] text-[calc(12rem/16)] text-[#FFFFFF] bg-[#ECECED1F] h-[calc(22rem/16)] "
        >
          更多
        </Button> */}
      </div>

      {handleRenderTab(currentTab)}
    </OpenOrdersContext.Provider>

    
  )
}



export default TradePage
