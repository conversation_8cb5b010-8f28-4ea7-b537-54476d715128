import { useCallback, useRef } from 'react'
import { DataTable, DataTableProps } from '@/pages/home/<USER>'
import { ColumnDef } from '@tanstack/react-table'

interface DataTableInfiniteScrollProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  isLoading?: boolean
  isFetchingMore?: boolean
  fetchMore?: () => void
  hasMore?: boolean
  tableProps?: Partial<DataTableProps<TData, TValue>>
}

export function DataTableInfiniteScroll<TData, TValue>({
  columns,
  data,
  isLoading = false,
  fetchMore,
  isFetchingMore = false,
  hasMore = true,
  tableProps = {
    isStickyHeader: true,
    isStickyFirstColumn: true,
    stickyBg: 'bg-white dark:bg-gray-800',
  },
}: DataTableInfiniteScrollProps<TData, TValue>) {
  const fetchingRef = useRef(false)

  const handleBottomReached = useCallback(() => {
    if (fetchMore && hasMore && !fetchingRef.current) {
      fetchingRef.current = true
      Promise.resolve(fetchMore()).finally(() => {
        fetchingRef.current = false
      })
    }
  }, [fetchMore, hasMore])

  return (
    <DataTable
      columns={columns}
      data={data}
      isLoading={isLoading}
      isFetchMore={isFetchingMore}
      onBottomReached={handleBottomReached}
      {...tableProps}
    />
  )
}