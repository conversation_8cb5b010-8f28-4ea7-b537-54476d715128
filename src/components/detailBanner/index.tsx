import { TokenDetail } from '@/@generated/gql/graphql-core.ts'
import { ChainIds } from '@/types/enums.ts'
import { LAUNCHPAD_LOGOS, LaunchPlatformOptions } from '@/lib/constant.ts'
import { getLaunchpad } from '@/utils/helpers.ts'
import { cn } from '@/lib/utils.ts'
import { CopyButton } from '@components/common/copy-button.tsx'

type DetailBannerProps = {
  tokenData: TokenDetail
}

const chainMap: Record<number, string> = {
  [ChainIds.Solana]: 'SOL',
  [ChainIds.Ethereum]: 'ETH',
}

const chainNameMap: Record<number, string> = {
  [ChainIds.Solana]: 'Solana',
  [ChainIds.Ethereum]: 'Ethereum',
}

const iconChainMap: Record<number, string> = {
  [ChainIds.Solana]: '/images/wallets/sw-solana.svg',
  [ChainIds.Ethereum]: '/images/wallets/ethereum.png',
}

const DetailBanner = ({ tokenData }: DetailBannerProps) => {
  const firstDex = tokenData?.dexes ? tokenData.dexes[0] : null
  const dex = firstDex ? LaunchPlatformOptions.find((item) => item.value === firstDex) : null
  const launchpad = getLaunchpad(tokenData?.dexes ?? [])
  const website = tokenData?.info?.websites?.[0]
  const twitter = tokenData?.info?.socials?.find((item) => item.type === 'twitter')

  return (
    <div className="flex flex-col gap-3 px-2.5 mt-2">
      <div className="flex flex-col gap-2">
        {/*Symbol*/}
        <div className="flex items-center gap-1 app-font-medium text-[16px] text-[#FFFFFF]">
          <div className="flex items-center gap-1 nophone">
            <span>{tokenData?.symbol}</span>
            <CopyButton icon="/images/icons/ic-copy2.svg" text={tokenData?.address ?? ''} className="size-4" />
          </div>
          <div>/ {tokenData?.chainId ? chainMap[tokenData.chainId] : ''}</div>
          {tokenData?.isHotToken ? (
            <img src="/images/tokenDetail/icon-hot.svg" className="w-[7.59px] min-w-[7.59px]" alt="" />
          ) : null}
          {tokenData?.topTrending && tokenData?.topTrending > 0 && (
            <span className="app-font-regular text-[10px] leading-[1] text-[#FACC14]">#{tokenData.topTrending}</span>
          )}
        </div>

        {/*Link*/}
        <div className="flex items-center gap-2 cursor-default">
          <div className="flex items-center gap-1 text-[12px] app-font-regular text-[#FFFFFFB2]">
            <img src={tokenData?.chainId ? iconChainMap[tokenData.chainId] : '/images/wallets/sw-solana.svg'} alt="" />
            <span className="leading-[1]">{tokenData?.chainId ? chainNameMap[tokenData.chainId] : 'Solana'}</span>
            <img src="/images/futuresDetail/arrow-right.svg" alt="chevron right" />
          </div>

          {dex && (
            <div className="flex items-center gap-1 text-[12px] app-font-regular text-[#FFFFFFB2]">
              <img src={dex.icon} className="size-[11px]" alt="" />
              <span className="leading-[1]">{dex.label}</span>
              {launchpad && <span className="text-[#FFFFFF80]">via</span>}
            </div>
          )}

          {launchpad && (
            <div className="flex items-center gap-1 text-[12px] app-font-regular text-[#FFFFFFB2]">
              <a href="#">
                <img src={LAUNCHPAD_LOGOS[launchpad]} className="size-[11px]" alt="" />
              </a>
              <a href="#" className="leading-[1]">
                {launchpad}
              </a>
            </div>
          )}
        </div>
      </div>

      <div
        className={cn(
          'relative flex items-end bg-[#141414B2] rounded-[8px] w-full',
          tokenData?.info?.bannerUrl ? 'aspect-[355/118]' : '',
        )}
      >
        {!!tokenData?.info?.bannerUrl && (
          <img
            src={tokenData.info?.bannerUrl as string}
            className="w-full h-full object-cover rounded-[8px] absolute top-0 left-0 z-[-1]"
            alt="banner"
          />
        )}

        {!!website || !!twitter ? (
          <div className="flex items-center rounded-lg bg-[#232329] w-full h-7 text-[12px] app-font-medium leading-[1]">
            {website && (
              <a href={website.url as string} target="_blank" className="flex flex-1 items-center justify-center gap-1">
                <img src="/images/tokenDetail/icon-global.svg" className="size-4" alt="" />
                Website
              </a>
            )}
            <div className="h-3 w-[1px] bg-[#ECECED14]"></div>
            {twitter && (
              <a href={twitter.url as string} target="_blank" className="flex flex-1 items-center justify-center gap-1">
                <img src="/images/tokenDetail/icon-twitter.svg" className="size-4" alt="" />
                Twitter
              </a>
            )}
          </div>
        ) : null}
      </div>
    </div>
  )
}

export default DetailBanner
