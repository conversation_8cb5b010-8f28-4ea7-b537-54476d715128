import { useEffect, useState } from 'react'
import SearchInput from './SearchInput'
import { Token } from '../types/Token'
import MoneyFormatted from '@/components/common/MoneyFormatted'
import { Avatar } from '@radix-ui/react-avatar'
import { Address } from 'viem'
import { useTransferContext } from '../context/TransferContext'
import { ITEMS_PER_PAGE } from '../constants'
import { useEvmBalances } from '../hook/useEvmBalances'
import { Chain } from '../types/Chain'
import { MathFun } from '@/lib/utils'

type AutocompleteSearchProps = {
  address: Address
  onSelected: (token: Token) => void
}

const AutocompleteSearch = (props: AutocompleteSearchProps) => {
  const balances = useEvmBalances(props.address!).balances

  const [query, setQuery] = useState('')

  const { chains, tokens } = useTransferContext()
  const [chain, setChain] = useState(chains && chains.length > 0 ? chains[0] : null)
  const [filteredTokens, setFilteredTokens] = useState<Token[] | null>(null)
  const [displayedTokens, setDisplayedTokens] = useState<Token[]>([])
  const [page, setPage] = useState(1)

  useEffect(() => {

    let updatedFilteredTokens = tokens ?? null

    if (query) {
      setChain(chains && chains.length > 0 ? chains[0] : null)
      updatedFilteredTokens =
        tokens?.filter((token) => {
          const tokenName = token.name.toLowerCase()
          const tokenSymbol = token.symbol.toLowerCase()
          const filter = query.toLowerCase()
          return tokenName.includes(filter) || tokenSymbol.includes(filter) || token.address.includes(filter)
        }) ?? null
    } else if (chain && chain.chainId !== 'all') {
      updatedFilteredTokens =
        tokens?.filter((token) => {
          return (
            token.blockChain === chain.shortName ||
            token.blockChain === chain.name ||
            token.blockChain === chain.displayName
          )
        }) ?? null
    }

    if (updatedFilteredTokens) {
      updatedFilteredTokens = [...updatedFilteredTokens].sort((a, b) => {
        const balanceA = MathFun.mul(Number(balances[`${a.blockChain.toUpperCase()}_${a.symbol}`] || 0), a.usdPrice)
        const balanceB = MathFun.mul(Number(balances[`${b.blockChain.toUpperCase()}_${b.symbol}`] || 0), b.usdPrice) 
        return balanceB - balanceA
      })
    }
    setFilteredTokens(updatedFilteredTokens)

    setPage(1)
    setDisplayedTokens(updatedFilteredTokens?.slice(0, ITEMS_PER_PAGE) ?? [])
  }, [query, chain, tokens, balances])

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget
    if (scrollTop + clientHeight >= scrollHeight - 10) {
      loadMoreTokens()
    }
  }

  const loadMoreTokens = () => {
    if (filteredTokens) {
      const nextPage = page + 1
      const nextTokens = filteredTokens.slice(0, nextPage * ITEMS_PER_PAGE)
      setDisplayedTokens(nextTokens)
      setPage(nextPage)
    }
  }

  const handleChainSelect = (chain: Chain) => {
    if (chain.chainId === 'all') {
      setChain(chains && chains.length > 0 ? chains[0] : null)
      setFilteredTokens(tokens)
    } else {
      setChain(chain)
    }
  }

  return (
    <div className="w-full mx-auto relative">
      <SearchInput
        placeholder="搜索币种名称、合约地址"
        value={query}
        onChange={(e) => {
          setQuery(e.target.value)
        }}
        onFocus={() => {}}
        onBlur={() => setTimeout(() => {})}
      />
      <div className="w-full mx-auto relative">
        <div className="mt-[15px]">
          <div className="flex items-center mb-[12px]">
            <span className="min-w-[65px] text-[13px] text-[#FFFFFF80]">选择网络：</span>
            <span className="text-[13px]">{chain?.name}</span>
          </div>
          <div className="flex items-center whitespace-nowrap overflow-x-auto gap-2">
            {chains?.map((item, index) => (
              <button
                key={index}
                className={`inline-block ${item.id === 'all' ? 'w-[60px]' : 'w-[30px]'} h-[30px] bg-[#ECECED14] rounded-[6px] p-[6px] flex items-center`}
                onClick={() => handleChainSelect(item)}
              >
                <div className="flex items-center">
                  <Avatar className="inline-block w-[18px] h-[18px]">
                    <img src={item.logo} alt="Chain Logo" className="w-full h-full object-contain" />
                  </Avatar>
                  {item.id === 'all' ? (
                    <span className="inline-block text-[14px] text-[#FFFFFF] ml-[6px]">{item.shortName}</span>
                  ) : null}
                </div>
              </button>
            ))}
          </div>
        </div>
        <div className="mt-[16px]">
          <div className="flex items-center">
            <span className="min-w-[65px] text-[13px] text-[#FFFFFF80]">选择币种</span>
          </div>
          <div className="flex flex-col items-center max-h-80 overflow-y-auto gap-2" onScroll={handleScroll}>
            {displayedTokens?.map((item, index) => (
              <button
                key={index}
                className="flex items-center w-full h-full py-[16px] px-[12px] text-[13px] text-[#FFFFFF80]"
                onClick={() => props.onSelected(item)}
              >
                <img src={item.image} alt="Chain Logo" className="w-[36px] h-[36px] object-contain rounded-full" />
                <span className="flex items-center justify-between w-full ml-[12px] text-[#FFFFFF80]">
                  <span className="text-left flex flex-col">
                    <span className="text-[16px] text-[#FFFFFF]">{item.symbol}</span>
                    <span className="text-[12px] text-[#FFFFFFB2]">
                      {query ? item.address : chain?.id === 'all' ? item.blockChain : item.name}
                    </span>
                  </span>
                  <span className="text-right flex flex-col">
                    <span className="text-[16px] text-[#FFFFFF]">
                      {balances[`${item.blockChain.toUpperCase()}_${item.symbol}`] || 0}
                    </span>
                    <span className="text-[12px] text-[#FFFFFFB2]">
                      <MoneyFormatted value={MathFun.mul( item.usdPrice, Number(balances[`${item.blockChain.toUpperCase()}_${item.symbol}`] || 0))} />
                    </span>
                  </span>
                </span>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default AutocompleteSearch
