import { ConfigBuyType, ConfigPlatform, ConfigSellType } from '@/@generated/gql/graphql-trading'
import { isSolanaWallet } from '@/utils/solana'
import { z } from 'zod'

// Validations

// Schema for batch rule
export const tpslConfigChema = z.object({
  value: z.union([z.string(), z.number()]).optional(),
  sellRate: z.union([z.string(), z.number()]).optional(),
})

// Main schema for the form
export const walletSettingsSchema = z
  .object({
    leaderAddress: z
      .string({
        required_error: 'required',
      })
      .superRefine((val, ctx) => {
        if (!isSolanaWallet(val)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Invalid SOL address format',
          })
          return false
        }
        return true
      }),
    buyType: z.enum([ConfigBuyType.MaxAmount, ConfigBuyType.FixedAmount]),
    configAmount: z
      .string({
        required_error: 'required',
      })
      .superRefine((val, ctx) => {
        if (!val || val === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Please input amount',
          })
          return false
        }
        return true
      }),
    sellType: z.enum([ConfigSellType.Auto, ConfigSellType.NoCopy, 'custom']),
    customSellType: z.enum([ConfigSellType.MultiTpsl, ConfigSellType.SingleTpsl]),
    tp: z.union([z.string(), z.number()]).optional(),
    sl: z.union([z.string(), z.number()]).optional(),
    trailingSl: z.boolean().optional(),
    tpslConfig: z.array(tpslConfigChema).optional(),
    minMarketCap: z.union([z.string(), z.number()]).optional(),
    maxMarketCap: z.union([z.string(), z.number()]).optional(),
    minLiquidity: z.union([z.string(), z.number()]).optional(),
    maxLiquidity: z.union([z.string(), z.number()]).optional(),
    minAmount: z.union([z.string(), z.number()]).optional(),
    maxAmount: z.union([z.string(), z.number()]).optional(),
    minCreationTimeInt: z.union([z.string(), z.number()]).optional(),
    maxCreationTimeInt: z.union([z.string(), z.number()]).optional(),
    minBurnLiquidity: z.union([z.string(), z.number()]).optional(),
    maxPurchasePerToken: z.union([z.string(), z.number()]).optional(),
    platform: z.array(
      z.enum([ConfigPlatform.Moonshot, ConfigPlatform.Others, ConfigPlatform.Pump, ConfigPlatform.Raydium]),
    ),
    blacklistTokens: z
      .array(
        z.object({
          id: z.string().optional(),
          address: z.string().optional(),
        }),
      )
      .optional(),
    // .refine((val) => Object.values(val).some(Boolean), { message: 'At least one platform must be selected' }),
    // blacklistTokens: z.array(
    //   z.object({
    //     id: z.number(),
    //     address: z.string(),
    //   }),
    // ),
  })
  .superRefine((data, ctx) => {
    if (data.sellType === 'custom' && data.customSellType === ConfigSellType.SingleTpsl) {
      // ✅ Validate tp và sl
      if (!data.tp || data.tp.toString().trim() === '') {
        ctx.addIssue({
          path: ['tp'],
          code: z.ZodIssueCode.custom,
          message: 'This field is required',
        })
      }
      if (!data.sl || data.sl.toString().trim() === '') {
        ctx.addIssue({
          path: ['sl'],
          code: z.ZodIssueCode.custom,
          message: 'This field is required',
        })
      }
    }
    if (data.sellType === 'custom' && data.customSellType === ConfigSellType.MultiTpsl) {
      if (!data.tpslConfig || data.tpslConfig.length === 0) {
        ctx.addIssue({
          path: ['tpslConfig'],
          code: z.ZodIssueCode.custom,
          message: 'This field is required',
        })
        return
      }

      data.tpslConfig?.forEach((item, index) => {
        if (!item.value) {
          ctx.addIssue({
            path: ['tpslConfig', index, 'value'],
            code: z.ZodIssueCode.custom,
            message: 'This field is required',
          })
          return false
        }
        if (!item.sellRate) {
          ctx.addIssue({
            path: ['tpslConfig', index, 'sellRate'],
            code: z.ZodIssueCode.custom,
            message: 'This field is required',
          })
          return false
        }
      })
    }
  })
export const CONTROL_KEYS = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'] as const
export const CONTROL_KEYS_MINUS = [...CONTROL_KEYS, '-'] as const
export const handleOnInput = (e: React.KeyboardEvent<HTMLInputElement>, decimal: number, ableMinus: boolean = true) => {
  const char = e.key
  const list_keys = ableMinus ? CONTROL_KEYS_MINUS : CONTROL_KEYS

  // Special case for reload shortcut
  if ((e.ctrlKey || e.metaKey) && char === 'r') {
    console.log('Ctrl+R or Cmd+R pressed!')
    return true
  }

  // Allow control keys
  if (list_keys.includes(char as (typeof CONTROL_KEYS)[number])) {
    return true
  }

  const input = e.currentTarget.value
  const [_, decimalPart] = input.split('.')

  // Prevent more than max decimal places
  if (decimalPart && decimalPart.length >= decimal) {
    e.preventDefault()
    return false
  }

  // Allow numeric input
  if (char >= '0' && char <= '9') {
    return true
  }

  // Allow first decimal point
  if (char === '.') {
    return input.indexOf('.') === -1
  }

  // Prevent other characters
  e.preventDefault()
  return false
}
export type WalletSettingsFormData = z.infer<typeof walletSettingsSchema>
export type BatchRule = z.infer<typeof batchRuleSchema>
