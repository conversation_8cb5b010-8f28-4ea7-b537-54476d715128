import React from 'react'
import { useFormContext } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { WalletSettingsFormData } from './schema'

const FollowAddressSection: React.FC<{ isEdit?: boolean }> = ({ isEdit = false }) => {
  const {
    register,
    setValue,
    setError,
    clearErrors,
    formState: { errors },
  } = useFormContext<WalletSettingsFormData>()
  const { t } = useTranslation()

  const handlePaste = async () => {
    try {
      const text = await navigator.clipboard.readText()
      return text
    } catch (error) {
      console.error('Failed to read clipboard contents: ', error)
    }
  }

  return (
    <div className="">
      <label className="justify-start text-primary text-base font-normal leading-none">
        {t('walletCopy.settings.followAddress')}
      </label>
      <div className="flex flex-row items-center border-b py-2 border-[#ECECED1F10] mt-2.5">
        <input
          className="flex-grow text-sm font-normal bg-transparent outline-none"
          placeholder={t('walletCopy.settings.enterAddress')}
          {...register('leaderAddress')}
          disabled={isEdit}
        />
        <button
          type="button"
          className="text-xs font-normal bg-[#878B991A] py-1.5 px-3 inline-flex rounded-sm"
          onClick={async () => {
            const text = await handlePaste()
            setValue('leaderAddress', text!, { shouldValidate: true })
          }}
        >
          {t('walletCopy.settings.paste')}
        </button>
      </div>
      {errors.leaderAddress && (
        <div className="mt-2 justify-start text-[#FF353C] text-xs font-normal leading-3">
          {t('walletCopy.settings.invalidAddress')}
        </div>
      )}
    </div>
  )
}

export default FollowAddressSection
