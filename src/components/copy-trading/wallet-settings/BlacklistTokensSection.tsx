import React, { useEffect, useState } from 'react'
import { useFieldArray, useFormContext } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { IconCPDelete, IconWarningCircleSolid } from '@/components/icon'
import { WalletSettingsFormData } from './schema'
import { XModal } from '@/components/ui'
import { isSolanaWallet } from '@/utils/solana'
import { useQuery } from '@apollo/client'
import { getListTokenBlacklistQuery, modifyTokenBlacklistMutate } from '@/services/copytrade.service'
import { useAppSelector } from '@/redux/store'
import { selectToken } from '@/redux/modules/auth.slice'
import { tradingClient } from '@/lib/gql/apollo-client'
import { toast } from 'sonner'
import { ModifyTokenBlacklistInput, TokenBlacklist, TokenBlacklistAction } from '@/@generated/gql/graphql-trading'
import ButtonGradient from '@/components/common/buttons/ButtonGradient'

const BlacklistTokensSection: React.FC = () => {
  const {
    register,
    control,
    setError,
    setValue,
    watch,
    clearErrors,
    formState: { errors },
  } = useFormContext<WalletSettingsFormData>()
  const { t } = useTranslation()
  const blacklistTokens = watch('blacklistTokens')
  const [showHint, setShowHint] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'blacklistTokens',
  })

  const tokenSelected = useAppSelector(selectToken)
  const activeAccount = useAppSelector((state) => state.wallet.activeAccount)
  const userId = tokenSelected?.[activeAccount]?.user_id
  const { data, refetch } = useQuery(getListTokenBlacklistQuery, {
    variables: {
      input: {
        userId: userId,
        page: 1,
        size: 10,
      },
    },
    skip: !userId,
    client: tradingClient,
  })

  const listTokenBlackLists = data?.listTokenBlacklist?.items as TokenBlacklist[]
  useEffect(() => {
    if (listTokenBlackLists) {
      const list = listTokenBlackLists.map((item: TokenBlacklist) => {
        return {
          id: item.id,
          address: item.token,
        }
      })
      if (list.length > 0) {
        setTimeout(() => {
          setValue('blacklistTokens', list)
        }, 500)
      }
    }
  }, [listTokenBlackLists])

  const addBlacklistToken = async () => {
    if (fields.length < 10) {
      const token = blacklistTokens?.[blacklistTokens.length - 1]
      const findToken = listTokenBlackLists.find((item: TokenBlacklist) => item.token === token?.address)
      console.log('findToken', findToken)
      if (!token || !!findToken?.token) {
        append({
          id: Date.now().toString(),
          address: '',
        })
        return
      }

      if (!token?.address) {
        toast.error(t('Plase enter token address'))
        return
      }

      if (!isSolanaWallet(token?.address)) {
        toast.error(t('walletCopy.settings.invalidToken'))
        return
      }

      try {
        setIsLoading(true)
        await tradingClient
          .mutate({
            mutation: modifyTokenBlacklistMutate,
            variables: {
              input: {
                token: token?.address,
                action: TokenBlacklistAction.Add,
              } as ModifyTokenBlacklistInput,
            },
          })
          .then((res) => {
            if (res) {
              toast.success(t('toast.saveSuccess'))
              refetch().then(() => {
                append({
                  id: Date.now().toString(),
                  address: '',
                })
              })
            }
          })
          .finally(() => setIsLoading(false))
      } catch (error) {
        toast.error(error?.[0]?.message)
        setIsLoading(false)
        console.warn(error)
        throw error
      }
    }
  }

  // const handleBlurBlacklistToken = (event: React.FocusEvent<HTMLInputElement>, index: number) => {
  //   const value = event.target.value
  //   if (!isSolanaWallet(value)) {
  //     // Set a warning/error if the wallet is invalid
  //     setError(`blacklistTokens.${index}`, {
  //       type: 'manual',
  //       message: t('walletCopy.settings.invalidAddr  ess'),
  //     })
  //   } else {
  //     // Clear the error when the wallet is valid
  //     clearErrors(`blacklistTokens.${index}`)
  //   }
  // }

  const removeToken = async (index: number) => {
    const token = blacklistTokens?.[index]
    const findToken = listTokenBlackLists.find((item: TokenBlacklist) => item.token === token?.address)
    if (!token?.address || !findToken) {
      remove(index)
      return
    }
    try {
      await tradingClient
        .mutate({
          mutation: modifyTokenBlacklistMutate,
          variables: {
            input: {
              token: token?.address,
              action: TokenBlacklistAction.Remove,
            } as ModifyTokenBlacklistInput,
          },
        })
        .then((res) => {
          if (res) {
            toast.success(t('toast.saveSuccess'))
            remove(index)
          }
        })
    } catch (error) {
      toast.error(error?.[0]?.message)
      console.warn(error)
      throw error
    }
    // remove(index)
  }
  return (
    <div className="mt-6 flex flex-row gap-2">
      <div className="w-24 min-w-24">
        <label className="text-primary text-[13px] font-normal inline-flex items-center gap-1 h-10 ">
          {t('walletCopy.settings.currencyBlacklist')} <IconWarningCircleSolid onClick={() => setShowHint(true)} />
        </label>
      </div>
      <div className="w-full">
        {fields.map((token, index) => (
          <div key={token.id} className="mb-2">
            <div className="flex items-center gap-2">
              <span className="text-sm">{index + 1}</span>
              <div className="flex-1 relative">
                <input
                  type="text"
                  {...register(`blacklistTokens.${index}.address` as const)}
                  placeholder={t('walletCopy.settings.enterTokenAddress')}
                  className="w-full bg-[#878B991A] text-white rounded-md h-11 px-3 outline-none border border-[#ECECED14]"
                  // onBlur={(event) => handleBlurBlacklistToken(event, index)}
                />
              </div>
              <button
                type="button"
                className="inline-flex items-center justify-center"
                onClick={() => removeToken(index)}
              >
                <IconCPDelete />
              </button>
            </div>
            {errors.blacklistTokens?.[index] && (
              <div className="text-[#FF353C] text-xs ml-7 mt-1">{t('walletCopy.settings.invalidToken')}</div>
            )}
          </div>
        ))}

        <div className="pl-4 pr-6">
          {fields.length < 10 && (
            // <button
            //   type="button"
            //   disabled={fields.length >= 10 || isLoading}
            //   style={{
            //     background:
            //       'var(--111-but1, linear-gradient(44deg, #E843FE 0%, #FFF 47.32%, #FFF 63.89%, #00FFCD 103.57%))',
            //   }}
            //   className="w-full rounded-[6px] py-0 px-3.5 text-center h-9 text-sm text-[#141414] font-normal "
            //   onClick={addBlacklistToken}
            // >
            //   {t('walletCopy.settings.addToBlacklist')}
            // </button>
            <ButtonGradient
              disabled={fields.length >= 10 || isLoading}
              type="button"
              className="rounded-full w-full py-0 px-3.5 text-center h-9 text-sm text-[#141414] font-normal hover-scale"
              onClick={addBlacklistToken}
            >
              {t('walletCopy.settings.addToBlacklist')}
            </ButtonGradient>
          )}
        </div>
      </div>
      <XModal
        showModal={showHint}
        setShowModal={setShowHint}
        title={t('listCoin.copyTrade.hint.blacklist')}
        showCloseButton={false}
        // showDialogPrimitiveClose={true}
      />
    </div>
  )
}

export default BlacklistTokensSection
