import React, { useCallback, useEffect, useState } from 'react'
import { useFormContext, Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { Radio } from '@/components/ui/Radio'
import { IconWarningCircleSolid } from '@/components/icon'
import { WalletSettingsFormData } from './schema'
import { XModal } from '@/components/ui'
import { cn } from '@/lib/utils'
import { useTokenPriceOneTime } from '@/hooks/useTokenPrice'
import { formatCurrency } from '@/lib/number'
import { ConfigBuyType } from '@/@generated/gql/graphql-trading'
import { useMultiChainWallet } from '@/hooks/useMultiChainWallet'
import { SOL_ADDRESS } from '@/lib/blockchain'
const CONTROL_KEYS = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'] as const
const MAX_DECIMAL_PART = 6
const BuySettingsSection: React.FC = () => {
  const {
    control,
    register,
    watch,
    formState: { errors },
  } = useFormContext<WalletSettingsFormData>()
  const { t } = useTranslation()
  const [showHint, setShowHint] = useState<boolean>(false)
  const [calcMoney, setCalcMoney] = useState<number>(0)
  const buyType = watch('buyType')
  const configAmount = watch('configAmount')
  const { activeWallet } = useMultiChainWallet({})
  const priceTokenSOL = useTokenPriceOneTime(SOL_ADDRESS);
  const balance = activeWallet?.balance?.formatted
  // Input validation handler
  const handleOnInput = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    const char = e.key

    // Special case for reload shortcut
    if ((e.ctrlKey || e.metaKey) && char === 'r') {
      console.log('Ctrl+R or Cmd+R pressed!')
      return true
    }

    // Allow control keys
    if (CONTROL_KEYS.includes(char as (typeof CONTROL_KEYS)[number])) {
      return true
    }

    const input = e.currentTarget.value
    const [_, decimalPart] = input.split('.')

    // Prevent more than max decimal places
    if (decimalPart && decimalPart.length >= MAX_DECIMAL_PART) {
      e.preventDefault()
      return false
    }

    // Allow numeric input
    if (char >= '0' && char <= '9') {
      return true
    }

    // Allow first decimal point
    if (char === '.') {
      return input.indexOf('.') === -1
    }

    // Prevent other characters
    e.preventDefault()
    return false
  }, [])

  return (
    <div className="mt-[21.5px]">
      <label className="justify-start text-primary text-base font-normal inline-flex items-center gap-1">
        {t('walletCopy.settings.buySettings')}{' '}
        <IconWarningCircleSolid onClick={() => setShowHint(true)} className="cursor-pointer" />
      </label>

      <div className="flex flex-row mt-5 space-x-6">
        <Controller
          name="buyType"
          control={control}
          render={({ field }) => (
            <>
              <Radio
                id="max-amount"
                name={field.name}
                value={ConfigBuyType.MaxAmount}
                checked={field.value === ConfigBuyType.MaxAmount}
                onChange={() => field.onChange(ConfigBuyType.MaxAmount)}
                label={t('walletCopy.settings.maxFollow')}
                labelClassName="text-[#FFFFFFCC]"
              />
              <Radio
                id="fixed-amount"
                name={field.name}
                value={ConfigBuyType.FixedAmount}
                checked={field.value === ConfigBuyType.FixedAmount}
                onChange={() => field.onChange(ConfigBuyType.FixedAmount)}
                label={t('walletCopy.settings.fixedBuy')}
                labelClassName="text-[#FFFFFFCC]"
              />
            </>
          )}
        />
      </div>

      <div className="mt-3">
        <div
          className="w-full relative rounded-md bg-[#878B991A] border-colors-alpha-dark-50 border-solid
          border-[1px] box-border h-11 flex flex-row items-center justify-between p-1 gap-0 text-center
           text-sm text-white/70 font-noto-sans-sc"
        >
          {buyType === ConfigBuyType.MaxAmount ? (
            <span
              className="rounded bg-[#141414] h-9 inline-flex flex-row items-center justify-center py-0 px-2 box-border text-center
              text-[14px] text-[#9B9B9B] "
            >
              {t('walletCopy.settings.quantity')}
            </span>
          ) : (
            <span
              className="rounded bg-[#141414] h-9 inline-flex flex-row items-center justify-center py-0 px-2 box-border text-center
            text-[14px] text-[#9B9B9B] "
            >
              {t('walletCopy.settings.amount')}
            </span>
          )}
          <input
            className={cn(
              'flex-grow text-sm bg-transparent outline-none px-2 text-center font-semibold text-white',
              buyType === ConfigBuyType.MaxAmount && errors.configAmount
                ? 'border border-red-500'
                : 'border-transparent',
            )}
            {...register('configAmount', {
              required: buyType === ConfigBuyType.MaxAmount
            })}
            onKeyDown={handleOnInput}
          />
          <span
            className="rounded bg-[#141414] h-9 inline-flex flex-row items-center justify-center py-0 px-2 box-border text-center
            text-[14px] text-[#9B9B9B] "
          >
            SOL
          </span>
        </div>

        <div className="text-xs text-[#9B9B9B] font-normal mt-3 flex flex-row items-center justify-between">
          <span>≈${formatCurrency(parseFloat(configAmount) * priceTokenSOL)}</span>
          <span>
            {t('walletCopy.settings.balanceWithUnit')}
            {balance} SOL
          </span>
        </div>
      </div>
      <XModal
        showModal={showHint}
        setShowModal={setShowHint}
        description={[t('listCoin.copyTrade.hint.buySettings.intro1'), t('listCoin.copyTrade.hint.buySettings.intro2')]}
        showCloseButton={false}
      />
    </div>
  )
}

export default BuySettingsSection
