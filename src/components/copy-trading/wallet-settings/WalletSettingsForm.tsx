import React, { useEffect } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { WalletSettingsFormData, walletSettingsSchema } from './schema'
import FollowAddressSection from './FollowAddressSection'
import BuySettingsSection from './BuySettingsSection'
import SellSettingsSection from './SellSettingsSection'
import AdvancedSettingsSection from './AdvancedSettingsSection'
import ActionButtons from './ActionButtons'
import {
  ConfigBuyType,
  ConfigPlatform,
  ConfigSellType,
  CopyTradeConfig,
  CreateCopyTradeConfigInput,
} from '@/@generated/gql/graphql-trading'
import { tradingClient } from '@/lib/gql/apollo-client'
import { createCopyTradeConfig, getCopyTradeConfigById, updateCopyTradeConfig } from '@/services/copytrade.service'
import { useMultiChainWallet } from '@/hooks/useMultiChainWallet'
import { toast } from 'sonner'
import { useTranslation } from 'react-i18next'
import { useNavigate, useParams } from 'react-router-dom'
import { useQuery } from '@apollo/client'
import { get } from 'lodash-es'

interface WalletSettingsFormProps {
  initialValues?: Partial<WalletSettingsFormData>
  onCancel: () => void
}

const WalletSettingsForm: React.FC<WalletSettingsFormProps> = ({ initialValues, onCancel }) => {
  const { activeWallet } = useMultiChainWallet({})
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { id } = useParams()

  const defaultValues: WalletSettingsFormData = {
    leaderAddress: '',
    buyType: ConfigBuyType.MaxAmount,
    configAmount: '',
    sellType: ConfigSellType.Auto,
    customSellType: ConfigSellType.SingleTpsl,
    tp: '',
    sl: '',
    trailingSl: false,
    minMarketCap: '',
    maxMarketCap: '',
    minLiquidity: '',
    maxLiquidity: '',
    minAmount: '',
    maxAmount: '',
    minCreationTimeInt: '',
    maxCreationTimeInt: '',
    minBurnLiquidity: '',
    maxPurchasePerToken: '',
    platform: [ConfigPlatform.Moonshot, ConfigPlatform.Pump, ConfigPlatform.Raydium],
    ...initialValues,
  }

  const methods = useForm<WalletSettingsFormData>({
    defaultValues,
    resolver: zodResolver(walletSettingsSchema),
    mode: 'onChange',
  })

  const { data: dataCopyTradeConfig } = useQuery(getCopyTradeConfigById, {
    variables: {
      input: {
        id: id,
      },
    },
    skip: !id,
    client: tradingClient,
  })

  useEffect(() => {
    if (id && dataCopyTradeConfig) {
      const config = dataCopyTradeConfig?.getCopyTradeConfig as CopyTradeConfig
      const configMapped = Object.fromEntries(
        Object.entries(config).map(([key, value]) => [key, value === '0' ? '' : value]),
      )
      methods.reset(
        Object.assign(defaultValues, {
          ...configMapped,
          customSellType: [ConfigSellType.MultiTpsl, ConfigSellType.SingleTpsl].includes(config?.sellType)
            ? configMapped?.sellType
            : ConfigSellType.MultiTpsl,
          sellType: [ConfigSellType.MultiTpsl, ConfigSellType.SingleTpsl].includes(config?.sellType)
            ? 'custom'
            : configMapped?.sellType,
          maxPurchasePerToken: configMapped?.maxPurchasePerToken ?? '',
          ...(!!config?.minMarketCap && { minMarketCap: +configMapped.minMarketCap / 1000 }),
          ...(!!config?.maxMarketCap && { maxMarketCap: +configMapped.maxMarketCap / 1000 }),
          ...(!!config?.minLiquidity && { minLiquidity: +configMapped.minLiquidity / 1000 }),
          ...(!!config?.maxLiquidity && { maxLiquidity: +configMapped.maxLiquidity / 1000 }),
        }),
      )
    }
  }, [id, dataCopyTradeConfig])

  const handleSubmit = methods.handleSubmit(async (data) => {
    let param: CreateCopyTradeConfigInput = {} as CreateCopyTradeConfigInput
    param = {
      ...data,
      sellType: data.sellType === 'custom' ? data.customSellType : data.sellType,
      walletAddress: activeWallet?.walletId,
      chainId: activeWallet?.chainId,
      minMarketCap: !!data.minMarketCap ? +data.minMarketCap * 1000 : '',
      maxMarketCap: !!data.maxMarketCap ? +data.maxMarketCap * 1000 : '',
      minLiquidity: !!data.minLiquidity ? +data.minLiquidity * 1000 : '',
      maxLiquidity: !!data.maxLiquidity ? +data.maxLiquidity * 1000 : '',
    }
    const paramCleaned = Object.fromEntries(Object.entries(param).filter(([_, value]) => value !== ''))
    delete paramCleaned.customSellType
    delete paramCleaned.blacklistTokens
    if (paramCleaned.sellType !== ConfigSellType.SingleTpsl) {
      delete paramCleaned.trailingSl
    }
    console.log('[param createCopyTradeConfig]: ', paramCleaned)
    if (!!id) modifyCopyTradeConfig(paramCleaned)
    else createTradeConfig(paramCleaned)
  })

  const createTradeConfig = async (params: any) => {
    try {
      await tradingClient
        .mutate({
          mutation: createCopyTradeConfig,
          variables: {
            input: params,
          },
        })
        .then((res) => {
          if (res) {
            toast.success(t('toast.saveSuccess'))
            navigate(-1)
          }
        })
    } catch (error: any) {
      // toast.error(t('walletCopy.settings.error.createConfig'),{
      //   position: 'bottom-center'
      // })
      const code = get(error[0], 'code', '')
      if(code == 'CopyTrade_CreateConfig_DuplicateConfig') {
        toast.error(t(code))
      }
      else{
        toast.error(error?.[0]?.message)
      }
      console.warn(error)
      throw error
    }
  }

  const modifyCopyTradeConfig = async (params: any) => {
    const param = params
    // delete param.walletAddress
    delete param.chainId
    delete param.walletAddress
    delete param.leaderAddress
    if (param.sellType === ConfigSellType.SingleTpsl) {
      delete param.tpslConfig
    }
    if (param.sellType === ConfigSellType.MultiTpsl) {
      delete param.tp
      delete param.sl
    }
    try {
      await tradingClient
        .mutate({
          mutation: updateCopyTradeConfig,
          variables: {
            input: {
              ...params,
              id: id,
            },
          },
        })
        .then((res) => {
          if (res) {
            toast.success(t('toast.saveSuccess'))
            navigate(-1)
          }
        })
    } catch (error: any) {
      toast.error(error?.[0]?.message)
      console.warn(error)
      throw error
    }
  }

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit} className="flex flex-col min-h-screen">
        <div className="flex-1">
          <FollowAddressSection />
          <BuySettingsSection />
          <SellSettingsSection />
          <AdvancedSettingsSection />
          {/* <TransactionSettingsSection /> */}
          <ActionButtons onCancel={onCancel} />
        </div>
      </form>
    </FormProvider>
  )
}

export default WalletSettingsForm
