import React from 'react'
import { useFormContext } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

interface ActionButtonsProps {
  onCancel: () => void
}

const ActionButtons: React.FC<ActionButtonsProps> = ({ onCancel }) => {
  const { formState: { isValid, isDirty, errors } } = useFormContext()
  const { t } = useTranslation()
  return (
    <div className="w-full border-solid border-t-[0.5px] box-border flex flex-col items-center justify-center py-4 text-center text-base">
      <div className="self-stretch flex flex-row items-start gap-2">
        <button
          type="button"
          onClick={onCancel}
          className="rounded-[200px] overflow-hidden p-[1px] border-solid border-[1px] h-11 flex-1 w-full bg-gradient-to-tr to-[#2D8474] from-[#794384] "
        >
          <span className="h-full w-full flex items-center justify-center bg-[#2D3132] rounded-[200px] ">
            {t('walletCopy.settings.cancel')}
          </span>
        </button>
        <button
          type="submit"
          // disabled={Object.keys(errors).length > 0}
          className="btn-gradient rounded-[200px] border-solid border-[1px] h-11 w-full flex-1 max-w-1/2 text-[#141414] disabled:opacity-50"
        >
          {t('walletCopy.settings.save')}
        </button>
      </div>
    </div>
  )
}

export default ActionButtons 