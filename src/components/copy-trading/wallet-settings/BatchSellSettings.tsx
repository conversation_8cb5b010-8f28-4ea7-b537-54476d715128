import React from 'react'
import { useFieldArray, useFormContext } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { IconCPDelete, IconSwap } from '@/components/icon'
import { handleOnInput, WalletSettingsFormData } from './schema'

const BatchSellSettings: React.FC = () => {
  const {
    register,
    control,
    formState: { errors },
    watch
  } = useFormContext<WalletSettingsFormData>()
  const { t } = useTranslation()

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'tpslConfig',
  })

  const addBatchRule = () => {
    append({
      value: '',
      sellRate: '',
    })
  }

  return (
    <div className="mt-4">
      {fields.map((rule, index) => {
        const isMinus = parseFloat(`${watch(`tpslConfig.${index}.value`)}`) < 0;
        return (
          <div key={rule.id} className="mb-7 flex flex-row gap-1">
            <div className="text-[13px] font-normal text-[#FFFFFFCC] whitespace-nowrap flex items-center">
              {t('walletCopy.settings.level')} {index + 1}
            </div>
            <div className="flex flex-row w-full gap-2">
              <div className="flex-1 relative">
                <div className="bg-[#141414] rounded-[6px] h-11 flex items-center p-0.5 pr-1.5">
                  <span
                    className="text-[10px] text-[#9B9B9B] h-full bg-[#878B991A] rounded-sm max-w-8 
                px-1.5 break-all leading-tight text-center inline-flex items-center"
                  >
                    {isMinus ? t('walletCopy.stopLoss') : t('walletCopy.settings.tpPercent')}
                  </span>

                  <input
                    {...register(`tpslConfig.${index}.value` as const, {
                      required: 'Take profit percent is required',
                    })}
                    className="bg-transparent text-sm outline-none flex-1 px-1.5 w-full text-center"
                  onKeyDown={(e) => handleOnInput(e, 2)}
                  />
                  <span className="text-[#FFFFFFB2] text-[12px] inline-flex items-center">
                    %
                    <button type="button" className="flex items-center justify-center">
                      <IconSwap />
                    </button>
                  </span>
                </div>
                {errors.tpslConfig?.[index]?.value && (
                  <div className="mt-1 text-red-500 text-xs absolute left-0 top-full">{errors.tpslConfig[index].value.message}</div>
                )}
              </div>
              <div className="flex-1">
                <div className="bg-[#141414] rounded-[6px] h-11 flex items-center p-0.5 pr-2.5">
                  <span
                    className="text-[10px] text-[#9B9B9B] h-full bg-[#878B991A] rounded-sm max-w-8 
                px-1.5 break-all leading-tight text-center inline-flex items-center"
                  >
                    {t('walletCopy.settings.sellOutPercent')}
                  </span>

                  <input
                    {...register(`tpslConfig.${index}.sellRate` as const, {
                      required: 'Sell out percent is required',
                    })}
                    className="bg-transparent text-sm outline-none flex-1 px-1.5 w-full text-center"
                    onKeyDown={(e) => handleOnInput(e, 2)}
                  />
                  <span className="text-[#FFFFFFB2] text-[12px] inline-flex items-center">%</span>
                </div>
                {errors.tpslConfig?.[index]?.sellRate && (
                  <div className="mt-1 text-red-500 text-xs">{errors.tpslConfig?.[index].sellRate.message}</div>
                )}
              </div>

              <button type="button" className="flex items-center justify-center" onClick={() => remove(index)}>
                <IconCPDelete />
              </button>
            </div>
          </div>
        )
      })}
      <div className="text-end">
        <button
          type="button"
          className="w-[83%] rounded-md [background:linear-gradient(10deg,_rgba(225,_73,_248,_0.1),_rgba(153,_69,_255,_0.1)_28%,_rgba(0,_243,_171,_0.1)_92%)] h-9 
          py-0 px-3.5 text-center text-sm text-[#FFFFFFCC] font-normal "
          onClick={addBatchRule}
        >
          {t('walletCopy.settings.addRule')}
        </button>

        <div className="w-[83%] text-[11px] text-[#ff6e27] inline-flex text-start mt-2">
          {t('walletCopy.settings.noFullSellWarning')}
        </div>
      </div>
    </div>
  )
}

export default BatchSellSettings
