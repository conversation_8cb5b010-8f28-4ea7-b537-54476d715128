import React, { useState } from 'react'
import { useFormContext } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { IconChevronDown, IconReset, IconWarningCircleSolid } from '@/components/icon'
import { handleOnInput, WalletSettingsFormData } from './schema'
import { Button } from '@/components/ui/button'
import { XModal } from '@/components/ui'
import PlatformSection from './PlatformSection'
import BlacklistTokensSection from './BlacklistTokensSection'

const AdvancedSettingsSection: React.FC = () => {
  const { register, reset } = useFormContext<WalletSettingsFormData>()
  const { t } = useTranslation()
  const [isOpen, setIsOpen] = useState(false)
  const [showHint, setShowHint] = useState<boolean>(false)
  const [currentHint, setCurrentHint] = useState<keyof typeof descriptionList>('followAmount')

  const descriptionList = {
    followAmount: [t('listCoin.copyTrade.hint.followAmount.intro1'), t('listCoin.copyTrade.hint.followAmount.intro2')],
    minBurnPool: [t('listCoin.copyTrade.hint.minBurnPool.intro1')],
    singleAddPositionCount: [
      t('listCoin.copyTrade.hint.singleAddPositionCount.intro1'),
      t('listCoin.copyTrade.hint.singleAddPositionCount.intro2'),
      t('listCoin.copyTrade.hint.singleAddPositionCount.intro3'),
    ],
  }

  const handleShowHint = (hint: keyof typeof descriptionList) => {
    setCurrentHint(hint)
    setShowHint(true)
  }

  const handleDetailsToggle = (e: React.SyntheticEvent<HTMLDetailsElement, Event>) => {
    setIsOpen(e.currentTarget.open)
  }

  function handleClickReset(e: React.MouseEvent<HTMLButtonElement>) {
    e.preventDefault()
    reset(
      {
        minMarketCap: '',
        maxMarketCap: '',
        minLiquidity: '',
        maxLiquidity: '',
        minAmount: '',
        maxAmount: '',
        minCreationTimeInt: '',
        maxCreationTimeInt: '',
        minBurnLiquidity: '',
        maxPurchasePerToken: '',
      },
      {
        keepValues: false,
        keepDirty: false,
        keepTouched: false,
        keepErrors: true,
        keepDefaultValues: true,
        keepIsSubmitted: true,
        keepSubmitCount: true,
        keepDirtyValues: false,
      },
    )
  }

  return (
    <div className="mt-8">
      <details className="group" onToggle={handleDetailsToggle}>
        <summary className="flex justify-between items-center cursor-pointer list-none">
          <div className="flex items-center gap-1">
            <span className="text-base font-normal">
              {t('walletCopy.settings.advancedSettings')}({0})
            </span>
            <Button
              type="button"
              className="text-[#FFFFFFCC] text-sm font-normal bg-transparent hover:opacity-80 transition-opacity ease-linear"
              onClick={handleClickReset}
            >
              <IconReset className="text-[#9B9B9B]" />
              <span>{t('walletCopy.settings.reset')}</span>
            </Button>
          </div>
          <button type="button" className="w-6 h-6 flex items-center justify-center pointer-events-none">
            <IconChevronDown className={isOpen ? 'rotate-180' : ''} />
          </button>
        </summary>

        <div className="mt-6 space-y-4">
          {/* Market Value */}
          <div className="flex items-center gap-2">
            <span className="w-24 min-w-24 tracking-tighter text-[13px] font-normal">
              {t('walletCopy.settings.marketValue')}
            </span>
            <div className="flex-1 grid grid-cols-2 gap-4">
              <div className="bg-[#878B991A] rounded-md flex items-center h-11 px-3 border border-[#ECECED14]">
                <input
                  {...register('minMarketCap')}
                  placeholder={t('walletCopy.settings.min')}
                  className="w-full bg-transparent outline-none placeholder:text-[rgba(255,255,255,0.36)]"
                  onKeyDown={(e) => handleOnInput(e, 6)}
                />
                <span className="text-sm text-[#FFFFFFB2] leading-[100%] text-center">K</span>
              </div>
              <div className="bg-[#878B991A] rounded-md flex items-center h-11 px-3 border border-[#ECECED14]">
                <input
                  {...register('maxMarketCap')}
                  placeholder={t('walletCopy.settings.max')}
                  className="bg-transparent outline-none w-full placeholder:text-[rgba(255,255,255,0.36)]"
                  onKeyDown={(e) => handleOnInput(e, 6)}
                />
                <span className="text-sm text-[#FFFFFFB2] leading-[100%] text-center">K</span>
              </div>
            </div>
          </div>

          {/* Pool Size */}
          <div className="flex items-center gap-2">
            <span className="w-24 min-w-24 tracking-tighter text-[13px] font-normal">
              {t('walletCopy.settings.pool')}
            </span>
            <div className="flex-1 grid grid-cols-2 gap-4">
              <div className="bg-[#878B991A] rounded-md flex items-center h-11 px-3 border border-[#ECECED14]">
                <input
                  {...register('minLiquidity')}
                  placeholder={t('walletCopy.settings.min')}
                  className="w-full bg-transparent outline-none placeholder:text-[rgba(255,255,255,0.36)]"
                  onKeyDown={(e) => handleOnInput(e, 6)}
                />
                <span className="text-sm text-[#FFFFFFB2] leading-[100%] text-center">K</span>
              </div>
              <div className="bg-[#878B991A] rounded-md flex items-center h-11 px-3 border border-[#ECECED14]">
                <input
                  {...register('maxLiquidity')}
                  placeholder={t('walletCopy.settings.max')}
                  className="bg-transparent outline-none w-full placeholder:text-[rgba(255,255,255,0.36)]"
                  onKeyDown={(e) => handleOnInput(e, 6)}
                />
                <span className="text-sm text-[#FFFFFFB2] leading-[100%] text-center">K</span>
              </div>
            </div>
          </div>

          {/* Follow Amount */}
          <div className="flex items-center gap-2">
            <span className="w-24 min-w-24 tracking-tighter text-[13px] font-normal inline-flex gap-1 items-center">
              {t('walletCopy.settings.followAmount')}
              <IconWarningCircleSolid className="text-[#9B9B9B]" onClick={() => handleShowHint('followAmount')} />
            </span>
            <div className="flex-1 grid grid-cols-2 gap-4">
              <div className="bg-[#878B991A] rounded-md flex items-center h-11 px-3 border border-[#ECECED14]">
                <input
                  {...register('minAmount')}
                  placeholder={t('walletCopy.settings.min')}
                  className="w-full bg-transparent outline-none placeholder:text-[rgba(255,255,255,0.36)]"
                  onKeyDown={(e) => handleOnInput(e, 6)}
                />
                <span className="text-sm text-[#FFFFFFB2] leading-[100%] text-center">SOL</span>
              </div>
              <div className="bg-[#878B991A] rounded-md flex items-center h-11 px-3 border border-[#ECECED14]">
                <input
                  {...register('maxAmount')}
                  placeholder={t('walletCopy.settings.max')}
                  className="bg-transparent outline-none w-full placeholder:text-[rgba(255,255,255,0.36)]"
                  onKeyDown={(e) => handleOnInput(e, 6)}
                />
                <span className="text-sm text-[#FFFFFFB2] leading-[100%] text-center">SOL</span>
              </div>
            </div>
          </div>

          {/* Creation Time */}
          <div className="flex items-center gap-2">
            <span className="w-24 min-w-24 tracking-tighter text-[13px] font-normal">
              {t('walletCopy.settings.creationTime')}
            </span>
            <div className="flex-1 grid grid-cols-2 gap-4">
              <div className="bg-[#878B991A] rounded-md flex items-center h-11 px-3 border border-[#ECECED14]">
                <input
                  {...register('minCreationTimeInt')}
                  placeholder={t('walletCopy.settings.min')}
                  className="w-full bg-transparent outline-none placeholder:text-[rgba(255,255,255,0.36)]"
                  onKeyDown={(e) => handleOnInput(e, 2)}
                />
                <span className="text-sm text-[#FFFFFFB2] leading-[100%] text-center">min</span>
              </div>
              <div className="bg-[#878B991A] rounded-md flex items-center h-11 px-3 border border-[#ECECED14]">
                <input
                  {...register('maxCreationTimeInt')}
                  placeholder={t('walletCopy.settings.max')}
                  className="bg-transparent outline-none w-full placeholder:text-[rgba(255,255,255,0.36)]"
                  onKeyDown={(e) => handleOnInput(e, 2)}
                />
                <span className="text-sm text-[#FFFFFFB2] leading-[100%] text-center">min</span>
              </div>
            </div>
          </div>

          {/* Slippage Tolerance */}
          <div className="flex items-center gap-2">
            <span className="w-24 min-w-24 tracking-tighter text-[13px] font-normal inline-flex gap-1 items-center">
              {t('walletCopy.settings.minBurnPool')}
              <IconWarningCircleSolid className="text-[#9B9B9B]" onClick={() => handleShowHint('minBurnPool')} />
            </span>
            <div className="flex-1">
              <div className="bg-[#878B991A] rounded-md flex items-center h-11 px-3 border border-[#ECECED14]">
                <input
                  {...register('minBurnLiquidity')}
                  className="w-full bg-transparent outline-none placeholder:text-[rgba(255,255,255,0.36)]"
                  onKeyDown={(e) => handleOnInput(e, 2)}
                />
                <span className="text-sm text-[#FFFFFFB2] leading-[100%] text-center">%</span>
              </div>
            </div>
          </div>

          {/* Single Add Position Count */}
          <div className="flex items-center gap-2">
            <span className="w-24 min-w-24 tracking-tighter text-[13px] font-normal inline-flex items-center">
              {t('walletCopy.settings.singleAddPositionCount')}
              <span className="min-w-[13.33px]">
                <IconWarningCircleSolid
                  className="text-[#9B9B9B] ml-1"
                  onClick={() => handleShowHint('singleAddPositionCount')}
                />
              </span>
            </span>
            <div className="flex-1">
              <div className="bg-[#878B991A] rounded-md flex items-center h-11 px-3 border border-[#ECECED14]">
                <input
                  type="number"
                  placeholder={t('walletCopy.settings.enterPositiveInteger')}
                  className="w-full bg-transparent outline-none placeholder:text-[rgba(255,255,255,0.36)]"
                  {...register('maxPurchasePerToken')}
                />
              </div>
            </div>
          </div>
          <PlatformSection />
          <BlacklistTokensSection />
        </div>
      </details>
      <XModal
        showModal={showHint}
        setShowModal={setShowHint}
        description={descriptionList[currentHint]}
        showCloseButton={false}
      />
    </div>
  )
}

export default AdvancedSettingsSection
