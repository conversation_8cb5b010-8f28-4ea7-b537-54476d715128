import { IconWarningCircleSolid } from '@/components/icon'
import { Radio } from '@/components/ui/Radio'
import React, { useEffect, useState } from 'react'
import { Controller, useFormContext } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import BatchSellSettings from './BatchSellSettings'
import { WalletSettingsFormData } from './schema'
import SingleSellSettings from './SingleSellSettings'
import { XModal } from '@/components/ui'
import { ConfigSellType } from '@/@generated/gql/graphql-trading'

const CONTROL_KEYS = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'] as const
const MAX_DECIMAL_PART = 6

const SellSettingsSection: React.FC = () => {
  const { control, watch } = useFormContext<WalletSettingsFormData>()
  const { t } = useTranslation()
  const [showHint, setShowHint] = useState<boolean>(false)
  const [currentHint, setCurrentHint] = useState<keyof typeof descriptionList>('auto')

  const sellType = watch('sellType')
  useEffect(() => {
    setCurrentHint(sellType as keyof typeof descriptionList)
  }, [sellType])
  const customSellType = watch('customSellType')
  const handleShowHint = () => {
    setShowHint(true)
  }
  const descriptionList = {
    auto: [t('listCoin.copyTrade.hint.sellSettings.intro1')],
    none: [t('listCoin.copyTrade.hint.sellSettings.intro2')],
    custom: [t('listCoin.copyTrade.hint.sellSettings.intro3')],
  }


  return (
    <div className="mt-[36.5px]">
      <label className="justify-start text-primary text-base font-normal inline-flex items-center gap-1">
        {t('walletCopy.settings.sellSettings')}{' '}
        <IconWarningCircleSolid onClick={handleShowHint} className="cursor-pointer" />
      </label>

      <div className="flex flex-row mt-5 space-x-6">
        <Controller
          name="sellType"
          control={control}
          render={({ field }) => (
            <>
              <Radio
                labelClassName="text-[#FFFFFFCC]"
                id="auto-sell"
                name={field.name}
                value={ConfigSellType.Auto}
                checked={field.value === ConfigSellType.Auto}
                onChange={() => field.onChange(ConfigSellType.Auto)}
                label={t('walletCopy.settings.autoFollow')}
              />
              <Radio
                labelClassName="text-[#FFFFFFCC]"
                id="no-sell"
                name={field.name}
                value={ConfigSellType.NoCopy}
                checked={field.value === ConfigSellType.NoCopy}
                onChange={() => field.onChange(ConfigSellType.NoCopy)}
                label={t('walletCopy.settings.noSell')}
              />
              <Radio
                labelClassName="text-[#FFFFFFCC]"
                id="custom-sell"
                name={field.name}
                value="custom"
                checked={field.value === 'custom'}
                onChange={() => field.onChange('custom')}
                label={t('walletCopy.settings.customTPSL')}
              />
            </>
          )}
        />
      </div>

      {sellType === 'custom' && (
        <div className="mt-5 bg-[#9B9B9B33] rounded-lg p-4">
          <div className="flex flex-row items-center justify-start mb-4 gap-2.5">
            <span className="min-w-[88px]">
              <Controller
                name="customSellType"
                control={control}
                render={({ field }) => (
                  <>
                    <Radio
                      id="single"
                      name={field.name}
                      value={ConfigSellType.SingleTpsl}
                      checked={field.value === ConfigSellType.SingleTpsl}
                      onChange={() => field.onChange(ConfigSellType.SingleTpsl)}
                      label={t('walletCopy.settings.single')}
                      labelClassName="text-[#FFFFFFCC]"
                    />
                  </>
                )}
              />
            </span>
            <span className="inline-flex items-center gap-1">
              <Controller
                name="customSellType"
                control={control}
                render={({ field }) => (
                  <Radio
                    id="batch"
                    name={field.name}
                    value={ConfigSellType.MultiTpsl}
                    checked={field.value === ConfigSellType.MultiTpsl}
                    onChange={() => field.onChange(ConfigSellType.MultiTpsl)}
                    label={t('walletCopy.settings.batch')}
                    labelClassName="text-[#FFFFFFCC]"
                  />
                )}
              />
              <IconWarningCircleSolid className="text-[#9B9B9B]" onClick={handleShowHint} />
            </span>
          </div>

          {customSellType === ConfigSellType.SingleTpsl ? <SingleSellSettings /> : <BatchSellSettings />}
        </div>
      )}
      <XModal
        showModal={showHint}
        setShowModal={setShowHint}
        description={descriptionList[currentHint]}
        showCloseButton={false}
      />
    </div>
  )
}

export default SellSettingsSection
