import React, { useCallback, useMemo, useState } from 'react'
import { useFormContext, Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { IconWarningCircleSolid } from '@/components/icon'
import { handleOnInput, WalletSettingsFormData } from './schema'
import { Switch } from '@/components/ui/switch'
import { XModal } from '@/components/ui'
import { useAppSelector } from '@/redux/store'
import { f } from 'fintech-number'
const SingleSellSettings: React.FC = () => {
  const {
    register,
    control,
    watch,
    formState: { errors },
  } = useFormContext<WalletSettingsFormData>()
  const { t } = useTranslation()
  const activeChain = useAppSelector((state) => state.wallet.activeChain)
  const [showHint, setShowHint] = useState<boolean>(false)

  const configAmount = watch('configAmount')
  const tp = watch('tp')
  const sl = watch('sl')

  const totalTp = useMemo(() => {
    if (!tp || !configAmount) return '--'
    return f((+tp * +configAmount) / 100, {
      decimal: 6,
    })
  }, [configAmount, tp])

  const totalSl = useMemo(() => {
    if (!sl || !configAmount) return '--'
    return f((+sl * +configAmount) / 100, {
      decimal: 6,
    })
  }, [configAmount, sl])
  return (
    <>
      <div className="flex items-start mb-2 mt-4">
        <span className="text-sm font-medium mr-3 leading-11 w-[100px]">{t('walletCopy.settings.takeProfit')}</span>
        <div className="flex-1">
          <div className="flex-1 bg-[#141414] rounded-md relative">
            <input
              placeholder={t('walletCopy.settings.enterTakeProfit')}
              className="w-full h-[44px] bg-transparent rounded-md pl-4 pr-10 outline-none text-sm"
              {...register('tp')}
              onKeyDown={(e) => handleOnInput(e, 2)}
            />
            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#9B9B9B]">%</span>
          </div>
          {errors.tp && (
            <div className="my-2 justify-start text-[#FF353C] text-xs font-normal leading-3">{errors.tp.message}</div>
          )}
          <div className="text-xs text-[#FFFFFFB2] mb-6 mt-2">
            {t('walletCopy.settings.estimatedProfit')} {totalTp} {activeChain.toUpperCase()}
          </div>
        </div>
      </div>

      <div className="flex items-start mb-2">
        <span className="text-sm font-medium mr-3 leading-11 w-[100px]">{t('walletCopy.settings.stopLoss')}</span>
        <div className="flex-1">
          <div className="flex-1 bg-[#141414] rounded-md relative">
            <input
              placeholder={t('walletCopy.settings.enterStopLoss')}
              className="w-full h-[44px] bg-transparent rounded-md pl-3 pr-9 outline-none text-sm"
              {...register('sl')}
              onKeyDown={(e) => handleOnInput(e, 2)}
            />
            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#9B9B9B]">%</span>
          </div>
          <div className="flex items-center justify-between mt-2">
            <div className="text-xs text-[#FFFFFFB2]">
              {t('walletCopy.settings.estimatedLoss')} {totalSl} {activeChain.toUpperCase()}
            </div>
            <div className="flex items-center gap-4 justify-between">
              <span className="inline-flex items-center">
                <label className="text-sm font-medium mr-1 text-[#FFFFFFCC]" htmlFor="trailingStopLoss">
                  {t('walletCopy.settings.trailingStopLoss')}
                </label>
                <IconWarningCircleSolid className="text-[#9B9B9B]" onClick={() => setShowHint(true)} />
              </span>
              <Controller
                name="trailingSl"
                control={control}
                render={({ field }) => (
                  <Switch id="trailingSl" ref={field.ref} checked={field.value} onCheckedChange={field.onChange} />
                )}
              />
            </div>
          </div>
        </div>
      </div>

      <XModal
        showModal={showHint}
        setShowModal={setShowHint}
        description={t('listCoin.copyTrade.hint.trailingStopLoss.intro')}
        showCloseButton={false}
      />
    </>
  )
}

export default SingleSellSettings
