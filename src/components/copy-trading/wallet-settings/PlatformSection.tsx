import React from 'react'
import { useForm<PERSON>ontext, Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { IconWarningCircleSolid } from '@/components/icon'
import { WalletSettingsFormData } from './schema'
import CheckboxWithLabel from '@/components/common/CheckboxWithLabel'
import { capitalize } from 'lodash-es'
import { XModal } from '@/components/ui'
import { ConfigPlatform } from '@/@generated/gql/graphql-trading'

const PlatformSection: React.FC = () => {
  const { t } = useTranslation()
  const {
    control,
    formState: { errors },
  } = useFormContext<WalletSettingsFormData>()
  const [showHint, setShowHint] = React.useState<boolean>(false)
  const LIST_PLATFORM = [ConfigPlatform.Pump, ConfigPlatform.Moonshot, ConfigPlatform.Raydium, ConfigPlatform.Others]
  const getLabelPlatform = (platform: ConfigPlatform) => {
    switch (platform) {
      case ConfigPlatform.Moonshot:
        return capitalize(t('walletCopy.settings.platform.moonshot'))
      case ConfigPlatform.Pump:
        return capitalize(t('walletCopy.settings.platform.pump'))
      case ConfigPlatform.Raydium:
        return capitalize(t('walletCopy.settings.platform.raydium'))
      default:
        return capitalize(t('walletCopy.settings.platform.other'))
    }
  }
  return (
    <div className="mt-6 flex flex-row gap-2">
      <div className="w-24 min-w-24 tracking-tighter flex flex-row items-start mb-4">
        <label className="text-primary text-base font-normal inline-flex items-center gap-1">
          {t('walletCopy.settings.platform.platform')}{' '}
          <IconWarningCircleSolid onClick={() => setShowHint(true)} className="cursor-pointer" />
        </label>
      </div>
      <div>
        <div className="grid grid-cols-2 gap-y-4 gap-x-6">
          <Controller
            control={control}
            name="platform"
            render={({ field }) => (
              <>
                {LIST_PLATFORM.map((platform) => (
                  <CheckboxWithLabel
                    label={getLabelPlatform(platform)}
                    // defaultChecked={field.value.includes(platform)}
                    checked={field.value.includes(platform)}
                    isChecked={field.value.includes(platform)}
                    isDisabled={field.value.includes(platform) && field.value.length === 1}
                    containerClassName=""
                    onChange={(checked) => {
                      let newList = [...field.value]
                      if (checked) {
                        if (!field.value.includes(platform)) {
                          newList = [...field.value, platform]
                        }
                      } else {
                        newList = field.value.filter((v) => v !== platform)
                      }
                      field.onChange(newList)
                    }}
                  />
                ))}
              </>
            )}
          />
        </div>
        {errors.platform && (
          <div className="text-red-500 text-xs mt-1">{t('walletCopy.settings.platform.error.minimumOne')}</div>
        )}
      </div>
      <XModal
        showModal={showHint}
        setShowModal={setShowHint}
        description={[t('listCoin.copyTrade.hint.platform.intro')]}
        showCloseButton={false}
      />
    </div>
  )
}

export default PlatformSection
