import { useContext } from 'react'
import { DiscoverPageContext } from '@components/discover/DiscoverPageContext.tsx'
import { TAB_CLASSIFICATION, TAB_MEME, TAB_WATCHLIST } from '@components/discover/DiscoverTabs.tsx'
import { TabWatchlist } from '@components/discover/tabs/TabWatchlist.tsx'
import { TabMeme } from '@components/discover/tabs/TabMeme.tsx'
import { TabTrending } from '@components/discover/tabs/TabTrending.tsx'
import { TabCategories } from '@components/discover/tabs/TabCategories.tsx'

export const DiscoverTabContent = () => {
  const { currentTab } = useContext(DiscoverPageContext)

  if (currentTab === TAB_WATCHLIST) return <TabWatchlist />
  if (currentTab === TAB_MEME) return <TabMeme />
  if (currentTab === TAB_CLASSIFICATION) return <TabCategories />
  return <TabTrending />
}
