import { useState, useMemo } from 'react'
import { HTMLAttributes } from 'react'
import { cn } from '@/lib/utils.ts'
import { z } from 'zod'
import { useTranslation } from 'react-i18next'
import { useAppDispatch, useAppSelector } from '@/redux/store'
import { useSelector } from 'react-redux'
import { RootState } from '@/redux/store'
import { CreateOrderInput, Order, OrderType, TransactionType } from '@/@generated/gql/graphql-trading'
import { useMultiChainWallet } from '@/hooks/useMultiChainWallet'
import { SOL_ADDRESS } from '@/hooks/useCreateOrder'
import {
  FRIST_FEE_SOL,
  MIN_BALANCE_SOL,
  TYPE_ACCOUNT,
  TYPE_CHAIN,
  PLATFORM_FEE_SOL,
  solanaConnection,
  BASIC_FEE,
  getIconChain,
} from '@/lib/blockchain'
import BigNumber from 'bignumber.js'
import { <PERSON>K<PERSON>, PublicKeyInitData, VersionedTransaction } from '@solana/web3.js'
import { Configs } from '@/const/configs'
import { useWallet } from '@solana/wallet-adapter-react'
import useSignWallet from '@/hooks/useSignWallet'
import { ServiceConfig } from '@/lib/gql/service-config'
import { toast } from 'sonner'
import { getAssociatedTokenAddress, TOKEN_PROGRAM_ID } from '@solana/spl-token'
import { orderActions } from '@/redux/modules/order.slice'
import useShowToastOrder from '@/hooks/useShowToastOrder'
import useNetworkFeeSubcription from '../mqtt/NetworkFeeSubcription'
import LoadingSpinner from '@/components/ui/loading-spinner.tsx'
import { formatChartPrice } from '@/utils/helpers'

const formSchema = z.object({
  transactionType: z.string({
    required_error: 'required',
  }),
  type: z.string({
    required_error: 'required',
  }),
  baseAddress: z.string({
    required_error: 'required',
  }),
  quoteAddress: z.string({
    required_error: 'required',
  }),
  userAddress: z.string({
    required_error: 'required',
  }),
  chainId: z.string({
    required_error: 'required',
  }),
  quoteAmount: z.string({}),
  baseAmount: z.string({}),
  doublePrincipalAfterPurchase: z.boolean({}),
  tp: z.string({}),
  sl: z.string({}),
  limitPrice: z.string({}),
  limitMarketCap: z.string({}),
  callbackRate: z.string({}),
  triggerPrice: z.string({}),
})

export type FormValues = z.infer<typeof formSchema>

type Props = {
  className?: HTMLAttributes<HTMLButtonElement>['className']
  token: any
}

export const QuickBuyButton = (props: Props) => {
  const { t } = useTranslation()
  const { className, token, ...rest } = props
  const dispatch = useAppDispatch()
  const activeChain = useAppSelector((state) => state.wallet.activeChain)
  const activeAccount = useAppSelector((state) => state.wallet.activeAccount)
  const { activeWallet } = useMultiChainWallet({})
  const { wallet, sendTransaction } = useWallet()
  const amount = useSelector((state: RootState) => state.quickBuy.amount)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const {
    showToastProcessOrder,
    showToastSubmittedSuccessOrder,
    showErrorMessageSubmitOrder,
    hiddenToastProcessOrder,
    isOrderProcessing,
  } = useShowToastOrder()

  const selectedPresetKey = useAppSelector((state) => state.tradeSettings.selectedPreset[activeChain])
  const newConfigs = useAppSelector((state) => state.tradeSettings.settings)?.[activeChain]?.[selectedPresetKey - 1]
  const { handleSignMessage } = useSignWallet({
    isAutoConnect: false,
  })
  const config = newConfigs?.buy
  const { maxcomputeUnit, priorityFeePrice } = useNetworkFeeSubcription()

  const fee = useMemo(() => {
    if (config) {
      return config?.fee?.type === 'custom' ? config?.fee?.value : priorityFeePrice?.[config?.fee?.type]
    }
    return 0
  }, [config, priorityFeePrice, config?.fee?.value])

  const handleCheckErrorSubmitCreateOrder = (values: FormValues) => {
    if (values.transactionType === TransactionType.Buy) {
      if (activeWallet?.balance?.formatted < MIN_BALANCE_SOL) {
        toast.error(
          t('orderForm.errors.rechargeIfBalanceInsufficientMinimum', {
            chain: activeChain.toUpperCase(),
            balance: MIN_BALANCE_SOL,
          }),
        )
        return false
      }
      if (values?.quoteAmount > activeWallet?.balance?.formatted) {
        toast.error(t('orderForm.errors.rechargeIfBalanceInsufficient'))
        return false
      }

      if (+values?.quoteAmount < 0.0000001) {
        toast.error(t('orderForm.errors.minimumOrderQuantity'))
        return false
      }

      const estimateQuoteAmount = +values?.quoteAmount * 1.01 + fee + BASIC_FEE
      if (estimateQuoteAmount > activeWallet?.balance?.formatted && values.transactionType === TransactionType.Buy) {
        toast.error(t('orderForm.errors.rechargeIfBalanceInsufficient'))
        return false
      }
    }

    if (!token) {
      toast.error(t('orderForm.errors.selectTransactionQuantity'))
      return false
    }

    return true
  }

  const createOrderByTele = async (params: CreateOrderInput) => {
    setIsLoading(true)
    dispatch(orderActions.newCreateOrder({ input: params }))
      .then((res) => {
        if (res?.meta?.requestStatus === 'fulfilled') {
          const orderRes = res?.payload?.createOrder
          if (params?.type === OrderType.Market) {
            showToastProcessOrder(orderRes)
          } else {
            toast.success(t('orderForm.status.createOrderSuccess'))
          }
        } else {
          const errCode = res?.payload?.[0]?.code as string
          showErrorMessageSubmitOrder(errCode ? t(`orderForm.status.${errCode}`) : t('orderForm.errors.orderFailed'))
        }
      })
      .finally(() => {
        setIsLoading(false)
      })
  }

  const createOrderByWallet = async (params: CreateOrderInput) => {
    const connection = solanaConnection
    if (params.transactionType === TransactionType.Buy) {
      const owner = new PublicKey(params?.userAddress)
      const tokenProgram = new PublicKey(TOKEN_PROGRAM_ID)
      const tokenAccounts = await connection.getParsedTokenAccountsByOwner(owner, {
        programId: tokenProgram,
      })
      let flag = 0
      for (let i = 0; i < tokenAccounts?.value.length; i++) {
        const data = tokenAccounts?.value?.[i]?.account?.data
        const tokenMint = data?.parsed?.info?.mint
        if (tokenMint === params.baseAddress) {
          flag = 1
          break
        }
      }

      if (flag === 0) {
        toast.info(
          t('orderForm.errors.firstTradeToken', {
            amount: FRIST_FEE_SOL,
            symbol: 'SOL',
          }),
        )

        const estimateQuoteAmount = +params?.quoteAmount * 1.01 + fee + FRIST_FEE_SOL + BASIC_FEE
        if (estimateQuoteAmount > activeWallet?.balance?.formatted) {
          toast.error(t('orderForm.errors.rechargeIfBalanceInsufficient'), {
            duration: 3500,
          })
          return
        }
      }
    }

    setIsLoading(true)
    const formatUnitBase = new BigNumber(10).pow(token?.decimals ? +token?.decimals : 6).toString() //tokenDetail?.decimals ? +tokenDetail?.decimals : 6
    const formatUnitQuote = new BigNumber(10).pow(9).toString()
    const isSell = params.transactionType === TransactionType.Sell
    const isBuy = params.transactionType === TransactionType.Buy
    const inputAmount = new BigNumber(
      params.transactionType === TransactionType.Buy ? params.quoteAmount : params.baseAmount,
    )
      .multipliedBy(isSell ? formatUnitBase : formatUnitQuote)
      .integerValue()
      .toString()
    const outputAmount = isBuy ? params.baseAmount : params.quoteAmount
    const inputAddress = isBuy ? params.quoteAddress : params.baseAddress
    const outputAddress = isBuy ? params.baseAddress : params.quoteAddress
    let amount = inputAmount
    let swapMode = 'ExactIn'
    if (inputAmount === 'NaN') {
      amount = outputAmount
      swapMode = 'ExactOut'
    }
    // if (swapMode === 'ExactIn') {
    //   amount = new BigNumber(inputAmount).dividedBy(1 - PLATFORM_FEE_SOL).toFixed(0)
    // }
    const slippage = new BigNumber((params.slippage ? params.slippage : 25) / 100).multipliedBy(10000).toString()
    const platformFee = new BigNumber(PLATFORM_FEE_SOL).multipliedBy(10000).toString()
    const quoteResponse = await (
      await fetch(
        `${Configs.urlApiJup}/swap/v1/quote?inputMint=${inputAddress}&outputMint=${outputAddress}&amount=${amount}&slippageBps=${slippage}&restrictIntermediateTokens=true&swapMode=${swapMode}&platformFeeBps=${platformFee}`,
      )
    ).json()

    if (quoteResponse?.error) {
      if (quoteResponse.errorCode === 'COULD_NOT_FIND_ANY_ROUTE') {
        showErrorMessageSubmitOrder(t('orderForm.status.ROUTE_NOT_FOUND'))
      } else {
        showErrorMessageSubmitOrder(t('orderForm.status.TRANSACTION_BUILD_FAILED'))
      }
      setIsLoading(false)
      return
    }

    const publicKey = new PublicKey(wallet?.adapter?.publicKey!.toBytes() as PublicKeyInitData).toString()

    const feeAccount = await getAssociatedTokenAddress(
      new PublicKey(params?.quoteAddress),
      new PublicKey('CG8Pfaf6BR2xvBSXkmy8ecx7NMsWJWFKMwR8TcLwf9Az'),
    ).catch(() => {
      showErrorMessageSubmitOrder(t('orderForm.status.ROUTE_NOT_FOUND'))
      setIsLoading(false)
    })

    const swapResponse = await (
      await fetch(`${Configs.urlApiJup}/swap/v1/swap`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          quoteResponse,
          userPublicKey: publicKey,
          dynamicComputeUnitLimit: false,
          dynamicSlippage: true,
          feeAccount: feeAccount,
        }),
      })
    ).json()

    if (swapResponse?.error) {
      showErrorMessageSubmitOrder(t('orderForm.status.TRANSACTION_BUILD_FAILED'))
      setIsLoading(false)
      return
    }

    const swapTransactionBuf = Buffer.from(swapResponse.swapTransaction, 'base64')
    const txid = await sendTransaction(VersionedTransaction.deserialize(swapTransactionBuf), connection, {}).catch(
      (res) => {
        setIsLoading(false)
        showErrorMessageSubmitOrder(t('orderForm.status.TRANSACTION_BUILD_FAILED'))
        return
      },
    )
    const paramsWeb3 = {
      ...params,
      baseSymbol: token.symbol,
      marketCap: token.marketCap,
      quoteSymbol: 'SOL',
    }

    if (txid) {
      showToastProcessOrder(paramsWeb3 as Order)
    }

    const latestBlockHash = await connection.getLatestBlockhash('processed').catch((res) => {
      setIsLoading(false)
      showErrorMessageSubmitOrder(t('orderForm.status.RPC_CONNECTION'))
      return
    })

    const confirmation = await connection
      .confirmTransaction({
        blockhash: latestBlockHash.blockhash,
        lastValidBlockHeight: latestBlockHash.lastValidBlockHeight,
        signature: txid,
      })
      .catch((res) => {
        showErrorMessageSubmitOrder(t('orderForm.status.TRANSACTION_BUILD_FAILED'))
        setIsLoading(false)
        return
      })

    if (confirmation?.value?.err) {
      // console.error('Transaction failed:', confirmation?.value?.err)
      // showErrorMessageSubmitOrder(t('orderForm.status.RPC_CONNECTION'))
    } else {
      if (txid) {
        dispatch(
          orderActions.createOrderByWeb3({
            input: {
              userAddress: paramsWeb3?.userAddress,
              txid: txid,
            },
          }),
        ).finally(() => {
          setIsLoading(false)
          hiddenToastProcessOrder()
          showToastSubmittedSuccessOrder(paramsWeb3 as Order)
        })
      }
    }
  }

  const submitBuyOrder = async (values: FormValues) => {
    if (activeAccount === TYPE_ACCOUNT.CHAIN && activeWallet.isConnected && !ServiceConfig.token) {
      handleSignMessage()
      toast.info(t('orderForm.errors.signMessageWalletInfo'))
      return
    }
    let params: CreateOrderInput = {} as CreateOrderInput
    params = {
      ...params,
      ...values,
      ...(!!config?.slippage && { slippage: ((config?.slippage !== 'auto' ? config?.slippage : 20) / 100).toString() }),
      quoteAmount: (+values.quoteAmount).toString(),
      baseAmount: (+values.baseAmount).toString(),
      mevProtect: config?.mevProtect,
      userAddress: activeWallet?.walletId,
      baseAddress: token?.token ? token?.token : '',
      priorityFeePrice: Math.floor((fee * Math.pow(10, 15)) / maxcomputeUnit) + '',
    } as CreateOrderInput

    if (values?.type === 'trailingTpSl') {
      params.callbackRate = +values?.callbackRate / 100
    } else {
      delete params['callbackRate']
      delete params['triggerPrice']
      // delete params['limitPrice']
    }
    if (values?.limitPrice) {
      delete params['limitMarketCap']
    }
    if (values?.limitMarketCap) {
      delete params['limitPrice']
    }
    if (values.transactionType === TransactionType.Buy) {
      delete params['baseAmount']
    }
    if (values.transactionType === TransactionType.Sell) {
      delete params['quoteAmount']
    }
    if (activeAccount === TYPE_ACCOUNT.TELEGRAM) {
      createOrderByTele(params)
    }
    if (activeAccount === TYPE_ACCOUNT.CHAIN) {
      if (activeChain === TYPE_CHAIN.ETH) {
        toast.info(t('orderForm.errors.changeTeleWalletToContinue'))
        return
      }
      createOrderByWallet(params)
    }
  }

  const onSubmitOrder = () => {
    const values: FormValues = {
      transactionType: TransactionType.Buy,
      type: OrderType.Market,
      baseAddress: token?.token ? token?.token : '',
      quoteAddress: SOL_ADDRESS,
      userAddress: activeWallet?.walletId,
      chainId: activeWallet?.chainId ? activeWallet?.chainId : '',
      quoteAmount: amount,
      baseAmount: '',
      doublePrincipalAfterPurchase: false,
      tp: '',
      sl: '',
      limitPrice: '',
      limitMarketCap: '',
      callbackRate: '',
      triggerPrice: '',
    }
    if (!handleCheckErrorSubmitCreateOrder(values)) return
    submitBuyOrder(values)
  }

  return (
    <button
      className={cn(
        'bg-[linear-gradient(43.83deg,#FC2CFF_0%,#FFFFFF_47.32%,#FFFFFF_63.89%,#00FFCA_103.57%)] flex items-center justify-center gap-0.5 h-6 min-w-8 px-2 rounded-full text-[#000000] text-[calc(11rem/16)] font-medium  hover:scale-105 transition-transform duration-200',
        className,
      )}
      {...rest}
      onClick={(e) => {
        e.stopPropagation()
        e.preventDefault()
        onSubmitOrder()
      }}
      disabled={!amount || isLoading || isOrderProcessing}
    >
      {isLoading || isOrderProcessing ? (
        <LoadingSpinner size={16} />
      ) : Number(amount) > 0 ? (
        <>
          <svg width="10" height="14" viewBox="0 0 10 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0.217889 7.86068L4.96098 1.02505C5.0727 0.864043 5.3253 0.943098 5.3253 1.13906V5.46867C5.3253 5.57913 5.41484 5.66867 5.5253 5.66867H9.47702C9.64004 5.66867 9.73458 5.85324 9.63933 5.98554L4.59122 12.9968C4.47776 13.1544 4.22892 13.0741 4.22892 12.8799V8.3747C4.22892 8.26424 4.13937 8.1747 4.02892 8.1747H0.382206C0.220778 8.1747 0.125861 7.99331 0.217889 7.86068Z"
              fill="black"
            />
          </svg>
          <div className="max-w-[20px] sm:max-w-[45px] overflow-hidden text-ellipsis whitespace-nowrap">
            {formatChartPrice(amount)}
          </div>
        </>
      ) : (
        <svg width="10" height="14" viewBox="0 0 10 14" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M0.217889 7.86068L4.96098 1.02505C5.0727 0.864043 5.3253 0.943098 5.3253 1.13906V5.46867C5.3253 5.57913 5.41484 5.66867 5.5253 5.66867H9.47702C9.64004 5.66867 9.73458 5.85324 9.63933 5.98554L4.59122 12.9968C4.47776 13.1544 4.22892 13.0741 4.22892 12.8799V8.3747C4.22892 8.26424 4.13937 8.1747 4.02892 8.1747H0.382206C0.220778 8.1747 0.125861 7.99331 0.217889 7.86068Z"
            fill="black"
          />
        </svg>
      )}
    </button>
  )
}
