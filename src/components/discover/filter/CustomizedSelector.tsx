import { RangeItem } from '@components/discover/filter/FilterFormData.ts'
import { CustomizeInput } from '@components/discover/filter/CustomizeInput.tsx'
import { useTranslation } from 'react-i18next'

export interface CustomizedSelectorProps {
  minimumLabel: string
  maximumLabel: string
  unit: string
  min: number | undefined
  max: number | undefined
  onChange: (item: RangeItem) => void
}

export const CustomizedSelector = (props: CustomizedSelectorProps) => {
  const { minimumLabel, maximumLabel, min, max, onChange, unit } = props
  const { t } = useTranslation()

  const handleMinChange = (newValue: number | null) => {
    onChange({
      min: newValue !== null ? +newValue : undefined,
      max: max,
      isCustom: true,
    })
  }

  const handleMaxChange = (newValue: number | null) => {
    onChange({
      min: min,
      max: newValue !== null ? +newValue : undefined,
      isCustom: true,
    })
  }

  return (
    <div>
      <div className="grid grid-cols-2 gap-3">
        <div>
          <CustomizeInput value={min} label={minimumLabel} suffix={unit} onValueChange={handleMinChange} />
        </div>
        <div>
          <CustomizeInput value={max} onValueChange={handleMaxChange} label={maximumLabel} suffix={unit} />
        </div>
      </div>
      {!!(min && max && min > max) && (
        <div className="mt-3 text-red-500 text-[calc(12px)]">{t('filter.minMaxError')}</div>
      )}
    </div>
  )
}
