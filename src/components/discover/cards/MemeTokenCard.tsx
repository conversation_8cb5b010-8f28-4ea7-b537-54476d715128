import { CopyButton } from '@components/common/copy-button.tsx'
import { TokenAge } from '@components/listCoin/TokenAge.tsx'
import { MarketDisplay } from '@components/common/FormattingDisplay.tsx'
import { fShortenNumber, parseNumber } from '@/lib/number.ts'
import { listCoinHelper } from '@/utils/list-coin-helper.ts'
import { QuickBuyButton } from '@components/discover/QuickBuyButton.tsx'
import { useTokenStatistic } from '@hooks/useTokenPrice.ts'
import { useTranslation } from 'react-i18next'
import { getBlock<PERSON>hainLogo, getLaunchpad, isNumber } from '@/utils/helpers.ts'
import { Link, useNavigate } from 'react-router-dom'
import { APP_PATH, CHAIN_SYMBOLS, LaunchPlatformOptions } from '@/lib/constant.ts'
import { MemeDto } from '@/@generated/gql/graphql-core.ts'
import { TimeframeOption } from '@components/discover/TimeframeSelector.tsx'
import { MouseEvent, ReactElement } from 'react'
import { TokenAvatarWithProgress } from '@components/discover/cards/TokenAvatarWithProgress.tsx'
import { IconCook } from '@components/icon/gradient/IconCook.tsx'
import { NumberOfTransactionPill } from '@components/discover/NumberOfTransactionPill.tsx'
import { IconTop10 } from '@components/icon/gradient/IconTop10.tsx'
import { IconSniper } from '@components/icon/gradient/IconSniper.tsx'
import { IconMouse } from '@components/icon/gradient/IconMouse.tsx'
import { IconMoneyChange } from '@components/icon/gradient/IconMoneyChange.tsx'
import { IconProfile2User } from '@components/icon/stroke/IconProfile2User.tsx'
import { IconDiamondDollar } from '@components/icon/stroke/IconDiamondDollar.tsx'
import { IconLaunch } from '@components/icon/stroke/IconLaunch.tsx'
import { IconsGroup } from '@components/discover/IconsGroup.tsx'
import { toast } from 'sonner'
import {
  HolderToastMessage,
  InsiderToastMessage,
  LaunchedToastMessage,
  SameSourceWalletToastMessage,
  SmartMoneyToastMessage,
  SniperToastMessage,
  Top10HoldersToastMessage,
  TransactionToastMessage,
  VolumeToastMessage,
} from '@components/discover/toasts/BaseToastMessage.tsx'
import { cn, getPath } from '@/lib/utils.ts'
import AiIcon from '@components/common/Card/AiIcon.tsx'

export interface MemeTokenCardProps {
  token: MemeDto
  timeframe: TimeframeOption
  showProgress?: boolean
  onAiClick?: (token: MemeDto) => void
}

const getLaunchpadLogo = (token: MemeDto) => {
  const launchpad = token.dexes ? getLaunchpad(token.dexes) : ''
  return LaunchPlatformOptions.find((item) => {
    return item.value === launchpad
  })?.icon
}

const getNumberOfTx = (token: MemeDto, timeframe: TimeframeOption) => {
  switch (timeframe) {
    case '1m':
      return {
        buy: token.buyTxs1m,
        sell: token.sellTxs1m,
      }
    case '5m':
      return {
        buy: token.buyTxs5m,
        sell: token.sellTxs5m,
      }
    case '1h':
      return {
        buy: token.buyTxs1h,
        sell: token.sellTxs1h,
      }
    case '6h':
      return {
        buy: token.buyTxs6h,
        sell: token.sellTxs6h,
      }
    case '24h':
      return {
        buy: token.buyTxs24h,
        sell: token.sellTxs24h,
      }
    default:
      return {
        buy: 0,
        sell: 0,
      }
  }
}

type PropertyKey = 'top10' | 'sniper' | 'insider' | 'sameOrigin' | 'holder' | 'smartMoney' | 'devLaunched'

type BottomItem = {
  key: PropertyKey
  value: string
  onClick?: () => void
  warning?: boolean
}

const getIcon = (key: PropertyKey, warning?: boolean) => {
  switch (key) {
    case 'top10':
      return <IconTop10 gradient={!warning} />
    case 'sniper':
      return <IconSniper gradient={!warning} />
    case 'insider':
      return <IconMouse gradient={!warning} />
    case 'sameOrigin':
      return <IconMoneyChange gradient={!warning} />
    case 'holder':
      return <IconProfile2User />
    case 'smartMoney':
      return <IconDiamondDollar />
    case 'devLaunched':
      return <IconLaunch />
  }
}

type ItemProps = {
  itemKey: PropertyKey
  value: string
  onClick?: () => void
  warning?: boolean
}

const LeftItem = (props: ItemProps) => {
  const { itemKey, value, onClick, warning } = props
  const handleClick = (event: MouseEvent) => {
    event.stopPropagation()
    event.preventDefault()
    onClick?.()
  }
  return (
    <div
      className={cn(
        'flex items-center gap-0.5 px-1.5 py-0.5 border rounded-full border-[#ECECED1F] cursor-pointer',
        warning ? 'text-[#FF353C]' : '',
      )}
      onClick={handleClick}
    >
      {getIcon(itemKey, warning)}
      <span
        className={cn(
          'text-[calc(10rem/16)] leading-4',
          warning ? 'text-[#FF353C]' : 'text-transparent bg-gradient-to-b from-[#00FFAE] to-[#00FFF7] bg-clip-text',
        )}
      >
        {value}
      </span>
    </div>
  )
}

const RightItem = (props: ItemProps) => {
  const { itemKey, value, onClick } = props
  const handleClick = (event: MouseEvent) => {
    event.stopPropagation()
    event.preventDefault()
    onClick?.()
  }
  return (
    <div className="flex items-center gap-1 cursor-pointer" onClick={handleClick}>
      {getIcon(itemKey)}
      <span className="text-[calc(11rem/16)] text-[#FFFFFFCC]">{value}</span>
    </div>
  )
}

const normalizeNumber = (value: number) => {
  if (value > 1e15) return '>9999T'
  return fShortenNumber(value)
}

const formatPercentage = (value: number) => {
  const normalizedValue = Math.max(0, Math.min(value, 100))
  return fShortenNumber(normalizedValue) + '%'
}

const TOAST_DURATION = 3000

const showToast = (jsx: (id: number | string) => ReactElement) => {
  toast.custom(jsx, {
    duration: TOAST_DURATION,
    className: '!bg-transparent',
    style: {
      background: 'transparent',
      boxShadow: 'none',
    },
  })
}

const BottomSection = (props: { token: MemeDto }) => {
  const { token } = props

  // TODO: Replace with actual data
  const leftItems: BottomItem[] = [
    {
      key: 'top10',
      value: token.top10Holder ? formatPercentage(token.top10Holder) : '--',
      warning: token.top10Holder > 20,
      onClick: () => showToast((id) => <Top10HoldersToastMessage id={id} top10Percent={token.top10Holder} />),
    },
    {
      key: 'sniper',
      value: token.txBySniperPct && isNumber(+token.txBySniperPct) ? formatPercentage(token.txBySniperPct) : '--',
      warning: token.txBySniperPct > 20,
      onClick: () => showToast((id) => <SniperToastMessage id={id} />),
    },
    {
      key: 'insider',
      value: token.insider ? formatPercentage(token.insider) : '--',
      onClick: () => showToast((id) => <InsiderToastMessage id={id} />),
    },
    {
      key: 'sameOrigin',
      value: token.sameSourceWallet ? formatPercentage(+token.sameSourceWallet) : '--',
      onClick: () => showToast((id) => <SameSourceWalletToastMessage id={id} />),
    },
  ]

  const rightItems: BottomItem[] = [
    {
      key: 'holder',
      value: token.numberOfHolder ? normalizeNumber(token.numberOfHolder) : '--',
      onClick: () => showToast((id) => <HolderToastMessage id={id} />),
    },
    {
      key: 'smartMoney',
      value: token.smartMoneyPct ? token.smartMoneyPct : '--',
      onClick: () => showToast((id) => <SmartMoneyToastMessage id={id} />),
    },
    {
      key: 'devLaunched',
      value: token.devLaunched ? token.devLaunched.toString() : '--',
      onClick: () => showToast((id) => <LaunchedToastMessage id={id} />),
    },
  ]

  return (
    <div className="py-1 flex items-center justify-between bg-[#ECECED0F] pl-1.5 pr-2.5 rounded-b-[6px]">
      <div className="flex items-center gap-2">
        {leftItems.map((item) => (
          <LeftItem
            key={item.key}
            itemKey={item.key}
            value={item.value}
            onClick={item.onClick}
            warning={item.warning}
          />
        ))}
      </div>
      <div className="flex items-center gap-2">
        {rightItems.map((item) => (
          <RightItem key={item.key} itemKey={item.key} value={item.value} onClick={item.onClick} />
        ))}
      </div>
    </div>
  )
}

export const MemeTokenCard = (props: MemeTokenCardProps) => {
  const { token: original, timeframe, showProgress, onAiClick } = props
  const token = useTokenStatistic(original)
  const { t } = useTranslation()
  const tokenLogo = token.image ?? getBlockChainLogo(token.chainId, token.token)
  const launchpadLogo = getLaunchpadLogo(token)
  const navigate = useNavigate()

  const navigateToTokenDetail = () => {
    navigate(
      getPath(APP_PATH.MEME_TOKEN_DETAIL, {
        address: token.token,
        chain: CHAIN_SYMBOLS[token.chainId],
      }),
    )
  }

  const { buy, sell } = getNumberOfTx(token, timeframe)

  const handleOnTxsClick = (event: MouseEvent) => {
    event.stopPropagation()
    event.preventDefault()
    showToast((id) => <TransactionToastMessage id={id} buy={buy} sell={sell} timeframe={timeframe} />)
  }

  const handleOnVolumeClick = (event: MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()

    showToast((id) => <VolumeToastMessage id={id} timeframe={timeframe} />)
  }

  const handleOnAiClick = (event: MouseEvent) => {
    event.stopPropagation()
    event.preventDefault()
    onAiClick?.(token)
  }

  return (
    <Link to={getPath(APP_PATH.MEME_TOKEN_DETAIL, { address: token.token, chain: CHAIN_SYMBOLS[token.chainId] })}>
      <div
        className="p-[1px] rounded-[6px] bg-[linear-gradient(104.53deg,#00FFB414_11.84%,#01B7820A_40.93%,#00996C00_70.02%,#00FFB466_91.19%)] cursor-pointer"
        onClick={navigateToTokenDetail}
      >
        <div className="bg-[#111111] rounded-[6px] w-full h-full">
          <div className="rounded-[6px] bg-[linear-gradient(105.98deg,#00E9A506_46.27%,#00FFB40F_93.65%)]">
            <div className="flex items-center pl-1.5 pr-2.5 pt-1.5 pb-1">
              <div className="flex items-center flex-1">
                <TokenAvatarWithProgress
                  tokenAvatar={tokenLogo}
                  chainLogo={launchpadLogo}
                  name={token.symbol as string}
                  progress={+token.internalMarketProgress}
                  showProgress={showProgress}
                />
                <div className="ml-2 flex-1 h-full">
                  <div className="flex items-center gap-1.5 mb-1.5">
                    <span className="text-title font-bold text-[calc(1rem*(13/16))] leading-[calc(1rem*(13/16))]">
                      {token.symbol ?? '--'}
                    </span>
                    <CopyButton text={token.token} icon="/images/icons/ic-copy2.svg" />
                    <div className="flex items-center gap-1">
                      <IconCook />
                      <span className="text-[calc(10rem/16)] font-medium bg-[linear-gradient(180deg,#00FFAE_0%,#00FFF7_100%)] text-transparent bg-clip-text">
                        {token.devHold !== undefined && token.devHold !== null
                          ? `${fShortenNumber(token.devHold)}%`
                          : '--'}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-1.5 mb-1.5">
                    <TokenAge createdTime={token.createdTime} />
                    <button className="cursor-pointer" onClick={handleOnAiClick}>
                      <AiIcon />
                    </button>
                    <IconsGroup
                      tokenAddress={token.token}
                      twitterUrl={token.twitterUrl as string}
                      websiteUrl={token.website as string}
                      twitterChangeCount={token.twitterNameChangeCount ? +token.twitterNameChangeCount : 0}
                      twitterPostId={token.tweetId as string}
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-[calc(12rem/16)] leading-4 cursor-pointer" onClick={handleOnVolumeClick}>
                      <span className="text-[calc(10rem/16)] text-[#FFFFFF80]">VL</span>{' '}
                      {token[`volume${timeframe}`] ? `$${listCoinHelper.getVolumes(token, timeframe)}` : '--'}
                    </div>
                    <div className="flex items-center gap-2 cursor-pointer" onClick={handleOnTxsClick}>
                      <div className="text-[calc(12rem/16)] leading-4">
                        <span className="text-[calc(10rem/16)] text-[#FFFFFF80]">TX</span>{' '}
                        {listCoinHelper.getNumOfTransactions(token, timeframe)}
                      </div>
                      <NumberOfTransactionPill buy={buy} sell={sell} />
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex items-center">
                <div className="mr-4 text-right">
                  <MarketDisplay
                    value={parseNumber(token.marketcap)}
                    className={'text-title text-[calc(1rem*(13/16))] leading-[calc(1rem*(13/16))] font-bold mb-1'}
                    showColor={true}
                  />
                  <p className="text-[calc(1rem*(10/16))] leading-[calc(1rem*(10/16))] text-(--text-tertiary)">
                    {t('listCoin.columns.marketCap')}
                  </p>
                </div>
                <QuickBuyButton token={token} />
              </div>
            </div>
            <BottomSection token={token} />
          </div>
        </div>
      </div>
    </Link>
  )
}
