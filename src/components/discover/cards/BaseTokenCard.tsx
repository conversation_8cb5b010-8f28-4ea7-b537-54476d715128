import { TokenStatisticDto } from '@/@generated/gql/graphql-core.ts'
import { useTokenStatistic } from '@hooks/useTokenPrice.ts'
import { useTranslation } from 'react-i18next'
import { getBlock<PERSON>hain<PERSON>ogo, getLaunchpad } from '@/utils/helpers.ts'
import { useTrendChartData } from '@hooks/useTrendChartData.ts'
import { fShortenNumber, parseNumber } from '@/lib/number.ts'
import { TokenAge } from '@components/listCoin/TokenAge.tsx'
import AIAnalysisDrawer from '@components/listCoin/AIAnalysisDrawer.tsx'
import { MarketDisplay } from '@components/common/FormattingDisplay.tsx'
import TrendChart from '@components/listCoin/card/TrendChart.tsx'
import { listCoinHelper } from '@/utils/list-coin-helper.ts'
import { TokenTrending } from '@/types/token.ts'
import { TimeframeOption } from '@components/discover/TimeframeSelector.tsx'
import { TokenAvatar } from '@components/discover/cards/TokenAvatar.tsx'
import { APP_PATH, CHAIN_SYMBOLS, LaunchPlatformOptions } from '@/lib/constant.ts'
import { CopyButton } from '@components/common/copy-button.tsx'
import { QuickBuyButton } from '@components/discover/QuickBuyButton.tsx'
import { Link } from 'react-router-dom'
import { ReactNode, MouseEvent } from 'react'
import { cn, getPath } from '@/lib/utils.ts'
import { IconsGroup } from '@components/discover/IconsGroup.tsx'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@components/ui/tooltip.tsx'

export type SubItem = {
  label: string
  value: string | ReactNode
}

export interface BaseTokenCardProps {
  token: TokenStatisticDto
  timeframe: TimeframeOption
  subItems: SubItem[]
  disabled?: boolean
  showTrendingScore?: boolean
  showTrendingScoreDebug?: boolean
}

const getLaunchpadLogo = (token: TokenStatisticDto) => {
  const launchpad = token.dexes ? getLaunchpad(token.dexes) : ''
  return LaunchPlatformOptions.find((item) => {
    return item.value === launchpad
  })?.icon
}

const classNames4Cols = ['sm:col-span-3', 'sm:col-span-3', 'sm:col-span-2', 'sm:col-span-2 sm:text-end']

const TokenSymbol = (props: { symbol: string; tooltip: boolean; tooltipText?: string }) => {
  const { symbol, tooltip, tooltipText } = props

  if (!tooltip) {
    return (
      <span className="text-title font-bold text-[calc(1rem*(13/16))] leading-[calc(1rem*(13/16))]">
        {symbol ?? '--'}
      </span>
    )
  }

  const preventTooltip = (event: MouseEvent) => {
    event.stopPropagation()
    event.preventDefault()
  }
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <span className="text-title font-bold text-[calc(1rem*(13/16))] leading-[calc(1rem*(13/16))]">
            {symbol ?? '--'}
          </span>
        </TooltipTrigger>
        <TooltipContent
          onClick={preventTooltip}
          className="bg-[linear-gradient(90deg,#A53EFF66_20%,#00F7A566_100%)] p-[1px] rounded-[4px]"
        >
          <p className="bg-[#141414] px-2 py-1 rounded-[4px]">{tooltipText}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

const TrendingScoreExplanation = () => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger
          className="cursor-pointer"
          onClick={(event: MouseEvent) => {
            event.stopPropagation()
            event.preventDefault()
          }}
        >
          <img
            src="/images/icons/info.svg"
            alt="info"
            className="w-4 h-4"
            onMouseDown={(event: MouseEvent) => {
              event.preventDefault()
            }}
          />
        </TooltipTrigger>
        <TooltipContent
          onClick={(event: MouseEvent) => {
            event.stopPropagation()
          }}
          className="bg-[linear-gradient(90deg,#A53EFF66_20%,#00F7A566_100%)] p-[1px] rounded-[4px]"
          collisionBoundary={document.getElementById('main-content') as HTMLElement}
        >
          <p className="bg-[#141414] px-2 py-3 rounded-[4px]">
            <span className="font-medium italic">TrendingScore = BaseScore × AgeBoost × SecBoost</span>
            <br />
            <br />
            <span>BaseScore = 0.25*z(TX) + 0.20*z(VOL) + 0.15*z(ΔP) + 0.10*z(Hgrow) + 0.10*z(LiqMC)</span>
            <br />
            <br />
            AgeBoost =
            <ul className="list-disc pl-8">
              <li>
                2.0 if <b>age_hours</b> ≤ 2
              </li>
              <li>
                1.5 if 2 &lt; <b>age_hours</b> ≤ 6
              </li>
              <li>
                1.2 if 6 &lt; <b>age_hours</b> ≤ 24
              </li>
              <li>
                1.0 if <b>age_hours</b> &gt; 24h
              </li>
            </ul>
            <br />
            SecBoost =
            <ul className="list-disc pl-8">
              <li>1.0 if token is safe</li>
              <li>0.5 if token is risky</li>
            </ul>
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

export const BaseTokenCard = (props: BaseTokenCardProps) => {
  const {
    token: original,
    timeframe,
    subItems,
    disabled,
    showTrendingScore = false,
    showTrendingScoreDebug = false,
  } = props
  const token = useTokenStatistic(original)
  const { t } = useTranslation()
  const tokenLogo = token.image ?? getBlockChainLogo(token.chainId, token.token)
  const tokenTrending = token as unknown as TokenTrending
  const { pricesData, volumesData, chartLength } = useTrendChartData({
    token: tokenTrending,
    timeframe: timeframe as string,
  })
  const launchpadLogo = getLaunchpadLogo(token)
  const trend = listCoinHelper.getTokenTrend(tokenTrending, timeframe)

  return (
    <Link to={getPath(APP_PATH.MEME_TOKEN_DETAIL, { address: token.token, chain: CHAIN_SYMBOLS[token.chainId] })}>
      <div
        className={cn(
          'p-[1px] rounded-[6px]',
          disabled ? 'cursor-not-allowed' : 'cursor-pointer',
          trend === 'down'
            ? 'bg-[linear-gradient(104.53deg,var(--fall-opacity-8)_11.84%,#01B7820A_40.93%,#00996C00_70.02%,var(--fall-opacity-40)_91.19%)]'
            : 'bg-[linear-gradient(104.53deg,var(--rise-opacity-8)_11.84%,#01B7820A_40.93%,#00996C00_70.02%,var(--rise-opacity-40)_91.19%)]',
        )}
      >
        <div className="bg-[#111111] rounded-[6px] w-full h-full">
          <div
            className={cn(
              'rounded-[6px]',
              trend === 'down'
                ? 'bg-[linear-gradient(105.98deg,#00E9A502_46.27%,var(--fall-opacity-6)_93.65%)]'
                : 'bg-[linear-gradient(105.98deg,#00E9A502_46.27%,var(--rise-opacity-6)_93.65%)]',
            )}
          >
            <div className="flex items-center pl-1.5 pr-2.5 pt-1.5 pb-1">
              <div className="flex items-center flex-1">
                <TokenAvatar tokenAvatar={tokenLogo} chainLogo={launchpadLogo} name={token.symbol as string} />
                <div className="ml-2 flex-1 h-full">
                  <div className="flex items-center gap-1.5 mb-1.5">
                    <TokenSymbol
                      symbol={token.symbol ?? '--'}
                      tooltip={showTrendingScore}
                      tooltipText={`${t('listCoin.trendingScore')}: ${token[`trendingScore${timeframe}`] ? fShortenNumber(token[`trendingScore${timeframe}`] as number) : '--'}`}
                    />
                    {showTrendingScoreDebug && <TrendingScoreExplanation />}
                    <CopyButton text={token.token} icon="/images/icons/ic-copy2.svg" />
                    <AIAnalysisDrawer address={token.token} />
                  </div>

                  <div className="flex items-center space-x-1.5">
                    <TokenAge createdTime={token.createdTime} />
                    <IconsGroup
                      tokenAddress={token.token}
                      twitterUrl={token.twitterUrl as string}
                      websiteUrl={token.website as string}
                    />
                  </div>
                </div>
              </div>
              <div className="flex items-center md:gap-3">
                <div className="mr-1 text-right">
                  <MarketDisplay
                    value={parseNumber(token.marketcap)}
                    className={'text-title text-[calc(1rem*(13/16))] leading-[calc(1rem*(13/16))] font-bold mb-1'}
                    showColor
                  />
                  <p className="text-[calc(1rem*(10/16))] leading-[calc(1rem*(10/16))] text-(--text-tertiary)">
                    {t('listCoin.columns.marketCap')}
                  </p>
                </div>
                <TrendChart
                  lineData={pricesData}
                  barData={volumesData}
                  barLength={chartLength}
                  glowingEffect
                  trend={trend}
                />
                <QuickBuyButton token={token} className="md:ml-1" />
              </div>
            </div>
            <div className="flex justify-between sm:grid sm:grid-cols-10 bg-[#ECECED0F] px-2.5 rounded-b-[6px]">
              {subItems.map((subItem, index) => (
                <div key={index} className={cn('py-1', classNames4Cols[index])}>
                  <div className="text-[#FFFFFF80] text-[calc(1rem*(10/16))] leading-4 break-keep">
                    <span className="text-[calc(1rem*(10/16))]">{subItem.label} </span>
                    <span className="text-[calc(1rem*(11/16))] font-medium text-[#FFFFFFCC]">{subItem.value}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Link>
  )
}
