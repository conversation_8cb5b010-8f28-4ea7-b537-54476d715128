import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import TradeSettingsBottomSheet from '@components/common/TradeSettingsBottomSheet.tsx'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '@/redux/store'
import { setQuickBuyAmount } from '@/redux/modules/quickBuy.slice'

const QuickBuy = () => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const amount = useSelector((state: RootState) => state.quickBuy.amount)
  const [openTradeSettings, setOpenTradeSettings] = useState(false)

  return (
    <div className="flex items-center justify-center gap-2 bg-transparent px-2 py-1 rounded-sm border-gradient">
      <div className="flex items-center justify-center gap-2">
        <div className="flex items-center justify-center gap-1">
          <img src="/images/icons/quickbuy-gradient.svg" alt="quick buy" className="w-[14px] h-[14px" />
          <span className="hidden sm:block text-[12px] text-white/70 font-normal leading-none whitespace-nowrap pr-2 border-r border-[#ECECED14]">
            {t('listCoin.quickBuy')}
          </span>
        </div>
        <input
          className="max-w-[50px] font-semibold text-[13px] leading-none text-white text-right"
          placeholder={t('orderForm.inputs.amount')}
          value={amount}
          onChange={(e) => {
            let newValue = e.target.value.replace(/[^0-9.]/g, '')
            if (newValue.includes('.')) {
              const parts = newValue.split('.')
              newValue = parts[0] + '.' + parts[1]
            }
            dispatch(setQuickBuyAmount(newValue))
          }}
        ></input>
      </div>
      <TradeSettingsBottomSheet open={openTradeSettings} setOpen={setOpenTradeSettings} />
    </div>
  )
}

export default QuickBuy
