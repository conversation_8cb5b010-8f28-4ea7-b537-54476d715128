import StatsCard from '@components/orderBook/StatsCard.tsx'
import { Button } from '@components/ui/button.tsx'
import { useTranslation } from 'react-i18next'
import { LastTransaction, Sort_Type, TokenDetail } from '@/@generated/gql/graphql-core'
import { formatMoney } from '@/utils/helpers'
import { fShortenNumber } from '@/lib/number.ts'
import { MarketDisplay } from '@components/common/FormattingDisplay.tsx'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { gqlClient } from '@/lib/gql/apollo-client'
import { useQuery } from '@apollo/client'
import { getLastTransactions } from '@/services/order.service'
import { Popover, PopoverContent, PopoverTrigger } from '@components/ui/popover.tsx'
import { useSubscription } from '@/lib/mqtt'
import { useAppDispatch, useAppSelector } from '@/redux/store'
import {
  selectLastTransactionUpdated,
  updateLastTransactionUpdated,
} from '@/redux/modules/lastTransactionSubscription.slice'
import { GetLastTransactionsResponse } from '@/types/responses.ts'
import { cn } from '@/lib/utils.ts'
import { DisplayPriceType, FilterTransactionAmountType } from '@/types/enums.ts'
import OrderBookTable from '@components/orderBook/OrderBookTable.tsx'
import useTokenPrice, { useTokenPriceInfo } from '@hooks/useTokenPrice.ts'
import { IconSpinner } from '@components/icon'
import { LIMIT_PER_PAGE } from '@const/smartMoney.ts'

interface OrderBookProps {
  tokenDetail: TokenDetail
}

const FILTER_OPTIONS = [
  { key: 'all', label: 'orderBook.all', range: [0, Infinity] },
  { key: 'lt1k', label: '< 1K', range: [0, 1000] },
  { key: '1k-3k', label: '1K - 3K', range: [1000, 3000] },
  { key: '3k-10k', label: '3K - 10K', range: [3000, 10000] },
  { key: 'gt10k', label: '> 10K', range: [10000, Infinity] },
]

const OrderBook: React.FC<OrderBookProps> = ({ tokenDetail }) => {
  const { t } = useTranslation()

  const dispatch = useAppDispatch()
  const lastTransactionUpdated = useAppSelector(selectLastTransactionUpdated)

  const tokenStatistic = useTokenPriceInfo(tokenDetail?.address ?? "")
  const price = useTokenPrice(tokenDetail?.address ?? "", tokenDetail?.price)

  const totalSupply = Number(tokenDetail?.totalSupply)
  const marketCap = (totalSupply * price) / (tokenDetail?.decimals ? Math.pow(10, Number(tokenDetail.decimals)) : 1)
  const volume = Number(tokenStatistic?.volume24h) > 0 ? Number(tokenStatistic?.volume24h) : Number(tokenDetail?.volume24h)
  const pool = Number(tokenStatistic?.liquidity) > 0 ? Number(tokenStatistic?.liquidity) : Number(tokenDetail?.liquidity)
  const tokenAddress = tokenDetail?.address ?? ''
  const skipQuery = !tokenAddress

  const [selectedFilter, setSelectedFilter] = useState<string>('all')
  const [filterOpen, setFilterOpen] = useState(false)
  const [sortDirection, setSortDirection] = useState<Sort_Type>(Sort_Type.Desc)
  const [currency, setCurrency] = useState<FilterTransactionAmountType>(FilterTransactionAmountType.USDT)
  const [priceType, setPriceType] = useState<DisplayPriceType>(DisplayPriceType.PRICE)
  const [isShowOverlay, setIsShowOverlay] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [lastTimestamp, setLastTimestamp] = useState<number | undefined>(undefined);

  const scrollRef = useRef<HTMLDivElement>(null);

  const getQueryVariables = useMemo(() => {
    if (selectedFilter === 'all') {
      return {
        token: tokenAddress,
      }
    }

    const filterOption = FILTER_OPTIONS.find((option) => option.key === selectedFilter)
    if (!filterOption) return { token: tokenAddress, limit: 1 }

    const [min, max] = filterOption.range
    return {
      token: tokenAddress,
      minAmount: min,
      maxAmount: max === Infinity ? null : max,
    }
  }, [selectedFilter, tokenAddress])

  const {
    data,
    loading,
    // error
  } = useQuery<GetLastTransactionsResponse>(getLastTransactions, {
    client: gqlClient,
    variables: {
      input: {
        ...getQueryVariables,
        lastTimestamp: lastTimestamp ? Number(lastTimestamp) : undefined,
        limit: LIMIT_PER_PAGE, // default limit for pagination
      },
    },
    skip: skipQuery,
  })

  const { message: _transactionMessage } = useSubscription(`public/transaction/new/${tokenAddress}`, {
    shouldSkip: !tokenAddress,
    clientOptions: {
      qos: 0,
    },
  })

  const handleClickSort = () => {
    setSortDirection((prev) => (prev === Sort_Type.Desc ? Sort_Type.Asc : Sort_Type.Desc))
  }

  const handleChangeCurrency = () => {
    setCurrency((prev) =>
      prev === FilterTransactionAmountType.USDT ? FilterTransactionAmountType.SOL : FilterTransactionAmountType.USDT,
    )
  }

  const handleChangePriceType = () => {
    setPriceType((prev) => (prev === DisplayPriceType.PRICE ? DisplayPriceType.MC : DisplayPriceType.PRICE))
  }

  const filteredTransactions = useMemo(() => {
    if (!lastTransactionUpdated || !Array.isArray(lastTransactionUpdated)) return []

    if (selectedFilter === 'all') {
      return sortDirection === Sort_Type.Desc
        ? lastTransactionUpdated
        : lastTransactionUpdated?.slice(0)?.reverse()
    }

    const filterOption = FILTER_OPTIONS.find((option) => option.key === selectedFilter)
    if (!filterOption) return []

    const [min, max] = filterOption.range
    return lastTransactionUpdated.filter((transaction) => {
      const amount = parseFloat(String(transaction?.baseAmount))
      return (
        amount > min
        && amount <= max
        && Number(transaction?.baseAmount) > 0
        && Number(transaction?.priceUsd) > 0
        && Number(transaction?.quoteAmount) > 0
      )
    })
  }, [lastTransactionUpdated, selectedFilter])

  const maxPriceUsd = useMemo(() => {
    if (!filteredTransactions.length) return 0

    return filteredTransactions
      .slice(0, 20)
      .reduce((max, tx) => {
        const price = Number(tx?.baseAmount) * Number(tx?.priceUsd)
        return price > max ? price : max
      }, 0)
  }, [filteredTransactions])

  const handleFilterSelect = (filterKey: string) => {
    setSelectedFilter(filterKey)
    setFilterOpen(false)
    setLastTimestamp(undefined)
  }

  const getSelectedFilterLabel = () => {
    const filter = FILTER_OPTIONS.find((option) => option.key === selectedFilter)
    return filter && filter?.key !== 'all' ? filter.label : t('orderBook.all')
  }

  const updateOverlayState = () => {
    const parentDiv = scrollRef.current;
    if (!parentDiv) return;

    const div = parentDiv.querySelector('.rounded-md.overflow-auto')
    if (!div) return

    const isOverflowing = div.scrollHeight > div.clientHeight;
    const isAtBottom = div.scrollTop + div.clientHeight >= div.scrollHeight - 1; // slight buffer for rounding

    setIsShowOverlay(isOverflowing && !isAtBottom);
  };

  const handleScrollToBottom = () => {
    setLastTimestamp(data?.lastTransactions?.fromTimestamp)
  }

  useEffect(() => {
    updateOverlayState(); // Initial check

    const parentDiv = scrollRef.current
    if (!parentDiv) return
    const div = parentDiv.querySelector('div.rounded-md.overflow-auto')
    if (!div) return

    div.addEventListener('scroll', updateOverlayState)

    const resizeObserver = new ResizeObserver(updateOverlayState)
    resizeObserver.observe(div)

    return () => {
      div.removeEventListener('scroll', updateOverlayState)
      resizeObserver.disconnect()
    };
  }, []);

  useEffect(() => {
    if (data && data?.lastTransactions?.data) {
      const newData = data.lastTransactions.data
      setHasMore(newData.length === LIMIT_PER_PAGE)

      if (newData.length > 0) {
        const existingTxids = new Set(lastTransactionUpdated.map(tx => tx.txid))
        const filteredNewData = newData.filter(tx => !existingTxids.has(tx.txid))

        if (filteredNewData.length === 0) return

        if (lastTimestamp) {
          dispatch(updateLastTransactionUpdated(filteredNewData))
        } else {
          dispatch(updateLastTransactionUpdated([...lastTransactionUpdated, ...filteredNewData]))
        }
      }
    }
  }, [data, dispatch])

  const transactionBuffer = useRef<LastTransaction[]>([])

  useEffect(() => {
    if (!_transactionMessage) return

    try {
      const messageStr = _transactionMessage?.message?.toString()
      const newTransaction: LastTransaction = messageStr ? JSON.parse(messageStr) : null

      if (
        newTransaction
        && Number(newTransaction?.priceUsd) > 0
        && Number(newTransaction?.baseAmount) > 0
        && Number(newTransaction?.quoteAmount) > 0
      ) {
        if (!newTransaction?.timestamp) {
          newTransaction.timestamp = Date.now()
        }

        // Push to buffer
        transactionBuffer.current.unshift(newTransaction)
      }
    } catch (error) {
      console.error('Error parsing transaction message:', error)
    }
  }, [_transactionMessage])

// Flush buffer to Redux every 500 ms
  useEffect(() => {
    const flushInterval = setInterval(() => {
      if (transactionBuffer?.current?.length > 0) {
        const batch = transactionBuffer.current.splice(0, transactionBuffer?.current?.length)
        if (lastTransactionUpdated && lastTransactionUpdated?.length > 0) {
          const transactions = [...batch, ...lastTransactionUpdated]
          const sortedTop20 = [...transactions]
            .slice(0, 20)
            .sort((a, b) => Number(b.timestamp) - Number(a.timestamp))
          dispatch(updateLastTransactionUpdated([...sortedTop20, ...transactions.slice(20)]))
        } else {
          dispatch(updateLastTransactionUpdated([...batch]))
        }
      }
    }, 500)

    return () => clearInterval(flushInterval)
  }, [dispatch, lastTransactionUpdated])

  return (
    <div className="w-[calc(37.75%-4px)] flex flex-col absolute left-[10px] top-0 bottom-[10px]">
      <StatsCard
        title={t('orderBook.currentMarketCap')}
        content={<MarketDisplay value={marketCap} />}
        className="mb-[4px] h-[52px] min-h-[52px] !border-[0.5px] "
      />
      <div className="flex gap-[2px] mb-[8px]">
        <StatsCard
          title={t('orderBook.volume')}
          content={formatMoney(volume)}
          className="flex-1 w-[calc(50%-1px)] h-[46px] min-h-[46px] !border-[0.5px] "
          titleClassName="text-[calc(1rem*(10/16))]"
          contentClassName="text-[calc(1rem*(13/16))]"
        />
        <StatsCard
          title={t('orderBook.pool')}
          content={pool && pool > 0 ? `$${fShortenNumber(pool)}` : '--'}
          className="flex-1 w-[calc(50%-1px)] h-[46px] min-h-[46px] !border-[0.5px] "
          titleClassName="text-[calc(1rem*(10/16))]"
          contentClassName="text-[calc(1rem*(13/16))]"
        />
      </div>
      <div className="w-full overflow-auto min-h-[calc(100%-110px)]">
        <div
          ref={scrollRef}
          className={cn(
            'flex flex-col overflow-y-auto relative h-full',
            isShowOverlay && !loading ? 'after:absolute after:left-0 after:bottom-[34px] after:right-0 after:h-12' : '',
            isShowOverlay && !loading ? 'after:content-[\'\'] after:bg-[linear-gradient(360deg,#000000E5,transparent)]' : '',
          )}
        >
          <OrderBookTable
            data={filteredTransactions}
            currency={currency}
            handleChangeCurrency={handleChangeCurrency}
            priceType={priceType}
            handleChangePriceType={handleChangePriceType}
            sortDirection={sortDirection}
            handleClickSort={handleClickSort}
            maxPriceUsd={maxPriceUsd}
            totalSupply={totalSupply}
            decimals={tokenDetail?.decimals?.toString()}
            handleScrollToBottom={handleScrollToBottom}
          />
          {
            loading && hasMore && (
              <div className="flex items-center justify-center w-full py-2">
                <IconSpinner className="size-4 animate-spin" />
              </div>
            )
          }
          <Popover open={filterOpen} onOpenChange={setFilterOpen}>
            <PopoverTrigger asChild>
              <Button className="w-full z-1 bg-[#ECECED14] rounded-[4px] p-2 flex items-center justify-between gap-[10px] app-font-medium text-[calc(1rem*(12/16))] text-[#FFFFFF99] leading-[1]">
                <span>{getSelectedFilterLabel()}</span>
                <img src="/images/orderForm/icon-dropdown.svg" className="w-[8.42px] h-[5.14px]" alt="" />
              </Button>
            </PopoverTrigger>
            <PopoverContent
              align="start"
              className="w-[var(--radix-popover-trigger-width)] p-0 bg-[#1E1E1E] border-[#333333] rounded-[4px]"
            >
              <div className="flex flex-col w-full">
                {FILTER_OPTIONS.map((option) => (
                  <Button
                    key={option.key}
                    variant="ghost"
                    className={`justify-start px-3 py-2 text-[calc(1rem*(12/16))] hover:bg-[#333333] ${selectedFilter === option.key ? 'text-white' : 'text-[#FFFFFF99]'}`}
                    onClick={() => handleFilterSelect(option.key)}
                  >
                    {option.key === 'all' ? t(option.label) : option.label}
                  </Button>
                ))}
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </div>
  )
}

export default OrderBook
