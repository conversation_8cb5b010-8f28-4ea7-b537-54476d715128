import { useTranslation } from 'react-i18next'
import { UITab } from '@/types/uiTabs.ts'
import { useState } from 'react'
import MovingBgGridFilterTags from '@components/common/MovingBgGridFilterTags.tsx'
import MyPositions from '@components/myPositions/index.tsx'
import TransactionHistory from '@components/transactionHistory'

const HoldingTab = () => {
  const { t } = useTranslation()

  const currentListTabs: UITab[] = [
    {
      value: 'holding',
      label: t('detail.tabs.myPositions'),
    },
    {
      value: 'history',
      label: t('detail.tabs.transactionHistory'),
    }
  ]

  const [currentTab, setCurrentTab] = useState<string>(currentListTabs[0].label as string)

  const handleOnTabChange = (tab: string) => {
    setCurrentTab(tab)
  }

  const handleRenderTabContent = () => {
    return currentTab === currentListTabs[0].label
      ? (<MyPositions />)
      : (<TransactionHistory />)
  }

  return (
    <div className="pb-10">
      <MovingBgGridFilterTags
        tabs={currentListTabs.map(tab => tab.label as string)}
        defaultTab={currentTab as string}
        activeTab={currentTab as string}
        onTabChange={handleOnTabChange}
        containerId="holdingTabs"
        containerClassName="m-2.5 w-[calc(100%-20px)]"
        tabsListClassName="w-full p-1 h-fit bg-[#ECECED0A] select-none"
        tabsTriggerClassName="w-1/2 py-1.5 h-[26px] rounded-[4px] bg-transparent"
        tabBgClassName="bg-[#00FFF61A]"
        activeColor={"!text-[#00FFB4]"}
      />

      {handleRenderTabContent()}
    </div>
  )
}

export default HoldingTab