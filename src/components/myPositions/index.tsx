import Container from '@components/common/Container.tsx'
import MyPositionsHeader from '@components/myPositions/MyPositionsHeader.tsx'
import MyPositionsCard from '@components/myPositions/MyPositionsCard.tsx'
import { gqlClient } from '@/lib/gql/apollo-client.ts'
import { getPortfolio } from '@services/tokens.service.ts'
import { useMultiChainWallet } from '@hooks/useMultiChainWallet.ts'
import { useEffect, useMemo } from 'react'
import { RootState, useAppDispatch, useAppSelector } from '@/redux/store'
import { PortfolioDTO } from '@/types/holding.ts'
import BaseListPortfolioCard from '@components/myPositions/BaseListPortfolio.tsx'
import {
  HoldingState,
  setData,
  setHasMore,
  setLoading,
  setLoadingMore,
  setPage,
} from '@/redux/modules/holding.slice.ts'
import { PortfolioResponse } from '@/types/responses.ts'
import useHoldingSubscription from '@components/mqtt/HoldingSubscription.ts'
import { mapWalletTokenDataToPortfolio } from '@/utils/mappingType.ts'
import useWatchWalletTokenBalance from '@hooks/useWatchWalletTokenBalance.ts'
import { useLocation } from 'react-router-dom'

type QueryGetPortfolioOptions = {
  currentPage: number
  hideSmallBalance: boolean
  hideSmallLiquidity: boolean
}

const LIMIT_PAGE = 20

const MyPositions = () => {
  const location = useLocation()
  const { activeWallet } = useMultiChainWallet({})
  const address = activeWallet?.walletId
  const dispatch = useAppDispatch()
  const {
    loading,
    data,
    hasMore,
    page,
    loadingMore,
    isHiddenSmallerThan1U,
    isHiddenSmallPoll,
    isShowOnlyCurrentCurrency,
  } = useAppSelector((state: RootState) => state.holding as HoldingState)

  const tokenAddress = useMemo(() => {
    const segments = location.pathname.split('/').filter(Boolean)
    return segments.at(-1) || ''
  }, [location.pathname])

  const newPortfolio = useHoldingSubscription(address, tokenAddress)

  const msg = useWatchWalletTokenBalance({address, token: tokenAddress})

  const queryGetPortfolio = async ({ currentPage, hideSmallBalance, hideSmallLiquidity }: QueryGetPortfolioOptions) => {
    try {
      dispatch(setLoading(true))
      if (!address) return
      const resp = await gqlClient.query<PortfolioResponse>({
        query: getPortfolio,
        variables: {
          input: {
            userAddress: address,
            limit: LIMIT_PAGE,
            page: currentPage,
            hideSmallLiquidity,
            hideSmallBalance,
            token: isShowOnlyCurrentCurrency ? tokenAddress : undefined,
          },
        },
      })
      if (resp?.data?.getPortfolio?.data) {
        const newData = currentPage === 0 ? resp.data.getPortfolio.data : [...data, ...resp.data.getPortfolio.data]
        dispatch(setData(newData))
        dispatch(setHasMore(resp?.data?.getPortfolio?.data.length === LIMIT_PAGE))
      } else {
        dispatch(setHasMore(false))
      }
    } catch (err) {
      console.error(err)
    } finally {
      dispatch(setLoading(false))
      dispatch(setLoadingMore(false))
    }
  }

  const filterData = (data: PortfolioDTO[]) => {
    return data.reduce<PortfolioDTO[]>((acc, item) => {
      const existingIndex = acc.findIndex(
        (existing) => existing.token === item.token && existing.chainId === item.chainId,
      )

      if (existingIndex !== -1) {
        acc[existingIndex] = item // Replace existing item with new one
      } else {
        acc.push(item)
      }

      if (isShowOnlyCurrentCurrency && tokenAddress) {
        return acc.filter((item) => item.token === tokenAddress)
      }

      return acc
    }, [])
  }

  const filteredData = filterData(data)

  const loadMore = async () => {
    if (loading || loadingMore || !hasMore) return false
    dispatch(setLoadingMore(true))
    dispatch(setPage(page + 1))
    return hasMore
  }
  const getItemKey = (index: number) => {
    const item = filteredData[index]
    return `${item?.token}-${item?.chainId}-${index}`
  }

  useEffect(() => {
    queryGetPortfolio({
      currentPage: page,
      hideSmallBalance: isHiddenSmallerThan1U,
      hideSmallLiquidity: isHiddenSmallPoll,
    }).catch(console.error)
  }, [address, page, isHiddenSmallPoll, isHiddenSmallerThan1U, isShowOnlyCurrentCurrency])

  useEffect(() => {
    const newItem = mapWalletTokenDataToPortfolio(newPortfolio)
    if (newItem && newItem?.token) {
      let newData: PortfolioDTO[]
      const isItemExisted = data.find(item => item.token === newItem?.token)
      if (!isItemExisted) {
        const isSmallAsset = isHiddenSmallerThan1U && newItem.totalUsdValue < 1
        const isSmallPool = isHiddenSmallPoll && newItem?.liquidity && newItem?.liquidity < 4000
        const isNotCurrentCurrency = isShowOnlyCurrentCurrency && newItem.token !== tokenAddress

        const shouldFilterOut = isSmallAsset || isSmallPool || isNotCurrentCurrency
        newData = shouldFilterOut ? filteredData : [newItem, ...filteredData]
      } else {
        newData = filteredData.map(item => item.token === newItem.token ? newItem : item)
      }
      dispatch(setData(newData))
    }
  }, [newPortfolio])

  useEffect(() => {
    if (msg && msg?.token && msg?.balance) {
      if (filteredData?.find((item) => item.token === msg.token)) {
        const newData = filteredData.map((item) => {
          if (item.token === msg.token) {
            return {
              ...item,
              totalBaseAmount: msg?.balance,
            }
          }
          return item
        })
        dispatch(setData(newData))
      }
    }
  }, [msg])

  useEffect(() => {
    return () => {
      dispatch(setPage(1))
      dispatch(setData([]))
    }
  }, [])

  return (
    <Container className="mt-2.5">
      <MyPositionsHeader />
      <BaseListPortfolioCard
        loading={loading}
        portfolios={filteredData}
        renderItem={(item, index) => <MyPositionsCard item={item} key={`${item?.token}-${index}`} />}
        loadMoreFn={loadMore}
        getItemKey={getItemKey}
      />
    </Container>
  )
}

export default MyPositions