import { useEffect, useState } from 'react'
import Container from '@components/common/Container.tsx'
import LogoWithChain from '@components/common/LogoWithChain.tsx'
import IconsDrawer from '@components/detailInfo/IconsDrawer.tsx'
import BuyersStatusDrawer from '@components/detailInfo/BuyersStatusDrawer.tsx'
import { DevAction, TokenDetail } from '@/@generated/gql/graphql-core'
import { formatDecimalLongValue, formatLongValue, getBlockChainLogo, getBlockchainLogo2 } from '@/utils/helpers'
import { CopyButton } from '../common/copy-button'
import { formatAddressWallet } from '@/lib/string'
import { ChainIds } from '@/types/enums'
import { useTranslation } from 'react-i18next'
import { useTokenPriceInfo } from '@hooks/useTokenPrice.ts'
import { useSubscription } from '@/lib/mqtt'
import MoneyFormatted from '@components/common/MoneyFormatted'
import TokenAge from './TokenAge'
import TokenSelectionDrawer from '@components/detailHeader/TokenSelectionDrawer.tsx'
import TokenHealthTooltip from '@components/detailInfo/TokenHealthTooltip.tsx'
import PriceToken from './PriceToken'

interface DetailInfoProps {
  tokenData: TokenDetail
}

// const bubbleMapChain: Record<number, string> = {
//   [ChainIds.Solana]: 'sol',
//   [ChainIds.Ethereum]: 'eth',
//   [ChainIds.Arbitrum]: 'arbi',
//   [ChainIds.Bsc]: 'bsc',
//   [ChainIds.Avalanche]: 'avax',
//   [ChainIds.FantomOpera]: 'ftm',
//   [ChainIds.Polygon]: 'poly',
//   [ChainIds.Base]: 'base',
// }

const DetailInfo = ({ tokenData }: DetailInfoProps) => {
  const { t } = useTranslation()

  const tokenSymbol = tokenData?.symbol ?? '--'
  const tokenAddress = tokenData?.address ?? '--'
  const chain = tokenData?.chainId ? Number(tokenData?.chainId) : undefined
  const tokenLogo = tokenData?.info?.logoUrl ?? getBlockChainLogo(chain as ChainIds, tokenData?.address ?? '')
  const chainLogo = getBlockchainLogo2(chain)
  const address = tokenData?.address
  const internalMarketProgress = tokenData?.internalMarketProgress ?? 0
  const internalMarketProgressLimited =
    internalMarketProgress < 0 ? 0 : internalMarketProgress > 100 ? 100 : internalMarketProgress
  // const quoteToken = tokenData?.pools?.[0]?.token1
  // const launchpad = getLaunchpad(tokenData?.dexes ?? [])

  const tokenStatistic = useTokenPriceInfo(address || '')

  // const [newTokenPrice, setNewTokenPrice] = useState<string>('')
  const [isTokenDrawerOpen, setIsTokenDrawerOpen] = useState(false)
  // const messageTokenPrice = useSubscription(`public/prices/usd/${address}`)
  const price24hChange = tokenStatistic?.price24hChange ?? tokenData?.price24hChange
  const numberProTrader = tokenData?.numberProTrader ?? 0

  // const messagePrice = messageTokenPrice?.message?.message

  // useEffect(() => {
  //   if (!messagePrice) return
  //   try {
  //     const data = JSON.parse(messagePrice.toString() || '')
  //     if (data) {
  //       setNewTokenPrice(data?.usd_price)
  //     }
  //   } catch (error) {
  //     console.warn('PriceSubscription error: ', error)
  //   }
  // }, [messagePrice])

  // const openBubbleMap = () => {
  //   if (!tokenData) return
  //   const chainId = tokenData.chainId as ChainIds
  //   const chainSymbol = bubbleMapChain[chainId]
  //   const url = `https://app.bubblemaps.io/${chainSymbol}/token/${tokenData.address}`
  //   window.open(url, '_blank')
  // }

  const handleRenderTagDevAction = (tag: DevAction | undefined): string => {
    if (!tag) return ''
    switch (tag) {
      case DevAction.AddLiquidity:
        return t('detail.tags.addLiquidity')
      case DevAction.Burnt:
        return t('detail.tags.burnt')
      case DevAction.Hold:
        return t('detail.tags.hold')
      case DevAction.RemoveLiquidity:
        return t('detail.tags.removeLiquidity')
      case DevAction.SellAll:
        return t('detail.tags.sellAll')
      default:
        return ''
    }
  }

  const tagDevAction = handleRenderTagDevAction(
    tokenData?.portrait?.devAction?.[tokenData?.portrait?.devAction?.length - 1],
  )

  return (
    <>
      <Container className="pt-[11px] pb-[9px]">
        <div className="flex flex-col">
          <div className="flex items-start justify-between">
            <div className="flex items-center">
              <img
                src="/images/tokenDetail/ic-list-arrow.svg"
                alt="icon list arrow"
                onClick={() => setIsTokenDrawerOpen(true)}
                className="cursor-pointer"
              />
              <div
                className="app-font-medium text-[1rem] text-white leading-[1] cursor-pointer select-none nophone"
                onClick={() => setIsTokenDrawerOpen(true)}
              >
                {tokenSymbol}
              </div>
              {tokenData?.isHotToken && (
                <img src="/images/tokenDetail/icon-hot.svg" className="w-[7.59px] min-w-[7.59px] ml-1.5" alt="" />
              )}
              <TokenHealthTooltip tokenData={tokenData} />
            </div>

            <div className="text-right">
              {/* {Number(newTokenPrice) > 0 ? (
                <MoneyFormatted
                  className="app-font-medium text-[calc(1rem*(22/16))] text-[#00FFB4] leading-[1] mb-[4px] whitespace-nowrap"
                  value={newTokenPrice}
                />
              ) : (
                <MoneyFormatted
                  className="app-font-medium text-[calc(1rem*(22/16))] text-[#00FFB4] leading-[1] mb-[4px] whitespace-nowrap"
                  value={tokenData?.price}
                />
              )} */}
              <PriceToken token={tokenData?.address}/>
            </div>
          </div>

          <div className="flex items-start justify-between">
            <div className="flex items-center gap-1">
              <div className="cursor-pointer" onClick={() => setIsTokenDrawerOpen(true)}>
                <LogoWithChain
                  logo={tokenLogo}
                  logoClassName="border-[0.5px] border-[#2E0066] w-[36px] min-w-[36px] h-[36px] !rounded-[6px]"
                  chainLogo={chainLogo}
                  chainContainerClassName="w-[14px] min-w-[14px]"
                  name={tokenSymbol}
                  logoContainerClassName="!rounded-none"
                />
              </div>

              <div>
                <div className="flex items-center gap-1.5">
                  <div className="flex items-center gap-[5px]">
                    <div className="text-[calc(1rem*(10/16))] text-[#FFFFFF99] leading-[1]">
                      {formatAddressWallet(tokenAddress, 5, 3)}
                    </div>
                    <CopyButton
                      icon="/images/tokenDetail/icon-copy.webp"
                      className="w-[10px] min-w-[10px] h-[10px]"
                      text={tokenAddress}
                    />
                  </div>

                  {tokenData?.createdTime ? (
                    <TokenAge createdTime={tokenData?.createdTime} />
                  ) : (
                    <span className="text-[calc(1rem*(10/16))] text-[#36D399] leading-[1]">--</span>
                  )}

                  {tagDevAction && tagDevAction !== '' && (
                    <div className="text-[calc(1rem*(10/16))] text-[#36D399] leading-[1]">{tagDevAction}</div>
                  )}

                  <div className="flex items-center gap-1">
                    {/*progress*/}
                    <div className="relative w-5 h-1 rounded-full bg-[#00FFB433]">
                      <div
                        style={{ width: `${internalMarketProgressLimited}%` }}
                        className="abosolute top-0 left-0 z-1 h-1 rounded-full bg-[#36D399]"
                      ></div>
                    </div>
                    <span className="text-[11px] leading-[1]">
                      {`${t('detail.tokenDetail.internalDisk')} ${formatDecimalLongValue(
                        internalMarketProgressLimited,
                        2,
                      )}%`}
                    </span>
                  </div>
                </div>

                <div className="flex items-center gap-1.5 mt-1.5">
                  <IconsDrawer tokenData={tokenData} tagDevAction={tagDevAction} />
                  {
                    <div className="flex px-[5px] py-[1px] items-center gap-[3px] border-[0.5px] rounded border-[#ECECED1F]">
                      <img src="/images/futuresDetail/chart-candle-icon.svg" className="w-[14px] h-[14px]" alt="" />
                      <span className="app-font-regular text-[11px] leading-[1] text-white">{numberProTrader}</span>
                    </div>
                  }
                  <BuyersStatusDrawer tokenData={tokenData} />
                </div>
              </div>
            </div>

            <div className="app-font-medium text-[calc(1rem*(12/16))] text-[#00FFB4] leading-[1] whitespace-nowrap mt-[6.5px]">
              {
                <span className={Number(price24hChange ?? 0) >= 0 ? 'text-[#00FFB4]' : 'text-[#AB57FF]'}>
                  {formatLongValue(Number(price24hChange), true) !== '--'
                    ? ` ${Number(price24hChange) > 0 ? '+' : ''}
                        ${formatLongValue(Number(price24hChange), true)}%`
                    : '--'}
                </span>
              }
            </div>
          </div>

          {/*<div className="flex items-center gap-[2px] cursor-pointer" onClick={openBubbleMap}>*/}
          {/*  <img src="/images/tokenDetail/icon-bubbles.svg" className="w-[16px] min-w-[16px]" alt="" />*/}
          {/*  <div className="text-[calc(1rem*(11/16))] text-white leading-[1] relative top-[-1px]">*/}
          {/*    {t('detail.tags.chipAnalysis')}*/}
          {/*  </div>*/}
          {/*</div>*/}
        </div>
      </Container>
      <TokenSelectionDrawer open={isTokenDrawerOpen} setOpen={setIsTokenDrawerOpen} currentToken={tokenData} />
    </>
  )
}

export default DetailInfo
