import { useEffect, useState } from 'react'
import MoneyFormatted from '../common/MoneyFormatted'
import eventBus from '@/lib/eventBus'
import { EVENT_MESSAGE_OHLC_UPDATED, HistoryData } from '@/datafeeds/dataUpdater'
import classNames from 'classnames'
import { Maybe } from '@/@generated/gql/graphql-trading'

const PriceToken = ({ token }: { token: Maybe<string> | undefined }) => {
  const [ohlcToken, setOhlcToken] = useState<HistoryData | null>()

  useEffect(() => {
    eventBus.on(EVENT_MESSAGE_OHLC_UPDATED, (data: any) => {
      if (data?.data) {
        setOhlcToken(data?.data)
      }
    })
    return () => {
      eventBus.remove(EVENT_MESSAGE_OHLC_UPDATED)
    }
  }, [])

  useEffect(() => {
    setOhlcToken(null)
  }, [token])
  
  return (
    <div className="text-right">
      <MoneyFormatted
        className={classNames('app-font-medium text-[calc(1rem*(22/16))] leading-[1] mb-[4px] whitespace-nowrap', {
          'text-rise': !!ohlcToken && +ohlcToken?.open <= +ohlcToken?.close,
          'text-fall': !!ohlcToken && +ohlcToken?.open > +ohlcToken?.close,
        })}
        value={ohlcToken?.close}
      />
    </div>
  )
}
export default PriceToken
