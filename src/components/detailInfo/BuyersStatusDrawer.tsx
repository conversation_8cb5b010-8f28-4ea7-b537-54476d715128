import React, { useMemo, useState } from 'react'
import AppDrawer from '@components/common/AppDrawer.tsx'
import { useTranslation } from 'react-i18next'
import { BuyerStatus, getStatusIcon } from './statusIcons'
import { useQuery } from '@apollo/client'
import { getTokenSnipers } from '@services/tokens.service.ts'
import { TokenDetail, TokenSniper } from '@/@generated/gql/graphql-core.ts'

interface BuyerStatusData {
  hold: number
  increase: number
  partialSell: number
  fullSell: number
  targetedBuyer: number
}

export interface BuyersStatusDrawerProps {
  tokenData: TokenDetail | undefined
}

const mappingStatus: Record<TokenSniper, BuyerStatus> = {
  Hold: 'hold',
  BuyMore: 'increase',
  SellPart: 'partialSell',
  SellAll: 'fullSell',
  Sniper: 'targetedBuyer',
}

const BuyersStatusDrawer = (props: BuyersStatusDrawerProps) => {
  const { t } = useTranslation()
  const [openDrawer, setOpenDrawer] = useState(false)
  const { tokenData } = props

  const { data } = useQuery(getTokenSnipers, {
    variables: {
      input: {
        tokenAddress: (tokenData?.address as string) ?? '',
        chainId: tokenData?.chainId!,
      },
    },
    skip: !tokenData,
    pollInterval: 60000,
  })

  const statusData: BuyerStatusData = useMemo(() => {
    if (!data) {
      return {
        hold: 0,
        increase: 0,
        partialSell: 0,
        fullSell: 0,
        targetedBuyer: 0,
      }
    }
    let hold = 0
    let increase = 0
    let partialSell = 0
    let fullSell = 0
    let targetedBuyer = 0

    data.getTokenSniper.snipers?.map((holder) => {
      switch (holder) {
        case TokenSniper.Hold: {
          hold += 1
          break
        }
        case TokenSniper.BuyMore: {
          increase += 1
          break
        }
        case TokenSniper.SellPart: {
          partialSell += 1
          break
        }
        case TokenSniper.SellAll: {
          fullSell += 1
          break
        }
        case TokenSniper.Sniper: {
          targetedBuyer += 1
          break
        }
      }
    })

    return {
      hold,
      increase,
      partialSell,
      fullSell,
      targetedBuyer,
    }
  }, [data])

  const totalHoldOrIncrease = useMemo(
    () => statusData.hold + statusData.increase,
    [statusData.hold, statusData.increase],
  )

  const totalBuyers = data?.getTokenSniper.snipers?.length ?? 100
  const currentProgress = `${totalHoldOrIncrease}/${totalBuyers}`

  const { top100HoldPercentage, top10HoldPercentage } = useMemo(() => {
    if (!data)
      return {
        top100HoldPercentage: 0,
        top10HoldPercentage: 0,
      }
    return {
      top100HoldPercentage: parseFloat(data.getTokenSniper.currentTotalHolding) * 100,
      top10HoldPercentage: parseFloat(data.getTokenSniper.top10Holders) * 100,
    }
  }, [data])

  const chunkedStatuses = useMemo(() => {
    const chunkSize = 10
    const chunks = []
    const allStatuses = data?.getTokenSniper.snipers ?? []
    for (let i = 0; i < allStatuses.length; i += chunkSize) {
      const chunk = allStatuses.slice(i, i + chunkSize)
      const chunkWithStatus = chunk.map((item) => mappingStatus[item])
      if (chunkWithStatus.length < chunkSize) {
        const remaining = chunkSize - chunkWithStatus.length
        for (let j = 0; j < remaining; j++) {
          chunkWithStatus.push('none')
        }
      }
      chunks.push(chunkWithStatus)
    }
    return chunks
  }, [data])

  return (
    <>
      <div
        className="rounded-[4px] border-[1px] border-[#ECECED1F] p-[4px] flex items-center gap-[4px] cursor-pointer"
        onClick={() => setOpenDrawer(true)}
      >
        <div className="rounded-full w-[6px] min-w-[6px] h-[6px] min-h-[6px] bg-[#36D399]" />
        <div className="text-[calc(1rem*(11/16))] text-white leading-[1] relative top-[-1px]">{currentProgress}</div>
      </div>

      <AppDrawer
        title={t('detail.buyersStatusDrawer.title', {
          token: tokenData?.symbol,
          total: totalBuyers,
          holding: totalHoldOrIncrease,
        })}
        open={openDrawer}
        setOpen={setOpenDrawer}
        drawerClassName="bg-[url('/images/tokenDetail/bg_top_100.png')] bg-no-repeat bg-center bg-cover"
        drawerHeaderClassName="py-[14px]"
        drawerContent={
          <div className="pb-4">
            <div className="mb-5 text-[#FFFFFFCC] text-sm">
              {t('detail.buyersStatusDrawer.holdingOrIncrease', { count: totalHoldOrIncrease })}
            </div>
            <div className="space-y-3 mb-5 justify-between">
              {chunkedStatuses.map((row, rowIndex) => (
                <div key={rowIndex} className="flex justify-between w-full">
                  {row.map((status, colIndex) => (
                    <React.Fragment key={colIndex}>
                      {getStatusIcon(status as BuyerStatus, 'normal', status === 'targetedBuyer')}
                    </React.Fragment>
                  ))}
                </div>
              ))}
            </div>

            <div className="mb-6">
              <div className="text-white text-base mb-4">{t('detail.buyersStatusDrawer.iconExplanation')}</div>
              <div className="grid grid-cols-2 gap-y-3 gap-x-4 font-[350]">
                <div className="flex items-center gap-2">
                  {getStatusIcon('hold', 'small')}
                  <div className="text-sm">
                    {t('detail.buyersStatusDrawer.holdingUnchanged')}: {statusData.hold}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon('increase', 'small')}
                  <div className="text-sm">
                    {t('detail.buyersStatusDrawer.increased')}: {statusData.increase}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon('partialSell', 'small')}
                  <div className="text-sm">
                    {t('detail.buyersStatusDrawer.partialSold')}: {statusData.partialSell}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon('fullSell', 'small')}
                  <div className="text-sm">
                    {t('detail.buyersStatusDrawer.allSold')}: {statusData.fullSell}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon('targetedBuyer', 'small', true)}
                  <div className="text-sm">
                    {t('detail.buyersStatusDrawer.targetedBuyer')}: {statusData.targetedBuyer}
                  </div>
                </div>
              </div>
            </div>

            <div className="text-white">
              <div>{t('detail.buyersStatusDrawer.proportion')}</div>
              <div className="flex justify-between mt-2 gap-4 text-sm">
                <div className="">
                  {t('detail.buyersStatusDrawer.latest100Holding')}: {top100HoldPercentage.toFixed(2)}%
                </div>
                <div className="">
                  {t('detail.buyersStatusDrawer.latest10Holding')}: {top10HoldPercentage.toFixed(2)}%
                </div>
              </div>
            </div>
          </div>
        }
      />
    </>
  )
}

export default BuyersStatusDrawer
