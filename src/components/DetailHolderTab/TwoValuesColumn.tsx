import { cn } from '@/lib/utils.ts'

type TwoValuesColumnProps = {
  upperValue?: string
  upperValueClassName?: string
  lowerValue?: string
  lowerValueClassName?: string
}
const TwoValuesColumn = ({
  upperValue,
  upperValueClassName,
  lowerValue,
  lowerValueClassName
}: TwoValuesColumnProps) => {
  const handleRenderColumn = () => {
    if (!upperValue || !lowerValue || upperValue === "--" || lowerValue === "--") {
      return <div className="text-[#FFFFFF] text-[13px] leading-[1]">--</div>
    }
    if (upperValue === "0" || lowerValue === "0") {
      return <div className="text-[#FFFFFFB2] text-[13px] leading-[1] w-full text-left">0</div>
    }
    return (
      <>
        <div className={cn("", upperValueClassName)}>
          {upperValue}
        </div>
        <div className={cn("", lowerValueClassName)}>
          {lowerValue}
        </div>
      </>
    )
  }

  return (
    <div className={ "flex flex-col items-start justify-center gap-1"}>
      {handleRenderColumn()}
    </div>
  )
}

export default TwoValuesColumn