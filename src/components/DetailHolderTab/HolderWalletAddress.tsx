import CopyBtn from '@components/common/CopyBtn.tsx'

type WalletAddressProps = {
  address: string,
}

const HolderWalletAddress = ({
  address
}: WalletAddressProps) => {

  return (
    <div className={"relative"}>
      <div className="flex items-center gap-1">
        <div className="max-w-[50px]">
          <div className="flex items-center gap-1 app-font-regular leading-[1]">
            <span className="text-[calc(1rem*(9/16))] text-[#00FFB4]">{address.slice(0, 6)}</span>
            <CopyBtn text={address ?? ""} />
          </div>
          <div className="mt-1 bg-[#00FFB433] h-1 w-[50px] relative rounded-r-full">
            <div className="absolute w-1/5 h-full bg-[#00FFB4] rounded-r-full"></div>
          </div>
        </div>

        <img src="/images/icons/ic-wallet-1.svg" alt="wallet" />
      </div>
    </div>

  )
}

export default HolderWalletAddress