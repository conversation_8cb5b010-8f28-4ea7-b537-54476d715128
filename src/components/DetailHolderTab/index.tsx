import { useTranslation } from 'react-i18next'
import { useLocation } from 'react-router-dom'
import { ColumnDef } from '@tanstack/react-table'
import { ChainIds, FollowedHolderColumnKeys, SortByCreateAtType } from '@/types/enums.ts'
import FilterAddress from '@components/detaiTokenTable/FilterAddress.tsx'
import { useEffect, useMemo, useState } from 'react'
import { DataTableInfiniteScroll } from '@components/ui/XTableInfiniteScroll.tsx'
import { HolderDto, TransactionClassification } from '@/@generated/gql/graphql-core.ts'
import HolderWalletAddress from '@components/DetailHolderTab/HolderWalletAddress.tsx'
import TwoValuesColumn from '@components/DetailHolderTab/TwoValuesColumn.tsx'
import { formatDecimalLongValue, formatTimestamp } from '@/utils/helpers.ts'
import { formatAddressWallet } from '@/lib/string.ts'
import { getTimeAgo } from '@/utils/time.ts'
import useGetHolders from '@hooks/useGetHolders.ts'
import { LIMIT_PER_PAGE } from '@const/smartMoney.ts'
import FilterArrowSort from '@components/detaiTokenTable/FilterArrowSort.tsx'
import { cn } from '@/lib/utils.ts'
import MovingBgGridFilterTags from '@components/common/MovingBgGridFilterTags.tsx'

type SortByType = {
  type: SortByCreateAtType | undefined
  field?: string
}

const DetailHolderTab = () => {
  const { t } = useTranslation()
  const location = useLocation()

  const [page, setPage] = useState(1)
  const [filteredData, setFilteredData] = useState<HolderDto[]>([])
  const [hasMore, setHasMore] = useState(true)
  const [holder, setHolder] = useState<string | undefined>(undefined)
  const [sortBy, setSortBy] = useState<SortByType>({
    type: undefined,
    field: undefined,
  })
  const tagFilters: string[] = [
    t('detail.filters.all'),
    t('detail.filters.smartMoney'),
    t('detail.filters.whale'),
    t('detail.filters.sniper'),
    t('detail.filters.sniper1'),
    t('detail.filters.projectParty'),
    t('detail.filters.ratWarehouse'),
    t('detail.filters.newWallet'),
    t('detail.filters.kol'),
  ]
  const [currentTab, setCurrentTab] = useState<string>(tagFilters[0])

  const getClassificationFromLabel = (label: string): TransactionClassification => {
    switch (label) {
      case t('detail.filters.all'):
        return TransactionClassification.All
      case t('detail.filters.smartMoney'):
        return TransactionClassification.SmartMoney
      case t('detail.filters.whale'):
        return TransactionClassification.Whale
      case t('detail.filters.sniper'):
      case t('detail.filters.sniper1'):
        return TransactionClassification.Sniper
      case t('detail.filters.projectParty'):
        return TransactionClassification.ProjectParty
      case t('detail.filters.ratWarehouse'):
        return TransactionClassification.Followed
      case t('detail.filters.newWallet'):
        return TransactionClassification.Fresh
      case t('detail.filters.kol'):
        return TransactionClassification.Kol
      default:
        return TransactionClassification.All
    }
  }

  const tokenAddress = useMemo(() => {
    const segments = location.pathname.split('/').filter(Boolean)
    return segments.at(-1) || ''
  }, [location.pathname])

  const { data, loading } = useGetHolders({
    input: {
      token: tokenAddress,
      limit: LIMIT_PER_PAGE,
      page,
      chainId: ChainIds.Solana,
      holder,
      classification: getClassificationFromLabel(currentTab),
      sortBy: sortBy?.field && sortBy?.type
        ? `${sortBy.type === SortByCreateAtType.DESC ? "-" : "+"}${sortBy.field}`
        : undefined,
    },
    skip: !tokenAddress,
  })

  const handleStringPercentage = (value: string) => {
    if (value !== '--' && value !== '0') {
      return `${value}%`
    }
    return value
  }
  const handleStringValue = (value: string) => {
    if (value !== '--' && value !== '0') {
      return `$${value}`
    }
    return value
  }

  const handleLoadMore = () => {
    if (!hasMore || loading) return
    setPage((prev) => prev + 1)
  }
  const handleSortByChange = (field: string) => {
    setSortBy((prev) => {
      if (prev.field === field) {
        return {
          type: prev.type === SortByCreateAtType.ASC ? SortByCreateAtType.DESC : SortByCreateAtType.ASC,
          field: field,
        }
      }
      return {
        type: SortByCreateAtType.DESC,
        field: field,
      }
    })
  }
  const handleTabChange = (tab: string) => {
    setCurrentTab(tab)
  }

  const followedHolderColumns: ColumnDef<HolderDto>[] = [
    {
      accessorKey: FollowedHolderColumnKeys.INDEX,
      header: () => <div className="min-w-5">#</div>,
      cell: ({ row }) => (
        <div className="app-font-regular text-[12px] leading-[1] text-[#FFFFFFB2]">{row.index + 1}</div>
      ),
    },
    {
      accessorKey: FollowedHolderColumnKeys.WALLET,
      header: () => (
        <div className="flex items-center gap-0.5 min-w-[88px]">
          <div className="text-[11px] leading-3 tracking-[0.28px] text-[#FFFFFF]/50">
            {t('detail.holderTable.holder')}
          </div>
          <FilterAddress
            onAddressChange={(value: string) => {
              setHolder(value)
            }}
          />
        </div>
      ),
      cell: ({ row }) => {
        const address = row?.original?.address

        return <HolderWalletAddress address={address ?? ''} />
      },
    },
    {
      accessorKey: FollowedHolderColumnKeys.POSITION_PERCENTAGE,
      header: () => {
        return (
          <div className="flex items-center gap-0.5 min-w-[74px]">
            <div className="text-[11px] leading-3 tracking-[0.28px] text-[#FFFFFF]/50">
              {t('detail.holderTable.positionPercentage')}
            </div>
            <FilterArrowSort
              type={'balance'}
              currentType={sortBy?.field}
              sortByCreatedAt={sortBy?.type}
              handleOnclickSort={() => handleSortByChange('balance')}
            />
          </div>
        )
      },
      cell: ({ row }) => {
        const holder = row?.original as HolderDto
        const positionPercentage =
          holder?.balance && holder?.totalSupply
            ? formatDecimalLongValue((Number(holder?.balance) / Number(holder?.totalSupply)) * 100, 2)
            : '--'
        const holdingValue = Number(holder?.balance) * Number(holder?.avgPriceUsd)

        return (
          <TwoValuesColumn
            upperValue={handleStringPercentage(positionPercentage)}
            lowerValue={handleStringValue(formatDecimalLongValue(holdingValue, 3))}
            upperValueClassName="app-font-medium text-[13px] leading-[1] text-[#FFFFFF]"
            lowerValueClassName="app-font-regular text-[11px] leading-[1] text-[#FFFFFF]/70"
          />
        )
      },
    },
    {
      accessorKey: FollowedHolderColumnKeys.TOTAL_BUY,
      header: () => {
        return (
          <div className="flex items-center gap-0.5 min-w-[74px]">
            <div className="text-[11px] leading-3 tracking-[0.28px] text-[#FFFFFF]/50">
              {t('detail.holderTable.totalBuy')}
            </div>
            <FilterArrowSort
              type={'totalBuy'}
              currentType={sortBy?.field}
              sortByCreatedAt={sortBy?.type}
              handleOnclickSort={() => handleSortByChange('totalBuy')}
            />
          </div>
        )
      },
      cell: ({ row }) => {
        const holder = row?.original as HolderDto
        const totalBuyUsd = Number(holder?.totalBuyUsd)
        const totalBuyAmount = Number(holder?.totalBuyQty)

        return (
          <TwoValuesColumn
            upperValue={handleStringValue(formatDecimalLongValue(totalBuyUsd, 2))}
            lowerValue={formatDecimalLongValue(totalBuyAmount, 2)}
            upperValueClassName="app-font-medium text-[13px] leading-[1] text-[#FFFFFF]"
            lowerValueClassName="app-font-regular text-[11px] leading-[1] text-[#FFFFFF]/70"
          />
        )
      },
    },
    {
      accessorKey: FollowedHolderColumnKeys.TOTAL_SELL,
      header: () => {
        return (
          <div className="flex items-center gap-0.5 min-w-[74px]">
            <div className="text-[11px] leading-3 tracking-[0.28px] text-[#FFFFFF]/50">
              {t('detail.holderTable.totalSell')}
            </div>
            <FilterArrowSort
              type={'totalSell'}
              currentType={sortBy?.field}
              sortByCreatedAt={sortBy?.type}
              handleOnclickSort={() => handleSortByChange('totalSell')}
            />
          </div>
        )
      },
      cell: ({ row }) => {
        const holder = row?.original as HolderDto
        const totalSellUsd = Number(holder?.totalSellUsd)
        const totalSellAmount = Number(holder?.totalSellQty)

        return (
          <TwoValuesColumn
            upperValue={handleStringValue(formatDecimalLongValue(totalSellUsd, 2))}
            lowerValue={formatDecimalLongValue(totalSellAmount, 2)}
            upperValueClassName="app-font-medium text-[13px] leading-[1] text-[#FFFFFF]"
            lowerValueClassName="app-font-regular text-[11px] leading-[1] text-[#FFFFFF]/70"
          />
        )
      },
    },
    {
      accessorKey: FollowedHolderColumnKeys.REALIZED,
      header: () => {
        return (
          <div className="flex items-center gap-0.5 min-w-[88px]">
            <div className="text-[11px] leading-3 tracking-[0.28px] text-[#FFFFFF]/50">
              {t('detail.holderTable.realized')}
            </div>
            <FilterArrowSort
              type={'realizedProfit'}
              currentType={sortBy?.field}
              sortByCreatedAt={sortBy?.type}
              handleOnclickSort={() => handleSortByChange('realizedProfit')}
            />
          </div>
        )
      },
      cell: ({ row }) => {
        const holder = row?.original as HolderDto
        const realizedProfit = Number(holder?.realizedProfit)
        const realizedProfitPercentage = (realizedProfit / Number(holder?.totalBuyUsd)) * 100

        return (
          <TwoValuesColumn
            lowerValue={handleStringPercentage(formatDecimalLongValue(realizedProfitPercentage, 2))}
            upperValue={handleStringValue(formatDecimalLongValue(realizedProfit, 3))}
            upperValueClassName="app-font-medium text-[13px] leading-[1] text-[#00FFB4]"
            lowerValueClassName="app-font-regular text-[11px] leading-[1] text-[#00FFB4]"
          />
        )
      },
    },
    {
      accessorKey: FollowedHolderColumnKeys.UNREALIZED,
      header: () => {
        return (
          <div className="flex items-center gap-0.5 min-w-[88px]">
            <div className="text-[11px] leading-3 tracking-[0.28px] text-[#FFFFFF]/50">
              {t('detail.holderTable.unrealized')}
            </div>
            <FilterArrowSort
              type={'unrealizedProfit'}
              currentType={sortBy?.field}
              sortByCreatedAt={sortBy?.type}
              handleOnclickSort={() => handleSortByChange('unrealizedProfit')}
            />
          </div>
        )
      },
      cell: ({ row }) => {
        const holder = row?.original as HolderDto
        const unrealizedProfit = Number(holder?.unrealizedProfit)
        const unrealizedProfitPercentage = (unrealizedProfit / Number(holder?.totalBuyUsd)) * 100

        return (
          <TwoValuesColumn
            lowerValue={handleStringPercentage(formatDecimalLongValue(unrealizedProfitPercentage, 2))}
            upperValue={handleStringValue(formatDecimalLongValue(unrealizedProfit, 3))}
            upperValueClassName="app-font-medium text-[13px] leading-[1] text-[#AB57FF]"
            lowerValueClassName="app-font-regular text-[11px] leading-[1] text-[#AB57FF]"
          />
        )
      },
    },
    {
      accessorKey: FollowedHolderColumnKeys.TOTAL_PROFIT,
      header: () => {
        return (
          <div className="flex items-center gap-0.5 min-w-[88px]">
            <div className="text-[11px] leading-3 tracking-[0.28px] text-[#FFFFFF]/50">
              {t('detail.holderTable.totalProfit')}
            </div>
            <FilterArrowSort
              type={'totalProfit'}
              currentType={sortBy?.field}
              sortByCreatedAt={sortBy?.type}
              handleOnclickSort={() => handleSortByChange('totalProfit')}
            />
          </div>
        )
      },
      cell: ({ row }) => {
        const holder = row?.original as HolderDto
        const totalProfit = Number(holder?.totalProfit)
        const realizedProfit = Number(holder?.realizedProfit)
        const unrealizedProfit = Number(holder?.unrealizedProfit)
        const totalProfitPercentage = ((realizedProfit + unrealizedProfit) / totalProfit) * 100

        return (
          <TwoValuesColumn
            lowerValue={handleStringPercentage(formatDecimalLongValue(totalProfitPercentage, 2))}
            upperValue={handleStringValue(formatDecimalLongValue(totalProfit, 3))}
            upperValueClassName="app-font-medium text-[13px] leading-[1] text-[#FFFFFF]"
            lowerValueClassName="app-font-regular text-[11px] leading-[1] text-[#FFFFFF]/70"
          />
        )
      },
    },
    {
      accessorKey: FollowedHolderColumnKeys.SOL_BALANCE,
      header: () => {
        return (
          <div className="flex items-center gap-0.5 min-w-[112px]">
            <div className="text-[11px] leading-3 tracking-[0.28px] text-[#FFFFFF]/50">
              {t('detail.holderTable.solBalance')}
            </div>
          </div>
        )
      },
      cell: ({ row }) => {
        const holder = row?.original as HolderDto
        const solBalance = Number(holder?.rawBalance)
        const txTime = holder?.createdAt ?? '--'

        return (
          <TwoValuesColumn
            upperValue={formatDecimalLongValue(solBalance, 2)}
            lowerValue={formatTimestamp(txTime, true)}
            upperValueClassName="app-font-medium text-[13px] leading-[1] text-[#FFFFFF]"
            lowerValueClassName="app-font-regular text-[11px] leading-[1] text-[#FFFFFF]/70"
          />
        )
      },
    },
    {
      accessorKey: FollowedHolderColumnKeys.FUND_SOURCE,
      header: () => {
        return (
          <div className="flex items-center gap-0.5 min-w-[112px]">
            <div className="text-[11px] leading-3 tracking-[0.28px] text-[#FFFFFF]/50">
              {t('detail.holderTable.fundSource')}
            </div>
          </div>
        )
      },
      cell: ({ row }) => {
        const holder = row?.original as HolderDto
        const fundSource = holder?.sourceOfFunding ?? '--'
        const txTime = holder?.createdAt ?? '--'

        return (
          <TwoValuesColumn
            upperValue={formatAddressWallet(fundSource)}
            lowerValue={formatTimestamp(txTime, true)}
            upperValueClassName="app-font-medium text-[13px] leading-[1] text-[#FFFFFF]"
            lowerValueClassName="app-font-regular text-[11px] leading-[1] text-[#FFFFFF]/70"
          />
        )
      },
    },
    {
      accessorKey: FollowedHolderColumnKeys.HOLDING_LENGTH,
      header: () => {
        return (
          <div className="flex items-center gap-0.5 min-w-[64px]">
            <div className="text-[11px] leading-3 tracking-[0.28px] text-[#FFFFFF]/50">
              {t('detail.holderTable.holdingLength')}
            </div>
          </div>
        )
      },
      cell: ({ row }) => {
        const holder = row?.original as HolderDto
        const holdingLength = holder?.createdAt ?? '--'

        return (
          <div className="app-font-regular text-[11px] leading-[1] text-[#FFFFFF]/70">
            {holdingLength === '--' ? holdingLength : getTimeAgo(holdingLength)}
          </div>
        )
      },
    },
    {
      accessorKey: FollowedHolderColumnKeys.AVG_BUY_SELL,
      header: () => {
        return (
          <div className="flex items-center gap-0.5 min-w-[112px]">
            <div className="text-[11px] leading-3 tracking-[0.28px] text-[#FFFFFF]/50">
              {t('detail.holderTable.avgBuySell')}
            </div>
          </div>
        )
      },
      cell: ({ row }) => {
        const holder = row?.original as HolderDto
        const avgBuyPrice = Number(holder?.totalBuyUsd) / Number(holder?.totalBuyQty)
        const avgSellPrice = Number(holder?.totalSellUsd) / Number(holder?.totalSellQty)

        return (
          <TwoValuesColumn
            upperValue={handleStringValue(formatDecimalLongValue(avgBuyPrice))}
            lowerValue={handleStringValue(formatDecimalLongValue(avgSellPrice))}
            upperValueClassName="app-font-medium text-[13px] leading-[1] text-[#FFFFFF]"
            lowerValueClassName="app-font-regular text-[11px] leading-[1] text-[#FFFFFF]/70"
          />
        )
      },
    },
    {
      accessorKey: FollowedHolderColumnKeys.TRANSACTION_COUNT,
      header: () => {
        return (
          <div className="flex items-center gap-0.5 min-w-[64px]">
            <div className="text-[11px] leading-3 tracking-[0.28px] text-[#FFFFFF]/50">
              {t('detail.holderTable.txCount')}
            </div>
          </div>
        )
      },
      cell: ({ row }) => {
        const holder = row?.original as HolderDto
        const buy = holder?.buys
        const sell = holder?.sells

        return (
          <div className="flex items-center gap-0.5 app-font-regular text-[11px] leading-[1] ">
            {!buy || !sell ? (
              '--'
            ) : (
              <>
                <span className="text-[#00FFB4]">{buy}</span>
                <span className="text-[#FFFFFF]/50">/</span>
                <span className="text-[#AB57FF]">{sell}</span>
              </>
            )}
          </div>
        )
      },
    },
    {
      accessorKey: FollowedHolderColumnKeys.LAST_ACTIVE,
      header: () => {
        return (
          <div className="flex items-center gap-0.5 min-w-[80px] ">
            <div className="text-[11px] leading-3 tracking-[0.28px] text-[#FFFFFF]/50">
              {t('detail.holderTable.lastActive')}
            </div>
          </div>
        )
      },
      cell: ({ row }) => {
        const holder = row?.original as HolderDto
        const holdingLength = holder?.updatedAt ?? '--'

        return (
          <div className="app-font-regular text-[11px] leading-[1] text-[#FFFFFF]/70">
            {`${getTimeAgo(holdingLength)} ${t('detail.tokenDetail.ago')}`}
          </div>
        )
      },
    },
  ]

  useEffect(() => {
    if (data?.getHolder?.data) {
      if (page === 1) {
        setFilteredData([...data.getHolder.data])
      } else {
        setFilteredData((prev) => [...prev, ...data.getHolder.data])
      }
      setHasMore(data.getHolder.data?.length === LIMIT_PER_PAGE)
    }
  }, [data])

  useEffect(() => {
    setPage(1)
  }, [holder, sortBy?.type, sortBy?.field])

  return (
    <div className="px-2.5">
      {/* Chart */}

      {/* Filter */}
      <div className={cn('relative pt-[1px] mt-[5px]')}>
        <MovingBgGridFilterTags
          tabs={tagFilters}
          defaultTab={tagFilters[0]}
          activeTab={currentTab}
          onTabChange={handleTabChange}
          containerId="token-detail-pairs"
          containerClassName="mt-2.5 w-full overflow-x-auto no-scrollbar"
        />
      </div>
      {/* Table */}
      <div className="relative mt-1.5 pb-4 z-[3]">
        <DataTableInfiniteScroll
          columns={followedHolderColumns}
          data={filteredData}
          fetchMore={handleLoadMore}
          hasMore={hasMore}
          isLoading={loading && page === 1}
          //isFetchingMore={hasMore && loading && page > 1}
          tableProps={{
            isStickyHeader: true,
            containerClassName: 'border-0 max-h-[500px] select-none',
            tableHeaderRowClassName: '!border-0 bg-black whitespace-nowrap',
            tableHeaderClassName: 'border-0 text-[#FFFFFF80] text-[calc(1rem*(11/16))] z-10 leading-3 app-font-medium',
          }}
        />
      </div>
    </div>
  )
}

export default DetailHolderTab