import FilterWallet from '@/components/common/FilterWallet'
import { DataTableInfiniteScroll } from '@/components/ui/XTableInfiniteScroll'
import { SkeletonList } from '@/components/ui/skeleton'
import { futureClient } from '@/lib/gql/apollo-client'
import { getSmartMoneyTxHistories } from '@/services/copytrade.service'
import { ChainIds } from '@/types/enums'
import { formatNumber, getBlockChainLogo, getBlockchainLogo2, getLinkExplorer } from '@/utils/helpers.ts'
import { getTimeAgo } from '@/utils/time'
import { useQuery } from '@apollo/client'
import LogoWithChain from '@components/common/LogoWithChain.tsx'
import MoneyFormatted, { MoneyFormattedOneTime } from '@components/common/MoneyFormatted.tsx'
import { IconSortDown, IconSortUp } from '@components/icon'
import { ColumnDef } from '@tanstack/react-table'
import { get } from 'lodash-es'
import { Fragment, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

type Props = {
  dataUnit: 'USD' | 'SOL' | 'ETH'
  address: string;
}

type TTokenInfo = {
  name: string,
  address: string,
  logo: string,
  totalSupply: string,
}

type TItem = {
  id: number,
  token: string,
  address: string,
  chainId: number,
  lastActive: string,
  type: string,
  amount: string,
  price: string,
  volume: string,
  quantity: number,
  transactionHash: string,
  tokenInfo: TTokenInfo,
  avgPriceUsd: string,
}

const pageSize = 20;

const ActivityTable = ({ dataUnit, address }: Props) => {
  const { t } = useTranslation()
  const [items, setItems] = useState<TItem[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const { loading } = useQuery(getSmartMoneyTxHistories, {
    variables: {
      input: {
        chain: "SOLANA",
        address,
        page,
      }
    },
    client: futureClient,
    onCompleted: (res) => {
      const _items = get(res, 'getSmartMoneyTxHistories', []);
      if (_items.length < pageSize) setHasMore(false);
      if (page === 1) {
        setItems(_items.map(mapOrderItem));
      } else {
        setItems(prev => {
          // If no new orders, return previous state
          if (_items.length) {
            const newItems = _items.map(mapOrderItem);
            return [...prev, ...newItems];
          }
          return prev;
        });
      }
    }
  })
  function mapOrderItem(item: any): TItem {
    return {
      id: Math.random(),
      token: get(item, 'token.name', ''),
      address: get(item, 'token.address', ''),
      chainId: ChainIds.Solana,
      lastActive: get(item, 'timestamp', ''),
      type: get(item, 'type', 'sell'),
      amount: get(item, 'quantity', 0),
      price: get(item, 'usdPrice', 0),
      volume: '',
      quantity: get(item, 'quantity', 0),
      transactionHash: get(item, 'transactionHash', ''),
      tokenInfo: {
        name: get(item, 'token.name', ''),
        address: get(item, 'token.address', ''),
        logo: get(item, 'token.logo', ''),
        totalSupply: get(item, 'token.totalSupply', ''),
      },
      avgPriceUsd: get(item, 'avgPriceUsd', '0'),
    };
  }
  const columns: ColumnDef<any>[] = [
    {
      accessorKey: 'token_overview',
      header: () => (
        <div className="flex items-center">
          <div className="">{t('walletDetail.activityTable.date')}</div>
          <div className="flex items-center cursor-pointer justify-center" onClick={() => { }}>
            <div className="flex flex-col ml-1">
              <IconSortUp currentColor={'#FFFFFF80'} />
              <IconSortDown currentColor={'#FFFFFF'} />
            </div>
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const token = row.original?.token
        const address = row.original?.address
        const chainId = row.original?.chainId
        const lastActive = row.original?.lastActive
        return (
          <div className="flex items-center gap-[5px]">
            <div className="relative">
              <LogoWithChain
                logo={getBlockChainLogo(chainId, address)}
                logoClassName="w-[30px] h-[30px]"
                chainLogo={getBlockchainLogo2(chainId)}
                name={token}
              />
            </div>
            <div>
              <div className="font-medium text-[13px]">{token}</div>
              <div className="mt-0.5 font-normal text-[#00FFB4] text-[10px]">{getTimeAgo(lastActive)}</div>
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: 'type',
      header: () => (
        <div className="flex items-center">
          <div className="">{t('walletDetail.activityTable.type')}</div>
          <div className="flex items-center cursor-pointer justify-center" onClick={() => { }}>
            <div className="flex flex-col ml-1">
              <IconSortUp currentColor={'#FFFFFF80'} />
              <IconSortDown currentColor={'#FFFFFF80'} />
            </div>
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const type = row.original?.type
        return (
          <div
            className={`font-medium text-[13px] ${type === 'buy' ? 'text-[#00FFB4]' : type === 'sell' ? 'text-[#AB57FF]' : 'text-white'}`}
          >
            {t(`walletDetail.token.${type}`)}
          </div>
        )
      },
    },
    {
      accessorKey: 'pnl',
      header: () => (
        <div className="flex items-center">
          <div className="">{t('walletDetail.activityTable.pnl')}</div>
          <div className="flex items-center cursor-pointer justify-center" onClick={() => { }}>
            <div className="flex flex-col ml-1">
              <IconSortUp currentColor={'#FFFFFF80'} />
              <IconSortDown currentColor={'#FFFFFF80'} />
            </div>
          </div>
        </div>
      ),
      cell: ({ row }) => {
        //PnL = (usdPrice - avgPriceUsd) * quantity
        const isBuy = row.original?.type === 'Buy' || row.original?.type === 'buy'
        const price = Number(row.original?.price) || 0
        const quantity = Number(row.original?.quantity) || 0
        const avgPriceUsd = Number(row.original?.avgPriceUsd) || 0
        const pnl = isBuy ? 0 : (price - avgPriceUsd) * quantity;
        return (
          <div className={`font-medium text-[13px] ${isBuy ? 'text-[#00FFB4]' : 'text-[#AB57FF]'}`}>
            {pnl >= 0 ? '+' : '-'}
            <MoneyFormattedOneTime value={Math.abs(pnl)} showUnit={dataUnit === 'USD'} isUSD={dataUnit === 'USD'} />
            {dataUnit !== 'USD' ? <span className="ml-1">{dataUnit}</span> : null}
          </div>
        )
      },
    },
    {
      accessorKey: 'amount',
      header: () => (
        <div className="flex items-center">
          <div className="">{t('walletDetail.activityTable.volume1')}</div>
          <div className="flex items-center cursor-pointer justify-center" onClick={() => { }}>
            <div className="flex flex-col ml-1">
              <IconSortUp currentColor={'#FFFFFF80'} />
              <IconSortDown currentColor={'#FFFFFF80'} />
            </div>
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const isBuy = row.original?.type === 'Buy' || row.original?.type === 'buy'
        const amount = Number(row.original?.amount)
        return (
          <div className={`font-medium text-[13px] ${isBuy ? 'text-[#00FFB4]' : 'text-[#AB57FF]'}`}>
            <MoneyFormattedOneTime value={Math.abs(amount)} showUnit={false} isUSD={dataUnit === 'USD'} />
            {dataUnit !== 'USD' ? <span className="ml-1">{amount}</span> : null}
          </div>
        )
      },
    },
    {
      accessorKey: 'price',
      header: () => (
        <div className="flex items-center">
          <div className="">{t('walletDetail.activityTable.finalPrice')}</div>
          <div className="flex items-center cursor-pointer justify-center" onClick={() => { }}>
            <div className="flex flex-col ml-1">
              <IconSortUp currentColor={'#FFFFFF80'} />
              <IconSortDown currentColor={'#FFFFFF80'} />
            </div>
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const isBuy = row.original?.type === 'Buy' || row.original?.type === 'buy'
        const price = Number(row.original?.price)
        return (
          <div className={`font-medium text-[13px] ${isBuy ? 'text-[#00FFB4]' : 'text-[#AB57FF]'}`}>
            <MoneyFormattedOneTime value={Math.abs(price)} showUnit isUSD={dataUnit === 'USD'} />
            {dataUnit !== 'USD' ? <span className="ml-1">{dataUnit}</span> : null}
          </div>
        )
      },
    },
    {
      accessorKey: 'volume',
      header: () => (
        <div className="flex items-center">
          <div className="">{t('walletDetail.activityTable.volume2')}</div>
          <div className="flex items-center cursor-pointer justify-center" onClick={() => { }}>
            <div className="flex flex-col ml-1">
              <IconSortUp currentColor={'#FFFFFF80'} />
              <IconSortDown currentColor={'#FFFFFF80'} />
            </div>
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const price = Number(row.original?.price)
        const amount = Number(row.original?.amount)
        return <div className="font-medium text-[13px]"><MoneyFormattedOneTime value={price * amount} showUnit isUSD={dataUnit === 'USD'} unit={dataUnit === 'USD' ? '$' : dataUnit} /></div>
      },
    },
    {
      header: t('walletDetail.activityTable.actions'),
      cell: ({ row }) => {
        const txid = row.original?.transactionHash || '';
        const chanId = Number(row.original?.chainId) as ChainIds
        return (
          <a
            href={getLinkExplorer(chanId, txid)}
            target="_blank"
            rel="noopener noreferrer"
            className="font-[400] text-[12px] hover:text-[#AB57FF] hover:underline flex items-center cursor-pointer"
          >
            <img src={getBlockchainLogo2(chanId)} alt="chain logo" className="w-[16px] h-[16px] inline-block mr-1 rounded-full" />
          </a>
        )
      },
    },
  ]

  const handleBottomReached = () => {
    if (!loading && hasMore) {
      setPage(p => p + 1);
    }
  };

  const activityFilters = useMemo(() => [
    {
      value: 'all',
      label: t('activityTable.filters.all'),
    },
    {
      value: 'buyit',
      label: t('activityTable.filters.buyit')
    },
    {
      value: 'sell',
      label: t('activityTable.filters.sell')
    },
    {
      value: 'addpood',
      label: t('activityTable.filters.addpood')
    },
    {
      value: 'reduce',
      label: t('activityTable.filters.reduce')
    },
  ], [t]);
  function handleOnChange(index: number) {
    // const newWalletType = Filters[index].value;
    // setSelectedWalletType(newWalletType);
    // The query will automatically refetch when selectedWalletType changes
    // due to it being in the queryKey
  }
  return (
    // <DataTable
    //   columns={columns}
    //   data={data}
    //   isStickyHeader
    //   isStickyFirstColumn
    //   stickyBg="rgb(23,24,27)"
    //   containerClassName="max-h-[400px]"
    //   tableClassName=""
    //   tableHeaderClassName="text-[rgba(255,255,255,0.48) bg-[#27272a]"
    //   tableHeaderRowClassName="border-b-[0.5px] border-[rgba(35,35,41,1)] text-[11px] text-[rgba(255, 255, 255, 0.48)] sticky top-0 whitespace-nowrap"
    //   tableHeadClassName="font-[350] text-[12px] text-white/50 px-2 py-1 items-center justify-center"
    //   tableBodyClassName="max-h-[300px]"
    //   tableBodyRowClassName="group whitespace-nowrap"
    //   tableCellClassName="group-hover:!bg-[#27272a] cursor-pointer"
    //   onRowClick={() => { }}
    //   onBottomReached={onBottomReached}
    //   ref={refTable}
    // />
    <Fragment>
      <div className="px-2 mt-2 flex align-middle justify-between pb-[6px] sticky top-[68px] bg-[#111111] z-20">
        <FilterWallet
          options={activityFilters.map(i => i.label)}
          onChange={handleOnChange}
          classNameActive='bg-[rgba(0,255,180,0.1)] text-[#00FFB4] rounded'
        />
      </div>
      <DataTableInfiniteScroll
        columns={columns}
        data={items}
        fetchMore={handleBottomReached}
        hasMore={hasMore}
        tableProps={{
          isStickyHeader: true,
          isStickyFirstColumn: true,
          stickyBg: "rgb(23,24,27)",
          tableClassName: "",
          containerClassName: "max-h-[400px]",
          tableHeadClassName: "font-[350] text-[12px] text-white/50 px-2 py-1 items-center justify-center",
          tableHeaderClassName: "text-[rgba(255,255,255,0.48) bg-[#27272a]",
          tableHeaderRowClassName: "border-b-[0.5px] border-[rgba(35,35,41,1)] text-[11px] text-[rgba(255, 255, 255, 0.48)] sticky top-0 whitespace-nowrap",
          tableBodyClassName: "max-h-[300px]",
          tableBodyRowClassName: "group whitespace-nowrap",
          tableCellClassName: "group-hover:!bg-[#27272a] cursor-pointer",
          skeletonComponent: <SkeletonList className="w-[100vw]" count={10} />
        }}
      />
    </Fragment>
  )
}

export default ActivityTable
