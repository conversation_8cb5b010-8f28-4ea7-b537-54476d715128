import { useTranslation } from 'react-i18next'
import { formatNumber, formatPercentage } from '@/utils/helpers'

type Props = {
  period: string
  data: any
}

const Distribution = ({ period, data }: Props) => {
  const { t } = useTranslation()

  const lvToLabel = (lv: number) => {
    switch (lv) {
      case 1:
        return '-50%'
      case 2:
        return '0~50%'
      case 3:
        return '0~200%'
      case 4:
        return '200%~500%'
      case 5:
        return '>500%'
      default:
        return 'Unknown'
    }
  }
  const lvToColor = (lv: number) => {
    switch (lv) {
      case 5:
        return '#00FFB4'
      case 4:
        return '#00DEA0'
      case 3:
        return '#009F73'
      case 2:
        return '#AB57FF'
      case 1:
        return '#8B19FE'
      default:
        return '#ECECED1F'
    }
  }
  return (
    <div className="mt-6">
      <div className="font-medium text-[16px] text-white leading-none">
        {t('walletDetail.distribution.title', { period })}
      </div>
      <div className="flex items-center justify-between text-[11px] text-white/50 leading-none mt-2">
        <div>{t('walletDetail.distribution.priceRange')}</div>
        <div>
          {t('walletDetail.distribution.tradedAmount')}/{t('walletDetail.distribution.profit')}
        </div>
      </div>
      <div className="mt-3">
        {data.map((item: any) => (
          <div key={item.lv} className="mt-4 grid grid-cols-3 sm:grid-cols-5 gap-2 sm:gap-3 items-center">
            <div className="col-span-1 whitespace-nowrap">{lvToLabel(item.lv)}</div>
            <div className="col-span-1 sm:col-span-3 h-[6px] bg-[#ECECED1F] rounded-[20px]">
              <div
                className="h-[6px] rounded-[20px]"
                style={{ width: `${Number(item.profitRate) * 100}%`, background: lvToColor(item.lv) }}
              ></div>
            </div>
            <div className="col-span-1 text-right">
              {formatNumber(Number(item.tradedAmount))} {Number(item.tradedAmount) ? `(${formatPercentage(Number(item.profitRate) * 100, false)})` : ''}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default Distribution
