import CopyBtn from '@/components/common/CopyBtn'
import { DataTableInfiniteScroll } from '@/components/ui/XTableInfiniteScroll'
import { Button } from '@/components/ui/button'
import { SkeletonList } from '@/components/ui/skeleton'
import useTimeAgoGlobal from '@/hooks/useTimeAgoGlobal'
import { futureClient } from '@/lib/gql/apollo-client'
import { getSmartMoneyTokenStatistics } from '@/services/copytrade.service'
import { ChainIds } from '@/types/enums'
import { formatNumber, formatPercentage, getBlockchainLogo2 } from '@/utils/helpers.ts'
import { listCoinHelper } from '@/utils/list-coin-helper'
import { formatPercent } from '@/utils/numbers'
import { covertSeconds } from '@/utils/time'
import { useQuery } from '@apollo/client'
import LogoWithChain from '@components/common/LogoWithChain.tsx'
import MoneyFormatted, { MoneyFormattedOneTime, MoneyFormattedRealtime, MoneyRealtimeProfit, MoneyRealtimeWrapper } from '@components/common/MoneyFormatted.tsx'
import { IconSortDown, IconSortUp } from '@components/icon'
import { ColumnDef } from '@tanstack/react-table'
import { get } from 'lodash-es'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

type Props = {
  dataUnit: 'USD' | 'SOL' | 'ETH'
  isOnlyHolding?: boolean
  address: string
}

type TItem = {
  id: number,
  logo: string,
  name: string,
  address: string,
  chainId: number,
  lastActive: string,
  unrealizedPnL: number,
  avgPriceUsd: number,
  realizedPnL: number,
  totalProfit: number,
  balance: number,
  positions: number,
  totalSupply: number,
  totalSellAmountUsd: number,
  totalBuyAmount: number,
  totalBuyQuantity: number,
  totalSellQuantity: number,
  buyTxs: number,
  sellTxs: number,
  holdingDuration: number,
}

const pageSize = 20; // Define the page size for pagination

const HoldingTable = ({ dataUnit, address, isOnlyHolding = false }: Props) => {
  const { t } = useTranslation()
  const [items, setItems] = useState<TItem[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const { loading } = useQuery(getSmartMoneyTokenStatistics, {
    variables: {
      input: {
        chain: "SOLANA",
        address,
        isOnlyHolding,
        page,
      }
    },
    client: futureClient,
    onCompleted: (res) => {
      const _items = get(res, 'getSmartMoneyTokenStatistics', []);
      if (_items.length < pageSize) setHasMore(false);
      if (page === 1) {
        setItems(_items.map(mapOrderItem));
      } else {
        setItems(prev => {
          // If no new orders, return previous state
          if (_items.length) {
            const newItems = _items.map(mapOrderItem);
            return [...prev, ...newItems];
          }
          return prev;
        });
      }
    }
  })

  function mapOrderItem(item: any): TItem {
    return {
      id: Math.random(),
      logo: get(item, 'token.logo', ''),
      name: get(item, 'token.name', ''),
      address: get(item, 'token.address', ''),
      chainId: 1,
      lastActive: get(item, 'lastTxTime', 0),
      unrealizedPnL: Number(get(item, 'avgPriceUsd', 0)),
      avgPriceUsd: get(item, 'avgPriceUsd', 0),
      realizedPnL: get(item, 'realizedPnlUsd', 0),
      totalProfit: get(item, 'totalProfit', 0),
      balance: get(item, 'balance', 0),
      positions: get(item, 'positions', 0),
      totalSupply: get(item, 'token.totalSupply', 0),
      totalSellAmountUsd: get(item, 'totalSellAmountUsd', 0),
      totalBuyAmount: get(item, 'totalBuyAmountUsd', 0),
      holdingDuration: get(item, 'holdingDuration', 0),
      totalBuyQuantity: get(item, 'totalBuyQuantity', 0),
      totalSellQuantity: get(item, 'totalSellQuantity', 0),
      buyTxs: get(item, 'buys', 0),
      sellTxs: get(item, 'sells', 0),
    };
  }
  const columns: ColumnDef<any>[] = [
    {
      accessorKey: 'overview',
      header: () => (
        <div className="flex items-center">
          <div className="">
            {t('walletDetail.holderTable.lastActive')}
          </div>
          <div className="flex items-center cursor-pointer justify-center" onClick={() => { }}>
            <div className="flex flex-col ml-1">
              <IconSortUp currentColor={'#FFFFFF80'} />
              <IconSortDown currentColor={'#FFFFFF'} />
            </div>
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const address = row.original?.address
        const logo = row.original?.logo
        const name = row.original?.name
        const lastActive = row.original?.lastActive
        return (
          <div className="flex items-center gap-[5px]">
            <div className="relative">
              <LogoWithChain
                logo={logo}
                logoClassName="w-[30px] h-[30px]"
                chainLogo={getBlockchainLogo2(ChainIds.Solana)}
                name={name}
              />
              {/* <div className="w-[30px] h-[30px] rounded-full">
                <img
                  src={logo}
                  alt={address}
                  className="w-full h-full rounded-full object-cover" />
              </div> */}
            </div>
            <div>
              <div className="uppercase flex items-center gap-1 leading-none">
                <span className="text-[13px] text-white font-medium">{name}</span>
                <Button
                  variant="link"
                  className="ml-1 p-0 text-[10px] text-[#FFFFFF80] hover:text-white h-5"
                  onClick={() => {
                    window.open(`https://solscan.io/token/${address}`, '_blank')
                  }}
                >
                  <img src="/images/icons/search-icon.svg" alt="search" className="w-3 h-3" />
                </Button>
              </div>
              <div className="font-medium text-[13px] flex items-center gap-1">
                <span className="text-[#FFFFFF50] font-normal">{listCoinHelper.formatWalletNameCustom(address)}</span>
                <CopyBtn
                  text={address}
                  className="w-[12px] h-[12px] cursor-pointer"
                  icon={<img src="/images/icons/icon-copy.svg" alt="copy" className="w-full h-full" />}
                />
              </div>
              <div className="mt-0.5 font-normal text-[#00FFB4] text-[10px]">{useTimeAgoGlobal(lastActive)}</div>
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: 'unrealizedPnL',
      header: () => (
        <div className="flex items-center">
          <div>{t('walletDetail.holderTable.unrealized')}</div>
          <div className="flex items-center cursor-pointer justify-center" onClick={() => { }}>
            <div className="flex flex-col ml-1">
              <IconSortUp currentColor={'#FFFFFF80'} />
              <IconSortDown currentColor={'#FFFFFF80'} />
            </div>
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const avgPriceUsd = Number(row.original?.avgPriceUsd)
        const balance = Number(row.original?.balance)
        return (
          <MoneyRealtimeWrapper
            className="font-medium text-[13px]"
            callbackClassName={(_price) => ((_price - avgPriceUsd) * balance) > 0 ? 'text-[#00FFB4]' : 'text-[#AB57FF]'}>
            <MoneyRealtimeProfit callback={(_price) => `${((_price - avgPriceUsd) * balance) > 0 ? '+' : '-'}`} />
            <MoneyFormattedRealtime callback={(_price) => `${(_price - avgPriceUsd) * balance}`} isUSD={dataUnit === 'USD'} />
            {dataUnit !== 'USD' ? <span className=" ml-1">{dataUnit}</span> : null}
          </MoneyRealtimeWrapper>
        )
      },
    },
    {
      accessorKey: 'realizedPnL',
      header: () => (
        <div className="flex items-center">
          <div>{t('walletDetail.holderTable.realized')}</div>
          <div className="flex items-center cursor-pointer justify-center" onClick={() => { }}>
            <div className="flex flex-col ml-1">
              <IconSortUp currentColor={'#FFFFFF80'} />
              <IconSortDown currentColor={'#FFFFFF80'} />
            </div>
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const realizedPnL = Number(row.original?.realizedPnL)
        return (
          <div className={`font-medium text-[13px] ${realizedPnL >= 0 ? 'text-[#00FFB4]' : 'text-[#AB57FF]'}`}>
            {realizedPnL >= 0 ? '+' : '-'}
            <MoneyFormattedOneTime value={Math.abs(realizedPnL)} showUnit={dataUnit === 'USD'} isUSD={dataUnit === 'USD'} />
            {dataUnit !== 'USD' ? <span className=" ml-1">{dataUnit}</span> : null}
          </div>
        )
      },
    },
    {
      accessorKey: 'totalPnL',
      header: () => (
        <div className="flex items-center">
          <div>{t('walletDetail.holderTable.totalProfit')}</div>
          <div className="flex items-center cursor-pointer justify-center" onClick={() => { }}>
            <div className="flex flex-col ml-1">
              <IconSortUp currentColor={'#FFFFFF80'} />
              <IconSortDown currentColor={'#FFFFFF80'} />
            </div>
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const totalProfit = Number(row.original?.totalProfit)
        return (
          <div className={`font-medium text-[13px] ${totalProfit >= 0 ? 'text-[#00FFB4]' : 'text-[#AB57FF]'}`}>
            {totalProfit >= 0 ? '+' : '-'}
            <MoneyFormattedOneTime value={Math.abs(totalProfit)} showUnit={dataUnit === 'USD'} isUSD={dataUnit === 'USD'} />
            {dataUnit !== 'USD' ? <span className=" ml-1">{dataUnit}</span> : null}
          </div>
        )
      },
    },
    {
      accessorKey: 'balance',
      header: () => (
        <div className="flex items-center">
          <div>{t('walletDetail.holderTable.holding')}</div>
          <div className="flex items-center cursor-pointer justify-center" onClick={() => { }}>
            <div className="flex flex-col ml-1">
              <IconSortUp currentColor={'#FFFFFF80'} />
              <IconSortDown currentColor={'#FFFFFF80'} />
            </div>
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const balance = Number(row.original?.balance)
        return (
          <div className="font-medium text-[13px] text-white">
            <MoneyFormattedOneTime value={balance} showUnit={dataUnit === 'USD'} isUSD={dataUnit === 'USD'} />
            {dataUnit !== 'USD' ? <span className=" ml-1">{dataUnit}</span> : null}
          </div>
        )
      },
    },
    {
      header: t('walletDetail.holderTable.amount'),
      cell: ({ row }) => {
        const positions = Number(row.original?.positions)
        return <div className="font-medium text-[13px] text-white">{formatNumber(positions)}</div>
      },
    },
    {
      header: t('walletDetail.holdings.holdingRate'),
      cell: ({ row }) => {
        const totalSupply = Number(row.original?.totalSupply)
        const balance = Number(row.original?.balance)
        return <div className="font-medium text-[13px] text-white">{formatPercentage((balance / totalSupply) * 100, false)}</div>
      },
    },
    {
      header: t('walletDetail.holderTable.positionPercentage'),
      cell: ({ row }) => {
        const totalSupply = Number(row.original?.totalSupply)
        const balance = Number(row.original?.balance)
        return <div className="font-medium text-[13px] text-white">{formatPercent((balance / totalSupply) * 100)}</div>
      },
    },
    {
      accessorKey: 'holdingDuration',
      header: () => (
        <div className="flex items-center">
          <div>{t('walletDetail.holderTable.holdingLength')}</div>
          <div className="flex items-center cursor-pointer justify-center" onClick={() => { }}>
            <div className="flex flex-col ml-1">
              <IconSortUp currentColor={'#FFFFFF80'} />
              <IconSortDown currentColor={'#FFFFFF80'} />
            </div>
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const holdingDuration = Number(row.original?.holdingDuration)
        return (
          <div className="font-medium text-[13px] text-white">
            {covertSeconds(holdingDuration)}
          </div>
        )
      },
    },
    {
      accessorKey: 'totalBuyAmount',
      header: () => (
        <div className="flex items-center">
          <div>{t('walletDetail.holderTable.totalBuy')}</div>
          <div className="flex items-center cursor-pointer justify-center" onClick={() => { }}>
            <div className="flex flex-col ml-1">
              <IconSortUp currentColor={'#FFFFFF80'} />
              <IconSortDown currentColor={'#FFFFFF80'} />
            </div>
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const totalBuyAmount = Number(row.original?.totalBuyAmount)
        return (
          <div className="font-medium text-[13px] text-white">
            <MoneyFormattedOneTime value={Math.abs(totalBuyAmount)} showUnit={dataUnit === 'USD'} isUSD={dataUnit === 'USD'} />
            {dataUnit !== 'USD' ? <span className=" ml-1">{dataUnit}</span> : null}
          </div>
        )
      },
    },
    {
      accessorKey: 'totalSell',
      header: () => (
        <div className="flex items-center">
          <div>{t('walletDetail.holderTable.totalSell')}</div>
          <div className="flex items-center cursor-pointer justify-center" onClick={() => { }}>
            <div className="flex flex-col ml-1">
              <IconSortUp currentColor={'#FFFFFF80'} />
              <IconSortDown currentColor={'#FFFFFF80'} />
            </div>
          </div>
        </div>
      ),
      cell: ({ row }) => {
        // const souldAmount = Number(row.original?.souldAmount)
        const totalSellAmountUsd = Number(row.original?.totalSellAmountUsd)
        return (
          <div className="font-medium text-[13px] text-white">
            <MoneyFormattedOneTime value={Math.abs(totalSellAmountUsd)} showUnit={dataUnit === 'USD'} isUSD={dataUnit === 'USD'} />
            {dataUnit !== 'USD' ? <span className=" ml-1">{dataUnit}</span> : null}
          </div>
        )
      },
    },
    {
      accessorKey: 'token',
      header: () => (
        <div className="flex items-center">
          <div>
            {t('walletDetail.holderTable.avgBuyPrice')}/{t('walletDetail.holderTable.avgSellPrice')}
          </div>
          <div className="flex items-center cursor-pointer justify-center" onClick={() => { }}>
            <div className="flex flex-col ml-1">
              <IconSortUp currentColor={'#FFFFFF80'} />
              <IconSortDown currentColor={'#FFFFFF80'} />
            </div>
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const totalBuyAmountUsd = Number(row.original?.totalBuyAmountUsd)
        const totalBuyQuantity = Number(row.original?.totalBuyQuantity)
        const totalSellAmountUsd = Number(row.original?.totalSellAmountUsd)
        const totalSellQuantity = Number(row.original?.totalSellQuantity)
        return (
          <div className="font-medium text-[13px] text-white">
            <MoneyFormatted value={Math.abs(totalBuyAmountUsd / totalBuyQuantity)} showUnit={false} />/
            <MoneyFormatted value={Math.abs(totalSellAmountUsd / totalSellQuantity)} showUnit={false} />
          </div>
        )
      },
    },
    {
      header: t('walletDetail.holderTable.txCount'),
      cell: ({ row }) => {
        const buyTxs = Number(row.original?.buyTxs)
        const sellTxs = Number(row.original?.sellTxs)
        return (
          <div className="font-medium text-[13px] text-white">
            <span className="text-[#00FFB4]">{formatNumber(buyTxs)}</span>/
            <span className="text-[#AB57FF]">{formatNumber(sellTxs)}</span>
          </div>
        )
      },
    },
  ]

  const handleBottomReached = () => {
    if (!loading && hasMore) {
      setPage(p => p + 1);
    }
  };
  return (
    // <DataTable
    //   columns={type === 'summary' ? columns : [...columns, ...[actionColumns]]}
    //   data={data}
    //   isStickyHeader
    //   isStickyFirstColumn
    //   stickyBg="rgb(23,24,27)"
    //   containerClassName="max-h-[400px]"
    //   tableClassName=""
    //   tableHeaderClassName="text-[rgba(255,255,255,0.48) bg-[#27272a]"
    //   tableHeaderRowClassName="border-b-[0.5px] border-[rgba(35,35,41,1)] text-[11px] text-[rgba(255, 255, 255, 0.48)] sticky top-0 whitespace-nowrap"
    //   tableHeadClassName="font-[350] text-[12px] text-white/50 px-2 py-1 items-center justify-center"
    //   tableBodyClassName="max-h-[300px]"
    //   tableBodyRowClassName="group whitespace-nowrap"
    //   tableCellClassName="group-hover:!bg-[#27272a] cursor-pointer"
    //   onRowClick={() => { }}
    //   onBottomReached={onBottomReached}
    //   ref={refTable}
    // />
    <DataTableInfiniteScroll
      columns={columns}
      data={items}
      fetchMore={handleBottomReached}
      hasMore={hasMore}
      tableProps={{
        containerClassName: "max-h-[400px]",
        tableHeadClassName: "text-[calc(11rem/16)] leading-[0.75rem] text-[#FFFFFF80] cursor-pointer",
        tableHeaderClassName: "text-[rgba(255,255,255,0.48) bg-[#27272a]",
        tableHeaderRowClassName: "border-b-[0.5px] border-[rgba(35,35,41,1)] text-[11px] text-[rgba(255, 255, 255)] sticky top-0 whitespace-nowrap bg-[##27272A] z-5",
        tableBodyClassName: "max-h-[300px]",
        tableBodyRowClassName: "group whitespace-nowrap",
        tableCellClassName: "group-hover:!bg-[#27272a] cursor-pointer",
        skeletonComponent: <SkeletonList className="w-[100vw]" count={10} />
      }}
    />
  )
}

export default HoldingTable
