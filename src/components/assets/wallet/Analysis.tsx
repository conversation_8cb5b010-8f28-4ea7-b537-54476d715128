import { MoneyFormattedOneTime, MoneyFormattedRealtime } from '@/components/common/MoneyFormatted'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { formatNumber, formatPercentage } from '@/utils/helpers'
import { useTranslation } from 'react-i18next'
type Props = {
  period: string
  data: any
  dataUnit?: 'USD' | 'SOL' | 'ETH'
}

const Analysis = ({ period, data, dataUnit = 'USD' }: Props) => {
  const { t } = useTranslation()
  const {
    balance,
    pnl,
    avgPriceUsd,
    realizedPnlUsd,
    buyAmountUsd,
    totalCost,
    avgDuration,
    tokenAvgRealizedProfits,
    txsBuy,
    txsSell,
    totalBuyUsd
  } = data;
  const isUSD = dataUnit === 'USD';
  return (
    <div className="mt-5 grid grid-cols-2 gap-2">
      <div className="px-[14px] py-3 bg-[#ECECED14] rounded-[8px]">
        <div className="text-[14px] text-white/70 leading-none">{t('walletDetail.totalPnL')}</div>
        <div
          className={`mt-2 font-medium text-[14px] leading-none break-all ${Number(pnl) >= 0 ? 'text-[#36D399]' : 'text-[#AB57FF]'
            }`}
        >
          {/* {formatMoney(Number(pnl))} ({formatPercentage((pnl / totalBuyUsd) * 100, true)}) */}
          <MoneyFormattedOneTime callback={(_price) => isUSD ? pnl : pnl / _price} showUnit unit={isUSD ? '$' : dataUnit} />
          <span className={`ml-1`}>
            ({formatPercentage((pnl / totalBuyUsd) * 100, true)})
          </span>
        </div>
      </div>
      <div className="px-[14px] py-3 bg-[#ECECED14] rounded-[8px]">
        <div className="text-[14px] text-white/70 leading-none">{t('walletDetail.unrealizedPnL')}</div>
        <div
          className={`mt-2 font-medium text-[14px] leading-none break-all ${Number(pnl) >= 0 ? 'text-[#36D399]' : 'text-[#AB57FF]'
            }`}
        >
          <MoneyFormattedRealtime callback={(_price) => `${isUSD ? (_price - avgPriceUsd) * balance : ((_price - avgPriceUsd) * balance) / _price}`} showUnit unit={isUSD ? '$' : dataUnit} />
        </div>
      </div>
      <div className="px-[14px] py-3 bg-[#ECECED14] rounded-[8px]">
        <div className="text-[14px] text-white/70 leading-none">
          {t('walletDetail.analysis.avgDuration', { period: period })}
        </div>
        <div className="mt-2 font-medium text-[14px] text-white leading-none">{avgDuration}</div>
      </div>
      <div className="px-[14px] py-3 bg-[#ECECED14] rounded-[8px]">
        <div className="text-[14px] text-white/70 leading-none flex items-center">
          {t('walletDetail.analysis.tokenAvgCost', { period: period })}
          <span className="ml-1">
            <TooltipProvider delayDuration={200}>
              <Tooltip>
                <TooltipTrigger>
                  <img src="/images/orderSetting/icon-info.svg" className="w-[16px] min-w-[16px]" alt="" />
                </TooltipTrigger>
                <TooltipContent className="max-w-[360px]">
                  <p className="text-xs leading-none">
                    {t("walletDetail.tooltip.tokenAvgCost")}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </span>
        </div>
        <div className="mt-2 font-medium text-[14px] text-white leading-none">
          {/* {formatMoney(Number(buyAmountUsd))} */}
          <MoneyFormattedOneTime callback={(_price) => isUSD ? totalCost : totalCost / _price} showUnit unit={isUSD ? '$' : dataUnit} />

        </div>
      </div>
      <div className="px-[14px] py-3 bg-[#ECECED14] rounded-[8px]">
        <div className="text-[14px] text-white/70 leading-none flex items-center">
          {t('walletDetail.analysis.markPrice')}
          <span className="ml-1">
            <TooltipProvider delayDuration={200}>
              <Tooltip>
                <TooltipTrigger>
                  <img src="/images/orderSetting/icon-info.svg" className="w-[16px] min-w-[16px]" alt="" />
                </TooltipTrigger>
                <TooltipContent className="max-w-[360px]">
                  <p className="text-xs leading-none">
                    {t("walletDetail.tooltip.markPrice")}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </span>
        </div>
        <div className="mt-2 font-medium text-[14px] text-white leading-none">
          <MoneyFormattedOneTime callback={(_price) => isUSD ? buyAmountUsd : buyAmountUsd / _price} showUnit unit={isUSD ? '$' : dataUnit} />
        </div>
      </div>
      <div className="px-[14px] py-3 bg-[#ECECED14] rounded-[8px]">
        <div className="text-[14px] text-white/70 leading-none flex items-center">
          {t('walletDetail.analysis.tokenAvgRealizedProfits', { period: period })}
          <span className="ml-1">
            <TooltipProvider delayDuration={200}>
              <Tooltip>
                <TooltipTrigger>
                  <img src="/images/orderSetting/icon-info.svg" className="w-[16px] min-w-[16px]" alt="" />
                </TooltipTrigger>
                <TooltipContent className="max-w-[360px]">
                  <p className="text-xs leading-none">
                    {t("walletDetail.tooltip.tokenAvgRealizedProfits")}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </span>
        </div>
        <div
          className={`mt-2 font-medium text-[14px] leading-none break-all ${Number(tokenAvgRealizedProfits) >= 0 ? 'text-[#36D399]' : 'text-[#AB57FF]'
            }`}
        >
          {/* {formatMoney(Number(tokenAvgRealizedProfits))} */}
          <MoneyFormattedOneTime callback={(_price) => isUSD ? tokenAvgRealizedProfits : tokenAvgRealizedProfits / _price} showUnit unit={isUSD ? '$' : dataUnit} />

        </div>
      </div>
      <div className="px-[14px] py-3 bg-[#ECECED14] rounded-[8px]">
        <div className="text-[14px] text-white/70 leading-none">
          {t('walletDetail.analysis.txs', { period: period })}
        </div>
        <div className="mt-2 font-medium text-[14px] text-white/50 leading-none">
          <span className="text-[#36D399]">{formatNumber(txsBuy)}</span> /{' '}
          <span className="text-[#AB57FF]">{formatNumber(txsSell)}</span>
        </div>
      </div>
      <div className="px-[14px] py-3 bg-[#ECECED14] rounded-[8px]">
        <div className="text-[14px] text-white/70 leading-none">
          {t('walletDetail.analysis.balance', { period: period })}
        </div>
        <div className="mt-2 font-medium text-[14px] text-white leading-none">
          <MoneyFormattedRealtime callback={(_price) => isUSD ? balance * _price : balance} showUnit unit={isUSD ? '$' : dataUnit} />
        </div>
      </div>
    </div>
  )
}

export default Analysis
