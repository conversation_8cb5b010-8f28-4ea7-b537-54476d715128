import SwitchWalletBottomSheet from '@components/common/SwitchWalletBottomSheet.tsx'
import { useState } from 'react'
import clsx from 'clsx'
import { useTranslation } from 'react-i18next'
import { useAppSelector } from '@/redux/store'

export const SwitchWallets = () => {
  const [openSwitchWalletBottomSheet, setOpenSwitchWalletBottomSheet] = useState(false)
  const { t } = useTranslation()
  const chain = useAppSelector((state) => state.wallet.activeChain)
  return (
    <>
      <div
        className="mr-[calc(1rem*(6/16))] flex items-center text-[calc(1rem*(15/16))] leading-[calc(1rem*(15/16))] text-title cursor-pointer"
        onClick={() => setOpenSwitchWalletBottomSheet(true)}
      >
        <p>{t('wallet.balance', { chain: chain.toUpperCase() })}</p>
        <div className={clsx(openSwitchWalletBottomSheet && '-rotate-180', 'transition')}>
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M10.8185 6.00098H7.85197H5.18067C4.72355 6.00098 4.49499 6.7093 4.81878 7.12452L7.28533 10.2875C7.68055 10.7943 8.32338 10.7943 8.7186 10.2875L9.65665 9.08461L11.1852 7.12452C11.5042 6.7093 11.2756 6.00098 10.8185 6.00098Z"
              fill="white"
            />
          </svg>
        </div>
      </div>
      <SwitchWalletBottomSheet open={openSwitchWalletBottomSheet} setOpen={setOpenSwitchWalletBottomSheet} />
    </>
  )
}
