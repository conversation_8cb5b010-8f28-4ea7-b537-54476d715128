import ButtonTelegram from '@components/common/LoginSection/ButtonTelegram.tsx'
import ButtonWallet from '@components/common/LoginSection/ButtonWallet.tsx'
import { AssetOverview } from '@components/assets/overview/AssetOverview.tsx'
import { useTranslation } from 'react-i18next'
import { ReactNode } from 'react'
import { useAppSelector } from '@/redux/store'
import { TYPE_ACCOUNT } from '@/lib/blockchain.ts'

export interface BaseAssetsHeaderProps {
  hideBalance: boolean
  setHideBalance: (value: boolean) => void
  walletName: ReactNode
  onItemClick: (key: string) => void
  hiddenKeys?: string[]
  balance?: number
  walletAddress?: string
}

export const BaseAssetsHeader = (props: BaseAssetsHeaderProps) => {
  const { hideBalance, setHideBalance, walletName, onItemClick, hiddenKeys = [], balance, walletAddress } = props

  const { activeAccount, activeChain, wallets } = useAppSelector((state) => state.wallet)

  const activeWallet =
    activeAccount === TYPE_ACCOUNT.CHAIN ? wallets?.[activeChain]?.chain : wallets?.[activeChain]?.telegram

  const { t } = useTranslation()

  const assetsNav = [
    {
      key: 'deposit',
      icon: '/images/icons/deposit.svg',
      title: t('assets.deposit.title'),
    },
    {
      key: 'withdraw',
      icon: '/images/icons/withdraw.svg',
      title: t('assets.withdraw'),
    },
    {
      key: 'transfer',
      icon: '/images/icons/transfer.svg',
      title: t('assets.transfer'),
    },
  ]

  if (!activeWallet.isConnected) {
    return (
      <div className="mt-10 flex items-center justify-center gap-3">
        <ButtonTelegram />
        <ButtonWallet />
      </div>
    )
  }

  return (
    <>
      <div className="mt-5">
        <AssetOverview
          hideBalance={hideBalance}
          setHideBalance={setHideBalance}
          walletName={walletName}
          balance={balance}
          walletAddress={walletAddress}
        />
      </div>
      <div className="mt-4 flex items-center justify-center gap-8 sm:gap-20">
        {assetsNav.map((item, index) => {
          if (hiddenKeys.includes(item.key)) return null
          return (
            <div key={index} className="flex flex-col items-center">
              <div
                className="flex items-center justify-center h-10 w-10 border-[1px] border-[#ECECED1F] bg-[#241c32] rounded-full cursor-pointer"
                onClick={() => onItemClick(item.key)}
              >
                <img
                  src={item.icon}
                  alt={item.title}
                  className="w-6 h-6 hover:scale-[1.1] transition-colors duration-100"
                />
              </div>
              <div className="mt-3 text-center font-[330] text-[14px] leading-none text-white/80">{t(item.title)}</div>
            </div>
          )
        })}
      </div>
    </>
  )
}
