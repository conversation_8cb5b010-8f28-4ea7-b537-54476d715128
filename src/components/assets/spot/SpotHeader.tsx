import { useContext, useMemo } from 'react'
import { AssetOverviewContext } from '@components/assets/overview/AssetOverviewContext.tsx'
import { useAppSelector } from '@/redux/store'
import { TYPE_ACCOUNT } from '@/lib/blockchain.ts'
import { getImgIconChain } from '@/components/orderForm/FormDeposit'
import { BaseAssetsHeader } from '@components/assets/BaseAssetsHeader.tsx'
import ListWallets from '@components/common/walletBalance/ListWallets.tsx'

export interface SpotHeaderProps {
  onItemClick: (key: string) => void
  balance?: number
}

export const SpotHeader = (props: SpotHeaderProps) => {
  const { onItemClick, balance } = props
  const { hideBalance, toggleHideBalance } = useContext(AssetOverviewContext)

  const { activeChain, activeAccount, wallets } = useAppSelector((state) => state.wallet)

  const walletIcon: string = useMemo(() => {
    if (activeAccount === TYPE_ACCOUNT.TELEGRAM) {
      return '/images/logo-tele.svg'
    }

    const activeWallet = wallets?.[activeChain]?.chain
    return activeWallet?.walletInfo?.icon ? activeWallet?.walletInfo?.icon : getImgIconChain(activeChain)
  }, [activeChain, activeAccount, wallets])

  return (
    <BaseAssetsHeader
      hideBalance={hideBalance}
      setHideBalance={toggleHideBalance}
      onItemClick={onItemClick}
      hiddenKeys={[]}
      balance={balance}
      walletName={
        <div className="flex items-center gap-1">
          <img src={walletIcon} alt="" className="size-4" />
          <ListWallets />
        </div>
      }
    />
  )
}
