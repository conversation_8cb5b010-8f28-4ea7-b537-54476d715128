.gradient-bg-long {
  background: linear-gradient(105.98deg, rgba(0, 238, 159, 0.0204) 46.27%, rgba(0, 255, 174, 0.0476) 93.65%);
}

.gradient-bg-short {
  background: linear-gradient(105.98deg, rgba(90, 0, 159, 0.06) 46.27%, rgba(184, 81, 255, 0.1) 93.65%);
}

.gradient-border-short {
  border: 1px solid transparent;
  background-origin: border-box;
  background-clip: padding-box, border-box;
  position: relative;
}

.gradient-border-short::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(
    100.68deg,
    rgba(255, 255, 255, 0.12) 10.82%,
    rgba(107, 0, 159, 0.08) 27.84%,
    rgba(107, 0, 159, 0.1) 74.62%,
    rgba(184, 81, 255, 0.6) 86.73%
  );
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

.gradient-border-long {
  border: 1px solid transparent;
  background-origin: border-box;
  background-clip: padding-box, border-box;
  position: relative;
}

.gradient-border-long::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(
    104.53deg,
    rgba(0, 255, 174, 0.08) 11.84%,
    rgba(0, 187, 125, 0.04) 40.93%,
    rgba(0, 156, 104, 0) 70.02%,
    rgba(0, 255, 174, 0.4) 91.19%
  );
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

.header-item-long {
  background: linear-gradient(90deg, rgba(0, 255, 174, 0.06) 0%, rgba(0, 156, 104, 0) 100%);
}

.header-item-short {
  background: linear-gradient(90deg, rgba(184, 81, 255, 0.06) 0%, rgba(110, 49, 159, 0.02) 100%);
}
