import { Position, UserPositionResponse } from '@/@generated/gql/graphql-symbolDex'
import { symbolDexClient } from '@/lib/gql/apollo-client'
import { useQuery } from '@apollo/client'
import { getUserPosition } from '@services/assets.service.ts'
import { useMemo } from 'react'
import ItemPosition from './ItemPosition'
import PositionsOverview from './PositionsOverview'

const TabPositions = () => {
  const { data, loading } = useQuery<UserPositionResponse>(getUserPosition, {
    client: symbolDexClient,
  })

  console.log({ data })

  const userPosition = useMemo(() => {
    if (!data) return null
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    return data?.getUserPosition
  }, [data])

  const mockupData = {
    positions: [
      {
        time: 1749206596940,
        size: 0.0001,
        pnl: -10.5455,
        pnlPercent: -0.01,
        positionValue: 10.3709,
        margin: 10.3709,
        liqPx: 0,
        entryPx: 105455,
        markPx: 0,
        fundingFee: -0.167805,
        symbol: 'BTC',
        openInterest: 0,
        leverage: {
          type: 'cross',
          value: 1,
        },
      },
      {
        time: 1749206596940,
        size: 17,
        pnl: -10.87796,
        pnlPercent: -1700,
        positionValue: 10.96262,
        margin: 10.96262,
        liqPx: 0,
        entryPx: 0.63988,
        markPx: 0,
        fundingFee: -0.001453,
        symbol: 'ADA',
        openInterest: 0,
        leverage: {
          type: 'cross',
          value: 1,
        },
      },
    ],
    balance: 49.764322,
    unrealizedPnl: -21.423460000000002,
    positionValue: 21.33352,
    availableWithdraw: 28.430802,
    availableMargin: 28.430802,
  }

  return (
    <>
      <div className="p-[10px] rounded-t-[10px] bg-[#14141480] relative bg-[url('/images/assets/futures/bg.svg')] bg-cover bg-center bg-no-repeat mb-80 ">
        <PositionsOverview data={userPosition} />
        <div className="mt-4 border-t border-[#ECECED14] pt-4 flex flex-col gap-2">
          {userPosition?.positions.map((item: Position, index: number) => (
            <ItemPosition key={index} item={item} />
          ))}
        </div>
      </div>
    </>
  )
}

export default TabPositions
