import { TradeHistory, UserTradeHistoryResponse } from '@/@generated/gql/graphql-symbolDex'
import { IconEmpty } from '@/components/icon'
import { Skeleton } from '@/components/ui/skeleton'
import { symbolDexClient } from '@/lib/gql/apollo-client'
import { cn } from '@/lib/utils'
import { getUserTradeHistory } from '@/services/assets.service'
import { formatNumberWithCommas, formatPercentage } from '@/utils/helpers'
import { useQuery } from '@apollo/client'
import LogoWithChain from '@components/common/LogoWithChain.tsx'
import dayjs from 'dayjs'
import { TFunction } from 'i18next'
import { memo, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

export enum SIDE_ENUM {
  LONG = 'Long',
  SHORT = 'Short',
}

const ItemAssetSkeleton = memo(() => {
  return (
    <div className="rounded-[8px] bg-gradient-to-r border border-[#ECECED14] animate-pulse">
      <div className="p-3 bg-[#FFFFFF05]">
        <div className="flex justify-between items-center">
          <div className="flex gap-2 items-center">
            <Skeleton className="w-[24px] h-[24px] rounded-full" />
            <div className="">
              <div className="flex gap-2">
                <div className="flex gap-1 items-center">
                  <Skeleton className="w-[60px] h-[14px]" />
                  <Skeleton className="w-[40px] h-[12px] rounded-[2px]" />
                </div>
              </div>
              <Skeleton className="w-[120px] h-[12px] mt-1" />
            </div>
          </div>
          <Skeleton className="w-5 h-5" />
        </div>
      </div>

      <div className="flex gap-2.5 py-2.5 flex-col">
        <div className="px-3 flex justify-between items-center gap-2">
          <div>
            <Skeleton className="w-[60px] h-[11px]" />
            <Skeleton className="w-[80px] h-[15px] mt-1" />
          </div>
          <div className="text-right">
            <Skeleton className="w-[70px] h-[11px]" />
            <Skeleton className="w-[30px] h-[12px] mt-1" />
          </div>
        </div>

        <div className="px-3 grid grid-cols-3 gap-2">
          <div>
            <Skeleton className="w-[50px] h-[11px]" />
            <Skeleton className="w-[60px] h-[12px] mt-1" />
          </div>
          <div className="text-center">
            <Skeleton className="w-[40px] h-[11px] mx-auto" />
            <Skeleton className="w-[50px] h-[12px] mt-1 mx-auto" />
          </div>
          <div className="text-right">
            <Skeleton className="w-[45px] h-[11px] ml-auto" />
            <Skeleton className="w-[55px] h-[12px] mt-1 ml-auto" />
          </div>
        </div>
      </div>
    </div>
  )
})

export const ItemAsset = memo(({ item, t }: { item: TradeHistory; t: TFunction }) => {
  const handleCheckSide = (dir: string) => {
    return dir.includes(SIDE_ENUM.LONG)
  }

  return (
    <div
      className={cn(
        'rounded-[8px] bg-gradient-to-r border border-[#ECECED14] cursor-pointer',
        handleCheckSide(item.dir) ? 'gradient-border-long gradient-bg-long' : 'gradient-border-short gradient-bg-short',
      )}
    >
      <div className={`p-3 ${handleCheckSide(item.dir) ? 'header-item-long' : 'header-item-short'}`}>
        <div className={`flex justify-between items-center `}>
          <div className="flex gap-2 items-center">
            <LogoWithChain logo="" logoClassName="w-[24px] h-[24px]" name={item.symbol} />
            <div className="">
              <div className="flex gap-2">
                <div className="flex gap-1 items-center">
                  <div className="font-medium text-[14px] leading-none text-white whitespace-nowrap pr-2">
                    {item.symbol}
                  </div>
                  <div
                    className={`px-1 py-[1px] font-medium text-[10px] rounded-[2px] border-[0.5px] leading-[calc(1rem*(10/16))] pb-[3.8px] ${
                      handleCheckSide(item.dir) ? 'border-[#00FFB4] text-[#00FFB4]' : 'border-[#AB57FF] text-[#AB57FF]'
                    }`}
                  >
                    {item.dir}
                  </div>
                  {/* <div
                    className={`px-1 py-[2.5px] font-medium text-[10px] leading-none rounded-[2px] border-[0.5px] border-[#FFFFFFCC] text-[#FFFFFFCC]`}
                  >
                    平空
                  </div> */}
                  {/* <div className="px-1 py-[2.5px] font-medium text-[10px] leading-none rounded-[2px] border-[0.5px] border-[#00FFF633] text-[#00FFF6] bg-[#00FFF633] capitalize whitespace-nowrap">{`${item.marginType === 'cross' ? t('position.cross') : t('position.isolated')} ${item.leverage}x`}</div> */}
                </div>
              </div>
              <div className="font-normal text-[12px] text-white/50 mt-0.5">
                {dayjs(item.time).format('YYYY-MM-DD HH:mm:ss')}
              </div>
            </div>
          </div>

          <img
            src="/images/icons/share.svg"
            alt="share"
            className="w-5 h-5 cursor-pointer hover:scale-[1.1]"
            onAbort={() => {}}
          />
        </div>
      </div>
      <div className="flex gap-2.5 py-2.5 flex-col">
        <div className="px-3  flex justify-between items-center gap-2">
          <div>
            <div className="font-normal text-[11px] leading-none text-white/50">{t('position.realizedPnl')}</div>
            <div className={`mt-1 ${Number(item.pnl) >= 0 ? 'text-rise' : 'text-fall'} text-[15px]`}>
              <span className="font-[500] pr-1">
                {formatNumberWithCommas(`${item.pnl > 0 ? '+' + item.pnl : item.pnl}`)}
              </span>
              <span className="text-[12px]">
                ({item.pnlPercent > 0 ? '+' : ''}
                {formatPercentage(Number(item.pnlPercent))})
              </span>
            </div>
          </div>
          <div className="text-right">
            <div className="font-normal text-[11px] leading-none text-white/50">{t('position.openInterest')}</div>
            <div className="mt-1 text-white text-[12px]">--</div>
            {/* <div className="mt-1 text-white">{formatNumberWithCommas(item.holdingValue)}</div> */}
          </div>
        </div>
        <div className="px-3 grid grid-cols-3 gap-2">
          <div>
            <div className="font-normal text-[11px] leading-none text-white/50">{t('position.transactionPrice')}</div>
            <div className={`mt-1 text-white text-[12px]`}>
              {/* {Number(item.todayPnl) >= 0 ? '+' : ''} */}
              {formatNumberWithCommas(`${item.px}`)}
            </div>
          </div>
          <div className="text-center">
            <div className="font-normal text-[11px] leading-none text-white/50">
              {t('assets.futures.positionHistory')}
            </div>
            <div className="mt-1 text-white text-[12px]">{formatNumberWithCommas(`${item.sz}`)}</div>
          </div>
          <div className="text-right">
            <div className="font-normal text-[11px] leading-none text-white/50">
              {t('assets.deposit.fee')} ({item.feeToken})
            </div>
            <div className="mt-1 text-white text-[12px]">{formatNumberWithCommas(`${item.fee}`)}</div>
          </div>
        </div>
      </div>
    </div>
  )
})

const TabAssets = () => {
  const { t } = useTranslation()

  const { data, loading } = useQuery<UserTradeHistoryResponse>(getUserTradeHistory, {
    client: symbolDexClient,
  })

  const histories: TradeHistory[] = useMemo(() => {
    if (!data) return []

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    return data?.getUserTradeHistory.histories
  }, [data])

  if (loading) {
    return (
      <div className="flex flex-col gap-2 mb-80">
        {Array.from({ length: 6 }).map((_, index) => (
          <ItemAssetSkeleton key={index} />
        ))}
      </div>
    )
  }

  return (
    <div className="flex flex-col gap-2 mb-80">
      {histories.length ? (
        histories.map((item) => (
          <ItemAsset item={item} t={t} />
        ))
      ) : (
        <div className="flex flex-col items-center justify-center mt-[30%]">
          <IconEmpty />
          <span className="text-[#FFFFFF80] text-[0.75rem]">{t('assets.futures.noDataYet')}</span>
        </div>
      )}
    </div>
  )
}

export default TabAssets
