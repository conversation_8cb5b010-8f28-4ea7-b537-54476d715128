import { Button } from '@/components/ui/button'
import { TYPE_ACCOUNT } from '@/lib/blockchain.ts'
import { useAppSelector } from '@/redux/store'
import ButtonTelegram from '@components/common/LoginSection/ButtonTelegram.tsx'
import ButtonWallet from '@components/common/LoginSection/ButtonWallet.tsx'
import { ReactNode } from 'react'
import { useTranslation } from 'react-i18next'
import { FuturesAssetOverview } from './FuturesAssetOverview'

export interface BaseAssetsHeaderProps {
  hideBalance: boolean
  setHideBalance: (value: boolean) => void
  walletName: ReactNode
  onItemClick: (key: string) => void
  hiddenKeys?: string[]
  balance?: number
  walletAddress?: string
}

export const AssetsHeader = (props: BaseAssetsHeaderProps) => {
  const { hideBalance, setHideBalance, walletName, onItemClick, hiddenKeys = [], balance, walletAddress } = props

  const { activeAccount, activeChain, wallets } = useAppSelector((state) => state.wallet)

  const activeWallet =
    activeAccount === TYPE_ACCOUNT.CHAIN ? wallets?.[activeChain]?.chain : wallets?.[activeChain]?.telegram

  const { t } = useTranslation()

  if (!activeWallet.isConnected) {
    return (
      <div className="mt-10 flex items-center justify-center gap-3">
        <ButtonTelegram />
        <ButtonWallet />
      </div>
    )
  }

  return (
    <>
      <div className="mt-5">
        <FuturesAssetOverview
          hideBalance={hideBalance}
          setHideBalance={setHideBalance}
          walletName={walletName}
          balance={balance}
          walletAddress={walletAddress}
        />
      </div>
      <div className="mt-4 flex items-center justify-center px-[10px] max-w-lg mx-auto">
        <Button
          variant="gradient"
          className="rounded-full text-[#141414] h-fit w-full"
          onClick={() => onItemClick('transfer')}
        >
          {t('assets.transfer')}
        </Button>
      </div>
    </>
  )
}
