import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { TabItem, Tabs } from '@components/assets/WaveTabs.tsx'
import { cn } from '@/lib/utils.ts'
import TabPositions from './TabPositions'
import TabAssets from './TabAssets'

const Futures = () => {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState(0)

  const tabs: TabItem[] = [
    { label: t('assets.futures.positions'), value: 'positions' },
    { label: t('assets.funding.orderHistory'), value: 'orderHistory' },
  ]

  const renderTabContent = () => {
    switch (activeTab) {
      case 0:
        return <TabPositions />
      case 1:
        return <TabAssets />
      default:
        return <TabPositions />
    }
  }

  return (
    <div className="mt-[14px] overflow-y-auto h-dvh _hidescrollbar">
      <Tabs tabs={tabs} activeTabIndex={activeTab} onTabChange={(_, index) => setActiveTab(index)} />
      <div className={cn('bg-[#ECECED14]')}>
        <div
          className={cn(
            'bg-[#141414] px-3 py-3 rounded-t-[16px] transition-all duration-500',
            activeTab === 0 ? 'rounded-tl-none' : '',
            activeTab === tabs.length - 1 ? 'rounded-tr-none' : '',
          )}
        >
          {renderTabContent()}
        </div>
      </div>
    </div>
  )
}

export default Futures
