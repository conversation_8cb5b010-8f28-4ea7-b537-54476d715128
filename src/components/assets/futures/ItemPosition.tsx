import { APP_PATH } from '@/lib/constant'
import { formatPercentage } from '@/utils/helpers'
import LogoWithChain from '@components/common/LogoWithChain.tsx'
import { Button } from '@components/ui/button.tsx'
import dayjs from 'dayjs'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import './styles.css'
import { cn } from '@/lib/utils'
import { Position } from '@/@generated/gql/graphql-symbolDex'

interface ItemPositionProps {
  item: Position
}

const ItemPosition = ({ item }: ItemPositionProps) => {
  const navigate = useNavigate()
  const { t } = useTranslation()

  return (
    <div
      className={cn(
        `rounded-[8px] cursor-pointer`,
        Number(item.pnlPercent) >= 0 ? 'gradient-border-long gradient-bg-long' : 'gradient-border-short gradient-bg-short',
      )}
      onClick={() => {
        navigate(APP_PATH.FUTURES_POSITION + `/${item.symbol}`)
      }}
    >
      <div
        className={`flex justify-between items-center p-3 ${Number(item.pnlPercent) >= 0 ? 'header-item-long' : 'header-item-short'}`}
      >
        <div className="flex gap-2">
          <LogoWithChain logo="" logoClassName="w-[24px] h-[24px]" name={item.symbol} />
          <div className="flex gap-1 items-center">
            <div className="font-medium text-[14px] leading-none text-white whitespace-nowrap">
              {item.symbol}
            </div>
            {/* <div
              className={`px-1 py-[2.5px] font-medium text-[11px] rounded-[2px] border-[0.5px] leading-[calc(1rem*(11/16))] pb-[5px] ${
                Number(item.pnlPercent) >= 0 ? 'border-[#00FFB4] text-[#00FFB4]' : 'border-[#AB57FF] text-[#AB57FF]'
              }`}
            >
              {Number(item.pnlPercent) >= 0 ? t('position.long') : t('position.short')}
            </div> */}
            <div className="px-1 py-[2.5px] font-medium text-[11px] rounded-[2px] border-[0.5px] leading-[calc(1rem*(11/16))] pb-[5px] text-[#00FFF6] bg-[#00FFF633] capitalize whitespace-nowrap">{`${item.leverage.type === 'cross' ? t('position.cross') : t('position.isolated')} ${item.leverage.value}x`}</div>
          </div>
        </div>
        <div className="font-normal text-right text-[12px] text-white/50">
          {dayjs(item.time).format('YYYY-MM-DD HH:mm:ss')}
        </div>
      </div>
      <div className="flex justify-between items-center p-3">
        <div>
          <div className="font-normal text-[11px] leading-[12px] text-white/50">{t('position.pnl')} (USDT)</div>
          <div
            className={`mt-1.5 font-semibold text-[15px] leading-none ${Number(item.pnl) >= 0 ? 'text-rise ' : 'text-fall'}`}
          >
            {item.pnl} ({formatPercentage(Number(item.pnlPercent))})
          </div>
        </div>
        <div className="text-right">
          <div className="font-normal text-[11px] leading-[12px] text-white/50">{t('position.openInterest')} (BTC)</div>
          <div className="mt-1.5 font-semibold text-[15px] leading-none text-white">{item.openInterest}</div>
        </div>
      </div>
      <div className="grid grid-cols-3 gap-2 px-3 pb-3">
        <div>
          <div className="font-normal text-[11px] leading-[12px] text-white/50">{t('position.size')} (USDT)</div>
          <div className="mt-1.5 font-semibold text-[15px] leading-none text-white">{item.size}</div>
        </div>
        <div className="text-center">
          <div className="font-normal text-[11px] leading-[12px] text-white/50">{t('position.margin')} (USDT)</div>
          <div className="mt-1.5 font-semibold text-[15px] leading-none text-white">{item.margin}</div>
        </div>
        <div className="text-right">
          <div className="font-normal text-[11px] leading-[12px] text-white/50">{t('position.liquidationPrice')}</div>
          <div className="mt-1.5 font-semibold text-[15px] leading-none text-white">{item.liqPx}</div>
        </div>
        <div>
          <div className="font-normal text-[11px] leading-[12px] text-white/50">{t('position.entryPrice')}</div>
          <div className="mt-1.5 font-semibold text-[15px] leading-none text-white">{item.entryPx}</div>
        </div>
        <div className="text-center">
          <div className="font-normal text-[11px] leading-[12px] text-white/50">{t('position.markPrice')}</div>
          <div className="mt-1.5 font-semibold text-[15px] leading-none text-white">{item.markPx}</div>
        </div>
        <div className="text-right">
          <div className="font-normal text-[11px] leading-[12px] text-white/50">{t('position.funding')} (USDT)</div>
          <div
            className={`mt-1.5 font-semibold text-[15px] leading-none ${Number(item.pnl) >= 0 ? 'text-rise ' : 'text-fall'}`}
          >
            {item.fundingFee}
          </div>
        </div>
      </div>
      <div className="flex justify-between items-center gap-3 p-3 border-t border-[#ECECED14]">
        <Button
          variant="borderGradient"
          size="sm"
          className="min-w-[80px] sm:min-w-[120px] rounded-full bg-gradient-to-r from-[#E149F8]/10 via-[#9945FF]/10 to-[#00F3AB]/10 font-medium text-[12px] leading-none text-[#00FFB4]"
          onClick={(e) => {
            e.stopPropagation()
            navigate(APP_PATH.FUTURES_POSITION + `/${item.symbol}`)
          }}
        >
          {`${t('position.takeProfit')}/${t('position.stopLoss')}`}
        </Button>
        <div
          className="h-8 px-[15px] sm:min-w-[120px] rounded-full border-[#ECECED1F] bg-[#ECECED14] font-medium text-[12px] leading-none text-white flex items-center justify-center cursor-pointer"
          onClick={(e) => {
            e.stopPropagation()
            navigate(APP_PATH.FUTURES_POSITION + `/${item.symbol}`)
          }}
        >
          {t('position.closePosition')}
        </div>
        <div className="flex items-center justify-center gap-1 font-normal text-[13px] leading-none text-white/50">
          <img
            src="/images/icons/share.svg"
            alt="share"
            className="w-[20px] h-[20px] cursor-pointer hover:scale-[1.1]"
            onClick={(e) => {
              e.stopPropagation()
              console.log('Share position clicked')
            }}
          />
          {t('position.share')}
        </div>
      </div>
    </div>
  )
}

export default ItemPosition
