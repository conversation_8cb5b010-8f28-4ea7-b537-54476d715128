import { UserPositionResponse } from '@/@generated/gql/graphql-symbolDex'
import MoneyFormatted from '@components/common/MoneyFormatted.tsx'
import { useTranslation } from 'react-i18next'

const PositionsOverview = ({
  data,
}: {
  data: Pick<
    UserPositionResponse,
    | 'balance'
    | 'unrealizedPnl'
    | 'positionValue'
    | 'availableWithdraw'
    | 'availableMargin'
  >
}) => {
  const { t } = useTranslation()

  return (
    <div className="flex flex-col gap-3">
      <div className="flex justify-between">
        <div className="text-left">
          <div className="font-semibold text-[22px] leading-none text-[#00FFB4]">
            {data?.balance ? <MoneyFormatted value={data.balance} /> : '--'}
          </div>
          <div className="mt-3 font-normal text-[14px] leading-none text-white/70">{t('assets.futures.balance')}</div>
        </div>
        <div className="text-right">
          <div
            className={`font-semibold text-[22px] leading-none ${Number(data?.unrealizedPnl ?? 0) >= 0 ? 'text-[#00FFB4]' : 'text-[#AB57FF]'}`}
          >
            {data?.unrealizedPnl ? (
              <>
                {Number(data.unrealizedPnl) >= 0 ? '+' : '-'}
                <MoneyFormatted value={Math.abs(Number(data.unrealizedPnl))} />
              </>
            ) : (
              '--'
            )}
          </div>
          <div className="mt-3 font-normal text-[14px] leading-none text-white/70">
            {t('assets.futures.unrealizedPnl')}{' '}
          </div>
        </div>
      </div>
      <div className="flex justify-between py-3">
        <div className="font-normal text-[14px] leading-none text-white/70">{t('assets.futures.positionValue')}</div>
        <div className="font-semibold text-[14px] leading-none text-white">
          {data?.positionValue ? <MoneyFormatted value={Math.abs(Number(data.positionValue))} /> : '--'}
        </div>
      </div>
      <div className="flex justify-between py-3">
        <div className="font-normal text-[14px] leading-none text-white/70">{t('assets.futures.availableMargin')}</div>
        <div className="font-semibold text-[14px] leading-none text-white">
          {data?.availableMargin ? <MoneyFormatted value={Math.abs(Number(data.availableMargin))} /> : '--'}
        </div>
      </div>
      <div className="flex justify-between py-3">
        <div className="font-normal text-[14px] leading-none text-white/70">
          {t('assets.futures.availableForWithdraw')}
        </div>
        <div className="font-semibold text-[14px] leading-none text-white">
          {data?.availableWithdraw ? <MoneyFormatted value={Math.abs(Number(data.availableWithdraw))} /> : '--'}
        </div>
      </div>
      <div className="flex justify-between py-3">
        <div className="font-normal text-[14px] leading-none text-white/70">{t('assets.futures.leverage')}</div>
        <div className="font-semibold text-[14px] leading-none text-[#00FFB4]">--</div>
      </div>
      <div className="flex justify-between py-3">
        <div className="font-normal text-[14px] leading-none text-white/70">{t('assets.futures.fullMarginRatio')}</div>
        <div className="font-semibold text-[14px] leading-none">--</div>
      </div>
      <div className="flex justify-between py-3">
        <div className="font-normal text-[14px] leading-none text-white/70">
          {t('assets.futures.maintenanceMargin')}
        </div>
        <div className="font-semibold text-[14px] leading-none text-white">--</div>
      </div>
    </div>
  )
}

export default PositionsOverview
