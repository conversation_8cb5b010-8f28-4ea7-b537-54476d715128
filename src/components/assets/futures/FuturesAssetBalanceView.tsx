import { NullableDataItem } from '@components/common/walletBalance/WalletChangeBox.tsx'
import { useTranslation } from 'react-i18next'
import { ReactNode, useMemo } from 'react'
import { formatBalanceWallet } from '@/lib/number.ts'
import { cn } from '@/lib/utils.ts'
import { EstimatedAssets } from '@components/assets/overview/EstimatedAssets.tsx'
import { formatPercentageChange } from '@/lib/format.ts'

export interface AssetBalanceViewProps {
  hideBalance: boolean
  toggleHideBalance: (hide: boolean) => void
  currentAmount: NullableDataItem
  firstItem: NullableDataItem | undefined
  timeRange: string
  chartExpanded: boolean
  expandChart: () => void
  walletName?: ReactNode
  showArrow?: boolean
  balance?: string
  walletBalance?: number
}

/**
 * @fileoverview AssetBalanceView component
 * @description This component displays the asset balance view.
 * It is used in the AssetOverview component to show the balance of a specific asset. Temporarily, it replaces the @components/common/walletBalance/BalanceView.tsx component.
 * @constructor
 */
export const FuturesAssetBalanceView = (props: AssetBalanceViewProps) => {
  const {
    hideBalance,
    currentAmount = { price: 100, changeAmount: 100, changePercentage: 100 },
    firstItem,
    timeRange,
    showArrow,
    walletName,
    walletBalance = 100,
  } = props

  const { t } = useTranslation()

  const changeAmount = useMemo(() => {
    if (currentAmount) {
      const isNegative = currentAmount.changeAmount < 0
      return (
        (isNegative ? '-' : '+') + formatBalanceWallet({ balance: Math.abs(+currentAmount.changeAmount), decimal: 4 })
      )
    }
    const amount = Number(walletBalance) - (firstItem?.price || 0)
    if (Math.abs(amount) <= 1e-5) return '+0'
    const isNegative = amount < 0
    return (isNegative ? '-' : '+') + formatBalanceWallet({ balance: Math.abs(+amount), decimal: 4 })
  }, [currentAmount, walletBalance, firstItem])

  const changePercentage = useMemo(() => {
    if (currentAmount) return currentAmount.changePercentage
    const balance = Number(walletBalance)
    const firstPrice = Number(firstItem?.price ?? 0)
    if (firstPrice === 0) return 0
    if (balance === firstPrice) return 0
    return ((balance - firstPrice) * 100) / firstPrice
  }, [currentAmount, walletBalance, firstItem])

  const timeRangeLabel = useMemo(() => {
    switch (timeRange) {
      case '1day':
        return t('chart.period.day')
      case '1week':
        return t('chart.period.week')
      case '1month':
        return t('chart.period.month')
      case '1year':
        return t('chart.period.year')
      default:
        return t('chart.period.day')
    }
  }, [timeRange])

  const balance = useMemo(() => {
    if (currentAmount) {
      return currentAmount.price >= 0 ? formatBalanceWallet({ balance: +currentAmount.price, decimal: 2 }) : '--'
    }
    if (walletBalance) return walletBalance
    return '0'
  }, [currentAmount, walletBalance])

  return (
    <div className="flex-1 mr-3">
      <div className="flex items-center mb-3">{walletName ?? <EstimatedAssets />}</div>

      <div
        className={cn(
          'flex items-center mb-2',
          walletBalance && Math.abs(walletBalance) < 1e-5 ? 'text-[#FFFFFF99]' : 'text-[#FFFFFF]',
        )}
      >
        <span className="text-[calc(1rem*(24/16))] leading-[calc(1rem*(24/16))] mr-1 font-bold">≈</span>
        <span className="text-[calc(1rem*(32/16))] leading-[calc(1rem*(32/16))] mr-2 font-bold">
          {hideBalance ? '*****' : formatBalanceWallet({ balance: +balance, decimal: 2 })}
        </span>
        <span className="text-[calc(1rem*(12/16))] leading-[calc(1rem*(12/16))] translate-y-[2px]">USD</span>
      </div>

      <div className="flex items-center text-(--text-tertiary) h-4">
        <div
          className={cn(
            'text-[calc(1rem*(13/16))] leading-[calc(1rem*(13/16))] mr-2',
            changeAmount.includes('+') ? 'text-rise' : 'text-fall',
          )}
        >
          {hideBalance ? '*****' : changeAmount}
        </div>
        {!hideBalance && (
          <>
            <div
              className={cn(
                'mr-2 py-0.5 px-1 text-[#1D1D20] rounded-[3px] text-[calc(1rem*(12/16))] leading-[calc(1rem*(12/16))]',
                changePercentage >= 0 ? 'bg-rise' : 'bg-fall',
              )}
            >
              {changePercentage >= 0.01 ? '+' : ''}
              {formatPercentageChange(changePercentage).label}
            </div>
          </>
        )}
        <div className="text-[calc(1rem*(12/16))] leading-[calc(1rem*(12/16))]">{timeRangeLabel}</div>
        {showArrow && (
          <div
            className={cn(
              'inline-block rounded-[200px] px-1.5 cursor-pointer transition-transform duration-300 rotate-270',
            )}
          >
            <img src="/images/icons/arrow-down-icon.svg" alt="Arrow Icon" />
          </div>
        )}
      </div>
    </div>
  )
}
