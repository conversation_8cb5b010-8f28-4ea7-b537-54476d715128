import { BaseAssetsHeader } from '@components/assets/BaseAssetsHeader.tsx'
import { useContext } from 'react'
import { AssetOverviewContext } from '@components/assets/overview/AssetOverviewContext.tsx'
import { IconEye, IconEyeSlash } from '@components/icon'
import { useTranslation } from 'react-i18next'

export interface FuturesHeaderProps {
  onItemClick: (key: string) => void
  balance?: number
}

export const FuturesHeader = (props: FuturesHeaderProps) => {
  const { t } = useTranslation()
  const { onItemClick, balance } = props

  const { hideBalance, toggleHideBalance } = useContext(AssetOverviewContext)
  const toggle = () => {
    toggleHideBalance(!hideBalance)
  }

  return (
    <BaseAssetsHeader
      hideBalance={hideBalance}
      setHideBalance={toggleHideBalance}
      onItemClick={onItemClick}
      hiddenKeys={[]}
      balance={balance}
      walletName={
        <div className="text-[calc(15rem/16)] leading-4 flex items-center gap-1">
          {t('assets.futuresAccount')}
          <button onClick={toggle}>{hideBalance ? <IconEyeSlash /> : <IconEye />}</button>
        </div>
      }
    />
  )
}
