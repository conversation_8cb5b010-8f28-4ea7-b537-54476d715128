import { createContext } from 'react'
import { WalletBalanceDto } from '@/@generated/gql/graphql-core.ts'

export interface WalletBalance {
  funding: WalletBalanceDto[] | undefined
  futures: WalletBalanceDto[] | undefined
  spot: WalletBalanceDto[] | undefined
}

export interface AssetOverviewContextState {
  hideBalance: boolean
  toggleHideBalance: (hide: boolean) => void
  walletBalanceData?: WalletBalance
}

const defaultValue: AssetOverviewContextState = {
  hideBalance: false,
  toggleHideBalance: () => {},
}

export const AssetOverviewContext = createContext<AssetOverviewContextState>(defaultValue)

export const AssetOverviewProvider = AssetOverviewContext.Provider
