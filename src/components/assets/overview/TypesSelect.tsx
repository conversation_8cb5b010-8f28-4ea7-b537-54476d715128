import { useMemo, useState } from 'react'
import { <PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DrawerTrigger } from '@components/ui/drawer.tsx'
import { useTranslation } from 'react-i18next'
import { IconCheckedCircle } from '@components/icon/IconCheckedCircle.tsx'
import { motion } from 'framer-motion'

type TypeOption = {
  label: string
  value: string
  disabled?: boolean
}

export const TypesSelect = () => {
  const [open, setOpen] = useState(false)
  const { t } = useTranslation()

  const [selectedOption, setSelectedOption] = useState<string>('all')

  const options: TypeOption[] = useMemo(() => {
    return [
      {
        label: t('assets.overview.allTypes'),
        value: 'all',
      },
      {
        label: t('assets.overview.deposit'),
        value: 'deposit',
      },
      {
        label: t('assets.overview.withdrawal'),
        value: 'withdraw',
      },
      {
        label: t('assets.overview.depositVault'),
        value: 'depositVault',
        disabled: true,
      },
      {
        label: t('assets.overview.redeemVault'),
        value: 'redeemVault',
        disabled: true,
      },
      {
        label: t('assets.overview.futuresSpotTransfer'),
        value: 'transfer',
        disabled: true,
      },
    ]
  }, [t])

  const optionText = useMemo(() => {
    const opt = options.find((item) => item.value === selectedOption)
    return opt ? opt.label : t('assets.overview.allTypes')
  }, [selectedOption])

  const handleSelect = (value: string) => {
    setSelectedOption(value)
    setOpen(false)
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <div className="flex items-center px-3 gap-1.5 bg-[#ECECED14] rounded-full cursor-pointer text-[calc(13rem/16)] leading-[calc(13rem/16)] h-[26px]">
          {optionText}
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M8.2115 11.045C8.08484 11.045 7.95817 10.9983 7.85817 10.8983L3.8115 6.85167C3.61817 6.65833 3.61817 6.33833 3.8115 6.145C4.00484 5.95167 4.32484 5.95167 4.51817 6.145L8.2115 9.83833L11.9048 6.145C12.0982 5.95167 12.4182 5.95167 12.6115 6.145C12.8048 6.33833 12.8048 6.65833 12.6115 6.85167L8.56484 10.8983C8.46484 10.9983 8.33817 11.045 8.2115 11.045Z"
              fill="white"
              fillOpacity={1}
            />
          </svg>
        </div>
      </DrawerTrigger>
      <DrawerContent className="w-full bg-[#232329] max-w-[768px] mx-auto pb-6 bg-[url('/images/popup-bg.png')] bg-cover bg-no-repeat">
        <DrawerTitle></DrawerTitle>
        <DrawerHeader className="flex justify-end">
          <img
            src="/images/icons/icon-x.svg"
            className="w-6 h-6 cursor-pointer"
            onClick={() => setOpen(false)}
            alt=""
          />
        </DrawerHeader>
        <div className="px-3">
          {options.map((option) => (
            <div
              key={option.value}
              className="flex items-center border-b  border-[#ECECED14] last:border-b-0 py-5"
              onClick={() => handleSelect(option.value)}
              style={{
                cursor: option.disabled ? 'not-allowed' : 'pointer',
                opacity: option.disabled ? 0.5 : 1,
              }}
            >
              <div className="text-[1rem] leading-4 flex-1">{option.label}</div>
              {selectedOption === option.value && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  <IconCheckedCircle className="size-5" />
                </motion.div>
              )}
            </div>
          ))}
        </div>
      </DrawerContent>
    </Drawer>
  )
}
