import ChainCurrencyIcon from '@components/common/ChainCurrencyIcon.tsx'
import dayjs from 'dayjs'
import { IconArrowDownCircle } from '@components/icon/IconArrowDownCircle.tsx'
import { IconArrowUpCircle } from '@components/icon/IconArrowUpCircle.tsx'
import { IconPlusCircle } from '@components/icon/IconPlusCircle.tsx'
import { IconNext } from '@components/icon/IconNext.tsx'
import { cn } from '@/lib/utils.ts'
import { useState } from 'react'
import { BaseTransferDetailsDrawer } from '@components/assets/transferDetails/BaseTransferDetailsDrawer.tsx'
import MoneyFormatted from '@components/common/MoneyFormatted.tsx'

export type AccountType = 'Futures' | 'Spot' | 'Funding' | 'Vault' | 'ArbitrumWallet'

export type FundingRecord = {
  chainId: string
  fromToken: {
    symbol: string
    icon: string
    chain?: string
  }
  toToken?: {
    symbol: string
    icon: string
    chain?: string
  }
  timestamp: number
  status: 'success' | 'failed'
  statusLabel: string
  direction: 'in' | 'out' | 'swap'
  directionLabel: string
  fromWallet?: string
  toWallet?: string
  amount: number
  unit: string
  fee: number
  transactionHash: string
} & (
  | {
      type: 'Deposit' | 'Withdrawal' | 'AddVault' | 'RemoveVault'
      senderAddress: string
      senderAddressTypes: AccountType[]
      receiverAddress: string
      receiverAddressTypes: AccountType[]
      amount: number
      amountInUSD: number
    }
  | {
      type: 'Exchange'
      fromAccountAddress: string
      fromAccountAddressType: 'Futures' | 'Spot' | 'Funding' | 'Vault'
      toAccountAddress: string
      toAccountAddressType: 'Futures' | 'Spot' | 'Funding' | 'Vault'
      fromAmount: number
      toAmount: number
    }
)

export interface FundingRecordItemProps {
  record: FundingRecord
}

const DirectionIcon = (props: { direction: 'in' | 'out' | 'swap' }) => {
  const { direction } = props
  if (direction === 'in') {
    return <IconArrowDownCircle />
  }
  if (direction === 'out') {
    return <IconArrowUpCircle />
  }
  return <IconPlusCircle />
}

export const FundingRecordItem = (props: FundingRecordItemProps) => {
  const { record } = props
  const {
    fromToken,
    toToken,
    timestamp,
    statusLabel,
    direction,
    directionLabel,
    fromWallet,
    toWallet,
    status,
    amount,
    unit,
  } = record

  const [openDrawer, setOpenDrawer] = useState(false)

  return (
    <div
      className={cn(
        'bg-size-[100%_100%] bg-no-repeat px-3 cursor-pointer rounded-[10px]',
        status === 'success'
          ? `border-gradient-funding-record-success-item funding-record-success-item-background`
          : `border-gradient-funding-record-failed-item funding-record-failed-item-background`,
      )}
      onClick={() => setOpenDrawer(!openDrawer)}
    >
      <div className="flex items-center py-2.5 gap-2">
        <div className="flex items-center">
          <ChainCurrencyIcon currencyIcon={fromToken.icon} className="z-[1]" />
          {!!toToken && (
            <ChainCurrencyIcon currencyIcon={toToken.icon} className="-mx-1.5 z-0" chainIcon={toToken.chain} />
          )}
        </div>
        <div className={cn('flex-1 flex gap-x-2', toToken ? 'flex-col items-start' : 'items-center')}>
          <div className="flex items-center text-[calc(14rem/16)] gap-1">
            {fromToken.symbol}
            {toToken && (
              <>
                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M8.41748 3.45898L11.9583 6.99982L8.41748 10.5407"
                    stroke="#B9B9B9"
                    strokeWidth="0.875"
                    strokeMiterlimit="10"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M2.04102 7H11.8585"
                    stroke="#B9B9B9"
                    strokeWidth="0.875"
                    strokeMiterlimit="10"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                {toToken.symbol}
              </>
            )}
          </div>
          <div className="text-[#FFFFFF80] text-[calc(11rem/16)]">{dayjs(timestamp).format('YYYY/MM/DD HH:mm:ss')}</div>
        </div>
        <div className="text-[#FFFFFF80] text-[calc(12rem/16)]">{statusLabel}</div>
      </div>

      <hr className="bg-[#ECECED14]" />

      <div className="flex items-center justify-between py-3">
        <div>
          <div className="flex items-center mb-2">
            <div className="bg-[#FFFFFF47] rounded-full mr-2">
              <DirectionIcon direction={direction} />
            </div>
            <span className="text-[1rem]">{directionLabel}</span>
          </div>
          <div className="flex items-center gap-2 text-[calc(14rem/16)]">
            {!!fromWallet && <div>{fromWallet}</div>}
            {!!toWallet && (
              <>
                <IconNext />
                <div>{toWallet}</div>
              </>
            )}
          </div>
        </div>
        <div className={cn('text-[calc(15rem/16)]', status === 'success' ? 'text-rise' : 'text-fall')}>
          {amount > 0 ? '+' : '-'}
          <MoneyFormatted value={Math.abs(amount)} unit={unit} />
        </div>
      </div>
      <BaseTransferDetailsDrawer open={openDrawer} setOpen={setOpenDrawer} record={record} />
    </div>
  )
}
