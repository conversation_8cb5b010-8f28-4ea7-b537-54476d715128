import { BaseAssetsHeader } from '@components/assets/BaseAssetsHeader.tsx'
import { AssetOverviewContext } from '@components/assets/overview/AssetOverviewContext.tsx'
import { useContext } from 'react'
import { EstimatedAssets } from '@components/assets/overview/EstimatedAssets.tsx'

export interface OverviewHeaderProps {
  onItemClick: (key: string) => void
  balance?: number
}

export const OverviewHeader = (prop: OverviewHeaderProps) => {
  const { onItemClick, balance } = prop
  const { hideBalance, toggleHideBalance } = useContext(AssetOverviewContext)

  return (
    <>
      <BaseAssetsHeader
        hideBalance={hideBalance}
        setHideBalance={toggleHideBalance}
        walletName={<EstimatedAssets />}
        onItemClick={onItemClick}
        hiddenKeys={['orderHistory']}
        balance={balance}
      />
    </>
  )
}
