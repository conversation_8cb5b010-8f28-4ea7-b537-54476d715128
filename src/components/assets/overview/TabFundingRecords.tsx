import { AssetSelect } from '@components/assets/overview/AssetSelect.tsx'
import { TypesSelect } from '@components/assets/overview/TypesSelect.tsx'
import { FundingRecordItem } from '@components/assets/overview/FundingRecordItem.tsx'
import { useWithdrawalHistory } from '@hooks/useWithdrawalHistory.ts'
import { useMultiChainWallet } from '@hooks/useMultiChainWallet.ts'

export const TabFundingRecords = () => {
  const { activeWallet } = useMultiChainWallet({})
  const walletAddress = activeWallet?.walletId
  const { records } = useWithdrawalHistory(walletAddress)
  return (
    <div>
      <div className="flex items-center justify-between">
        <AssetSelect />
        <TypesSelect />
      </div>
      <div className="space-y-2 py-4">
        {records.map((record, index) => (
          <FundingRecordItem record={record} key={index} />
        ))}
      </div>
    </div>
  )
}
