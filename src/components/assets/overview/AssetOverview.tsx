import { cn } from '@/lib/utils.ts'
import { BalanceCollapseChart } from '@components/common/walletBalance/BalanceCollapseChart.tsx'
import BalanceExpendChartWrapper from '@components/common/walletBalance/BalanceExpendChartWrapper.tsx'
import { ReactNode, useCallback, useEffect, useMemo, useState } from 'react'
import { NullableDataItem } from '@components/common/walletBalance/WalletChangeBox.tsx'
import { usePreference } from '@hooks/usePreference.ts'
import { UITab } from '@/types/uiTabs.ts'
import { formatBalanceWallet } from '@/lib/number.ts'
import { useTranslation } from 'react-i18next'
import { AssetBalanceView } from '@components/assets/overview/AssetBalanceView.tsx'
import { WalletDuration } from '@/@generated/gql/graphql-core.ts'
import { useAssetChart } from '@hooks/useAssetChart.ts'

export interface AssetOverviewProps {
  hideBalance: boolean
  setHideBalance: (value: boolean) => void
  showArrow?: boolean
  walletName?: ReactNode
  balance?: number
  walletAddress?: string
}

const timeList: UITab[] = [
  {
    label: '1日',
    value: '1day',
  },
  {
    label: '1周',
    value: '1week',
  },
  {
    label: '1月',
    value: '1month',
  },
  {
    label: '1年',
    value: '1year',
  },
]

const periodMap: Record<string, WalletDuration> = {
  '1day': WalletDuration.D1,
  '1week': WalletDuration.W1,
  '1month': WalletDuration.M1,
  '1year': WalletDuration.Y1,
}

const paddingChart = 0
const maxWidthChart = 768

export const AssetOverview = (props: AssetOverviewProps) => {
  const { hideBalance, setHideBalance, showArrow, walletName, balance, walletAddress } = props
  const [currentAmount, setCurrentAmount] = useState<NullableDataItem>()
  const [isExpendChart, setExpendChart] = useState<boolean>(false)
  const [width, setWidth] = useState(getCalcW())

  const { preference, updatePreference } = usePreference()
  const { t } = useTranslation()

  const currentTime = useMemo(() => {
    return timeList.find((item) => item.value === preference.assetTimeRange) || timeList[1]
  }, [preference.assetTimeRange])

  const handleHoverChange = (point: NullableDataItem) => {
    setCurrentAmount(point)
  }

  function getCalcW() {
    return window.innerWidth - paddingChart >= maxWidthChart ? maxWidthChart : window.innerWidth - paddingChart
  }

  useEffect(() => {
    const handleResize = () => {
      setWidth(getCalcW())
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  const formatChartValue = useCallback((value: number) => {
    return '$' + formatBalanceWallet({ balance: +value, decimal: 2 })
  }, [])

  const setCurrentTime = (item: UITab) => {
    updatePreference({ assetTimeRange: item.value })
  }

  const translatedTimeList = timeList.map((item) => ({
    ...item,
    label: t(
      `chart.period.${item.label === '1日' ? 'day' : item.label === '1周' ? 'week' : item.label === '1月' ? 'month' : 'year'}`,
    ),
  }))

  const { collapsedData, expandedData } = useAssetChart({
    walletAddress: walletAddress,
    duration: periodMap[currentTime.value],
  })

  const firstItem = useMemo(() => {
    if (!expandedData || expandedData.length === 0) return undefined
    return expandedData.find((item) => Number(item.price) > 0)
  }, [expandedData])

  return (
    <>
      <div className="flex items-end mb-[calc(1rem*(14/16))] px-[10px]">
        <AssetBalanceView
          hideBalance={hideBalance}
          toggleHideBalance={() => setHideBalance(!hideBalance)}
          currentAmount={currentAmount ?? null}
          firstItem={firstItem}
          timeRange={currentTime.value}
          chartExpanded={isExpendChart}
          expandChart={() => setExpendChart(!isExpendChart)}
          showArrow={showArrow}
          walletName={walletName}
          walletBalance={balance}
        />

        <div
          className={cn(
            'w-[calc(1rem*(120/16))] h-[calc(1rem*(84/16))] transition-opacity duration-500 ease-in-out',
            isExpendChart ? 'opacity-0 pointer-events-none' : 'opacity-100',
            collapsedData ? 'block' : 'hidden',
          )}
          onClick={() => setExpendChart(true)}
        >
          <BalanceCollapseChart firstItem={firstItem} data={collapsedData} width={120} height={84} />
        </div>
      </div>

      <div
        className={cn(
          'overflow-hidden transition-all duration-500 ease-in-out',
          isExpendChart ? 'max-h-[560px]' : 'max-h-0',
          expandedData ? 'block' : 'hidden',
        )}
      >
        <div className={cn('mb-2', width == maxWidthChart ? 'mx-auto w-[768px]' : '')}>
          <BalanceExpendChartWrapper
            data={expandedData}
            width={width}
            height={width / 1.6}
            period={currentTime.value}
            onHoverChange={handleHoverChange}
            isThumb={false}
            formatChartValue={formatChartValue}
            firstItem={firstItem}
          />

          <div className="text-center grid grid-cols-4 mt-2 select-none">
            {translatedTimeList.map((item) => (
              <div key={item.value} className="flex justify-center">
                <div
                  className={cn(
                    'cursor-pointer inline-block px-2 py-1 text-(--text-tertiary) rounded-[200px] text-[calc(1rem*(12/16))] leading-[calc(1rem*(12/16))]',
                    currentTime.value === item.value ? 'text-(--text-primary) bg-[rgba(236,236,237,0.08)]' : '',
                  )}
                  onClick={() => setCurrentTime(timeList.find((t) => t.value === item.value) || timeList[0])}
                >
                  {item.label}
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="flex justify-center select-none">
          <div
            className={cn(
              'inline-block rounded-[200px] px-1.5 bg-(--bg-secondary) cursor-pointer transition-transform duration-300',
              isExpendChart ? 'rotate-180' : '',
              expandedData && expandedData.length > 0 ? 'visible' : 'invisible pointer-events-none',
            )}
            onClick={() => setExpendChart(!isExpendChart)}
          >
            <img src="/images/icons/arrow-down-icon.svg" alt="Arrow Icon" />
          </div>
        </div>
      </div>
    </>
  )
}
