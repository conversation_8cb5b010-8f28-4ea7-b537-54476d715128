import { cn } from '@/lib/utils.ts'
import { useTranslation } from 'react-i18next'
import { useContext, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { APP_PATH } from '@/lib/constant.ts'
import { AssetOverviewContext } from '@components/assets/overview/AssetOverviewContext.tsx'
import { formatBalanceWallet } from '@/lib/number.ts'

type Section = {
  title: string
  value: number | string
  onClick?: () => void
}

const formatBalance = (value: number | undefined) => {
  if (value === undefined || value === null) return '--'
  return '$' + formatBalanceWallet({ balance: value, decimal: 2 })
}

export const TabAssets = () => {
  const { t } = useTranslation()

  const navigate = useNavigate()
  const { hideBalance, walletBalanceData } = useContext(AssetOverviewContext)

  const fundingBalance = useMemo(() => {
    if (!walletBalanceData) return 0
    return walletBalanceData.funding?.reduce((acc, cur) => {
      return acc + parseFloat(cur.usdBalance || '0')
    }, 0)
  }, [walletBalanceData])

  const sections: Section[] = useMemo(() => {
    return [
      {
        title: t('assets.funding.title'),
        value: Number(fundingBalance),
        onClick: () => {
          navigate(APP_PATH.MEME_ASSETS + '?tab=funding')
        },
      },
      {
        title: t('assets.overview.futures'),
        value: '--',
        onClick: () => {
          navigate(APP_PATH.MEME_ASSETS + '?tab=futures')
        },
      },
      // {
      //   title: t('assets.overview.spot'),
      //   value: 0,
      //   onClick: () => {
      //     navigate(APP_PATH.MEME_ASSETS + '?tab=spot')
      //   },
      // },
      // {
      //   title: t('assets.overview.vaults'),
      //   value: 0,
      //   onClick: () => {
      //     navigate(APP_PATH.MEME_ASSETS + '?tab=futures&sub=vaults')
      //   },
      // },
    ]
  }, [fundingBalance])

  return (
    <div className="space-y-2 py-3">
      {sections.map((section, index) => (
        <div
          key={index}
          className="border bg-[linear-gradient(105.98deg,#9945FF05_46.27%,#01FFB520_93.65%)] rounded-[10px] cursor-pointer"
          onClick={section.onClick}
        >
          <div className="px-3.5 py-6 flex items-center justify-between text-[calc(1rem*(18/16))] leading-[calc(1rem*(18/16))] text-[#FFFFFF]">
            <div className="pr-2">{section.title}</div>
            <div className="flex items-center">
              {hideBalance ? '***' : typeof section.value === 'number' ? formatBalance(section.value) : section.value}
              <img
                className={cn('ml-2 cursor-pointer transition-all duration-200 -rotate-90')}
                src="/images/assets/arrow-down.svg"
                alt=""
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
