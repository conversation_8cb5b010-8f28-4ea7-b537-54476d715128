import { useContext, useMemo } from 'react'
import { AssetOverviewContext } from '@components/assets/overview/AssetOverviewContext.tsx'
import { BaseAssetsHeader } from '@components/assets/BaseAssetsHeader.tsx'
import { TYPE_ACCOUNT } from '@/lib/blockchain.ts'
import { getImgIconChain } from '@/components/orderForm/FormDeposit'
import { useMultiChainWallet } from '@hooks/useMultiChainWallet.ts'
import { SwitchWallets } from '@components/assets/SwitchWallets.tsx'

export interface FundingHeaderProps {
  onItemClick: (key: string) => void
  balance?: number
}

export const FundingHeader = (props: FundingHeaderProps) => {
  const { onItemClick, balance } = props
  const { hideBalance, toggleHideBalance } = useContext(AssetOverviewContext)

  const { activeWallet, activeChain, activeAccount } = useMultiChainWallet({})

  const walletIcon: string = useMemo(() => {
    if (activeAccount === TYPE_ACCOUNT.TELEGRAM) {
      return '/images/logo-tele.svg'
    }

    return activeWallet?.walletInfo?.icon ? activeWallet?.walletInfo?.icon : getImgIconChain(activeChain)
  }, [activeChain, activeAccount, activeWallet])

  return (
    <BaseAssetsHeader
      hideBalance={hideBalance}
      setHideBalance={toggleHideBalance}
      onItemClick={onItemClick}
      hiddenKeys={[]}
      balance={balance}
      walletAddress={activeWallet?.walletId}
      walletName={
        <div className="flex items-center gap-1">
          <img src={walletIcon} alt="" className="size-4" />
          {/*<ListWallets />*/}
          <SwitchWallets />
        </div>
      }
    />
  )
}
