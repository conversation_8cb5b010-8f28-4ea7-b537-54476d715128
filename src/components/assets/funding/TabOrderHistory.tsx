import { useEffect, useState, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { formatNumber, getBlockChainLogo, getBlockchainLogo2 } from '@/utils/helpers'
import { DataTable } from '@pages/home/<USER>'
import { ColumnDef } from '@tanstack/react-table'
import dayjs from 'dayjs'
import {
  ChainType,
  OrderSortField,
  SearchOrderInput,
  SortDirection,
  TransactionType,
} from '@/@generated/gql/graphql-trading.ts'
import { useMultiChainWallet } from '@/hooks/useMultiChainWallet'
import { ChainIds } from '@/types/enums.ts'
import { tradingClient } from '@/lib/gql/apollo-client'
import { getTransactionHistory } from '@/services/order.service'
import { ServiceConfig } from '@/lib/gql/service-config'
import { Order } from '@/@generated/gql/graphql-trading.ts'
import { useAppSelector } from '@/redux/store'
import LogoWithChain from '@components/common/LogoWithChain.tsx'
import MoneyFormatted from '@components/common/MoneyFormatted.tsx'
import ChainSelect from '../ChainSelect'

const TabOrderHistory = () => {
  const { t } = useTranslation()
  const price = useAppSelector((state) => state.price.list)
  const ref = useRef<HTMLDivElement>(null)
  const { activeWallet } = useMultiChainWallet({})
  const userAddress = activeWallet?.walletId

  const [chainFilter, setChainFilter] = useState<string | undefined>(undefined)
  const [offset, setOffset] = useState(0)
  const [transactions, setTransactions] = useState<any[]>([])
  const activeChain = useAppSelector((state) => state.wallet.activeChain)

  const [dataUnit, setDataUnit] = useState(activeChain.toUpperCase())
  const [showMC, setShowMC] = useState(false)

  const columns: ColumnDef<Order>[] = [
    {
      header: t('transaction.token'),
      cell: ({ row }) => {
        const baseSymbol = row.original?.baseSymbol
        const baseToken = row.original?.baseAddress
        const chainId = Number(row.original?.chainId) as ChainIds

        return (
          <div className="flex items-center gap-2">
            <LogoWithChain
              logo={getBlockChainLogo(chainId, baseToken)}
              logoClassName="w-[30px] h-[30px]"
              chainLogo={getBlockchainLogo2(chainId)}
              name={baseSymbol}
            />
            <div>
              <div className="font-medium text-white text-[13px] leading-none">{baseSymbol}</div>
              <div className="mt-1 font-normal text-white/50 text-[10px] leading none">{baseSymbol}</div>
            </div>
          </div>
        )
      },
    },
    {
      header: t('transaction.type'),
      cell: ({ row }) => {
        const type = row.original?.transactionType
        return (
          <span
            className={`font-medium text-[12px] leading-none ${type === TransactionType.Buy ? 'text-[#00FFB4]' : type === TransactionType.Sell ? 'text-[#AB57FF]' : ''}`}
          >
            {(() => {
              switch (type) {
                case TransactionType.Buy:
                  return t('history.buy')
                case TransactionType.Sell:
                  return t('history.sell')
                // case 'add':
                //   return t('history.addLiquidity')
                // case 'remove':
                //   return t('history.removeLiquidity')
                default:
                  return type
              }
            })()}
          </span>
        )
      },
    },
    {
      accessorKey: 'quoteAmount',
      header: () => (
        <div className="flex items-center gap-0.5">
          <span>{t('transaction.amount')}</span>
          <img
            src="/images/icons/fund-icon.svg"
            className="w-4 h-4 cursor-pointer hover:scale-[1.1]"
            alt="arrow-swap-horizontal.svg"
            onClick={() => {
              setDataUnit(dataUnit === 'USD' ? activeChain.toUpperCase() : 'USD')
            }}
          />
        </div>
      ),
      cell: ({ row }) => {
        const quoteAmount = Number(row.original?.quoteAmount) // SOL or ETH amount
        if (dataUnit === 'USD') {
          return <MoneyFormatted value={quoteAmount * price[activeChain.toUpperCase()]} roundType="floor" />
        }
        return <MoneyFormatted value={quoteAmount} unit={dataUnit} roundType="floor" />
      },
    },
    {
      header: t('history.tradeVolume'),
      cell: ({ row }) => {
        const amount = Number(row.original?.baseAmount)
        return <MoneyFormatted value={amount} showUnit={false} roundType="floor" />
      },
    },
    {
      accessorKey: 'marketCap',
      header: () => (
        <div className="flex items-center gap-0.5">
          <span>{showMC ? t('transaction.price') : t('transaction.marketCap')}</span>
          <img
            src="/images/icons/arrow-swap-horizontal.svg"
            className="w-4 h-4 cursor-pointer hover:scale-[1.1]"
            alt="arrow-swap-horizontal.svg"
            onClick={() => {
              setShowMC(!showMC)
            }}
          />
        </div>
      ),
      cell: ({ row }) => {
        const marketCap = row.original?.marketCap
        const closePriceUsd = Number(row.original?.closePriceUsd)
        const type = row.original?.transactionType
        if (!showMC) {
          return <MoneyFormatted value={marketCap} />
        }
        return (
          <div
            className={`${type === TransactionType.Buy ? 'text-[#00FFB4]' : type === TransactionType.Sell ? 'text-[#AB57FF]' : ''}`}
          >
            <MoneyFormatted value={closePriceUsd} showUnit={true} roundType="ceil" />
          </div>
        )
      },
    },
    {
      accessorKey: 'date',
      header: () => <div className="text-right">{t('transaction.time')}</div>,
      cell: ({ row }) => {
        const createdAt = row.original?.createdAt
        return <div className="text-right">{dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss')}</div>
      },
    },
  ]

  const fetchTransactionHistory = async () => {
    if (!userAddress || !ServiceConfig.token) return
    const queryInput: SearchOrderInput = {
      userAddress: userAddress,
      limit: 20,
      offset: offset,
      sortDir: SortDirection.Desc,
      sortField: OrderSortField.CreatedAt,
    }
    if (chainFilter) {
      queryInput.chain = chainFilter as ChainType
    }
    const response = await tradingClient.query({
      query: getTransactionHistory,
      variables: {
        input: queryInput,
      },
    })
    if (offset > 0) {
      setTransactions((prev) => [...prev, ...response.data.getTransactions])
    } else {
      setTransactions(response.data.getTransactions)
    }
  }

  const onChainChange = (chain: any) => {
    setChainFilter(chain?.chainName)
  }

  useEffect(() => {
    if (offset === 0) {
      fetchTransactionHistory()
    }
    setOffset(0)
  }, [chainFilter])

  useEffect(() => {
    fetchTransactionHistory()
  }, [userAddress, offset, chainFilter])

  return (
    <div>
      <div className="flex items-center justify-between">
        <ChainSelect chain={chainFilter} onChange={onChainChange} />
        <div className="flex items-center justify-center w-6 h-6 bg-[#ECECED14] rounded-[4px] cursor-pointer">
          <img src="/images/icons/icon-filter.svg" className="w-[14px] h-[14px] hover:scale-[1.1]" alt="icon-filter" />
        </div>
      </div>
      <div className="mt-4">
        <DataTable
          columns={columns}
          data={transactions}
          isStickyHeader
          isStickyFirstColumn
          stickyBg="rgb(23,24,27)"
          containerClassName="max-h-[600px]"
          tableClassName=""
          tableHeaderClassName="text-white/50 bg-[#27272a]"
          tableHeaderRowClassName="border-b-[0.5px] border-[rgba(35,35,41,1)] text-[12px] text-white/50 sticky top-0 whitespace-nowrap"
          tableHeadClassName="app-font-light px-2 py-1 items-center justify-center"
          tableBodyClassName="max-h-[300px]"
          tableBodyRowClassName="group whitespace-nowrap"
          tableCellClassName="group-hover:!bg-[#27272a] text-[13px] leading-none text-[#EFEFEF] cursor-pointer"
          onRowClick={(rowData) => {
            console.log('Row clicked:', rowData)
          }}
          onBottomReached={() => {
            if (transactions.length < offset + 20) return
            setOffset((prev) => prev + 20)
          }}
          ref={ref}
        />
      </div>
    </div>
  )
}

export default TabOrderHistory
