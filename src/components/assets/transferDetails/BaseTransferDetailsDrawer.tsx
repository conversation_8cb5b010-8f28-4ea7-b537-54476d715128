import AppDrawer from '@components/common/AppDrawer.tsx'
import { Dispatch, SetStateAction } from 'react'
import { IconCheckCircleSolid, IconCopySolid, IconDangerCircleSolid } from '@components/icon'
import { FundingRecord } from '@components/assets/overview/FundingRecordItem.tsx'
import { Avatar, AvatarImage } from '@components/ui/avatar.tsx'
import { getBlockchainLogo2, getLinkExplorer } from '@/utils/helpers.ts'
import { ChainIds } from '@/types/enums.ts'
import { formatAddressWallet } from '@/lib/string.ts'
import dayjs from 'dayjs'
import CopyBtn from '@components/common/CopyBtn.tsx'
import { useTranslation } from 'react-i18next'
import { TFunction } from 'i18next'
import MoneyFormatted from '@components/common/MoneyFormatted.tsx'

export interface BaseTransferDetailsDrawerProps {
  open?: boolean
  setOpen: Dispatch<SetStateAction<boolean>>
  record: FundingRecord
}

const transactionTypeLabelKeys: Record<'Deposit' | 'Withdrawal' | 'AddVault' | 'RemoveVault' | 'Exchange', string> = {
  Deposit: 'assets.overview.deposit',
  Withdrawal: 'assets.overview.withdrawal',
  AddVault: 'assets.overview.depositVault',
  RemoveVault: 'assets.overview.redeemVault',
  Exchange: 'assets.overview.exchange',
}

const WalletTag = (props: { walletType: 'Futures' | 'Spot' | 'Funding' | 'Vault' | 'ArbitrumWallet' }) => {
  const { walletType } = props
  return (
    <div className="flex items-center text-[calc(12rem/16)] text-[#FFFFFFB2] bg-[#ECECED14] w-fit px-2 py-0.5 rounded-[4px]">
      {walletType === 'ArbitrumWallet' ? (
        <img src={getBlockchainLogo2(ChainIds.Arbitrum)} alt="" className="size-3" />
      ) : (
        <div className="size-1 bg-[#00FFB4] rounded-full" />
      )}
      <span className="text-[calc(13rem/16)] leading-[calc(13rem/16)] text-[#00FFB4] ml-1">
        {walletType === 'ArbitrumWallet' ? 'Arbitrum' : walletType}
      </span>
    </div>
  )
}

const BasicTransferDetails = (props: { record: FundingRecord }) => {
  const { record } = props
  const { t } = useTranslation()
  if (record.type === 'Exchange') return null
  const token = record.fromToken
  const tokenIcon = token.icon

  return (
    <div>
      <div className="flex flex-col items-center mb-4">
        <Avatar className="size-16 mb-2">
          <AvatarImage src={tokenIcon} alt="" className="size-16" />
        </Avatar>
        <div className="">
          <span className="text-[calc(24rem/16)] font-bold">
            {record.amount > 0 ? '+' : '-'}
            <MoneyFormatted value={Math.abs(record.amount)} unit={token.symbol} />
          </span>{' '}
          <span className="text-[calc(18rem/16)] font-medium">{token.symbol}</span>
        </div>
        <div className="text-[calc(16rem/16)] text-[#FFFFFFB2]">≈${Math.abs(record.amountInUSD).toFixed(2)}</div>
      </div>

      <div className="border border-[#ECECED14] bg-[#1414145C] py-2 px-4 rounded-[8px]">
        <div className="mb-3">
          <div className="text-[calc(13rem/16)] text-[#FFFFFFB2]">
            {t('assets.overview.fundingHistory.senderAddress')}
          </div>
          <div className="flex mb-1">
            <div className="text-[calc(13rem/16)] wrap-anywhere leading-4">{record.senderAddress}</div>
            <CopyBtn className="ml-1" text={record.senderAddress} icon={<IconCopySolid />} />
          </div>
          {record.senderAddressTypes.map((type) => (
            <WalletTag key={type} walletType={type} />
          ))}
        </div>

        <div className="mb-3">
          <div className="text-[calc(13rem/16)] text-[#FFFFFFB2]">
            {t('assets.overview.fundingHistory.receiverAddress')}
          </div>
          <div className="flex mb-1">
            <div className="text-[calc(13rem/16)] wrap-anywhere leading-4">{record.receiverAddress}</div>
            <CopyBtn className="ml-1" text={record.receiverAddress} icon={<IconCopySolid />} />
          </div>
          {record.receiverAddressTypes.map((type) => (
            <WalletTag key={type} walletType={type} />
          ))}
        </div>

        <BasicInfo record={record} />
      </div>
    </div>
  )
}

const ExchangeDetails = (props: { record: FundingRecord }) => {
  const { record } = props
  if (record.type !== 'Exchange') return null
  return (
    <div>
      <div className="bg-[#141414CC] py-3.5 px-4 rounded-[12px] mb-4">
        <div className="text-[calc(14rem/16)] text-[#FFFFFFB2] mb-2">从 合约账户</div>

        <div className="flex items-center mb-2">
          <Avatar className="relative overflow-visible">
            <AvatarImage src={record.fromToken.icon} alt="" className="size-10 rounded-full" />
            {record.fromToken.chain && (
              <AvatarImage
                src={record.fromToken.chain}
                alt=""
                className="size-4 absolute bottom-0 -right-0.5 border-2 rounded-full border-[#261236]"
              />
            )}
          </Avatar>
          <div className="ml-3">
            <div>
              <span className="text-[calc(20rem/16)]">
                {record.fromAmount > 0 ? '+' : ''}
                {record.fromAmount} {record.fromToken.symbol}
              </span>
            </div>
            <div className="flex items-center gap-1 text-[calc(12rem/16)] text-[#FFFFFF80]">
              {formatAddressWallet(record.fromAccountAddress, 10)}
              <CopyBtn text={record.fromAccountAddress} icon={<IconCopySolid />} />
            </div>
          </div>
        </div>
      </div>

      <div className="bg-[#141414CC] py-3.5 px-4 rounded-[12px] mb-4">
        <div className="text-[calc(14rem/16)] text-[#FFFFFFB2] mb-2">到 现货账户</div>
        <div className="flex items-center mb-2">
          <Avatar className="relative overflow-visible">
            <AvatarImage src={record.toToken!.icon} alt="" className="size-10 rounded-full" />
            {record.toToken!.chain && (
              <AvatarImage
                src={record.toToken!.chain}
                alt=""
                className="size-4 absolute bottom-0 -right-0.5 border-2 rounded-full border-[#261236]"
              />
            )}
          </Avatar>
          <div className="ml-3">
            <div>
              <span className="text-[calc(20rem/16)]">
                {record.toAmount > 0 ? '+' : ''}
                {record.toAmount} {record.toToken!.symbol}
              </span>
            </div>
            <div className="flex items-center gap-1 text-[calc(12rem/16)] text-[#FFFFFF80]">
              {formatAddressWallet(record.toAccountAddress, 10)}
              <CopyBtn text={record.toAccountAddress} icon={<IconCopySolid />} />
            </div>
          </div>
        </div>
      </div>

      <BasicInfo record={record} />
    </div>
  )
}

const BasicInfo = (props: { record: FundingRecord }) => {
  const { record } = props
  const { t } = useTranslation()
  return (
    <div className="w-full">
      <div className="flex items-center justify-between py-1">
        <div className="text-[calc(13rem/16)] text-[#FFFFFFB2]">{t('assets.overview.fundingHistory.date')}</div>
        <div className="text-white text-[calc(14rem/16)]">{dayjs(record.timestamp).format('YYYY-MM-DD hh:mm')}</div>
      </div>
      <div className="flex items-center justify-between py-1">
        <div className="text-[calc(13rem/16)] text-[#FFFFFFB2]">{t('assets.overview.fundingHistory.type')}</div>
        <div className="text-white text-[calc(14rem/16)]">{t(transactionTypeLabelKeys[record.type])}</div>
      </div>
      {/* <div className="flex items-center justify-between py-1">
        <div className="text-[calc(13rem/16)] text-[#FFFFFFB2]">{t('assets.overview.fundingHistory.fee')}</div>
        <div className="text-white text-[calc(14rem/16)]">{record.fee}</div>
      </div> */}
      <div className="flex items-center justify-between py-1">
        <div className="text-[calc(13rem/16)] text-[#FFFFFFB2]">
          {t('assets.overview.fundingHistory.transactionHash')}
        </div>
        <div className="text-white text-[calc(14rem/16)] flex items-center">
          <a
            href={getLinkExplorer(record.chainId, record.transactionHash)}
            target="_blank"
            rel="noopener noreferrer"
            className="underline"
          >
            {formatAddressWallet(record.transactionHash)}
          </a>
          <CopyBtn className="ml-2" text={record.transactionHash} icon={<IconCopySolid />} />
        </div>
      </div>
    </div>
  )
}

const getDrawerTitleSuccess = (t: TFunction, record: FundingRecord) => {
  const { type } = record
  switch (type) {
    case 'Deposit':
      return t('assets.overview.fundingHistory.titleDepositSuccess')
    case 'Withdrawal':
      return t('assets.overview.fundingHistory.titleWithdrawalSuccess')
    case 'AddVault':
      return t('assets.overview.fundingHistory.titleAddVaultSuccess')
    case 'RemoveVault':
      return t('assets.overview.fundingHistory.titleRedemptionSuccess')
    case 'Exchange':
      return t('assets.overview.fundingHistory.titleExchangeSuccess')
    default:
      return t('assets.overview.fundingHistory.titleDepositSuccess')
  }
}

const getDrawerTitleFail = (t: TFunction, record: FundingRecord) => {
  const { type } = record
  switch (type) {
    case 'Deposit':
      return t('assets.overview.fundingHistory.titleDepositFailed')
    case 'Withdrawal':
      return t('assets.overview.fundingHistory.titleWithdrawalFailed')
    case 'AddVault':
      return t('assets.overview.fundingHistory.titleAddVaultFailed')
    case 'RemoveVault':
      return t('assets.overview.fundingHistory.titleRedemptionFailed')
    case 'Exchange':
      return t('assets.overview.fundingHistory.titleExchangeFailed')
    default:
      return t('assets.overview.fundingHistory.titleDepositFailed')
  }
}

const TransferDetails = (props: { record: FundingRecord }) => {
  const { record } = props
  if (record.type === 'Exchange') {
    return <ExchangeDetails record={record} />
  }
  return <BasicTransferDetails record={record} />
}

export const BaseTransferDetailsDrawer = (props: BaseTransferDetailsDrawerProps) => {
  const { open, setOpen, record } = props

  const { t } = useTranslation()

  return (
    <AppDrawer
      open={open}
      setOpen={setOpen}
      drawerClassName="bg-[url('/images/popup-bg.png')] bg-cover bg-no-repeat bg-center"
      leftIcon={<div className="size-6" />}
      title={
        <div className="flex items-center text-[calc(16rem/16)] ml-">
          {record.status === 'success' ? (
            <>
              <IconCheckCircleSolid />
              <div className="text-[#00FFB4] ml-1">{getDrawerTitleSuccess(t, record)}</div>
            </>
          ) : (
            <>
              <IconDangerCircleSolid />
              <div className="text-[#FF353C] ml-1">{getDrawerTitleFail(t, record)}</div>
            </>
          )}
        </div>
      }
      drawerContent={
        <div className="py-6">
          <TransferDetails record={record} />
        </div>
      }
    />
  )
}
