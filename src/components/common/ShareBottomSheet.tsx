import React, { useRef } from 'react'
import { Drawer, Drawer<PERSON>ontent, DrawerHeader } from '@components/ui/drawer.tsx'
import { DialogTitle } from '@radix-ui/react-dialog'
import { toJpeg } from 'html-to-image'
import ListAppShare from '@components/common/share/listAppShare.tsx'
import { useTranslation } from 'react-i18next'

type Props = {
  open: boolean
  setOpen: (open: boolean) => void
  title: string
  children?: React.ReactNode
}

const ShareBottomSheet = ({ open, setOpen, title, children }: Props) => {
  const { t } = useTranslation()
  const posterRef = useRef<HTMLDivElement>(null)

  const onSave = async () => {
    if (!posterRef.current) return

    try {
      const dataUrl = await toJpeg(posterRef.current, {
        quality: 1,
        pixelRatio: 2,
      })

      const link = document.createElement('a')
      link.download = 'share.png'
      link.href = dataUrl
      link.click()
    } catch (error) {
      console.error('Error saving image:', error)
    }
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className="w-full max-w-[768px] mx-auto max-h-[80vh] bg-[#232329]">
        <DrawerHeader className="px-3 flex w-full items-center justify-between">
          <DialogTitle>
            <div className="text-[18px] font-[500] text-white">{title}</div>
          </DialogTitle>
          <img
            src="/images/icons/icon-x.svg"
            className="w-6 h-6 cursor-pointer"
            onClick={() => setOpen(false)}
            alt="close"
          />
        </DrawerHeader>
        <div className="overflow-y-auto mb-3 px-3">
          <div className="flex items-center justify-center px-3" ref={posterRef} id="posterRef">
            <div className=" bg-[#232329] border-gradient border-[0.5px] flex items-center flex-col justify-center">
              <div className="flex-1 flex items-center justify-center p-5">{children}</div>
              <div className="w-full flex items-center gap-3.5 bg-[linear-gradient(90deg,_rgba(153,_69,_255,_0.1)_0%,_rgba(255,_255,_255,_0.08)_50%,_rgba(0,_243,_193,_0.1)_100%)] px-5 py-3.5">
                <img className="w-15 h-15" src="/images/logo-xbit-rounded.svg" alt="logo xbit" />
                <div>
                  <img src="/images/logo-xbit-text.svg" alt="logo xbit text" />
                  <span className="app-font-regular text-[calc(1rem*(12/16))] text-[#FFFFFFB2] leading-3 tracking-[1.63px]">
                    {t('detail.myPositions.decentralizedExchange')}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <ListAppShare onSave={() => onSave()} />
      </DrawerContent>
    </Drawer>
  )
}

export default ShareBottomSheet
