import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { SelectContentProps, SelectProps, SelectTriggerProps, SelectValueProps } from '@radix-ui/react-select'
import { cn } from '@/lib/utils.ts'
import React, { useState } from 'react'

export type FilterSelectOption = {
  value: string
  label: string | React.ReactNode
  icon?: string
}

type FilterSelectProps = {
  options: FilterSelectOption[]
  selectProps?: SelectProps
  selectTriggerProps?: SelectTriggerProps
  selectValueProps?: SelectValueProps
  selectContentProps?: SelectContentProps
  triggerIconClassname?: string
  triggerIcon?: string
  defaultValue?: string
  childrenTrigger?: React.ReactNode
  value?: string
  onValueChange?: (value: string) => void
}

const FilterSelect = ({
  options,
  defaultValue = options[0].value,
  selectProps,
  selectTriggerProps,
  selectValueProps,
  selectContentProps,
  triggerIconClassname,
  triggerIcon,
  childrenTrigger,
  onValueChange,
  value,
}: FilterSelectProps) => {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <Select
      open={isOpen}
      onOpenChange={setIsOpen}
      {...selectProps}
      value={value}
      defaultValue={defaultValue}
      onValueChange={onValueChange}
    >
      <SelectTrigger
        {...selectTriggerProps}
        className={cn(
          'rounded-[6px] max-w-[120px] px-[7px] py-[5px]  h-auto text-[calc(1rem*(13/16))] font-[400] text-[#FFFFFFCC] leading-[1] [&_.lucide]:hidden',
          selectTriggerProps?.className,
        )}
      >
        {childrenTrigger}
        <SelectValue {...selectValueProps} className={cn('flex align-middle', selectValueProps?.className)} />
        <img
          src={triggerIcon ?? '/images/icons/icon-chevron-down.svg'}
          className={cn('w-[6.67px] h-[4.67px]', triggerIconClassname, isOpen && 'rotate-180')}
          alt=""
        />
      </SelectTrigger>
      <SelectContent {...selectContentProps} className={cn('', selectContentProps?.className)}>
        {options.map((option) => (
          <SelectItem value={option.value} key={option.value}>
            <div className="flex items-center align-middle leading-[1]">
              {option.icon && <img src={option.icon} className="w-[14px] h-[14px] mr-[4px] mt-[1px]" alt="" />}
              {option.label}
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}

export default FilterSelect
