import { useEffect, useState } from 'react'
import { Button, ButtonProps } from '@/components/ui/button'
import { cn } from '@/lib/utils'

type FilterWalletButtonProps = ButtonProps & {
  isActive?: boolean
  classNameActive?: string
}

type FilterWalletProps = {
  options: string[]
  defaultSelectedIndex?: number
  onChange?: (index: number, option?: string) => any
  classNameActive?: string
}

const FilterWalletButton = ({ className, children, isActive, classNameActive, ...rest }: FilterWalletButtonProps) => {
  return (
    <Button
      className={cn(
        'rounded-[0] bg-[none] min-w-[52px] h-[auto] px-[10px] py-[5px] text-[#FFFFFF99] text-[calc(1rem*(11/16))] leading-[calc(1rem*(11/16))] font-[400]',
        isActive && 'bg-[#ECECED14] text-[#FFFFFF] rounded',
        isActive && classNameActive,
        className,
      )}
      {...rest}
    >
      {children}
    </Button>
  )
}

const FilterWallet = ({ options, onChange, defaultSelectedIndex = 0, classNameActive = '' }: FilterWalletProps) => {
  const [selectedIndex, setSelectedIndex] = useState<number>(defaultSelectedIndex)

  const handleFilterClick = (index: number, option?: string) => {
    setSelectedIndex(index)
    if (onChange) {
      onChange(index, option)
    }
  }

  useEffect(() => {
    setSelectedIndex(defaultSelectedIndex)
  }, [defaultSelectedIndex])

  return (
    <div className="overflow-x-auto _hidescrollbar">
      <div className="rounded-[4px] flex whitespace-nowrap w-max bg-[#ECECED0A]">
        {options.map((option, index) => (
          <FilterWalletButton
            key={index}
            isActive={selectedIndex === index}
            onClick={() => handleFilterClick(index, option)}
            classNameActive={classNameActive}
          >
            {option}
          </FilterWalletButton>
        ))}
      </div>
    </div>
  )
}

export default FilterWallet
