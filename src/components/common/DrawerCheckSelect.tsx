import { Drawer, DrawerContent, DrawerTrigger } from '@/components/ui/drawer'
import { cn } from '@/lib/utils.ts'
import { UITab } from '@/types/uiTabs.ts'
import { ReactNode, useState } from 'react'
import Text from './Text'

type DrawerCheckSelectProps = {
  options: UITab[]
  childrenTrigger: string | ReactNode
  value: string
  title?: string
  titleClassName?: string
  optionClassName?: string
  drawerContentClassName?: string
  onChange?: (value: string) => void
  isClose?: boolean
  headerClassName?: string
}

const DrawerCheckSelect = ({
  childrenTrigger,
  options,
  value,
  optionClassName,
  drawerContentClassName,
  onChange,
  title,
  titleClassName,
  isClose,
  headerClassName,
}: DrawerCheckSelectProps) => {
  const [open, setOpen] = useState(false)

  const handleClickChange = (value: string) => {
    if (onChange) {
      onChange(value)
    }
    setTimeout(() => {
      setOpen(false)
    }, 500)
  }

  return (
    <>
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerTrigger asChild>{childrenTrigger}</DrawerTrigger>
        <DrawerContent
          className={cn(
            'min-h-[258px] w-full bg-[url("/images/futuresDetail/gradient-bg.png")] bg-no-repeat bg-cover bg-center max-w-[768px] mx-auto',
            drawerContentClassName,
          )}
        >
          <div className={cn('flex justify-between items-center', headerClassName)}>
            {title && (
              <Text className={cn('px-3 mt-6', titleClassName)} text={title} fontSize={15} fontWeight="medium" />
            )}
            {isClose && (
              <img
                src="/images/icons/icon-x.svg"
                className="w-6 h-6 cursor-pointer"
                onClick={() => setOpen(false)}
                alt=""
              />
            )}
          </div>
          <div className="px-3 pb-8 mt-2">
            {options.map((item) => {
              return (
                <div
                  className={cn('flex justify-between items-center py-[18px] cursor-pointer border-b-[0.5px] border-b-[#ECECED14]', optionClassName)}
                  key={item.value}
                  onClick={() => {
                    handleClickChange(item.value)
                  }}
                >
                  <div className="flex-1 max-w-[calc(1rem*(305/16))]">
                    <p className="text-[calc(1rem*(16/16))] leading-[calc(1rem*(16/16))]"> {item.label}</p>
                    {item?.desc && (
                      <p className="text-[#FFFFFFCC] text-[calc(1rem*(14/16))] leading-[calc(1rem*(21/16))] mt-2">
                        {item?.desc}
                      </p>
                    )}
                  </div>

                  <img
                    className={cn(
                      'transition-opacity duration-300',
                      value !== item.value ? 'opacity-0 pointer-events-none' : 'opacity-100',
                    )}
                    src="/images/futuresDetail/selected-icon.svg"
                  />
                </div>
              )
            })}
          </div>
        </DrawerContent>
      </Drawer>
    </>
  )
}

export default DrawerCheckSelect
