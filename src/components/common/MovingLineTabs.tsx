import { useState, useEffect, useRef, HTMLAttributes } from 'react'
import { cn } from '@/lib/utils.ts'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { UITab } from '@/types/uiTabs.ts'

type MovingLineTabsProps = {
  tabs: UITab[]
  defaultTab: string
  disabledTabs?: string[]
  onTabChange?: (tab: string) => any
  containerClassName?: HTMLAttributes<HTMLDivElement>['className']
  tabsListClassName?: string
  tabsClassName?: string
  itemClassName?: string
  widthMoveLine?: number
  wrapperClassName?: HTMLAttributes<HTMLDivElement>['className']
  forceUpdate?: boolean
}

export default function MovingLineTabs({
  tabs,
  defaultTab,
  disabledTabs,
  onTabChange,
  containerClassName,
  tabsClassName,
  tabsListClassName,
  itemClassName,
  widthMoveLine,
  wrapperClassName,
  forceUpdate,
}: MovingLineTabsProps) {
  const [activeTab, setActiveTab] = useState<string>(defaultTab)
  const [lineStyle, setLineStyle] = useState<{ left: number; width: number }>({ left: 0, width: 0 })
  const tabsRef = useRef<Array<HTMLButtonElement | null>>([])
  const containerRef = useRef<HTMLDivElement | null>(null)

  const handleActiveTab = (tab: string) => {
    setActiveTab(tab)
  }

  const handleTabChange = (tab: string) => {
    handleActiveTab(tab)
    if (onTabChange) {
      onTabChange(tab)
    }
  }

  useEffect(() => {
    const activeIndex = tabs.findIndex((tab) => tab.value === activeTab)
    const activeTabElement = tabsRef.current[activeIndex]

    if (activeTabElement) {
      const { offsetLeft, offsetWidth } = activeTabElement
      /**
       * change design move tabs line fix with 38px
       */
      setLineStyle({ left: offsetLeft, width: offsetWidth })
    }
  }, [activeTab, tabs, forceUpdate])

  useEffect(() => {
    setActiveTab(defaultTab)
    const activeIndex = tabs.findIndex((tab) => tab.value === defaultTab)
    const activeTabElement = tabsRef.current[activeIndex]
    const container = containerRef.current as HTMLElement
    if (container && activeTabElement) {
      const containerRect = container.getBoundingClientRect()
      const targetRect = activeTabElement.getBoundingClientRect()
      const centerPointInScreen = (containerRect.left + containerRect.width) / 2
      container.scrollTo({
        left: activeTabElement.offsetLeft - centerPointInScreen + targetRect.width / 2,
        behavior: 'smooth',
      })
    }
  }, [defaultTab, tabs])

  return (
    <div className={cn('relative', wrapperClassName)}>
      <div
        ref={containerRef}
        className={cn(
          `flex items-center justify-center max-w-[100%] rounded-bl-none rounded-br-none rounded-tl-[8px] rounded-tr-[8px] 
      overflow-x-auto overflow-y-hidden _hidescrollbar
      after:content-[''] 
      after:absolute after:left-0 after:right-0 after:bottom-0 after:h-[0.5px] 
      after:bg-gradient-to-r after:from-[#3C2E77] after:to-[#006952] after:z-[-1] after:opacity-[0.5]
      `,
          containerClassName,
        )}
      >
        <Tabs
          className={cn('max-w-[100%] relative', tabsClassName)}
          onValueChange={handleTabChange}
          value={activeTab}
          defaultValue={defaultTab}
        >
          <TabsList className={cn(`relative bg-[none] shadow-none `, tabsListClassName)}>
            {tabs.map((tab, index) => (
              <TabsTrigger
                value={tab.value}
                key={tab.value}
                ref={(el) => {
                  tabsRef.current[index] = el
                }}
                className={cn(
                  ' text-(--text-tertiary) !bg-transparent transition-all select-none',
                  activeTab === tab.value ? 'text-(--text-primary) font-bold' : '',
                  itemClassName,
                )}
                disabled={disabledTabs?.includes(tab.value)}
              >
                {tab.label}
              </TabsTrigger>
            ))}
            <div
              style={{
                left: `${lineStyle.left}px`,
                width: `${lineStyle.width}px`,
              }}
              className={cn(
                `absolute
              transition-[.3s]
              bottom-0
              before:content-['']
              before:block
              before:h-[1.5px]
              before:rounded-[100%]
              before:bg-linear-to-r
              before:from-[#9945FF]
              before:to-[#00F3AB]
              after:w-[26px]
              after:aspect-[16/6]
              after:absolute
              after:left-[50%]
              after:bottom-[-1px]
              after:translate-x-[-50%]
              after:bg-[url(/images/moving-line-tab-blur.webp)]
              after:bg-contain`,
                widthMoveLine
                  ? `before:w-[${widthMoveLine}px] before:left-[50%] before:translate-[-50%] before:relative`
                  : ``,
              )}
            />
          </TabsList>
        </Tabs>
      </div>
    </div>
  )
}
