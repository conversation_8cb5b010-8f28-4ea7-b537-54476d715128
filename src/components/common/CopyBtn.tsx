// import useCustomTranslation from '@/hooks/useCustomTranslation.ts';
import { useCallback, useState } from 'react';
import { FaCheck } from 'react-icons/fa';
import { LuCheckCheck } from 'react-icons/lu';

type CopyBtnProps = React.ComponentProps<'span'> & {
  text: string,
  colorClassName?: string,
  className?: string,
  icon?: React.ReactNode,
}

export default function CopyBtn({text, colorClassName, className, icon, ...rest}: CopyBtnProps) {
  // const { t } = useCustomTranslation();
  const [checked, setChecked] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const copy = useCallback((e: React.MouseEvent<HTMLSpanElement>) => {
    // if (Notification.permission === 'default') {
    //   Notification.requestPermission().then((permission) => {
    //     alert(permission)
    //   })
    // }
    e.stopPropagation()
    navigator.clipboard
      .writeText(text)
      .then(() => {
        setChecked(true);
        setError(null);
        setTimeout(() => {
          setChecked(false);
        }, 1000);
      })
      .catch((err) => {
        console.warn(err);
        setError('Failed to copy text');
      });
  }, [text]);

  return (
    <span
      {...rest}
      onClick={copy}
      className={`cursor-pointer ${checked ? 'text-lime-500' : colorClassName ?? 'text-slate-50'} ${className ?? ''}`}
    >
      {checked ?
        <LuCheckCheck size={16} className="min-w-4" /> :
          icon ?
            icon :
            <img src="/images/icons/icon-copy-stroke.svg" className="w-4 min-w-4" alt="" />
      }
      {error && <span className="text-red-500 ml-2">{error}</span>}
      {checked && (
        <div className="flex fixed text-[12px] text-white top-[50%] left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-[#333] p-1 items-center gap-1 rounded-md z-10">
          <FaCheck />
          <p>Copy success!</p>
        </div>
      )}
    </span>
  );
}
