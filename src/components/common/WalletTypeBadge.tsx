import { cn } from '@/lib/utils'
import { useTranslation } from 'react-i18next';

export type WalletType = 'smartmoney' | 'new' | 'kol' | 'sniper' | 'pumpsm' | 'whale' | string

const walletTypeMap: Record<WalletType, { label: string; bgColor: string; textColor: string }> = {
  toptrader: {
    label: 'walletCopy.tags.toptrader',
    bgColor: '#00FFB4',
    textColor: '#00FFB4',
  },
  smartmoney: {
    label: 'walletCopy.tags.smartmoney',
    bgColor: '#00FFB4',
    textColor: '#00FFB4',
  },
  fresh: {
    label: 'walletCopy.tags.fresh',
    bgColor: '#20D3EE',
    textColor: '#20D3EE',
  },
  kol: {
    label: 'walletCopy.tags.kol',
    bgColor: '#E7B008',
    textColor: '#E7B008',
  },
  sniper: {
    label: 'walletCopy.tags.sniper',
    bgColor: '#F471B5',
    textColor: '#F471B5',
  },
  pumpsm: {
    label: 'walletCopy.tags.pumpsm',
    bgColor: 'transparent',
    textColor: 'trasparent',
  },
  whale: {
    label: 'walletCopy.tags.whale',
    bgColor: '#F471B5',
    textColor: '#F471B5',
  },
}

type WalletTypeBadgeProps = {
  type: WalletType[] | string;
  className?: string
}

const WalletTypeBadge = ({ type, className }: WalletTypeBadgeProps) => {
  const { t } = useTranslation()
  if (typeof type === 'string') {
    type = [String(type).toLowerCase()]
  }
  return (
    <div className="flex gap-0.5">
      {type?.map((item, index) => {
        if (!walletTypeMap[String(item).toLowerCase()]) {
          return null
        }
        const { label, bgColor, textColor } = walletTypeMap[String(item).toLowerCase()]
        return (
          <span
            key={`wallet-type-${index}`}
            className={cn(
              'text-[calc(1rem*(8/16))] leading-[calc(1rem*(8/16))] border-[0.5px] rounded-[2px] p-[2px] w-[14px] aspect-square block text-center',
              className,
            )}
            style={{
              borderColor: bgColor,
              color: textColor,
            }}
          >
            {
              label === 'walletCopy.tags.pumpsm' ?
                <img src="/images/icons/pump-icon.svg" className="w-[14px] h-[14px]" alt="" />
                : String(t(label)).slice(0, 1).toUpperCase()
            }
          </span>
        )
      })}
    </div>
  )
}

export default WalletTypeBadge
