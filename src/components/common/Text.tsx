import { cn } from '@/lib/utils'

interface TextProps {
  text: string
  className?: string
  color?: string
  fontSize?: 7 | 8 | 9 | 10 | 11 | 12 | 13 | 14 | 15 | 16 | 18 | 19 | 20 | 21 | 22 | 23 | 24 | 32
  fontWeight?: 'light' | 'regular' | 'medium' | 'semibold'
  highLightText?: string
  highLightColor?: string
  onClick?: () => void
}

const Text = ({
  text,
  className,
  color = '#FFFFFF',
  fontSize = 14,
  fontWeight = 'regular',
  onClick,
  highLightText,
  highLightColor = '#00FFB4',
}: TextProps) => {
  const renderHighlightedText = () => {
    if (!highLightText || !text) return text

    const regex = new RegExp(`(${highLightText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  
    const parts = text.split(regex)
    
    return parts.map((part, index) => {
      const isHighlighted = part.toLowerCase() === highLightText.toLowerCase()
      
      return (
        <span 
          key={index}
          className={isHighlighted ? `text-[${highLightColor}]` : ''}
          style={isHighlighted ? { color: highLightColor } : {}}
        >
          {part}
        </span>
      )
    })
  }

  return (
    <div
      className={cn(`app-font-${fontWeight} text-[calc(1rem*(${fontSize}/16))] text-[${color}]`, className)}
      style={{ color: color }}
      onClick={onClick}
    >
      {highLightText ? renderHighlightedText() : text}
    </div>
  )
}

export default Text