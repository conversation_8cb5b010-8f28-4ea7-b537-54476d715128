import { cn } from '@/lib/utils'
interface TextProps {
  text: string
  className?: string
  color?: string
  fontSize?: 7 | 8 | 9 | 10 | 11 | 12 | 13 | 14 | 15 | 16 | 18 | 19 | 20 | 21 | 22 | 23 | 24 | 32
  fontWeight?: 'light' | 'regular' | 'medium' | 'semibold'
  highLightText?: string
  highLightColor?: string
  onClick?: () => void
}

const Text = ({
  text,
  className,
  color = '#FFFFFF',
  fontSize = 14,
  fontWeight = 'regular',
  onClick,
  highLightText,
  highLightColor = '#00FFB4',
}: TextProps) => {
  return (
    <div
      className={cn(`app-font-${fontWeight} text-[calc(1rem*(${fontSize}/16))] text-[${color}]`, className)}
      style={{ color: color }}
      onClick={onClick}
    >
      {highLightText
        ? text.split(highLightText).map((item, index) => {
            return (
              <span key={index}>
                {item}
                {index < text.split(highLightText).length - 1 && (
                  <span className={`text-[${highLightColor}]`}>{highLightText}</span>
                )}
              </span>
            )
          })
        : text}
    </div>
  )
}

export default Text
