import { Button, ButtonProps } from '@components/ui/button.tsx'
import { cn } from '@/lib/utils.ts'
// import TooltipTag from '@components/common/TooltipTag.tsx'
import { Ref } from 'react'

export type ButtonGreen = ButtonProps & {
  isLoggedIn?: boolean
  toolTip?: string
  tooltipVariant?: 'normal' | 'gradient'
  ref?: Ref<HTMLButtonElement>
}

const ButtonGreen = ({ isLoggedIn, toolTip, tooltipVariant, className, children, ...rest }: ButtonGreen) => {
  return (
    <Button className={cn('bg-gradient-to-r from-[#00E9A4] to-[#01AC79] text-white hover:bg-[#2bb17f] shadow-sm', className)} {...rest}>
      {children}
    </Button>
  )
}

export default ButtonGreen
