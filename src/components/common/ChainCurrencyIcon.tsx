import { IMAGES_CONSTANTS } from '@/lib/constant'
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar'
import { cn } from '@/lib/utils.ts'

type ChainCurrencyIconProps = {
  chainIcon?: string
  currencyIcon: string
  avatarClassName?: string
  name?: string
  fallbackClassName?: string
  className?: string
  avatarImageClassName?: string
  fallbackImageEnable?: boolean
  fallbackImage?: string
}

export default function ChainCurrencyIcon({
  chainIcon,
  currencyIcon,
  avatarClassName,
  name,
  fallbackClassName,
  className,
  avatarImageClassName,
  fallbackImageEnable = false,
  fallbackImage = IMAGES_CONSTANTS.DEFAULT.FALLBACK,
}: ChainCurrencyIconProps) {
  return (
    <div className={cn('relative size-[calc(1rem*(30/16))]', className)}>
      <Avatar className={cn('size-[calc(1rem*(30/16))] border-[0.5px] border-[#d3d3d345]', avatarClassName)}>
        <AvatarImage className={cn('size-[calc(1rem*(30/16))]', avatarImageClassName)} src={currencyIcon} alt="" />
        {fallbackImageEnable ? (
          <AvatarImage className={cn('size-[calc(1rem*(30/16))]', avatarImageClassName)} src={fallbackImage} alt="" />
        ) : (
          <AvatarFallback
            className={cn(
              'size-[calc(1rem*(30/16))] text-[calc(14rem/16)] rounded-full object-cover bg-[#111111] flex items-center justify-center capitalize select-none',
              fallbackClassName,
            )}
          >
            {name?.slice(0, 2).toLowerCase()}
          </AvatarFallback>
        )}
      </Avatar>
      {!!chainIcon && (
        <img className="size-[calc(1rem*(12/16))] rounded-full absolute right-0 bottom-0" src={chainIcon} alt="" />
      )}
    </div>
  )
}
