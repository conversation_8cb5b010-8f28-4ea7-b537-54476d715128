import React, { ReactNode, useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { TradeDetailItem } from '@components/listCoin/TabMainstream.tsx'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogOverlay,
} from '@/components/ui/dialog'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { useMutation } from '@apollo/client'
import { addTokenToFavorite, removeTokenFromFavorite } from '@services/tokens.service.ts'
import { toast } from 'sonner'
import { cn, getPath } from '@/lib/utils.ts'
import { f } from 'fintech-number'
import isNumber from 'lodash/isNumber'
import { APP_PATH, CHAIN_SYMBOLS } from '@/lib/constant'
import { ServiceConfig } from '@/lib/gql/service-config.ts'
import useSignWallet from '@hooks/useSignWallet.ts'
import { ChainIds } from '@/types/enums.ts'
import { followWallet, unFollowWallet } from '@/services/wallet.service'
import { gqlClient } from '@/lib/gql/apollo-client'
type CurrencyListCardProps = {
  tradeDetails: TradeDetailItem[]
  className?: string
  cols?: number
}

const ConfirmCollectModal = (props: {
  defaultCollect: boolean
  token: string
  showDialog?: boolean
  onRemove?: () => void
  onRemoveSuccess?: () => void
  onRemoveFailed?: () => void
  onAdded?: () => void
  tokenSymbol: string
  triggerClassName?: string
  isFlow?: boolean
}) => {
  const {
    token,
    defaultCollect = false,
    onRemove,
    onRemoveSuccess,
    onRemoveFailed,
    showDialog = false,
    onAdded,
    tokenSymbol,
    triggerClassName,
    isFlow = false,
  } = props
  const isLogin = ServiceConfig.isLogin;
  const [open, setOpen] = useState(false)
  const [isCollect, setCollect] = useState<boolean>(defaultCollect)
  const { t } = useTranslation()
  const [addToFavoritesMutation] = useMutation(isFlow ? followWallet : addTokenToFavorite, isFlow ? {
    client: gqlClient
  } : {})
  const [removeFromFavoritesMutation] = useMutation(isFlow ? unFollowWallet : removeTokenFromFavorite, isFlow ? {
    client: gqlClient
  } : {})
  const { handleSignMessage } = useSignWallet({ isAutoConnect: false })

  const addToFavorites = () => {
    setCollect(true)
    addToFavoritesMutation({
      variables: isFlow ? {
        input: {
          chain: "SOLANA",
          walletAddress: token,
        }
      } : { token: token }
    })
      .then(() => {
        toast.success(
          isFlow ? t('walletDetail.msg.flow') :
            t('toast.addFavoriteSuccess')
          , { position: 'bottom-center' })
        onAdded?.()
      })
      .catch(() => {
        setCollect(false)
        toast.warning(t('toast.addFavoriteFailed'), { position: 'bottom-center' })
      })
  }

  const removeFromFavorites = () => {
    setCollect(false)
    setOpen(false)
    onRemove?.()
    removeFromFavoritesMutation({
      variables: isFlow ? {
        input: {
          chain: "SOLANA",
          walletAddress: token,
        }
      } : { token: token }
    })
      .then(() => {
        toast.success(
          isFlow ? t('walletDetail.msg.unflow') :
            t('toast.removeFavoriteSuccess')
          , { position: 'bottom-center' })
        onRemoveSuccess?.()
      })
      .catch(() => {
        toast.warning(t('toast.removeFavoriteFailed'), { position: 'bottom-center' })
        setCollect(true)
        onRemoveFailed?.()
      })
  }

  const performAddOrRemove = () => {
    if (!isCollect) {
      // add to favorites
      addToFavorites()
    } else if (!showDialog) {
      // not show dialog
      removeFromFavorites()
    } else {
      // show dialog
      setOpen(true)
    }
  }

  const handleCollectChange = (event: React.MouseEvent) => {
    event.stopPropagation()

    if (!ServiceConfig.token) {
      handleSignMessage().then(() => {
        setTimeout(() => {
          if (ServiceConfig.token) {
            // sign successfully, perform add/remove action
            performAddOrRemove()
          } else {
            toast.warning(
              isFlow ? t('walletCopy.warning.login') :
                t('listCoin.requireLoginToFavorite')
              , { position: 'bottom-center' })
          }
        }, 500)
      })
      return
    }
    performAddOrRemove()
  }

  useEffect(() => {
    if (!!ServiceConfig.token) {
      setCollect(defaultCollect)
    } else {
      setCollect(false)
    }
  }, [ServiceConfig.token])

  const handleCancelCollect = (event: React.MouseEvent) => {
    event.stopPropagation()
    removeFromFavorites()
  }

  const handleOverlayClick = (event: React.MouseEvent) => {
    event.stopPropagation()
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <div className={cn('p-2 pr-0 cursor-pointer', triggerClassName)}>
        <img
          onClick={handleCollectChange}
          className="transition-all duration-100 hover:scale-[1.2]"
          src={isCollect ? '/images/icons/star-active-icon.svg' : '/images/icons/star-icon.svg'}
          alt=""
        />
      </div>
      <DialogOverlay
        onPointerDown={(event) => handleOverlayClick(event)}
        onClick={(event) => handleOverlayClick(event)}
      >
        <DialogContent
          onPointerDown={(event) => event.stopPropagation()}
          onClick={(event) => event.stopPropagation()}
          className="w-[335px] bg-[#232329] rounded-2xl p-5"
        >
          <DialogHeader>
            <DialogTitle className="text-center">
              <p className="text-[18px] py-3">{t('listCoin.removeWatchlist', { coinName: tokenSymbol })}</p>
            </DialogTitle>
            <div className="flex justify-center items-center flex-row gap-2.5 mt-4">
              <Button variant="close" className="flex-1 rounded-[50px]" onClick={() => setOpen(false)}>
                {t('toast.cancel')}
              </Button>
              <Button variant="gradient" className="text-[#261236] flex-1 rounded-[50px]" onClick={handleCancelCollect}>
                {t('toast.confirm')}
              </Button>
            </div>
          </DialogHeader>
          <DialogDescription />
        </DialogContent>
      </DialogOverlay>
    </Dialog>
  )
}

const CardHead: React.FC<CardWrapperProps> = ({ children }) => {
  return (
    <div className="px-2 py-[calc(1rem*(10/16))] rounded-tl-[6px] rounded-tr-[6px]">
      <div className="flex items-center justify-between">{children}</div>
    </div>
  )
}

const classNames4Cols = ['sm:col-span-3', 'sm:col-span-3', 'sm:col-span-2', 'sm:col-span-2 sm:text-end']
const classNames3Cols = ['flex-5 sm:col-span-4', 'flex-4 sm:col-span-4', 'flex-3 text-right sm:col-span-2']

const CardBottom = ({ tradeDetails, cols = 4, className = '' }: CurrencyListCardProps) => {
  const classNames = cols === 3 ? classNames3Cols : classNames4Cols
  return (
    <div className={cn('px-2 pt-0.5 pb-1.5 flex justify-between sm:grid sm:grid-cols-10 bg-[#232329] rounded-bl-[6px] rounded-br-[6px]', className)}>
      {tradeDetails.map((item, index) => (
        <div key={index} className={classNames[index]}>
          <span className="text-(--text-tertiary) text-[calc(1rem*(10/16))] leading-[calc(1rem*(10/16))] mr-1">
            {item.label}
          </span>
          <span className="text-(--text-primary) app-font-medium text-[calc(1rem*(11/16))] leading-[calc(1rem*(11/16))]">
            {isNumber(item.value) ? f(item.value) : item.value}
          </span>
        </div>
      ))}
    </div>
  )
}

interface CardWrapperProps {
  children: ReactNode
  address?: string
  chainId?: ChainIds
  disabled?: boolean
  onClick?: () => void
  className?: string
}

const CardWrapper: React.FC<CardWrapperProps> = (props) => {
  const { children, address, disabled = false, chainId = ChainIds.Solana, onClick, className } = props
  const navigate = useNavigate()

  const handleCardClick = () => {
    if (onClick) {
      onClick?.()
      return
    }
    if (disabled || !address) return
    navigate(getPath(APP_PATH.MEME_TOKEN_DETAIL, { address, chain: CHAIN_SYMBOLS[chainId] }))
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <div className={cn('relative', disabled ? 'cursor-not-allowed' : 'cursor-pointer', className)} onClick={handleCardClick}>
      {children}
      {disabled && <div className="absolute inset-0 bg-[#141414B3]" />}
    </div>
  )
}

export { CardHead, CardBottom, CardWrapper, ConfirmCollectModal }
