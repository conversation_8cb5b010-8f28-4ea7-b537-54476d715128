// import { useTranslation } from 'react-i18next'


const shareFooter = () => {
  // const { t } = useTranslation()
  return (
    <div className="text-center w-full h-[72px] flex items-center justify-center relative overflow-hidden bg-gradient-to-tr from-[#9945FF]/10 via-[#FFFFFF]/8 to-[#00F3C1]/10">
      {/* <img src="/images/share/bg.png" alt="" className="absolute"/> */}
      <div className="absolute w-full h-full opacity-20" style={{
          backgroundImage:'url(/images/share/bg.png)',
          backgroundSize:'160%',
          backgroundPosition:'50%',
          backgroundRepeat:'no-repeat'
        }}>
        {/* <img src="/images/share/bg.png" alt=""/> */}
      </div>
      <img src="/images/logo-xbit.webp" alt="" className="size-10 mr-2"/>
      <div className="text-left">
        <img src="/images/logo-xbit-txt.png" alt="" className="absolute top-0 left-0"></img>
        <div className="flex items-baseline">
          <img src="/images/logo-xbit-txt.webp" alt=""></img>
          <span className="text-[calc(1rem*(16/16))] font-bold">.com</span>
          </div>
        <div className="text-white text-[calc(1rem*(10/16))]">去中心化交易所(DEX)</div>
      </div>
     
    </div>
  )
}

export default shareFooter
