import { useTranslation } from 'react-i18next'

type ListAppShareType = {
  title: string
  url: string
  iconUrl: string
  onClick?: () => void
}

interface ListAppShareProps {
  onSave: () => void
}

const ListAppShare = ({ onSave }: ListAppShareProps) => {
  const { t } = useTranslation()

  const listApp: ListAppShareType[] = [
    {
      title: t('detail.myPositions.saveToAlbum'),
      url: '/',
      iconUrl: '/images/share/icon-save-local.png',
      onClick: onSave,
    },
    {
      title: t('detail.myPositions.wechat'),
      url: '/',
      iconUrl: '/images/share/icon-wechat.png',
    },
    {
      title: t('detail.myPositions.moments'),
      url: '/',
      iconUrl: '/images/share/icon-moment.png',
    },
    {
      title: 'Twitter(X)',
      url: '/',
      iconUrl: '/images/share/icon-x.png',
    },
    {
      title: 'Telegram',
      url: '/',
      iconUrl: '/images/share/icon-tele.png',
    },
    // {
    //   title: t('detail.myPositions.more'),
    //   url: '/',
    //   iconUrl: '/images/share/icon-more.png',
    // },
  ]
  return (
    <div className="flex items-center gap-8  p-3 overflow-x-auto _hidescrollbar">
      {listApp.map((item, index) => {
        const handleClick = () => {
          if (item?.onClick) {
            item?.onClick?.()
            return
          }
        }
        return (
          <div key={index} className="flex flex-col items-center gap-2.5 cursor-pointer" onClick={handleClick}>
            <img className="w-12 h-12 min-w-12" src={item.iconUrl} alt="icon share" />
            <span className="app-font-regular text-[calc(1rem*(11/16))] text-[#FFFFFFB2] text-center leading-3">
              {item.title}
            </span>
          </div>
        )
      })}
    </div>
  )
}

export default ListAppShare
