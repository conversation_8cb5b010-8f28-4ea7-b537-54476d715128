import { Drawer, DrawerContent, DrawerHeader, DrawerTitle } from '@components/ui/drawer.tsx'
import { SetStateAction } from 'react'
import { cn } from '@/lib/utils.ts'

export type AppDrawerProps = {
  open?: boolean
  setOpen: React.Dispatch<SetStateAction<boolean>>
  title?: string | React.ReactNode
  leftIcon?: React.ReactNode
  drawerContent?: React.ReactNode
  drawerFooter?: React.ReactNode
  drawerHeaderClassName?: string
  drawerBg?: React.ReactNode
  maxHeight?: string
  drawerClassName?: string
  drawerContentClassName?: string
  rightIcon?: React.ReactNode
  activeElement?: Element
  drawerContentRef?: React.RefObject<HTMLDivElement>
  isShowControlIcon?: boolean
}

const AppDrawer = ({
  open,
  setOpen,
  title,
  drawerContent,
  drawerFooter,
  drawerHeaderClassName,
  maxHeight = '80vh',
  drawerBg,
  drawerClassName,
  drawerContentClassName,
  leftIcon,
  rightIcon,
  activeElement,
  drawerContentRef,
  isShowControlIcon = true,
}: AppDrawerProps) => {
  return (
    <Drawer
      open={open}
      onOpenChange={(val) => {
        if (!val) {
          if (activeElement) {
            return
          }
          setOpen(val)
        } else {
          setOpen(val)
        }
      }}
    >
      <DrawerContent
        className={cn(
          'w-full bg-[#232329] max-w-[768px] bg-[url(/images/popup-bg.png)] bg-cover bg-center bg-no-repeat mx-auto focus-visible:outline-none z-50',
          drawerClassName,
        )}
        style={{
          maxHeight: maxHeight,
        }}
        onClick={(event) => event.stopPropagation()}
        isShowControlIcon={isShowControlIcon}
      >
        {drawerBg}
        <DrawerHeader
          className={cn(
            'py-[19px] px-[12px] flex w-full items-center justify-between leading-[1]',
            drawerHeaderClassName,
          )}
        >
          {leftIcon}
          <DrawerTitle className="app-font-medium text-[calc(1rem*(18/16))] text-white">{title}</DrawerTitle>
          {rightIcon ? (
            rightIcon
          ) : (
            <img
              src="/images/icons/icon-x.svg"
              className="w-6 h-6 cursor-pointer"
              onClick={() => setOpen(false)}
              alt=""
            />
          )}
        </DrawerHeader>

        <div ref={drawerContentRef} className={cn('px-3 pb-3 overflow-y-auto', drawerContentClassName)}>
          {drawerContent}
        </div>

        {drawerFooter}
      </DrawerContent>
    </Drawer>
  )
}

export default AppDrawer
