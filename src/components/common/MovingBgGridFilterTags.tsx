import { useEffect, useRef, useState } from 'react'
import { cn } from '@/lib/utils.ts'
import {
  Tabs,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"

type MovingBgFilterTagsProps = {
  tabs: string[];
  defaultTab: string;
  activeTab?: string;
  containerId: string;
  containerClassName?: string;
  tabsListClassName?: string;
  tabsTriggerClassName?: string;
  tabBgClassName?: string;
  onTabChange?: (tab: string) => any;
  activeColor?: string;
}

type ActiveTagRect = {
  width: number;
  height: number;
  left: number;
  top: number;
}

const getTagRect = (containerId: string, tabId: string): ActiveTagRect => {
  const tabElement = document.getElementById(`${containerId}-${tabId}`)
  if (!tabElement) return {
    width: 0,
    height: 0,
    left: 0,
    top: 0,
  }

  return {
    width: tabElement.offsetWidth,
    height: tabElement.offsetHeight,
    left: tabElement.offsetLeft,
    top: tabElement.offsetTop,
  }
}

const MovingBgGridFilterTags = ({
  tabs,
  defaultTab = tabs[0],
  activeTab = tabs[0],
  containerId,
  containerClassName,
  tabsListClassName,
  tabsTriggerClassName,
  tabBgClassName,
  onTabChange,
  activeColor = "!text-[#FFFFFF99]"
}: MovingBgFilterTagsProps) => {
  const [activeTagRect, setActiveTagRect] = useState<ActiveTagRect>(getTagRect(containerId, defaultTab))
  const tabBgRef = useRef<HTMLSpanElement>(null)

  const tabBgStyles = {
    width: `${activeTagRect.width - 0.5}px`,
    height: `${activeTagRect.height}px`,
    transform: `translateX(${activeTagRect.left - 1}px) translateY(${activeTagRect.top}px)`,
  }

  const handleTabChange = (tab: string) => {
    setActiveTagRect(getTagRect(containerId, tab))
    if (onTabChange) {
      onTabChange(tab)
    }
  }

  useEffect(() => {
    setActiveTagRect(getTagRect(containerId, defaultTab))
    let tabBgTransitionTimeout: ReturnType<typeof setTimeout>
    if (tabBgRef?.current) {
      tabBgRef?.current?.classList.remove('opacity-0')
      tabBgTransitionTimeout = setTimeout(() => {
        tabBgRef?.current?.classList.add('transition-[.3s]')
      }, 50)
    }

    return () => {
      clearTimeout(tabBgTransitionTimeout)
    }
  }, [])

  return (
    <Tabs
      id={containerId}
      className={cn('h-auto leading-[1]', containerClassName)}
      onValueChange={handleTabChange}
      value={activeTab}
      defaultValue={defaultTab}
    >
      <TabsList
        className={cn(
          '!rounded-md p-0 h-full relative overflow-x-auto no-scrollbar flex flex-wrap justify-start gap-y-2.5 gap-x-0 bg-transparent',
          tabsListClassName,
        )}
      >
        {tabs.map((tab: string) => (
          <TabsTrigger
            className={cn(
              'static rounded-none bg-[#ECECED0A] text-[11px] min-w-[58px] w-fit h-5 leading-[1] app-font-regular px-2.5 py-1 ',
              activeTab === tab
                ? activeColor
                : '!text-[#FFFFFF99]',
              tabsTriggerClassName,
            )}
            key={tab}
            id={`${containerId}-${tab}`}
            value={tab}
          >
            {tab}
          </TabsTrigger>
        ))}
        <span
          className={cn(
            'opacity-0 absolute z-0 left-[1px] top-0 rounded-[3px] bg-[#ECECED14]',
            tabBgClassName,
          )}
          style={tabBgStyles}
          ref={tabBgRef}
        />
      </TabsList>
    </Tabs>
  )
}

export default MovingBgGridFilterTags