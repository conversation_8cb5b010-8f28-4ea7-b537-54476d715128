import useSignWallet from '@/hooks/useSignWallet'
import { APP_PATH, CHAIN_SYMBOLS } from '@/lib/constant'
import { getPath } from '@/lib/utils.ts'
import { DiscoverNavLink } from '@components/common/bottomNav/DiscoverNavLink.tsx'
import NavLink, { NavLinkProps } from '@components/common/bottomNav/NavLink.tsx'
import { LoginDrawer } from '@components/common/LoginDrawer.tsx'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useLocation } from 'react-router-dom'
import { LoginEvmDrawer } from '../LoginEvmDrawer'

const BottomNavDex = () => {
  const [currentNav, setCurrentNav] = useState<number>(0)
  const { pathname } = useLocation()
  const { t } = useTranslation()
  const [showLoginDrawer, setShowLoginDrawer] = useState(false)
  const [showLoginEvmDrawer, setShowLoginEvmDrawer] = useState(false)

  useSignWallet({
    isAutoConnect: true,
  })

  const navItems: NavLinkProps[] = [
    {
      to: APP_PATH.FUTURES_DISCOVER,
      title: t('bottomNav.homepage'),
      icons: {
        default: '/images/icons/BottomNavDex/nav-icon-discover-dex.svg',
        active: '/images/icons/BottomNavDex/nav-icon-discover-active-dex.svg',
      },
    },
    {
      to: APP_PATH.FUTURES_MARKET,
      title: t('bottomNav.market'),
      icons: {
        default: '/images/icons/BottomNavDex/nav-icon-market-dex.svg',
        active: '/images/icons/BottomNavDex/nav-icon-market-active-dex.svg',
      },
    },
    {
      to: APP_PATH.FUTURES,
      title: t('bottomNav.trading'),
      isMain: true,
      isDexRouter: true,
      icons: {
        default: '/images/icons/nav-icon-xbit.webp',
        active: '/images/icons/nav-icon-xbit.webp',
      },
    },
    {
      to: APP_PATH.MEME_SMART_MONEY,
      title: t('bottomNav.smartMoney'),
      icons: {
        default: '/images/icons/nav-icon-bitcoin.svg',
        active: '/images/icons/nav-icon-bitcoin-active.svg',
      },
      isDisabled: true,
    },
    {
      to: APP_PATH.FUTURES_ASSET,
      title: t('bottomNav.assets'),
      isDisabled: false,
      icons: {
        default: '/images/icons/nav-icon-wallet.webp',
        active: '/images/icons/nav-icon-wallet-active.webp',
      },
    },
  ]

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>, index: number) => {
    const shouldNavigate = navItems[index].onClick ? navItems[index].onClick(e) : true
    if (shouldNavigate) {
      setCurrentNav(index)
    }
  }

  const findActiveNavIndex = (pathname: string): number => {
    const sortedNavItems = navItems
      .map((item, index) => ({ ...item, originalIndex: index }))
      .sort((a, b) => String(b.to).length - String(a.to).length)

    for (const item of sortedNavItems) {
      if (pathname === item.to) {
        return item.originalIndex
      }
      if (pathname.startsWith(String(item.to))) {
        const nextChar = pathname[String(item.to).length]
        if (!nextChar || nextChar === '/') {
          return item.originalIndex
        }
      }
    }

    return -1
  }

  useEffect(() => {
    const currentNavIndex = findActiveNavIndex(pathname)

    console.log('currentNav', currentNavIndex)
    console.log('pathname', pathname)

    if (currentNavIndex !== -1) {
      setCurrentNav(currentNavIndex)
    } 
    // else if (/^\/futures\/[^/]+\/token\/[^/]+$/.test(pathname)) {
    //   setCurrentNav(2) // Match to the trading tab
    // } else {
    //   setCurrentNav(0) // Default to the first tab if no match
    // }
  }, [pathname])

  return (
    <nav className="bg-(--bottom-nav-bg) border-[1px] border-(--bottom-nav-border-color) max-w-[768px] mx-auto">
      <div className="flex align-bottom relative top-[-25px]">
        {navItems.map((item, index) => (
          <NavLink
            key={index}
            {...item}
            isActive={currentNav === index}
            onClick={(e: React.MouseEvent<HTMLAnchorElement>) => handleClick(e, index)}
          />
        ))}
      </div>
      <LoginDrawer setOpen={setShowLoginDrawer} open={showLoginDrawer} />
      {showLoginEvmDrawer && <LoginEvmDrawer open={showLoginEvmDrawer} setOpen={setShowLoginEvmDrawer} />}
    </nav>
  )
}

const BottomNav = () => {
  const [currentNav, setCurrentNav] = useState<number>(0)
  const { pathname } = useLocation()
  const { t } = useTranslation()
  const [showLoginDrawer, setShowLoginDrawer] = useState(false)
  const [showLoginEvmDrawer, setShowLoginEvmDrawer] = useState(false)
  useSignWallet({
    isAutoConnect: true,
  })
  const getTradePath = () => {
    const recentToken = sessionStorage.getItem('recentToken')
      ? JSON.parse(sessionStorage.getItem('recentToken')!)
      : null
    if (!recentToken)
      return getPath(APP_PATH.MEME_TOKEN_DETAIL, {
        address: import.meta.env.VITE_DEFAULT_MEME_TOKEN,
        chain: CHAIN_SYMBOLS[+import.meta.env.VITE_DEFAULT_MEME_CHAIN_ID],
      })
    return getPath(APP_PATH.MEME_TOKEN_DETAIL, {
      address: recentToken.token,
      chain: recentToken.chain,
    })
  }

  const navItems: NavLinkProps[] = [
    {
      to: APP_PATH.MEME_DISCOVER,
      title: t('bottomNav.discover'),
      icons: {
        default: '/images/icons/nav-icon-discover.webp',
        active: '/images/icons/nav-icon-discover-active.webp',
      },
    },
    {
      to: APP_PATH.MEME_SMART_MONEY,
      title: t('bottomNav.smartMoney'),
      icons: {
        default: '/images/icons/nav-icon-bitcoin.svg',
        active: '/images/icons/nav-icon-bitcoin-active.svg',
      },
    },
    {
      to: getTradePath(),
      title: t('bottomNav.trading'),
      isMain: true,
      icons: {
        default: '/images/icons/nav-icon-trade.png',
        active: '/images/icons/nav-icon-trade.png',
      },
      onClick: (e: React.MouseEvent<HTMLAnchorElement>) => {
        const isMatch = /^\/meme\/[^/]+\/token\/[^/]+$/.test(pathname)
        if (isMatch) {
          e.preventDefault()
          return false
        }
      },
    },
    {
      to: APP_PATH.MEME_MONITORING,
      title: t('bottomNav.monitoring'),
      icons: {
        default: '/images/icons/nav-icon-monitor.png',
        active: '/images/icons/nav-icon-monitor-active.png',
      },
    },
    {
      to: APP_PATH.MEME_ASSETS,
      title: t('bottomNav.assets'),
      isDisabled: false,
      icons: {
        default: '/images/icons/nav-icon-wallet.webp',
        active: '/images/icons/nav-icon-wallet-active.webp',
      },
    },
  ]

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>, index: number) => {
    const shouldNavigate = navItems[index].onClick ? navItems[index].onClick(e) : true
    if (shouldNavigate) {
      setCurrentNav(index)
    }
  }

  useEffect(() => {
    const currentNav = navItems.findIndex((item) => pathname.includes(item.to.toString()))
    //const isMatch = /^\/meme\/[^/]+\/token\/[^/]+$/.test(pathname)
    if (currentNav !== -1) {
      setCurrentNav(currentNav)
    } else if (/^\/meme\/[^/]+\/token\/[^/]+$/.test(pathname)) {
      setCurrentNav(2) // Match to the trading tab
    }
  }, [pathname])

  return (
    <nav className="bg-(--bottom-nav-bg) border-[1px] border-(--bottom-nav-border-color) max-w-[768px] mx-auto">
      <div className="flex align-bottom relative top-[-25px]">
        {navItems.map((item, index) => {
          if (index === 0)
            return <DiscoverNavLink key={index} isActive={currentNav === 0} onClick={(e) => handleClick(e, 0)} />
          return (
            <NavLink
              key={index}
              {...item}
              isActive={currentNav === index}
              onClick={(e: React.MouseEvent<HTMLAnchorElement>) => handleClick(e, index)}
            />
          )
        })}
      </div>
      <LoginDrawer setOpen={setShowLoginDrawer} open={showLoginDrawer} />
      {showLoginEvmDrawer && <LoginEvmDrawer open={showLoginEvmDrawer} setOpen={setShowLoginEvmDrawer} />}
    </nav>
  )
}

export { BottomNav, BottomNavDex }

