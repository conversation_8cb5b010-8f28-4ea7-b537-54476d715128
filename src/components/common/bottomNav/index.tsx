import { useEffect, useState } from 'react'
import NavLink, { NavLinkProps } from '@components/common/bottomNav/NavLink.tsx'
import { useTranslation } from 'react-i18next'
import { useLocation } from 'react-router-dom'
import { APP_PATH, CHAIN_SYMBOLS } from '@/lib/constant'
import { useMultiChainWallet } from '@hooks/useMultiChainWallet.ts'
import { LoginDrawer } from '@components/common/LoginDrawer.tsx'
import { LoginEvmDrawer } from '../LoginEvmDrawer'
import { getPath } from '@/lib/utils.ts'
import useSignWallet from '@/hooks/useSignWallet'

const BottomNavDex = () => {
  const [currentNav, setCurrentNav] = useState<number>(0)
  const { pathname } = useLocation()
  const { t } = useTranslation()
  const { activeWallet } = useMultiChainWallet({})
  const isWalletConnected: boolean = activeWallet?.isConnected || false
  const [showLoginDrawer, setShowLoginDrawer] = useState(false)
  const [showLoginEvmDrawer, setShowLoginEvmDrawer] = useState(false)

  const navItems: NavLinkProps[] = [
    {
      to: APP_PATH.FUTURES_DISCOVER,
      title: t('bottomNav.homepage'),
      icons: {
        default: '/images/icons/BottomNavDex/nav-icon-discover-dex.svg',
        active: '/images/icons/BottomNavDex/nav-icon-discover-active-dex.svg',
      },
    },
    {
      to: APP_PATH.FUTURES_MARKET,
      title: t('bottomNav.market'),
      icons: {
        default: '/images/icons/BottomNavDex/nav-icon-market-dex.svg',
        active: '/images/icons/BottomNavDex/nav-icon-market-active-dex.svg',
      },
    },
    {
      to: APP_PATH.FUTURES,
      title: t('bottomNav.trading'),
      isMain: true,
      icons: {
        default: '/images/icons/nav-icon-xbit.webp',
        active: '/images/icons/nav-icon-xbit.webp',
      },
    },
    {
      to: APP_PATH.MEME_SMART_MONEY,
      title: t('bottomNav.smartMoney'),
      icons: {
        default: '/images/icons/nav-icon-bitcoin.svg',
        active: '/images/icons/nav-icon-bitcoin-active.svg',
      },
      isDisabled: true,
    },
    {
      to: APP_PATH.FUTURES_ASSET,
      title: t('bottomNav.assets'),
      isDisabled: false,
      icons: {
        default: '/images/icons/nav-icon-wallet.webp',
        active: '/images/icons/nav-icon-wallet-active.webp',
      },
      onClick: (e: React.MouseEvent<HTMLAnchorElement>) => {
        if (isWalletConnected) return true
        e.preventDefault()
        setShowLoginEvmDrawer(true)
        return false
      },
    },
  ]

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>, index: number) => {
    const shouldNavigate = navItems[index].onClick ? navItems[index].onClick(e) : true
    if (shouldNavigate) {
      setCurrentNav(index)
    }
  }

  useEffect(() => {
    const currentNav = navItems.findIndex((item) => pathname.includes(item.to.toString()))
    if (currentNav !== -1) {
      setCurrentNav(currentNav)
    }
  }, [pathname])

  return (
    <nav className="bg-(--bottom-nav-bg) border-[1px] border-(--bottom-nav-border-color) max-w-[768px] mx-auto">
      <div className="flex align-bottom relative top-[-25px]">
        {navItems.map((item, index) => (
          <NavLink
            key={index}
            {...item}
            isActive={currentNav === index}
            onClick={(e: React.MouseEvent<HTMLAnchorElement>) => handleClick(e, index)}
          />
        ))}
      </div>
      <LoginDrawer setOpen={setShowLoginDrawer} open={showLoginDrawer} />
      {showLoginEvmDrawer && <LoginEvmDrawer open={showLoginEvmDrawer} setOpen={setShowLoginEvmDrawer} />}
    </nav>
  )
}

const BottomNav = () => {
  const [currentNav, setCurrentNav] = useState<number>(0)
  const { pathname } = useLocation()
  const { t } = useTranslation()
  const [showLoginDrawer, setShowLoginDrawer] = useState(false)
  const [showLoginEvmDrawer, setShowLoginEvmDrawer] = useState(false)
  useSignWallet({
    isAutoConnect: true,
  })
  const getTradePath = () => {
    const recentToken = sessionStorage.getItem('recentToken')
      ? JSON.parse(sessionStorage.getItem('recentToken')!)
      : null
    if (!recentToken)
      return getPath(APP_PATH.MEME_TOKEN_DETAIL, {
        address: import.meta.env.VITE_DEFAULT_MEME_TOKEN,
        chain: CHAIN_SYMBOLS[+import.meta.env.VITE_DEFAULT_MEME_CHAIN_ID],
      })
    return getPath(APP_PATH.MEME_TOKEN_DETAIL, {
      address: recentToken.token,
      chain: recentToken.chain,
    })
  }

  const navItems: NavLinkProps[] = [
    {
      to: APP_PATH.MEME_DISCOVER,
      title: t('bottomNav.discover'),
      icons: {
        default: '/images/icons/nav-icon-discover.webp',
        active: '/images/icons/nav-icon-discover-active.webp',
      },
    },
    {
      to: APP_PATH.MEME_SMART_MONEY,
      title: t('bottomNav.smartMoney'),
      icons: {
        default: '/images/icons/nav-icon-bitcoin.svg',
        active: '/images/icons/nav-icon-bitcoin-active.svg',
      },
    },
    {
      to: getTradePath(),
      title: t('bottomNav.trading'),
      isMain: true,
      icons: {
        default: '/images/icons/nav-icon-xbit.webp',
        active: '/images/icons/nav-icon-xbit.webp',
      },
      onClick: (e: React.MouseEvent<HTMLAnchorElement>) => {
        const isMatch = /^\/meme\/[^/]+\/token\/[^/]+$/.test(pathname)
        if (isMatch) {
          e.preventDefault()
          return false
        }
      },
    },
    {
      to: APP_PATH.MEME_MONITORING,
      title: t('bottomNav.monitoring'),
      icons: {
        default: '/images/icons/nav-icon-monitor.png',
        active: '/images/icons/nav-icon-monitor-active.png',
      },
    },
    {
      to: APP_PATH.MEME_ASSETS,
      title: t('bottomNav.assets'),
      isDisabled: false,
      icons: {
        default: '/images/icons/nav-icon-wallet.webp',
        active: '/images/icons/nav-icon-wallet-active.webp',
      },
      // onClick: (e: React.MouseEvent<HTMLAnchorElement>) => {
      //   if (isWalletConnected) return true
      //   e.preventDefault()
      //   if (activeChain === TYPE_CHAIN.SOLANA) {
      //     setShowLoginDrawer(true)
      //     return false
      //   }
      //   setShowLoginEvmDrawer(true)
      // },
    },
  ]

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>, index: number) => {
    const shouldNavigate = navItems[index].onClick ? navItems[index].onClick(e) : true
    if (shouldNavigate) {
      setCurrentNav(index)
    }
  }

  useEffect(() => {
    const currentNav = navItems.findIndex((item) => pathname.includes(item.to.toString()))
    //const isMatch = /^\/meme\/[^/]+\/token\/[^/]+$/.test(pathname)
    if (currentNav !== -1) {
      setCurrentNav(currentNav)
    } else if (/^\/meme\/[^/]+\/token\/[^/]+$/.test(pathname)) {
      setCurrentNav(2) // Match to the trading tab
    }
  }, [pathname])

  return (
    <nav className="bg-(--bottom-nav-bg) border-[1px] border-(--bottom-nav-border-color) max-w-[768px] mx-auto">
      <div className="flex align-bottom relative top-[-25px]">
        {navItems.map((item, index) => (
          <NavLink
            key={index}
            {...item}
            isActive={currentNav === index}
            onClick={(e: React.MouseEvent<HTMLAnchorElement>) => handleClick(e, index)}
          />
        ))}
      </div>
      <LoginDrawer setOpen={setShowLoginDrawer} open={showLoginDrawer} />
      {showLoginEvmDrawer && <LoginEvmDrawer open={showLoginEvmDrawer} setOpen={setShowLoginEvmDrawer} />}
    </nav>
  )
}

export { BottomNavDex, BottomNav }
