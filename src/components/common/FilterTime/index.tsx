import { Button, ButtonProps } from '@components/ui/button.tsx'
import { cn } from '@/lib/utils.ts'
import { useEffect, useState } from 'react'

type FilterTimeButtonProps = ButtonProps & {
  isActive?: boolean,
}

export type TimeUnit = 's' | 'm' | 'h' | 'D' | 'W' | 'M' | 'Y'

export type FilterTimeOption = {
  value: string,
  unit: TimeUnit,
}
type FilterTimeProps = {
  options: FilterTimeOption[],
  defaultSelectedIndex?: number,
  onChange?: (index: number, option?: FilterTimeOption) => any,
}

const FilterTimeButton = ({ className, children, isActive, ...rest }: FilterTimeButtonProps) => {
  return (
    <Button
      className={cn(
        'border-r-[0.5px] border-r-[#ECECED14] last:border-r-[none] rounded-[0] bg-[none] min-w-[32px] h-[auto] px-[7px] py-[5px] text-[#FFFFFF99] text-[calc(1rem*(11/16))] font-[400]',
        isActive && 'bg-[#ECECED14] text-[#EFEFEF]',
        className,
      )}
      {...rest}
    >
      {children}
    </Button>
  )
}

const FilterTime = ({ options, onChange, defaultSelectedIndex = 0 }: FilterTimeProps) => {
  const [selectedIndex, setSelectedIndex] = useState<number>(defaultSelectedIndex)

  const handleFilterClick = (index: number, option?: FilterTimeOption) => {
    setSelectedIndex(index)
    if (onChange) {
      onChange(index, option)
    }
  }

  useEffect(() => {
    setSelectedIndex(defaultSelectedIndex)
  }, [defaultSelectedIndex]);

  return (
    <div className="rounded-[4px] border-[0.5px] border-[#ECECED14] flex overflow-hidden">
      {options.map((option, index) => (
        <FilterTimeButton key={index} isActive={selectedIndex === index} onClick={() => handleFilterClick(index, option)} className={`flex items-center justify-center gap-2 px-2 py-0.5 min-w-[48px] ${selectedIndex === index ? 'rounded-sm border-gradient' : ''}`}>
          {option.value}{option.unit}
        </FilterTimeButton>
      ))}
    </div>
  )
}

export default FilterTime
