import { cn } from '@/lib/utils.ts'
import { Tabs, TabsList, TabsTrigger } from '@components/ui/tabs.tsx'

type FilterTagsGridProps = {
  tabs: string[];
  defaultTab: string;
  activeTab?: string;
  containerId: string;
  containerClassName?: string;
  tabsListClassName?: string;
  tabsTriggerClassName?: string;
  tabBgClassName?: string;
  onTabChange?: (tab: string) => any;
  activeColor?: string;
}

const FilterTagsGrid = ({
  tabs,
  defaultTab = tabs[0],
  activeTab = tabs[0],
  containerId,
  containerClassName,
  tabsListClassName,
  tabsTriggerClassName,
  onTabChange,
  activeColor = "!text-[#FFFFFF99]"
}: FilterTagsGridProps) => {

  const handleTabChange = (tab: string) => {
    if (onTabChange) {
      onTabChange(tab)
    }
  }

  return (
    <Tabs
      id={containerId}
      className={cn('h-auto leading-[1]', containerClassName)}
      onValueChange={handleTabChange}
      value={activeTab}
      defaultValue={defaultTab}
    >
      <TabsList
        className={cn(
          '!rounded-md p-0 h-full relative overflow-x-auto no-scrollbar flex flex-wrap justify-start gap-y-2.5 gap-x-0 bg-transparent',
          tabsListClassName,
        )}
      >
        {tabs.map((tab: string) => (
          <TabsTrigger
            className={cn(
              'static rounded-none bg-[#ECECED0A] text-[11px] min-w-[58px] w-fit h-5 leading-[1] app-font-regular px-2.5 py-1 ',
              activeTab === tab
                ? `${activeColor} !bg-[#ECECED14]`
                : '!text-[#FFFFFF99]',
              tabsTriggerClassName,
            )}
            key={tab}
            id={`${containerId}-${tab}`}
            value={tab}
          >
            {tab}
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  )
}

export default FilterTagsGrid