import React, { FC, JSX, use, useEffect, useRef, useState } from 'react'
import { isNumber } from '@/utils/helpers'
import { formatTokenPrice } from '@/utils/helpers'
import { f } from 'fintech-number'
import useTokenPrice, { useTokenPriceOneTime } from '@/hooks/useTokenPrice'
import { SOL_ADDRESS } from '@/lib/blockchain'
import { cn } from '@/lib/utils'
import styled, { keyframes } from "styled-components";
/**
 * Props for the MoneyFormatted component
 * @interface MoneyFormattedProps
 * @extends {React.HTMLAttributes<HTMLSpanElement>}
 */
interface MoneyFormattedProps extends React.HTMLAttributes<HTMLSpanElement> {
  /** The value to be formatted */
  value?: number | string
  /** Currency unit or custom React node to display before the value */
  unit?: string | React.ReactNode
  /** Optional className for styling the unit element */
  unitClassName?: React.HTMLAttributes<HTMLSpanElement>['className']
  /** Whether to show the currency unit */
  showUnit?: boolean
  /** Round type */
  roundType?: 'round' | 'floor' | 'ceil'
  /** wallet address */
  wallet?: string
  /** unit is USD */
  isUSD?: boolean
  /** Callback function to handle value changes */
  callback?: (price: number, priceSol: number) => string
  callbackClassName?: (value: number) => string
  loading?: boolean
}

const l3 = keyframes`
  20% { background-position: 0% 0%, 50% 50%, 100% 50%; }
  40% { background-position: 0% 100%, 50% 0%, 100% 50%; }
  60% { background-position: 0% 50%, 50% 100%, 100% 0%; }
  80% { background-position: 0% 50%, 50% 50%, 100% 100%; }
`;

export const Loader = styled.div`
  width: 20px;
  aspect-ratio: 2;
  --_g: no-repeat radial-gradient(circle closest-side, #fff 90%, #0000);
  background:
    var(--_g) 0% 50%,
    var(--_g) 50% 50%,
    var(--_g) 100% 50%;
  background-size: calc(100%/3) 50%;
  animation: ${l3} 1s infinite linear;
`;

const LoadingMoneyFormatted: FC = () => {
  return <Loader />;
}

/**
 * Formats and displays monetary values with proper formatting
 *
 * Features:
 * - Displays currency unit
 * - Formats numbers with thousands separators
 * - Handles decimal parts with special formatting for small numbers
 * - Shows placeholder when value is invalid
 *
 * @param {MoneyFormattedProps} props - Component props
 * @returns {JSX.Element} Formatted monetary value
 */
const MoneyFormatted: FC<MoneyFormattedProps> = ({
  value,
  unit = '$',
  unitClassName,
  showUnit = true,
  roundType = 'round',
  loading = false,
  ...rest
}: MoneyFormattedProps): JSX.Element => {
  const valueNumbered = Number(value)

  // Return placeholder for invalid numbers
  if (!isNumber(valueNumbered)) return <span {...rest}>--</span>

  const moneyFormatted = formatTokenPrice(valueNumbered, {
    roundType,
  })

  return loading ? <LoadingMoneyFormatted /> : <span {...rest}>
    {showUnit && unit === '$' && <span className={unitClassName}>{unit}</span>}
    {f(+moneyFormatted.integerPart)}
    {!!moneyFormatted.decimalPart && (
      <>
        .
        {moneyFormatted.zeroCount > 0 && (
          <span>
            0<span className="inline-block text-[70%] translate-y-[15%]">{moneyFormatted.zeroCount}</span>
          </span>
        )}
        {moneyFormatted.decimalPart.replace(/0+$/, '')}
      </>
    )}
    {showUnit && unit !== '$' && <span className={unitClassName}> {unit}</span>}
  </span>
}

/**
 * Real-time money formatting component
 * default will use SOL_ADDRESS to fetch the price and return the formatted value price * value input
 * @returns Formatted monetary value
 */
export const MoneyFormattedRealtime: FC<MoneyFormattedProps> = ({
  value = 1,
  wallet = SOL_ADDRESS,
  callback,
  ...rest
}): JSX.Element => {
  const price = useTokenPrice(wallet);
  const priceSOL = useTokenPrice(SOL_ADDRESS);
  return <MoneyFormatted value={callback ? callback(price, priceSOL) : Number(value) * price} {...rest} />
}

/**
 * MoneyFormatOneTime is a component that formats a single monetary value
 */
export const MoneyFormattedOneTime: FC<MoneyFormattedProps> = ({
  value = 1,
  wallet = SOL_ADDRESS,
  isUSD = true,
  callback,
  ...rest
}) => {
  const isFirstRender = useRef(true);
  const [price, setPrice] = useState<number>(0);
  const [priceSOL, setPriceSOL] = useState<number>(0);
  const priceToken = useTokenPriceOneTime(wallet);
  const priceTokenSOL = useTokenPriceOneTime(SOL_ADDRESS);
  useEffect(() => {
    if (isFirstRender.current && priceToken && priceTokenSOL) {
      isFirstRender.current = false;
      // Set the price only on the first render
      setPrice(priceToken);
      setPriceSOL(priceTokenSOL);
    }
    //if isFirstRender is false, it means the price has been set already is unsubscribed 
  }, [priceToken, priceTokenSOL]);
  // get price one time
  return <MoneyFormatted value={callback ? callback(price, priceSOL) : Number(value) * price} {...rest} />
}

/**
 * MoneyRealtimeProfit return boolean value of the price of the token
 * @param param0 
 * @returns 
 */

export const MoneyRealtimeProfit: FC<{ className?: string, wallet?: string, isUSD?: boolean, callback: (value: number, valueSOL: number) => string }> = ({ className = '', wallet = SOL_ADDRESS, callback }) => {
  const price = useTokenPrice(wallet);
  const priceSOL = useTokenPrice(SOL_ADDRESS);
  return (
    <span className={cn('', className)}>
      {callback(price, priceSOL)}
    </span>
  )
}

export const MoneyRealtimeWrapper: FC<{ className?: string, wallet?: string, callbackClassName: (value: number, valueSOL: number) => string, children?: React.ReactNode }> = ({ className = '', wallet = SOL_ADDRESS, callbackClassName, children }) => {
  const price = useTokenPrice(wallet);
  const priceSOL = useTokenPrice(SOL_ADDRESS);
  return (
    <span className={cn(className, callbackClassName ? callbackClassName(price, priceSOL) : '')}>{children}</span>
  )
}

export default MoneyFormatted
