import { Dispatch, SetStateAction, useState } from 'react'
import TransactionTypeFilter from './TransactionTypeFilter'
import FollowedSmartMoney from './FollowedSmartMoney'
import TransactionAmountFilter from './TransactionAmountFilter'
import { SmartMoneyFilterType } from '@/types/monitoring.ts'

interface SmartMoneyFilterProps {
  setFilter: Dispatch<SetStateAction<SmartMoneyFilterType>>
}

const SmartMoneyFilter = ({ setFilter }: SmartMoneyFilterProps) => {
  const [openTransactionFilter, setOpenTransactionFilter] = useState(false)
  const [openFollowedSM, setOpenFollowedSM] = useState(false)
  const [openTransactionAmountFilter, setOpenTransactionAmountFilter] = useState(false)

  return (
    <div className="flex items-center justify-between gap-2 overflow-auto no-scrollbar">
      <div className="flex items-center gap-2">
        <FollowedSmartMoney open={openFollowedSM} setOpen={setOpenFollowedSM} setFilter={setFilter} />
        <TransactionTypeFilter
          open={openTransactionFilter}
          setOpen={setOpenTransactionFilter}
          onChange={(value) => setFilter(
            (prev) => ({ ...prev, transactionType: value })
          )}
        />
        <TransactionAmountFilter
          open={openTransactionAmountFilter}
          setOpen={setOpenTransactionAmountFilter}
          onChange={(value: number | undefined) => setFilter(
            (prev) => ({ ...prev, minAmountUsd: value })
          )}
        />
      </div>
    </div>
  )
}

export default SmartMoneyFilter
