import { useTranslation } from 'react-i18next'
import { Drawer, Drawer<PERSON>ontent, Drawer<PERSON>eader, DrawerTrigger } from '@components/ui/drawer.tsx'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { DialogTitle } from '@radix-ui/react-dialog'
import { IconCheckCircleSolid } from '../icon'
import { Button } from '../ui/button'
import useGetTotalFollowingAddress from '@hooks/useGetTotalFollowingAddress.ts'
import get from 'lodash-es/get'
import { SmartMoneyFilterType } from '@/types/monitoring.ts'

export interface FollowedSmartMoneyProps {
  open: boolean
  setOpen: Dispatch<SetStateAction<boolean>>
  setFilter: Dispatch<SetStateAction<SmartMoneyFilterType>>
}

export default function FollowedSmartMoney(props: FollowedSmartMoneyProps) {
  const { open, setOpen, setFilter } = props
  const { t } = useTranslation()

  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [isAllSelected, setIsAllSelected] = useState(false)

  const { data: totalData } = useGetTotalFollowingAddress()
  const listFollowing = (get(totalData, 'getFollowingWalletAddressess') as string[]) ?? []

  useEffect(() => {
    if (listFollowing.length > 0) {
      const allAddresses = [...listFollowing]
      setSelectedItems(allAddresses)
      setIsAllSelected(true)
    }
  }, [listFollowing])

  const onSelectItem = (address: string) => {
    setSelectedItems((prev) => {
      if (prev.includes(address)) {
        const newSelected = prev.filter((item) => item !== address)
        setIsAllSelected(newSelected.length === listFollowing.length)
        return newSelected
      } else {
        const newSelected = [...prev, address]
        setIsAllSelected(newSelected.length === listFollowing.length)
        return newSelected
      }
    })
  }

  const onSelectAll = () => {
    if (isAllSelected) {
      setSelectedItems([])
      setIsAllSelected(false)
    } else {
      const allAddresses = [...listFollowing]
      setSelectedItems(allAddresses)
      setIsAllSelected(true)
    }
  }

  const handleApply = () => {
    setOpen(false)
    setFilter((prev) => ({ ...prev, address: [...selectedItems] }))
  }

  const handleClear = () => {
    setSelectedItems([])
    setIsAllSelected(false)
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <div className="rounded-[6px] border-[0.8px] border-[#ECECED1F] flex items-center gap-[6px] p-2 pr-3 cursor-pointer">
          <span className="text-[calc(1rem*(13/16))] leading-[calc(1rem*(13/16))] text-[#FFFFFFCC] whitespace-nowrap">
            {t('detail.smartMoney.follow8SmartMoney', { length: listFollowing.length })}
          </span>
          <img src="/images/icons/icon-chevron-down.svg" className="w-[6.67px] h-[4.67px]" alt="" />
        </div>
      </DrawerTrigger>
      <DrawerContent className="w-full max-w-[768px] mx-auto bg-[url(/images/bg-bottom-sheet.png)] bg-cover bg-center bg-no-repeat">
        <DrawerHeader className="px-3.5 flex w-full items-center justify-between">
          <DialogTitle className="mb-0.5 flex items-center justify-between w-full">
            <div className="flex gap-2 items-end">
              <span className="text-[calc(18rem/16)] leading-[calc(18rem/16)] app-font-medium text-[#FFFFFF]">
                {t('detail.smartMoney.smartMoney')} (<span className="text-[#00FFF6]">{selectedItems.length}</span>/
                {listFollowing.length})
              </span>
              <span
                className="text-[calc(14rem/16)] leading-[calc(14rem/16)] text-[#00FFF6]  cursor-pointer"
                onClick={handleClear}
              >
                {t('detail.smartMoney.clear')}
              </span>
            </div>
            <img
              src="/images/icons/icon-x.svg"
              className="w-6 h-6 cursor-pointer"
              onClick={() => setOpen(false)}
              alt=""
            />
          </DialogTitle>
        </DrawerHeader>
        <div className="px-3">
          <div
            className="py-4 border-b text-[1rem] font-medium flex items-center justify-between cursor-pointer"
            onClick={onSelectAll}
          >
            <span className="text-[1rem] leading-[1rem]">{t('detail.smartMoney.all')}</span>
            {isAllSelected && <IconCheckCircleSolid className="!size-4" />}
          </div>
          {listFollowing.map((smartMoney) => (
            <div
              key={smartMoney}
              className="py-4 border-b-[0.5px] border-[#ECECED0A] text-[1rem] font-medium flex items-center justify-between cursor-pointer"
              onClick={() => onSelectItem(smartMoney)}
            >
              <span className="text-[1rem] leading-[1rem]">{`${smartMoney?.slice(0, 5)}...${smartMoney?.slice(-5)}`}</span>
              {selectedItems.includes(smartMoney) && <IconCheckCircleSolid className="!size-4" />}
            </div>
          ))}
        </div>
        <div className="flex items-center justify-between gap-2 p-4">
          <Button
            variant="borderGradient"
            className="rounded-full flex-1 bg-[#ECECED1F]"
            onClick={() => setOpen(false)}
          >
            {t('detail.smartMoney.cancel')}
          </Button>
          <Button variant="gradient" className="rounded-full flex-1 text-[#141414]" onClick={handleApply}>
            {t('detail.smartMoney.confirm')}
          </Button>
        </div>
      </DrawerContent>
    </Drawer>
  )
}
