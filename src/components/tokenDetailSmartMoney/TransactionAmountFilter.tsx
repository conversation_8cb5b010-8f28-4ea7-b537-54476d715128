import { useTranslation } from 'react-i18next'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DrawerTrigger } from '@components/ui/drawer.tsx'
import { Dispatch, SetStateAction, useMemo, useState } from 'react'
import { DialogTitle } from '@radix-ui/react-dialog'
import { IconCheckCircleSolid } from '../icon'
import { Button } from '../ui/button'

export interface TransactionAmountFilterProps {
  open: boolean
  setOpen: Dispatch<SetStateAction<boolean>>
  onChange: (value: number | undefined) => void
}

const TransactionAmountFilter = (props: TransactionAmountFilterProps) => {
  const { open, setOpen, onChange } = props
  const { t } = useTranslation()
  const [selectedItem, setSelectedItem] = useState<number | undefined>(undefined)
  const items = useMemo(() => {
    return [
      {
        label: t('detail.filters.all'),
        value: undefined,
      },
      {
        label: '$>10K',
        value: 10000,
      },
      {
        label: '$>5K',
        value: 5000,
      },
      {
        label: '$>1K',
        value: 1000,
      },
    ]
  }, [])

  const onSelect = (value: number | undefined) => {
    setSelectedItem(value)
  }

  const handleApply = () => {
    onChange(selectedItem)
    setOpen(false)
  }

  const handleClear = () => {
    onChange(undefined)
    setOpen(false)
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <div className="rounded-[6px] border-[0.8px] border-[#ECECED1F] flex items-center gap-[6px] p-2 cursor-pointer">
          <span className="text-[calc(1rem*(13/16))] leading-[calc(1rem*(13/16))] text-[#FFFFFFCC] whitespace-nowrap">
            {t('detail.smartMoney.transactionAmount')}
          </span>
          <img src="/images/icons/icon-chevron-down.svg" className="w-[6.67px] h-[4.67px]" alt="" />
        </div>
      </DrawerTrigger>
      <DrawerContent className="w-full max-w-[768px] mx-auto bg-[url(/images/bg-bottom-sheet.png)] bg-cover bg-center bg-no-repeat">
        <DrawerHeader className="px-3.5 flex w-full items-center justify-between">
          <DialogTitle className="mb-0.5 flex items-center justify-between w-full">
            <div className="flex gap-2 items-end">
              <span className="text-[calc(18rem/16)] leading-[calc(18rem/16)] app-font-medium text-[#FFFFFF]">
                {t('detail.smartMoney.transactionAmount')}
              </span>
              <span
                className="text-[calc(14rem/16)] leading-[calc(14rem/16)] text-[#00FFF6]  cursor-pointer"
                onClick={handleClear}
              >
                {t('detail.smartMoney.clear')}
              </span>
            </div>
            <img
              src="/images/icons/icon-x.svg"
              className="w-6 h-6 cursor-pointer"
              onClick={() => setOpen(false)}
              alt=""
            />
          </DialogTitle>
        </DrawerHeader>
        <div className="px-3.5">
          {items.map((item, index) => (
            <div
              key={index}
              className="py-4 border-b-[0.5px] border-[#ECECED0A] text-[1rem] font-medium flex items-center justify-between cursor-pointer"
              onClick={() => onSelect(item.value)}
            >
              <span className="text-[1rem] leading-[1rem]">{item.label}</span>
              {selectedItem === item.value && <IconCheckCircleSolid className="!size-4" />}
            </div>
          ))}
        </div>
        <div className="flex items-center justify-between gap-2 p-4">
          <Button
            variant="borderGradient"
            className="rounded-full flex-1 bg-[#ECECED1F]"
            onClick={() => setOpen(false)}
          >
            {t('detail.smartMoney.cancel')}
          </Button>
          <Button variant="gradient" className="rounded-full flex-1 text-[#141414]" onClick={handleApply}>
            {t('detail.smartMoney.confirm')}
          </Button>
        </div>
      </DrawerContent>
    </Drawer>
  )
}

export default TransactionAmountFilter
