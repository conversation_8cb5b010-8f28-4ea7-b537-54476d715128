import { useTranslation } from 'react-i18next'
import Container from '../common/Container'
import SmartMoneyFilter from './SmartMoneyFilter'
import { useQuery } from '@apollo/client'
import { getSmartMoneyActions } from '@/services/smartMoney.service'
import SmartMoneyItem from './SmartMoneyItem'
import { RootState, useAppDispatch, useAppSelector } from '@/redux/store'
import { useCallback, useEffect, useRef, useState } from 'react'
import { GetSmartMoneyResponse } from '@/types/responses'
import { Skeleton } from '../ui/skeleton'
import { IconEmpty, IconSpinner } from '../icon'
import { SmartMoneyAction } from '@/types/tokenDetail'
import { SmartMoneyFilterType } from '@/types/monitoring.ts'
import { TransactionType } from '@/@generated/gql/graphql-core.ts'
import { TYPE_CHAIN } from '@/lib/blockchain.ts'
import { useSelector } from 'react-redux'
import { setQuickBuyAmount } from '@/redux/modules/quickBuy.slice.ts'
import TradeSettingsBottomSheet from '@components/common/TradeSettingsBottomSheet.tsx'

const buildChainFilter = (chain: string) => {
  switch (chain) {
    case 'eth':
      return 'EVM'
    case 'sol':
      return 'SOLANA'
    case 'tron':
      return 'TRON'
    default:
      return 'ALL'
  }
}

const TokenDetailSmartMoney = () => {
  const { t } = useTranslation()
  const [filter, setFilter] = useState<SmartMoneyFilterType>({
    transactionType: TransactionType.All,
    minAmountUsd: 0,
    address: [],
  })
  const [page, setPage] = useState(1)
  const [allSmartMoneys, setAllSmartMoneys] = useState<SmartMoneyAction[]>([])
  const [hasMore, setHasMore] = useState(true)
  const observer = useRef<IntersectionObserver | null>(null)
  const lastSmartMoneyRef = useRef<HTMLDivElement | null>(null)
  const selectedChain = useAppSelector((state) => state.wallet?.activeChain)
  const [filterChanged, setFilterChanged] = useState(false)
  const activeChain = useAppSelector((state) => state.wallet.activeChain) as TYPE_CHAIN
  const amount = useSelector((state: RootState) => state.quickBuy.amount)
  const dispatch = useAppDispatch()
  const selectedPresetKey = useAppSelector((state) => state.tradeSettings.selectedPreset[activeChain])
  const [openTradeSettings, setOpenTradeSettings] = useState(false)

  const { data, loading } = useQuery<GetSmartMoneyResponse>(getSmartMoneyActions, {
    variables: {
      filter: {
        chain: buildChainFilter(selectedChain),
        page,
        limit: 20,
        walletAddresses: filter.address.length > 0 ? [...filter.address] : undefined,
        minAmountUsd: filter.minAmountUsd,
        transactionType: filter.transactionType,
      },
    },
  })

  useEffect(() => {
    if (page !== 1) {
      setFilterChanged(true)
      setPage(1)
    } else {
      refetchWithFilter()
    }
  }, [filter, selectedChain])

  const refetchWithFilter = () => {
    if (filterChanged) {
      setAllSmartMoneys([])
      setFilterChanged(false)
    }
  }

  useEffect(() => {
    if (!data?.getSmartMoneyActions?.actions || loading) return

    if (page === 1) {
      setAllSmartMoneys(data.getSmartMoneyActions.actions)
    } else {
      setAllSmartMoneys((prev) => [...prev, ...data.getSmartMoneyActions.actions])
    }

    setHasMore(data.getSmartMoneyActions.actions.length === 20)
  }, [data, loading])

  useEffect(() => {
    if (loading) return

    if (observer.current) {
      observer.current.disconnect()
    }

    observer.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loading) {
          setPage((prevPage) => prevPage + 1)
        }
      },
      { threshold: 0.5 },
    )

    if (lastSmartMoneyRef.current) {
      observer.current.observe(lastSmartMoneyRef.current)
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect()
      }
    }
  }, [loading, hasMore, allSmartMoneys.length])

  const handleFilterChange = (newFilter: any) => {
    setFilter(newFilter)
  }

  const renderSmartMoneys = () => {
    if (loading && page === 1) {
      return (
        <div className="space-y-2">
          {Array.from({ length: 10 }).map((_, i) => (
            <Skeleton key={i} className="h-[89px]" />
          ))}
        </div>
      )
    }

    if (!loading && allSmartMoneys.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center h-80">
          <IconEmpty />
          <span className="text-[#FFFFFF80] text-[0.75rem]">{t('detail.myPositions.noData')}</span>
        </div>
      )
    }

    return (
      <>
        <div className="flex flex-col gap-2">
          {allSmartMoneys.map((smartMoney, index) => {
            if (index === allSmartMoneys.length - 1) {
              return (
                <div key={`${smartMoney?.address}-${index}`} ref={lastSmartMoneyRef}>
                  <SmartMoneyItem {...smartMoney} />
                </div>
              )
            }
            return <SmartMoneyItem key={`${smartMoney?.address}-${index}`} {...smartMoney} />
          })}
        </div>
        {loading && page > 1 && (
          <div className="h-[89px] w-full flex justify-center items-center">
            <IconSpinner className="size-4 animate-spin" />
          </div>
        )}
      </>
    )
  }

  const getActiveChainLogo = useCallback((chain: string) => {
    switch (chain) {
      case 'sol':
        return '/images/solana.webp'
      case 'eth':
        return '/images/ether.svg'
      case 'arb':
        return '/images/arbitrum.svg'
      default:
        return ''
    }
  }, [])

  return (
    <Container className="mt-2.5">
      <div className="flex items-center gap-4 mb-2.5">
        <div className="flex-1 rounded-full border border-[#ECECED2E] px-3 py-1.5 gap-2 flex items-center h-9">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="0.4" y="0.4" width="15.2" height="15.2" rx="7.6" stroke="#44403C" strokeWidth="0.8" />
            <path
              d="M4.17431 9.08776L7.96879 3.61926C8.05816 3.49045 8.26024 3.5537 8.26024 3.71047V7.17416C8.26024 7.26252 8.33188 7.33416 8.42024 7.33416H11.5816C11.712 7.33416 11.7877 7.48181 11.7115 7.58765L7.67298 13.1967C7.58221 13.3227 7.38313 13.2585 7.38313 13.1032V9.49898C7.38313 9.41061 7.3115 9.33898 7.22313 9.33898H4.30577C4.17662 9.33898 4.10069 9.19387 4.17431 9.08776Z"
              fill="#44403C"
            />
          </svg>
          <span className="text-white text-[calc(13rem/16)] font-medium pr-2 border-r border-[#ECECED1F] break-keep">
            {t('listCoin.quickBuy')}
          </span>
          <div className="flex-1 flex items-center justify-end">
            <input
              className="text-[calc(13rem/16)] w-full text-right ml-auto"
              value={amount}
              onChange={(e) => {
                const newValue = e.target.value.replace(/[^0-9.]/g, '')
                dispatch(setQuickBuyAmount(newValue))
              }}
            />
          </div>
          <img src={getActiveChainLogo(activeChain)} alt="chain-logo" className="w-[16px] h-[16px] rounded-full" />
        </div>
        <div
          className="border border-[#ECECED14] rounded-full bg-[#ECECED14] flex items-center justify-center h-9 px-3 gap-2 cursor-pointer"
          onClick={() => setOpenTradeSettings(true)}
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M2 6.07292V9.91958C2 11.3329 2 11.3329 3.33333 12.2329L7 14.3529C7.55333 14.6729 8.45333 14.6729 9 14.3529L12.6667 12.2329C14 11.3329 14 11.3329 14 9.92625V6.07292C14 4.66625 14 4.66625 12.6667 3.76625L9 1.64625C8.45333 1.32625 7.55333 1.32625 7 1.64625L3.33333 3.76625C2 4.66625 2 4.66625 2 6.07292Z"
              stroke="#B9B9B9"
              strokeWidth="1.14286"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M8 10C9.10457 10 10 9.10457 10 8C10 6.89543 9.10457 6 8 6C6.89543 6 6 6.89543 6 8C6 9.10457 6.89543 10 8 10Z"
              stroke="#B9B9B9"
              strokeWidth="1.14286"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="text-white text-[calc(14rem/16)] font-medium leading-3.5">P{selectedPresetKey ?? 1}</span>
        </div>
        <div className="hidden">
          <TradeSettingsBottomSheet open={openTradeSettings} setOpen={setOpenTradeSettings} />
        </div>
      </div>
      <div className="flex items-center justify-between">
        <SmartMoneyFilter setFilter={handleFilterChange} />
      </div>
      <div className="mt-2.5 pb-[55px] no-scrollbar">{renderSmartMoneys()}</div>
    </Container>
  )
}

export default TokenDetailSmartMoney
