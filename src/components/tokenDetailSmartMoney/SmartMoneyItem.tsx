import { useTranslation } from 'react-i18next'
import { IconChevronDown } from '../icon'
import { ReactNode, useState } from 'react'
import { DataTable } from '@/pages/home/<USER>'
import { ColumnDef } from '@tanstack/react-table'
import { cn, getPath } from '@/lib/utils'
import useTimeAgoGlobal from '@/hooks/useTimeAgoGlobal'
import { CopyButton } from '../common/copy-button'
import { SmartMoneyAction, SmartMoneyTradeHistories } from '@/types/tokenDetail'
import { formatMarketValue } from '@/lib/format'
import { useNavigate } from 'react-router-dom'
import { APP_PATH, CHAIN_SYMBOLS } from '@/lib/constant'
import {
  formatDecimalLongValue,
  formatWalletName,
  getBlockChainLogo,
  getFirstAndLastFiveChars,
} from '@/utils/helpers.ts'
import { useGetSmartMonetTradeHistories } from '@hooks/useGetSmartMonetTradeHistories.ts'
import { ChainType } from '@/@generated/gql/graphql-core.ts'
import { TransactionType } from '@/types/enums.ts'
import dayjs from 'dayjs'
import { QuickBuyButton } from '@components/discover/QuickBuyButton.tsx'
import { fShortenNumber } from '@/lib/number.ts'

interface NormalHeadProps {
  tKey: string
  children?: ReactNode
  onClick?: () => void
}

const NormalHead = (props: NormalHeadProps) => {
  const { tKey, children, onClick } = props
  const { t } = useTranslation()
  return (
    <div className="text-[calc(11rem/16)] flex items-center text-[#FFFFFF80] cursor-pointer w-max" onClick={onClick}>
      {children ?? t('detail.tokenDetail.' + tKey)}
    </div>
  )
}

const handleTextColor = (type: string) => {
  switch (type) {
    case TransactionType.Buy:
    case TransactionType.AddLiquidity:
    case 'Buy':
      return 'text-[#00FFB4]'
    case TransactionType.Sell:
    case TransactionType.RemoveLiquidity:
    case 'Sell':
      return 'text-[#AB57FF]'
    default:
      return 'text-[#FFFFFF]'
  }
}

const transactionInSixHoursColumns: ColumnDef<any, any>[] = [
  {
    accessorKey: 'timeWalletAddress',
    header: () => <NormalHead tKey="timeWalletAddress" />,
    cell: (props) => {
      const { timestamp, address } = props.row.original
      return (
        <div className="flex flex-col gap-2 text-[calc(1rem*(12/16))] leading-[calc(1rem*(12/16))]">
          <span className="text-[#FFFFFFB2]">{dayjs(timestamp).format('MM/DD hh:mm')}</span>
          <div className="flex gap-1 items-center">
            <span>{getFirstAndLastFiveChars(address)}</span>
            <CopyButton icon="/images/icons/icon-copy.webp" className="w-[10px] min-w-[10px] h-[10px]" text={address} />
          </div>
        </div>
      )
    },
  },
  {
    accessorKey: 'usdAmount',
    header: () => <NormalHead tKey="transactionAmount" />,
    cell: (props) => {
      const { type } = props.row.original as SmartMoneyTradeHistories

      return (
        <span className={cn(handleTextColor(type))}>
          {Number(props.getValue()) > 0 ? `$${formatDecimalLongValue(props.getValue())}` : '--'}
        </span>
      )
    },
  },
  {
    accessorKey: 'holdingRatio',
    header: () => <NormalHead tKey="holdingRatio" />,
    cell: (props) => {
      const { token, amount } = props.row.original as SmartMoneyTradeHistories
      const ratio = token?.totalSupply !== 0 ? (Number(amount) * 100) / Number(token?.totalSupply) : Infinity
      return <span>{isFinite(ratio) ? `${formatDecimalLongValue(ratio, 2)}%` : '--'}</span>
    },
  },
  {
    accessorKey: 'marketValue',
    header: () => <NormalHead tKey="marketValueSpecial" />,
    cell: (props) => {
      const { token, usdPrice } = props.row.original as SmartMoneyTradeHistories
      const marketValue = Number(token?.totalSupply) * Number(usdPrice)
      return <span>{formatMarketValue(marketValue, '$')}</span>
    },
  },
]

const txTypeLabelKeys: Record<string, string> = {
  [TransactionType.Buy]: 'detail.tokenDetail.openPosition',
  [TransactionType.Sell]: 'detail.tokenDetail.decreasePosition',
}

const SmartMoneyItem = (props: SmartMoneyAction) => {
  const { t, i18n } = useTranslation()
  const navigate = useNavigate()

  const [open, setOpen] = useState(false)
  const { address, token, timestamp, transactionInSixHours, txType, baseAmount } = props

  const { data } = useGetSmartMonetTradeHistories({
    skipCondition: !token?.address,
    token: {
      chain: ChainType.Solana,
      address: token?.address,
    },
  })

  const handleOnTokenDetail = () => {
    navigate(
      getPath(APP_PATH.MEME_TOKEN_DETAIL, {
        address: token?.address,
        chain: CHAIN_SYMBOLS[token.chainId!],
      }),
    )
  }

  const timeAgo = useTimeAgoGlobal(timestamp, {
    formatFn: (timestampMs) => {
      const lang = i18n.language || 'en'
      const time = dayjs(timestampMs)
      return time.locale(lang).fromNow()
    },
  })

  return (
    <div
      className={cn(
        'rounded-[6px]',
        txType.toLocaleLowerCase() === TransactionType.Buy ||
          txType.toLocaleLowerCase() === TransactionType.AddLiquidity
          ? 'border-gradient-funding-record-success-item funding-record-success-item-background'
          : 'border-gradient-funding-record-failed-item funding-record-failed-item-background',
      )}
    >
      <div className={cn('p-2 border-[0.5px] rounded-t-[6px]', !transactionInSixHours && 'rounded-b-[6px]')}>
        <span className="text-[calc(1rem*(10/16))] leading-[calc(1rem*(12/16))] text-[#00FFB4] font-normal">
          {timeAgo}
        </span>
        <div className="mt-3 flex items-center">
          <div className="flex gap-[5px] flex-3">
            <img src="/images/tokenDetail/trump.webp" className="size-7 rounded-full" alt="" />
            <div className="flex flex-col justify-center gap-1.5">
              <div className="flex items-center gap-1">
                <span className="text-[calc(1rem*(12/16))] leading-[calc(1rem*(12/16))] font-medium text-[#FFFFFF]">
                  {formatWalletName(address)}
                </span>
                <CopyButton icon="/images/icons/ic-copy.svg" className="!size-3 min-w-3" text={address} />
              </div>
              <div className="flex items-center gap-1">
                <span className={cn('text-[calc(1rem*(11/16))] leading-[calc(1rem*(11/16))]', handleTextColor(txType))}>
                  <span className="bg-[#ECECED14] px-1 rounded-[2px] mr-1">
                    {t(txTypeLabelKeys[txType.toLocaleLowerCase()])}
                  </span>
                  <span className="bg-[#ECECED14] px-1 rounded-[2px] font-semibold">
                    <span className="text-[#FFFFFFCC]">{t('detail.tokenDetail.amount')}</span>{' '}
                    {fShortenNumber(baseAmount)}
                  </span>
                </span>
              </div>
            </div>
          </div>
          <div className="">
            {[TransactionType.Buy, TransactionType.AddLiquidity].includes(
              txType.toLocaleLowerCase() as TransactionType,
            ) ? (
              <img src="/images/icons/in-circle.svg" className="h-6 w-6" alt="buy" />
            ) : (
              <img src="/images/icons/out-circle.svg" className="h-6 w-6" alt="sell" />
            )}
          </div>
          <div className="flex gap-[5px] items-center justify-end flex-3 md:gap-3">
            <div className="flex gap-[5px] items-center">
              <div onClick={handleOnTokenDetail}>
                <img
                  src={token?.info?.logoUrl ?? getBlockChainLogo(token.chainId, token.address)}
                  className="size-7 rounded-[6px] cursor-pointer"
                  alt=""
                />
              </div>
              <div className="flex flex-col justify-center gap-1.5">
                <div className="flex items-center gap-1">
                  <span className="text-[calc(1rem*(12/16))] leading-[calc(1rem*(12/16))] text-[#FFFFFF] truncate">
                    {token?.name.slice(0, 5)}
                  </span>
                  <CopyButton icon="/images/icons/icon-copy.webp" className="!size-3 min-w-3" text={token?.name} />
                </div>
                <span className="text-[calc(1rem*(11/16))] leading-[calc(1rem*(11/16))] text-[#FFFFFF80] break-keep">
                  {t('detail.tokenDetail.marketCap')} --
                </span>
              </div>
            </div>
            <QuickBuyButton
              token={{
                token: token.address,
              }}
            />
          </div>
        </div>
      </div>
      {data?.getSmartMoneyTradeHistories && data?.getSmartMoneyTradeHistories?.length > 0 && (
        <div>
          <div
            className="flex items-center justify-between px-3 py-[10px] bg-[#23232999] rounded-b-[6px] border-[0.5px] border-t-0 border-[#ECECED0A] cursor-pointer"
            onClick={() => setOpen(!open)}
          >
            <div className="flex items-center gap-2">
              <div className="flex -space-x-2">
                {data.getSmartMoneyTradeHistories
                  .filter((_, index) => index < 4)
                  .map((value, indexItem) => (
                    <img
                      key={value?.address + '-' + indexItem}
                      src={
                        value?.token?.logo && value?.token?.logo !== ''
                          ? value?.token?.logo
                          : '/images/tokenDetail/trump.webp'
                      }
                      className="size-5 rounded-full"
                      alt="avatar"
                    />
                  ))}
              </div>
              <span className="text-[calc(1rem*(12/16))] leading-[calc(1rem*(12/16))] text-[#FFFFFF]">
                {t('detail.tokenDetail.recentTrades', { hours: 6, count: data.getSmartMoneyTradeHistories.length })}
              </span>
            </div>
            <IconChevronDown className={`transition-transform duration-300 ${open ? 'rotate-180' : ''}`} />
          </div>
          <div
            className={cn(
              'bg-smart-money-gradient overflow-hidden transition-all duration-300',
              open ? 'max-h-[1000px] mt-2' : 'max-h-0',
            )}
          >
            <DataTable
              columns={transactionInSixHoursColumns}
              data={data.getSmartMoneyTradeHistories}
              containerClassName="overflow-x-auto no-scrollbar border-hidden"
              tableHeadClassName="text-[calc(11rem/16)] leading-[0.75rem] text-[#FFFFFF80] cursor-pointer"
              tableCellClassName="text-[calc(13rem/16)] leading-[0.75rem] font-medium break-keep"
              tableHeaderClassName="border-hidden"
              tableBodyRowClassName="border-hidden"
            />
          </div>
        </div>
      )}
    </div>
  )
}

export default SmartMoneyItem
