import { gql } from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTime: { input: any; output: any; }
  DecimalScalar: { input: any; output: any; }
};

/** Supported provider for authentication */
export enum AuthProvider {
  ChainEvm = 'CHAIN_EVM',
  ChainSol = 'CHAIN_SOL',
  ChainTron = 'CHAIN_TRON',
  Google = 'GOOGLE',
  Telegram = 'TELEGRAM'
}

export type BuilderInput = {
  b: Scalars['String']['input'];
  f: Scalars['Float']['input'];
};

export type CancelActionInput = {
  cancels: Array<CancelInput>;
  type: Scalars['String']['input'];
};

export type CancelInput = {
  a: Scalars['Int']['input'];
  o: Scalars['Float']['input'];
};

/** Supported blockchain types */
export enum ChainType {
  Arb = 'ARB',
  Evm = 'EVM',
  Solana = 'SOLANA',
  Tron = 'TRON'
}

export type GetWalletSubOrgInputDto = {
  /** Address of 3rd wallet */
  address: Scalars['String']['input'];
  chainType: ChainType;
  fingerprint?: InputMaybe<Scalars['String']['input']>;
  referrerCode?: InputMaybe<Scalars['String']['input']>;
  /** Signature of GET_PUBLIC_KEY */
  signature: Scalars['String']['input'];
};

export type GoogleLoginInput = {
  idToken: Scalars['String']['input'];
};

export type InputLoginWalletV2Dto = {
  /** Sub Organization ID */
  organizationId: Scalars['String']['input'];
  /** Stamp header name */
  stampHeaderName: Scalars['String']['input'];
  /** Stamp header value */
  stampHeaderValue: Scalars['String']['input'];
  /** URL for the API call */
  url: Scalars['String']['input'];
};

export type InputPerpetualStatusDto = {
  agentExpiredAt?: InputMaybe<Scalars['Float']['input']>;
  feeBuilderAddress: Scalars['String']['input'];
  feeBuilderPercent: Scalars['Float']['input'];
  referralCode: Scalars['String']['input'];
  setFeeBuilder: Scalars['Boolean']['input'];
  setReferral: Scalars['Boolean']['input'];
};

export type InputSignApproveAgentDto = {
  activityId: Scalars['String']['input'];
  agentAddress: Scalars['String']['input'];
  agentName: Scalars['String']['input'];
  nonce: Scalars['Float']['input'];
};

export type InputSignApproveFeeBuilderDto = {
  activityId: Scalars['String']['input'];
  nonce: Scalars['Float']['input'];
};

export type InputSignApproveReferralDto = {
  activityId: Scalars['String']['input'];
  nonce: Scalars['Float']['input'];
};

export type InputSignCancelOrderDto = {
  action: CancelActionInput;
  nonce: Scalars['Float']['input'];
  vaultAddress?: InputMaybe<Scalars['String']['input']>;
};

export type InputSignCreateOrderDto = {
  action: OrderRequestInput;
  nonce: Scalars['Float']['input'];
  vaultAddress?: InputMaybe<Scalars['String']['input']>;
};

export type InputSignUpdateLeverageDto = {
  action: UpdateLeverageActionInput;
  nonce: Scalars['Float']['input'];
  vaultAddress?: InputMaybe<Scalars['String']['input']>;
};

export type LimitInput = {
  tif: Scalars['String']['input'];
};

export type LoginDto = {
  __typename?: 'LoginDTO';
  accessToken: Scalars['String']['output'];
  fingerprint?: Maybe<Scalars['String']['output']>;
  referrerCode?: Maybe<Scalars['String']['output']>;
  refreshToken: Scalars['String']['output'];
  userId: Scalars['String']['output'];
};

export type LoginV2Dto = {
  __typename?: 'LoginV2DTO';
  accessToken: Scalars['String']['output'];
  fingerprint?: Maybe<Scalars['String']['output']>;
  referrerCode?: Maybe<Scalars['String']['output']>;
  refreshToken: Scalars['String']['output'];
  subOrgId: Scalars['String']['output'];
  turnKeyResponse?: Maybe<TurnKeyLoginResponseDto>;
  userId: Scalars['String']['output'];
};

export type Mutation = {
  __typename?: 'Mutation';
  approveHyperLiquidApproveAgent: SignatureDto;
  approveHyperLiquidFeeBuilder: SignatureDto;
  approveHyperLiquidReferral: SignatureDto;
  checkHyperLiquidWallet: PerpetualStatusDto;
  createWalletSubOrg: SubOrgResponseDto;
  disable2FA: Scalars['Boolean']['output'];
  getAccessToken: RefreshAccessTokenDto;
  loginByTelegram: LoginDto;
  loginByWallet: LoginDto;
  loginByWalletV2: LoginV2Dto;
  loginWithGoogle: LoginDto;
  signHyperLiquidCancelOrder: SignedCancelOrderDto;
  signHyperLiquidCreateOrder: SignedCreateOrderDto;
  signHyperLiquidUpdateLeverage: SignedCancelOrderDto;
  updateHyperLiquidWallet: PerpetualStatusDto;
  updatePreference: Scalars['Boolean']['output'];
};


export type MutationApproveHyperLiquidApproveAgentArgs = {
  input: InputSignApproveAgentDto;
};


export type MutationApproveHyperLiquidFeeBuilderArgs = {
  input: InputSignApproveFeeBuilderDto;
};


export type MutationApproveHyperLiquidReferralArgs = {
  input: InputSignApproveReferralDto;
};


export type MutationCreateWalletSubOrgArgs = {
  input: GetWalletSubOrgInputDto;
};


export type MutationDisable2FaArgs = {
  otpCode: Scalars['String']['input'];
};


export type MutationGetAccessTokenArgs = {
  refreshToken: Scalars['String']['input'];
};


export type MutationLoginByTelegramArgs = {
  code: Scalars['String']['input'];
  fingerprint?: InputMaybe<Scalars['String']['input']>;
  referrerCode?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};


export type MutationLoginByWalletArgs = {
  chainType: ChainType;
  fingerprint?: InputMaybe<Scalars['String']['input']>;
  isOkxWallet?: Scalars['Boolean']['input'];
  message: Scalars['String']['input'];
  referrerCode?: InputMaybe<Scalars['String']['input']>;
  signature: Scalars['String']['input'];
};


export type MutationLoginByWalletV2Args = {
  input: InputLoginWalletV2Dto;
};


export type MutationLoginWithGoogleArgs = {
  input: GoogleLoginInput;
};


export type MutationSignHyperLiquidCancelOrderArgs = {
  input: InputSignCancelOrderDto;
};


export type MutationSignHyperLiquidCreateOrderArgs = {
  input: InputSignCreateOrderDto;
};


export type MutationSignHyperLiquidUpdateLeverageArgs = {
  input: InputSignUpdateLeverageDto;
};


export type MutationUpdateHyperLiquidWalletArgs = {
  input: InputPerpetualStatusDto;
};


export type MutationUpdatePreferenceArgs = {
  input: UpdatePreferenceInput;
};

export type NotificationType = {
  __typename?: 'NotificationType';
  categoryCode: Scalars['String']['output'];
  categoryName: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  deletedAt: Scalars['DateTime']['output'];
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

/** Notification type category code */
export enum NotificationTypeCategoryCode {
  FuturesSignal = 'FuturesSignal',
  Others = 'Others',
  PriceChange = 'PriceChange',
  SmartMoneyActivity = 'SmartMoneyActivity'
}

export type OrderInput = {
  a: Scalars['Float']['input'];
  b: Scalars['Boolean']['input'];
  c?: InputMaybe<Scalars['String']['input']>;
  p: Scalars['String']['input'];
  r: Scalars['Boolean']['input'];
  s: Scalars['String']['input'];
  t: OrderTypeInput;
};

export type OrderRequestInput = {
  builder?: InputMaybe<BuilderInput>;
  grouping?: InputMaybe<Scalars['String']['input']>;
  orders: Array<OrderInput>;
  type: Scalars['String']['input'];
};

export type OrderTypeInput = {
  limit?: InputMaybe<LimitInput>;
  trigger?: InputMaybe<TriggerInput>;
};

export type PerpetualStatusDto = {
  __typename?: 'PerpetualStatusDTO';
  agent?: Maybe<Scalars['String']['output']>;
  agentName: Scalars['String']['output'];
  approvedAgent: Scalars['Boolean']['output'];
  feeBuilderAddress: Scalars['String']['output'];
  feeBuilderPercent: Scalars['Float']['output'];
  referralCode: Scalars['String']['output'];
  setFeeBuilder: Scalars['Boolean']['output'];
  setReferral: Scalars['Boolean']['output'];
};

export type Query = {
  __typename?: 'Query';
  account: User;
  getNonce: Scalars['String']['output'];
  setupNew2FA: SetupNew2FaResponse;
  userSettings: UserSettingsDto;
  verify2FA: Scalars['Boolean']['output'];
  verifyTOTP: Scalars['Boolean']['output'];
};


export type QueryGetNonceArgs = {
  wallAddress: Scalars['String']['input'];
};


export type QueryVerify2FaArgs = {
  code: Scalars['String']['input'];
};


export type QueryVerifyTotpArgs = {
  code: Scalars['String']['input'];
};

export type RefreshAccessTokenDto = {
  __typename?: 'RefreshAccessTokenDTO';
  accessToken: Scalars['String']['output'];
};

export type SetupNew2FaResponse = {
  __typename?: 'SetupNew2FAResponse';
  recoveryCodes: Array<Scalars['String']['output']>;
  secretCode: Scalars['String']['output'];
  uri: Scalars['String']['output'];
};

export type SignatureDto = {
  __typename?: 'SignatureDTO';
  signature: SignatureType;
  userId: Scalars['String']['output'];
};

export type SignatureType = {
  __typename?: 'SignatureType';
  r: Scalars['String']['output'];
  s: Scalars['String']['output'];
  v: Scalars['Int']['output'];
};

export type SignedCancelOrderDto = {
  __typename?: 'SignedCancelOrderDTO';
  signature: SignatureType;
  userId: Scalars['String']['output'];
};

export type SignedCreateOrderDto = {
  __typename?: 'SignedCreateOrderDTO';
  signature: SignatureType;
  userId: Scalars['String']['output'];
};

export type SubOrgResponseDto = {
  __typename?: 'SubOrgResponseDTO';
  subOrgId: Scalars['String']['output'];
  userId: Scalars['String']['output'];
};

export type TriggerInput = {
  isMarket: Scalars['Boolean']['input'];
  tpsl: Scalars['String']['input'];
  triggerPx: Scalars['String']['input'];
};

export type TurnKeyLoginResponseDto = {
  __typename?: 'TurnKeyLoginResponseDto';
  organizationId: Scalars['String']['output'];
  organizationName: Scalars['String']['output'];
  userId: Scalars['String']['output'];
  username: Scalars['String']['output'];
};

export type TwoFactorDto = {
  __typename?: 'TwoFactorDTO';
  isEnabled: Scalars['Boolean']['output'];
  userId: Scalars['String']['output'];
};

export type UpdateLeverageActionInput = {
  asset: Scalars['Int']['input'];
  isCross: Scalars['Boolean']['input'];
  leverage: Scalars['Int']['input'];
  type: Scalars['String']['input'];
};

export type UpdatePreferenceInput = {
  isEnabled: Scalars['Boolean']['input'];
  notificationTypeCode: NotificationTypeCategoryCode;
};

export type User = {
  __typename?: 'User';
  authProvider: AuthProvider;
  avatar?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  googleId?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  userEmbeddedWallets: Array<UserEmbeddedWalletDto>;
  userManagedWallets: Array<UserManagedWalletDto>;
  walletAddress?: Maybe<Scalars['String']['output']>;
};

export type UserEmbeddedWalletDto = {
  __typename?: 'UserEmbeddedWalletDTO';
  balance: Scalars['Float']['output'];
  chain: ChainType;
  id: Scalars['String']['output'];
  walletAccountId: Scalars['String']['output'];
  walletAddress: Scalars['String']['output'];
  walletId: Scalars['String']['output'];
};

export type UserManagedWalletDto = {
  __typename?: 'UserManagedWalletDTO';
  balance: Scalars['Float']['output'];
  chain: ChainType;
  id: Scalars['String']['output'];
  walletAddress: Scalars['String']['output'];
};

export type UserNotificationPreferenceDto = {
  __typename?: 'UserNotificationPreferenceDTO';
  channel: Scalars['String']['output'];
  id: Scalars['String']['output'];
  isEnabled: Scalars['Boolean']['output'];
  notificationTypeCode: Scalars['String']['output'];
  userId: Scalars['String']['output'];
};

export type UserSettingsDto = {
  __typename?: 'UserSettingsDTO';
  googleAuthenticator?: Maybe<TwoFactorDto>;
  id: Scalars['String']['output'];
  notificationPreferences: Array<UserNotificationPreferenceDto>;
  withdrawalWhitelistAddresses: Array<UserWithdrawalWhitelistAddressDto>;
};

export type UserWithdrawalWhitelistAddressDto = {
  __typename?: 'UserWithdrawalWhitelistAddressDTO';
  address: Scalars['String']['output'];
  id: Scalars['String']['output'];
  nickname?: Maybe<Scalars['String']['output']>;
  userId: Scalars['String']['output'];
};
