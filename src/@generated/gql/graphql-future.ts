import { gql } from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  Decimal: { input: any; output: any; }
  Int64: { input: any; output: any; }
  JSON: { input: any; output: any; }
  Time: { input: any; output: any; }
};

/** Supported blockchain types */
export enum ChainType {
  Eth = 'ETH',
  Evm = 'EVM',
  Solana = 'SOLANA',
  Tron = 'TRON'
}

export type Query = {
  __typename?: 'Query';
  getSmartMoneyInfo?: Maybe<SmartMoneyInfo>;
  getSmartMoneyStatistic?: Maybe<SmartMoneyDetailResp>;
  getSmartMoneyTokenStatistics?: Maybe<Array<Maybe<SmartMoneyTokenStatisticResp>>>;
  getSmartMoneyTradeHistories?: Maybe<Array<Maybe<SmartMoneyTradeHistoryResp>>>;
  getSmartMoneyTxHistories?: Maybe<Array<Maybe<SmartMoneyTxHistoryResp>>>;
};


export type QueryGetSmartMoneyInfoArgs = {
  req: SmartMoneyInfoReq;
};


export type QueryGetSmartMoneyStatisticArgs = {
  req: SmartMoneyDetailReq;
};


export type QueryGetSmartMoneyTokenStatisticsArgs = {
  req: SmartMoneyTokenStatisticReq;
};


export type QueryGetSmartMoneyTradeHistoriesArgs = {
  req: SmartMoneyTradeHistoryReq;
};


export type QueryGetSmartMoneyTxHistoriesArgs = {
  req: SmartMoneyTxHistoryReq;
};

export type SmartMoneyDetailReq = {
  /** wallet address */
  address: Scalars['String']['input'];
  chain: ChainType;
  /** the time range in days used for statistics or data filtering (e.g., past 1, 7, or 30 days) */
  dayDuration: Scalars['Int']['input'];
};

export type SmartMoneyDetailResp = {
  __typename?: 'SmartMoneyDetailResp';
  /** The average cost buy tokens (buyAmountUsd / (number of token has tx in time range)) */
  avgBuyAmountUsd: Scalars['Float']['output'];
  /** The average duration that tokens hold of the wallet */
  avgHoldDuration: Scalars['Int64']['output'];
  /** The average profit and loss (Pnl) per token in time range */
  avgRealizedPnlUsd: Scalars['Float']['output'];
  /** Total amount spent on purchasing all tokens within the time range. */
  buyAmountUsd: Scalars['Float']['output'];
  /** data for share wallet feature */
  buyAmountUsd30D: Scalars['Float']['output'];
  /** number of buy tx in time range */
  buys: Scalars['Int']['output'];
  /** data for share wallet feature */
  buys30D: Scalars['Int']['output'];
  /** num of token has 200% < pnl percent <= 500% */
  pnl2xTo5xNum: Scalars['Int']['output'];
  /** num of token has pnl percent > 500% */
  pnlGt5xNum: Scalars['Int']['output'];
  /** num of token has 0% < pnl percent <= 200% */
  pnlLt2xNum: Scalars['Int']['output'];
  /** num of token has pnl percent <= -50% */
  pnlLtMinusDot5Num: Scalars['Int']['output'];
  /** num of token has -50% < pnl percent <= 0% */
  pnlMinusDot5To0xNum: Scalars['Int']['output'];
  /** Realized profit and loss (PnL) over the selected time range, calculated in USD. */
  realizedPnlUsd: Scalars['Float']['output'];
  /** data for share wallet feature */
  realizedPnlUsd30D: Scalars['Float']['output'];
  /** number of sell tx in time range */
  sells: Scalars['Int']['output'];
  /** data for share wallet feature */
  sells30D: Scalars['Int']['output'];
  /** token holding info */
  tokenHoldings: Array<Maybe<TokenHolding>>;
  totalBuyUsd: Scalars['Float']['output'];
  /** Total realized profit and loss (PnL), calculated in USD. */
  totalRealizedPnlUsd: Scalars['Float']['output'];
};

export type SmartMoneyInfo = {
  __typename?: 'SmartMoneyInfo';
  address: Scalars['String']['output'];
  avatar?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  tags?: Maybe<Array<Maybe<SmartMoneyType>>>;
};

export type SmartMoneyInfoReq = {
  address: Scalars['String']['input'];
  chain: ChainType;
};

export type SmartMoneyTokenStatisticReq = {
  address: Scalars['String']['input'];
  chain: ChainType;
  isOnlyHolding: Scalars['Boolean']['input'];
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  sortBy?: InputMaybe<SortTokenHolding>;
  sortOrder?: InputMaybe<SortDirection>;
};

export type SmartMoneyTokenStatisticResp = {
  __typename?: 'SmartMoneyTokenStatisticResp';
  avgPriceUsd: Scalars['Decimal']['output'];
  balance: Scalars['Float']['output'];
  buys: Scalars['Int']['output'];
  holdingDuration: Scalars['Int64']['output'];
  lastTxTime: Scalars['Int64']['output'];
  realizedPnlUsd: Scalars['Float']['output'];
  sells: Scalars['Int']['output'];
  token: Token;
  totalBuyAmountUsd: Scalars['Decimal']['output'];
  totalBuyQuantity: Scalars['Decimal']['output'];
  totalSellAmountUsd: Scalars['Decimal']['output'];
  totalSellQuantity: Scalars['Decimal']['output'];
};

export type SmartMoneyTradeHistoryReq = {
  duration?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  token: TokenReq;
};

export type SmartMoneyTradeHistoryResp = {
  __typename?: 'SmartMoneyTradeHistoryResp';
  /** wallet address */
  address: Scalars['String']['output'];
  amount: Scalars['Decimal']['output'];
  timestamp: Scalars['Int64']['output'];
  token: Token;
  transactionHash: Scalars['String']['output'];
  type: TransactionType;
  usdAmount: Scalars['Decimal']['output'];
  usdPrice: Scalars['Decimal']['output'];
};

export type SmartMoneyTxHistoryReq = {
  address: Scalars['String']['input'];
  chain: ChainType;
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  sortBy?: InputMaybe<SortTransactionHistory>;
  sortOrder?: InputMaybe<SortDirection>;
  tokenAddress?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Array<InputMaybe<TransactionType>>>;
};

export type SmartMoneyTxHistoryResp = {
  __typename?: 'SmartMoneyTxHistoryResp';
  avgPriceUsd: Scalars['Decimal']['output'];
  nativeAmount: Scalars['Decimal']['output'];
  nativePrice: Scalars['Decimal']['output'];
  price: Scalars['Decimal']['output'];
  quantity: Scalars['Decimal']['output'];
  timestamp: Scalars['Int64']['output'];
  token: Token;
  transactionHash: Scalars['String']['output'];
  type: TransactionType;
  usdAmount: Scalars['Decimal']['output'];
  usdPrice: Scalars['Decimal']['output'];
};

export enum SmartMoneyType {
  Fresh = 'Fresh',
  Kol = 'KOL',
  PumpSm = 'PumpSM',
  SmartMoney = 'SmartMoney',
  Sniper = 'Sniper',
  TopTrader = 'TopTrader',
  Whale = 'Whale'
}

export enum SortDirection {
  Asc = 'asc',
  Desc = 'desc'
}

export enum SortTokenHolding {
  LastTxTime = 'lastTxTime',
  RealizedPnl = 'realizedPnl'
}

export enum SortTransactionHistory {
  Timestamp = 'timestamp'
}

export type Token = {
  __typename?: 'Token';
  address: Scalars['String']['output'];
  logo: Scalars['String']['output'];
  name: Scalars['String']['output'];
  totalSupply: Scalars['Decimal']['output'];
};

export type TokenHolding = {
  __typename?: 'TokenHolding';
  address: Scalars['String']['output'];
  avgPriceUsd: Scalars['Decimal']['output'];
  balance: Scalars['Float']['output'];
};

export type TokenReq = {
  address: Scalars['String']['input'];
  chain: ChainType;
};

export enum TransactionType {
  Add = 'add',
  Buy = 'buy',
  Remove = 'remove',
  Sell = 'sell'
}
