import { gql } from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  Decimal: { input: any; output: any; }
  JSON: { input: any; output: any; }
  Time: { input: any; output: any; }
};

export type AlertSymbolSetting = {
  __typename?: 'AlertSymbolSetting';
  isActivated: Scalars['Boolean']['output'];
  isReminderOnce: Scalars['Boolean']['output'];
  settingId: Scalars['String']['output'];
  symbol: Scalars['String']['output'];
  triggerValue: Scalars['Float']['output'];
  type: NotificationTypeEnum;
};

export type Asset = {
  __typename?: 'Asset';
  address: Scalars['String']['output'];
  blockchain: Scalars['String']['output'];
  symbol: Scalars['String']['output'];
};

export type BridgeExtra = {
  __typename?: 'BridgeExtra';
  destTx: Scalars['String']['output'];
  requireRefundAction: Scalars['Boolean']['output'];
  srcTx: Scalars['String']['output'];
};

export type CallBackRequest = {
  requestId: Scalars['String']['input'];
  step: Scalars['Int']['input'];
  txHash: Scalars['String']['input'];
};

export type CallBackResponse = {
  __typename?: 'CallBackResponse';
  success: Scalars['String']['output'];
};

export type CategoryResponse = {
  __typename?: 'CategoryResponse';
  categories: Array<Maybe<Scalars['String']['output']>>;
};

export type CheckApprovalRequest = {
  requestId: Scalars['String']['input'];
  txHash: Scalars['String']['input'];
};

export type CheckApprovalResponse = {
  __typename?: 'CheckApprovalResponse';
  currentApprovedAmount: Scalars['String']['output'];
  isApproved: Scalars['Boolean']['output'];
  requiredApprovedAmount: Scalars['String']['output'];
  txStatus: Scalars['String']['output'];
};

export type CheckStatusRequest = {
  requestId: Scalars['String']['input'];
  step: Scalars['Int']['input'];
  txHash: Scalars['String']['input'];
};

export type CheckStatusResponse = {
  __typename?: 'CheckStatusResponse';
  bridgeExtra: BridgeExtra;
  diagnosisUrl?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  errorCode?: Maybe<Scalars['Int']['output']>;
  explorerUrl?: Maybe<Array<Maybe<ExplorerInfo>>>;
  extraMessage?: Maybe<Scalars['String']['output']>;
  failedType?: Maybe<Scalars['String']['output']>;
  newTx?: Maybe<CheckStatusTx>;
  outputAmount: Scalars['String']['output'];
  outputToken: OutputToken;
  outputType: Scalars['String']['output'];
  referrals?: Maybe<Array<Maybe<Referral>>>;
  status: Scalars['String']['output'];
  steps?: Maybe<Scalars['JSON']['output']>;
  timestamp: Scalars['Int']['output'];
  traceId?: Maybe<Scalars['Int']['output']>;
};

export type CheckStatusTx = {
  __typename?: 'CheckStatusTx';
  data: Scalars['String']['output'];
  gasLimit: Scalars['String']['output'];
  gasPrice: Scalars['String']['output'];
  nonce: Scalars['String']['output'];
  to: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type ConfirmRouteRequest = {
  destination: Scalars['String']['input'];
  requestId: Scalars['String']['input'];
  selectedWallets: Array<SelectedWallet>;
};

export type ConfirmRouteResponse = {
  __typename?: 'ConfirmRouteResponse';
  requestId: Scalars['String']['output'];
  routeErr?: Maybe<Scalars['String']['output']>;
  status: Scalars['String']['output'];
};

export type CreateAlertSymbolSettingRequest = {
  isReminderOnce: Scalars['Boolean']['input'];
  note?: InputMaybe<Scalars['String']['input']>;
  symbol: Scalars['String']['input'];
  type: NotificationTypeEnum;
  value: Scalars['Float']['input'];
};

export type CreateAlertSymbolSettingResponse = {
  __typename?: 'CreateAlertSymbolSettingResponse';
  error?: Maybe<Scalars['String']['output']>;
  settingId: Scalars['String']['output'];
  status: Scalars['String']['output'];
};

export type CreateTxRequest = {
  requestId: Scalars['String']['input'];
  step: Scalars['Int']['input'];
  userSettings: UserSettingsRequest;
  validations: ValidationsRequest;
};

export type CreateTxResponse = {
  __typename?: 'CreateTxResponse';
  error?: Maybe<Scalars['String']['output']>;
  errorCode: Scalars['Int']['output'];
  ok: Scalars['Boolean']['output'];
  traceId: Scalars['Int']['output'];
  transaction?: Maybe<Transaction>;
};

export type DeleteAlertSymbolSettingRequest = {
  settingIds: Array<Scalars['String']['input']>;
};

export type DeleteAlertSymbolSettingResponse = {
  __typename?: 'DeleteAlertSymbolSettingResponse';
  error?: Maybe<Scalars['String']['output']>;
  status: Scalars['String']['output'];
};

export type ExplorerInfo = {
  __typename?: 'ExplorerInfo';
  description: Scalars['String']['output'];
  url: Scalars['String']['output'];
};

export type FavoriteSymbolsResponse = {
  __typename?: 'FavoriteSymbolsResponse';
  list: Array<Maybe<Symbol>>;
};

export type Fee = {
  __typename?: 'Fee';
  amount: Scalars['String']['output'];
  asset: Asset;
  expenseType: Scalars['String']['output'];
  meta?: Maybe<Meta>;
  name: Scalars['String']['output'];
  price: Scalars['Float']['output'];
};

export type FromTo = {
  __typename?: 'FromTo';
  address: Scalars['String']['output'];
  blockchain: Scalars['String']['output'];
  symbol: Scalars['String']['output'];
};

export type GetAlertSymbolSettingResponse = {
  __typename?: 'GetAlertSymbolSettingResponse';
  settings: Array<Maybe<AlertSymbolSetting>>;
};

export type GetAllPossibleRoutesRequest = {
  amount: Scalars['String']['input'];
  fromBlockchain: Scalars['String']['input'];
  fromSymbol: Scalars['String']['input'];
  fromTokenAddress?: InputMaybe<Scalars['String']['input']>;
  slippage: Scalars['String']['input'];
  toBlockchain: Scalars['String']['input'];
  toSymbol: Scalars['String']['input'];
  toTokenAddress?: InputMaybe<Scalars['String']['input']>;
};

export type GetAllPossibleRoutesResponse = {
  __typename?: 'GetAllPossibleRoutesResponse';
  diagnosisMessages: Array<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  errorCode: Scalars['Int']['output'];
  from: FromTo;
  requestAmount: Scalars['String']['output'];
  results: Array<Result>;
  routeId: Scalars['String']['output'];
  to: FromTo;
  traceId: Scalars['Int']['output'];
};

export type GetBestRouteRequest = {
  amount: Scalars['String']['input'];
  fromAddress: Scalars['String']['input'];
  fromBlockChain: Scalars['String']['input'];
  fromSymbol: Scalars['String']['input'];
  fromTokenAddress?: InputMaybe<Scalars['String']['input']>;
  toAddress: Scalars['String']['input'];
  toBlockChain: Scalars['String']['input'];
  toSymbol: Scalars['String']['input'];
  toTokenAddress?: InputMaybe<Scalars['String']['input']>;
};

export type GetBestRouteResponse = {
  __typename?: 'GetBestRouteResponse';
  diagnosisMessages: Array<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  errorCode?: Maybe<Scalars['String']['output']>;
  from: FromTo;
  missingBlockchains: Array<Scalars['String']['output']>;
  requestAmount: Scalars['String']['output'];
  requestId: Scalars['String']['output'];
  result: Result;
  to: FromTo;
  traceId?: Maybe<Scalars['String']['output']>;
  validationStatus: Scalars['String']['output'];
  walletNotSupportingFromBlockchain: Scalars['Boolean']['output'];
};

export type GetSignalResponse = {
  __typename?: 'GetSignalResponse';
  signals: Array<Signal>;
  total: Scalars['Int']['output'];
};

export type InternalSwaps = {
  __typename?: 'InternalSwaps';
  estimatedTimeInSeconds: Scalars['Int']['output'];
  fee: Array<Fee>;
  from: TokenInfo;
  fromAmount: Scalars['String']['output'];
  fromAmountMaxValue?: Maybe<Scalars['String']['output']>;
  fromAmountMinValue?: Maybe<Scalars['String']['output']>;
  fromAmountPrecision: Scalars['String']['output'];
  fromAmountRestrictionType?: Maybe<Scalars['String']['output']>;
  includesDestinationTx: Scalars['Boolean']['output'];
  internalSwaps: Array<InternalSwaps>;
  maxRequiredSign: Scalars['Int']['output'];
  recommendedSlippage: Scalars['String']['output'];
  routes: Array<Route>;
  swapChainType: Scalars['String']['output'];
  swapperId: Scalars['String']['output'];
  swapperLogo: Scalars['String']['output'];
  swapperType: Scalars['String']['output'];
  timeStat: Scalars['String']['output'];
  to: TokenInfo;
  toAmount: Scalars['String']['output'];
  warnings: Scalars['String']['output'];
};

export type Leverage = {
  __typename?: 'Leverage';
  type: Scalars['String']['output'];
  value: Scalars['Int']['output'];
};

export type Meta = {
  __typename?: 'Meta';
  gasLimit: Scalars['String']['output'];
  gasPrice: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export type Mutation = {
  __typename?: 'Mutation';
  callBack: CallBackResponse;
  confirmRoute: ConfirmRouteResponse;
  createAlertSymbolSetting: CreateAlertSymbolSettingResponse;
  createTx: CreateTxResponse;
  deleteAlertSymbolSetting: DeleteAlertSymbolSettingResponse;
  signTx: SignTxResponse;
  storeTxInformation: StoreTxInformationResponse;
  updateAlertSymbolSetting: UpdateAlertSymbolSettingResponse;
  updateUserSymbolPreference: UserSymbolPreferenceResponse;
  upsertFavoriteSymbol: UpsertFavoriteSymbolResponse;
};


export type MutationCallBackArgs = {
  input: CallBackRequest;
};


export type MutationConfirmRouteArgs = {
  input: ConfirmRouteRequest;
};


export type MutationCreateAlertSymbolSettingArgs = {
  input: CreateAlertSymbolSettingRequest;
};


export type MutationCreateTxArgs = {
  input: CreateTxRequest;
};


export type MutationDeleteAlertSymbolSettingArgs = {
  input: DeleteAlertSymbolSettingRequest;
};


export type MutationSignTxArgs = {
  input: CreateTxRequest;
};


export type MutationStoreTxInformationArgs = {
  input: StoreTxInformationRequest;
};


export type MutationUpdateAlertSymbolSettingArgs = {
  input: UpdateAlertSymbolSettingRequest;
};


export type MutationUpdateUserSymbolPreferenceArgs = {
  input: UpdateUserSymbolPreferenceRequest;
};


export type MutationUpsertFavoriteSymbolArgs = {
  input: UpsertFavoriteSymbolRequest;
};

export type NewSymbolResponse = {
  __typename?: 'NewSymbolResponse';
  list: Array<Maybe<Scalars['String']['output']>>;
};

export type Node = {
  __typename?: 'Node';
  inputAmount: Scalars['String']['output'];
  marketId: Scalars['String']['output'];
  marketName: Scalars['String']['output'];
  outputAmount: Scalars['String']['output'];
  percent: Scalars['Float']['output'];
  pools: Array<Scalars['String']['output']>;
};

export type Nodes = {
  __typename?: 'Nodes';
  from: Scalars['String']['output'];
  fromAddress: Scalars['String']['output'];
  fromBlockchain: Scalars['String']['output'];
  fromLogo: Scalars['String']['output'];
  nodes: Array<Node>;
  to: Scalars['String']['output'];
  toAddress: Scalars['String']['output'];
  toBlockchain: Scalars['String']['output'];
  toLogo: Scalars['String']['output'];
};

export enum NotificationTypeEnum {
  Percent24hDecline = 'percent24hDecline',
  Percent24hIncrease = 'percent24hIncrease',
  PriceFell = 'priceFell',
  PriceRise = 'priceRise'
}

export type Ohlc = {
  __typename?: 'OHLC';
  close: Scalars['Float']['output'];
  high: Scalars['Float']['output'];
  low: Scalars['Float']['output'];
  open: Scalars['Float']['output'];
  timestamp: Scalars['Int']['output'];
  volume: Scalars['Float']['output'];
};

export enum OhlcIntervalEnum {
  EightHours = 'EIGHT_HOURS',
  FifteenMinutes = 'FIFTEEN_MINUTES',
  FiveMinutes = 'FIVE_MINUTES',
  FourHours = 'FOUR_HOURS',
  OneDay = 'ONE_DAY',
  OneHour = 'ONE_HOUR',
  OneMinute = 'ONE_MINUTE',
  OneMonth = 'ONE_MONTH',
  OneWeek = 'ONE_WEEK',
  ThirtyMinutes = 'THIRTY_MINUTES',
  ThreeDays = 'THREE_DAYS',
  ThreeMonths = 'THREE_MONTHS',
  TwelveHours = 'TWELVE_HOURS',
  TwoHours = 'TWO_HOURS'
}

export type OhlcRequest = {
  fromTimeStamp?: InputMaybe<Scalars['Int']['input']>;
  interval: OhlcIntervalEnum;
  limit: Scalars['Int']['input'];
  symbol: Scalars['String']['input'];
};

export type OhlcResponse = {
  __typename?: 'OHLCResponse';
  data: Array<Ohlc>;
  symbol: Scalars['String']['output'];
};

export type OpenOrder = {
  __typename?: 'OpenOrder';
  direction: Scalars['String']['output'];
  orderPx: Scalars['Float']['output'];
  originSize: Scalars['Float']['output'];
  reduceOnly: Scalars['Boolean']['output'];
  size: Scalars['Float']['output'];
  symbol: Scalars['String']['output'];
  time: Scalars['Int']['output'];
  triggerPx: Scalars['Float']['output'];
  type: Scalars['String']['output'];
};

export type OutputToken = {
  __typename?: 'OutputToken';
  address: Scalars['String']['output'];
  blockchain: Scalars['String']['output'];
  coinSource?: Maybe<Scalars['String']['output']>;
  coinSourceUrl?: Maybe<Scalars['String']['output']>;
  decimals: Scalars['Int']['output'];
  image: Scalars['String']['output'];
  isPopular: Scalars['Boolean']['output'];
  isSecondaryCoin: Scalars['Boolean']['output'];
  name?: Maybe<Scalars['String']['output']>;
  supportedSwappers: Array<Scalars['String']['output']>;
  symbol: Scalars['String']['output'];
  usdPrice: Scalars['Float']['output'];
};

export type PopularSymbolRequest = {
  number: Scalars['Int']['input'];
};

export type PopularSymbolResponse = {
  __typename?: 'PopularSymbolResponse';
  list: Array<Maybe<Symbol>>;
};

export type Position = {
  __typename?: 'Position';
  entryPx: Scalars['Float']['output'];
  fundingFee: Scalars['Float']['output'];
  leverage: Leverage;
  liqPx: Scalars['Float']['output'];
  margin: Scalars['Float']['output'];
  markPx: Scalars['Float']['output'];
  openInterest: Scalars['Float']['output'];
  pnl: Scalars['Float']['output'];
  pnlPercent: Scalars['Float']['output'];
  positionValue: Scalars['Float']['output'];
  size: Scalars['Float']['output'];
  symbol: Scalars['String']['output'];
  time: Scalars['Int']['output'];
};

export type Query = {
  __typename?: 'Query';
  checkApproval: CheckApprovalResponse;
  checkStatus: CheckStatusResponse;
  getAlertSymbolsSetting: GetAlertSymbolSettingResponse;
  getAllPossibleRoutes: GetAllPossibleRoutesResponse;
  getBestRoute: GetBestRouteResponse;
  getCategory: CategoryResponse;
  getExchangeMeta: RangoMetaResponse;
  getFavoriteSymbols: FavoriteSymbolsResponse;
  getHistories: RangoHistoryResponse;
  getNewSymbol: NewSymbolResponse;
  getOHLC: OhlcResponse;
  getPopularSymbol: PopularSymbolResponse;
  getSignals: GetSignalResponse;
  getSymbolDetail: SymbolDetailResponse;
  getSymbolList: SymbolListResponse;
  getTradeOrders: TradeOrdersResponse;
  getUserBalance: UserBalanceResponse;
  getUserInfo: UserInfoResponse;
  getUserOpenOrder: UserOpenOrderResponse;
  getUserPosition: UserPositionResponse;
  getUserSymbolPreference: UserSymbolPreferenceResponse;
  getUserTradeHistory: UserTradeHistoryResponse;
  getVaultPositions: VaultPositionsResponse;
  searchSymbol: SearchSymbolResponse;
};


export type QueryCheckApprovalArgs = {
  input: CheckApprovalRequest;
};


export type QueryCheckStatusArgs = {
  input: CheckStatusRequest;
};


export type QueryGetAllPossibleRoutesArgs = {
  input: GetAllPossibleRoutesRequest;
};


export type QueryGetBestRouteArgs = {
  input: GetBestRouteRequest;
};


export type QueryGetHistoriesArgs = {
  input: RangoHistoryRequest;
};


export type QueryGetOhlcArgs = {
  input: OhlcRequest;
};


export type QueryGetPopularSymbolArgs = {
  input: PopularSymbolRequest;
};


export type QueryGetSymbolDetailArgs = {
  input: SymbolDetailRequest;
};


export type QueryGetSymbolListArgs = {
  input: SymbolListRequest;
};


export type QueryGetTradeOrdersArgs = {
  input: TradeOrdersRequest;
};


export type QueryGetUserInfoArgs = {
  input: UserInfoRequest;
};


export type QueryGetUserOpenOrderArgs = {
  input: UserOpenOrderRequest;
};


export type QueryGetUserSymbolPreferenceArgs = {
  input: UserSymbolPreferenceRequest;
};


export type QueryGetVaultPositionsArgs = {
  input: VaultPositionsRequest;
};


export type QuerySearchSymbolArgs = {
  input: SearchSymbolRequest;
};

export type RangoHistory = {
  __typename?: 'RangoHistory';
  diagnosisMessages?: Maybe<Scalars['String']['output']>;
  failReason?: Maybe<Scalars['String']['output']>;
  fromAddress: Scalars['String']['output'];
  fromBlockchain: Scalars['String']['output'];
  fromSymbol: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  missingBlockchains?: Maybe<Scalars['String']['output']>;
  outputAmount: Scalars['String']['output'];
  requestAmount: Scalars['String']['output'];
  requestId: Scalars['String']['output'];
  resultType: Scalars['String']['output'];
  status: Scalars['String']['output'];
  step: Scalars['Int']['output'];
  swaps?: Maybe<Array<RangoSwaps>>;
  toAddress: Scalars['String']['output'];
  toBlockchain: Scalars['String']['output'];
  toSymbol: Scalars['String']['output'];
  userAddress: Scalars['String']['output'];
  validationStatus: Scalars['String']['output'];
  walletNotSupportingFromBlockchain: Scalars['Boolean']['output'];
};

export type RangoHistoryRequest = {
  address: Scalars['String']['input'];
  blockchain: Scalars['String']['input'];
  endTime?: InputMaybe<Scalars['Int']['input']>;
  page: Scalars['Int']['input'];
  pageSize: Scalars['Int']['input'];
  startTime?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
};

export type RangoHistoryResponse = {
  __typename?: 'RangoHistoryResponse';
  list: Array<RangoHistory>;
  total: Scalars['Int']['output'];
};

export type RangoMetaChains = {
  __typename?: 'RangoMetaChains';
  addressPatterns?: Maybe<Scalars['String']['output']>;
  chainId?: Maybe<Scalars['String']['output']>;
  color?: Maybe<Scalars['String']['output']>;
  defaultDecimals?: Maybe<Scalars['Int']['output']>;
  displayName?: Maybe<Scalars['String']['output']>;
  enabled?: Maybe<Scalars['Boolean']['output']>;
  feeAssets?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  info?: Maybe<Scalars['String']['output']>;
  logo?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  shortName?: Maybe<Scalars['String']['output']>;
  sort?: Maybe<Scalars['Int']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

export type RangoMetaResponse = {
  __typename?: 'RangoMetaResponse';
  blockchains?: Maybe<Array<Maybe<RangoMetaChains>>>;
  popularTokens?: Maybe<Array<Maybe<RangoMetaTokens>>>;
  swappers?: Maybe<Array<Maybe<RangoMetaSwappers>>>;
  tokens?: Maybe<Array<Maybe<RangoMetaTokensWithChain>>>;
};

export type RangoMetaSwappers = {
  __typename?: 'RangoMetaSwappers';
  enabled?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['ID']['output'];
  logo?: Maybe<Scalars['String']['output']>;
  swapperGroup?: Maybe<Scalars['String']['output']>;
  swapperId: Scalars['String']['output'];
  title?: Maybe<Scalars['String']['output']>;
  types?: Maybe<Scalars['String']['output']>;
};

export type RangoMetaTokens = {
  __typename?: 'RangoMetaTokens';
  address: Scalars['String']['output'];
  blockChain: Scalars['String']['output'];
  coinSource?: Maybe<Scalars['String']['output']>;
  coinSourceUrl?: Maybe<Scalars['String']['output']>;
  decimals?: Maybe<Scalars['Int']['output']>;
  id: Scalars['ID']['output'];
  image?: Maybe<Scalars['String']['output']>;
  isPopular?: Maybe<Scalars['Boolean']['output']>;
  isSecondaryCoin?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  supportedSwappers?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  symbol: Scalars['String']['output'];
  usdPrice?: Maybe<Scalars['Float']['output']>;
};

export type RangoMetaTokensWithChain = {
  __typename?: 'RangoMetaTokensWithChain';
  address: Scalars['String']['output'];
  blockChain: Scalars['String']['output'];
  chainDetail?: Maybe<RangoMetaChains>;
  coinSource?: Maybe<Scalars['String']['output']>;
  coinSourceUrl?: Maybe<Scalars['String']['output']>;
  decimals?: Maybe<Scalars['Int']['output']>;
  id: Scalars['ID']['output'];
  image?: Maybe<Scalars['String']['output']>;
  isPopular?: Maybe<Scalars['Boolean']['output']>;
  isSecondaryCoin?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  supportedSwappers?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  symbol: Scalars['String']['output'];
  usdPrice?: Maybe<Scalars['Float']['output']>;
};

export type RangoSwaps = {
  __typename?: 'RangoSwaps';
  callData?: Maybe<Scalars['String']['output']>;
  callDataHash?: Maybe<Scalars['String']['output']>;
  estimatedTimeInSeconds: Scalars['Int']['output'];
  fee?: Maybe<Scalars['String']['output']>;
  fromAmount: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  maxRequiredSign: Scalars['Int']['output'];
  requestId: Scalars['String']['output'];
  step: Scalars['Int']['output'];
  swapChainType: Scalars['String']['output'];
  swapperId: Scalars['String']['output'];
  swapperLogo?: Maybe<Scalars['String']['output']>;
  swapperType: Scalars['String']['output'];
  toAmount: Scalars['String']['output'];
  txHash?: Maybe<Scalars['String']['output']>;
  userAddress: Scalars['String']['output'];
};

export type RecommendedSlippage = {
  __typename?: 'RecommendedSlippage';
  error: Scalars['Boolean']['output'];
  slippage: Scalars['String']['output'];
};

export type Referral = {
  __typename?: 'Referral';
  address?: Maybe<Scalars['String']['output']>;
  amount: Scalars['String']['output'];
  blockChain: Scalars['String']['output'];
  decimals: Scalars['Int']['output'];
  symbol: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export type Result = {
  __typename?: 'Result';
  missingBlockchains: Array<Scalars['String']['output']>;
  outputAmount: Scalars['String']['output'];
  priceImpactUsd: Scalars['String']['output'];
  priceImpactUsdPercent: Scalars['String']['output'];
  requestId: Scalars['String']['output'];
  resultType: Scalars['String']['output'];
  scores: Array<Score>;
  swaps: Array<Swap>;
  tags: Array<Tag>;
  walletNotSupportingFromBlockchain: Scalars['Boolean']['output'];
};

export type Route = {
  __typename?: 'Route';
  nodes: Array<Nodes>;
};

export type Score = {
  __typename?: 'Score';
  preferenceType: Scalars['String']['output'];
  score: Scalars['Int']['output'];
};

export type SearchSymbolRequest = {
  filter: Scalars['String']['input'];
};

export type SearchSymbolResponse = {
  __typename?: 'SearchSymbolResponse';
  list: Array<Maybe<Symbol>>;
};

export type SelectedWallet = {
  address: Scalars['String']['input'];
  blockchain: Scalars['String']['input'];
};

export type SignTxResponse = {
  __typename?: 'SignTxResponse';
  blockChain: Scalars['String']['output'];
  data: Scalars['String']['output'];
  from: Scalars['String']['output'];
  gasLimit: Scalars['String']['output'];
  gasPrice: Scalars['String']['output'];
  maxFeePerGas: Scalars['String']['output'];
  maxPriorityFeePerGas: Scalars['String']['output'];
  nonce: Scalars['String']['output'];
  signature: Scalars['String']['output'];
  to: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type Signal = {
  __typename?: 'Signal';
  confidence: Scalars['Float']['output'];
  description: Scalars['String']['output'];
  expiryTime: Scalars['Int']['output'];
  price: Scalars['Float']['output'];
  signalType: Scalars['String']['output'];
  source: Scalars['String']['output'];
  symbol: Scalars['String']['output'];
  time: Scalars['Int']['output'];
};

export type StoreTxInformationRequest = {
  avgPx?: InputMaybe<Scalars['Float']['input']>;
  direction: Scalars['String']['input'];
  expiresAfter?: InputMaybe<Scalars['Int']['input']>;
  isBuy: Scalars['Boolean']['input'];
  nonce: Scalars['Int']['input'];
  oid: Scalars['String']['input'];
  orderType: Scalars['String']['input'];
  price: Scalars['Float']['input'];
  reduceOnly: Scalars['Boolean']['input'];
  size: Scalars['Float']['input'];
  status: Scalars['String']['input'];
  symbol: Scalars['String']['input'];
  txType: Scalars['String']['input'];
  vaultAddress?: InputMaybe<Scalars['String']['input']>;
};

export type StoreTxInformationResponse = {
  __typename?: 'StoreTxInformationResponse';
  error?: Maybe<Scalars['String']['output']>;
  status: Scalars['String']['output'];
};

export type Swap = {
  __typename?: 'Swap';
  estimatedTimeInSeconds: Scalars['Int']['output'];
  fee: Array<Fee>;
  from: TokenInfo;
  fromAmount: Scalars['String']['output'];
  fromAmountMaxValue?: Maybe<Scalars['String']['output']>;
  fromAmountMinValue?: Maybe<Scalars['String']['output']>;
  fromAmountPrecision: Scalars['String']['output'];
  fromAmountRestrictionType?: Maybe<Scalars['String']['output']>;
  includesDestinationTx: Scalars['Boolean']['output'];
  internalSwaps: Array<InternalSwaps>;
  maxRequiredSign: Scalars['Int']['output'];
  recommendedSlippage: RecommendedSlippage;
  routes: Array<Route>;
  swapChainType: Scalars['String']['output'];
  swapperId: Scalars['String']['output'];
  swapperLogo: Scalars['String']['output'];
  swapperType: Scalars['String']['output'];
  timeStat: TimeStat;
  to: TokenInfo;
  toAmount: Scalars['String']['output'];
  warnings: Array<Scalars['String']['output']>;
};

export type Symbol = {
  __typename?: 'Symbol';
  changPxPercent: Scalars['Float']['output'];
  currentPrice: Scalars['Float']['output'];
  marketCap: Scalars['Float']['output'];
  maxLeverage: Scalars['Int']['output'];
  openInterest: Scalars['Float']['output'];
  symbol: Scalars['String']['output'];
  volume: Scalars['Float']['output'];
};

export enum SymbolConditionEnum {
  Category = 'category',
  Gainer = 'gainer',
  Loser = 'loser',
  MarketCap = 'marketCap',
  OpenInterest = 'openInterest',
  Trend = 'trend',
  Volume = 'volume'
}

export type SymbolDetailRequest = {
  symbol: Scalars['String']['input'];
};

export type SymbolDetailResponse = {
  __typename?: 'SymbolDetailResponse';
  changPxPercent: Scalars['Float']['output'];
  currentPrice: Scalars['Float']['output'];
  high: Scalars['Float']['output'];
  low: Scalars['Float']['output'];
  marginTableID: Scalars['Int']['output'];
  marketCap: Scalars['Float']['output'];
  maxLeverage: Scalars['Int']['output'];
  onlyIsolated: Scalars['Boolean']['output'];
  sizeDecimals: Scalars['Int']['output'];
  symbol: Scalars['String']['output'];
  volume: Scalars['Float']['output'];
};

export type SymbolListRequest = {
  category?: InputMaybe<Scalars['String']['input']>;
  condition: SymbolConditionEnum;
};

export type SymbolListResponse = {
  __typename?: 'SymbolListResponse';
  list: Array<Maybe<Symbol>>;
};

export type Tag = {
  __typename?: 'Tag';
  label: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type TimeStat = {
  __typename?: 'TimeStat';
  avg: Scalars['Int']['output'];
  max: Scalars['Int']['output'];
  min: Scalars['Int']['output'];
};

export type Token = {
  __typename?: 'Token';
  address: Scalars['String']['output'];
  blockChain: Scalars['String']['output'];
  coinSource?: Maybe<Scalars['String']['output']>;
  coinSourceUrl?: Maybe<Scalars['String']['output']>;
  decimals?: Maybe<Scalars['Int']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  isPopular?: Maybe<Scalars['Boolean']['output']>;
  isSecondaryCoin?: Maybe<Scalars['Boolean']['output']>;
  name: Scalars['String']['output'];
  supportedSwappers?: Maybe<Array<Scalars['String']['output']>>;
  symbol: Scalars['String']['output'];
  usdPrice?: Maybe<Scalars['Float']['output']>;
};

export type TokenInfo = {
  __typename?: 'TokenInfo';
  address: Scalars['String']['output'];
  blockchain: Scalars['String']['output'];
  blockchainLogo: Scalars['String']['output'];
  decimals: Scalars['Int']['output'];
  logo: Scalars['String']['output'];
  symbol: Scalars['String']['output'];
  usdPrice: Scalars['Float']['output'];
};

export type TradeHistory = {
  __typename?: 'TradeHistory';
  dir: Scalars['String']['output'];
  fee: Scalars['Float']['output'];
  feeToken: Scalars['String']['output'];
  hash: Scalars['String']['output'];
  oid: Scalars['Int']['output'];
  pnl: Scalars['Float']['output'];
  pnlPercent: Scalars['Float']['output'];
  px: Scalars['Float']['output'];
  startPosition: Scalars['String']['output'];
  symbol: Scalars['String']['output'];
  sz: Scalars['Float']['output'];
  tid: Scalars['Int']['output'];
  time: Scalars['Int']['output'];
};

export type TradeOrder = {
  __typename?: 'TradeOrder';
  amount: Scalars['Float']['output'];
  createTime: Scalars['Int']['output'];
  orderID: Scalars['String']['output'];
  price: Scalars['Float']['output'];
  side: Scalars['String']['output'];
  status: Scalars['String']['output'];
  symbol: Scalars['String']['output'];
  updateTime: Scalars['Int']['output'];
};

export type TradeOrdersRequest = {
  accountID: Scalars['Int']['input'];
  address: Scalars['String']['input'];
  chainID: Scalars['Int']['input'];
  endTime?: InputMaybe<Scalars['Int']['input']>;
  page: Scalars['Int']['input'];
  pageSize: Scalars['Int']['input'];
  startTime?: InputMaybe<Scalars['Int']['input']>;
  symbol: Scalars['String']['input'];
};

export type TradeOrdersResponse = {
  __typename?: 'TradeOrdersResponse';
  orders: Array<TradeOrder>;
  total: Scalars['Int']['output'];
};

export type Transaction = {
  __typename?: 'Transaction';
  blockChain: Scalars['String']['output'];
  data: Scalars['String']['output'];
  from: Scalars['String']['output'];
  gasLimit: Scalars['String']['output'];
  gasPrice: Scalars['String']['output'];
  identifier: Scalars['String']['output'];
  instructions: Array<Scalars['String']['output']>;
  isApprovalTx: Scalars['Boolean']['output'];
  maxFeePerGas: Scalars['String']['output'];
  maxPriorityFeePerGas: Scalars['String']['output'];
  nonce: Scalars['String']['output'];
  recentBlockhash: Scalars['String']['output'];
  serializedMessage: Array<Scalars['Int']['output']>;
  signatures: Array<Scalars['String']['output']>;
  spender: Scalars['String']['output'];
  to: Scalars['String']['output'];
  txType: Scalars['String']['output'];
  type: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type UpdateAlertSymbolSettingRequest = {
  isActivated: Scalars['Boolean']['input'];
  settingId: Scalars['String']['input'];
};

export type UpdateAlertSymbolSettingResponse = {
  __typename?: 'UpdateAlertSymbolSettingResponse';
  error?: Maybe<Scalars['String']['output']>;
  status: Scalars['String']['output'];
};

export type UpdateUserSymbolPreferenceRequest = {
  isCross?: InputMaybe<Scalars['Boolean']['input']>;
  leverage?: InputMaybe<Scalars['Int']['input']>;
  orderUnitInBase?: InputMaybe<Scalars['Boolean']['input']>;
  symbol: Scalars['String']['input'];
  tpslUnit?: InputMaybe<Scalars['String']['input']>;
};

export type UpsertFavoriteSymbolRequest = {
  isFavorite: Scalars['Boolean']['input'];
  symbol: Array<Scalars['String']['input']>;
};

export type UpsertFavoriteSymbolResponse = {
  __typename?: 'UpsertFavoriteSymbolResponse';
  error?: Maybe<Scalars['String']['output']>;
  status: Scalars['String']['output'];
};

export type UserBalanceResponse = {
  __typename?: 'UserBalanceResponse';
  error?: Maybe<Scalars['String']['output']>;
  status: Scalars['String']['output'];
};

export type UserInfo = {
  __typename?: 'UserInfo';
  activeTrades: Scalars['Int']['output'];
  address: Scalars['String']['output'];
  lastUpdated: Scalars['Int']['output'];
  totalValue: Scalars['Float']['output'];
  vaultValue: Scalars['Float']['output'];
};

export type UserInfoRequest = {
  address: Scalars['String']['input'];
  chainID: Scalars['Int']['input'];
  userId: Scalars['Int']['input'];
};

export type UserInfoResponse = {
  __typename?: 'UserInfoResponse';
  userInfo: UserInfo;
};

export type UserOpenOrderRequest = {
  lastID: Scalars['String']['input'];
  limit: Scalars['Int']['input'];
};

export type UserOpenOrderResponse = {
  __typename?: 'UserOpenOrderResponse';
  openOrders: Array<Maybe<OpenOrder>>;
};

export type UserPositionResponse = {
  __typename?: 'UserPositionResponse';
  availableMargin: Scalars['Float']['output'];
  availableWithdraw: Scalars['Float']['output'];
  balance: Scalars['Float']['output'];
  positionValue: Scalars['Float']['output'];
  positions: Array<Maybe<Position>>;
  unrealizedPnl: Scalars['Float']['output'];
};

export type UserSettingsRequest = {
  infiniteApprove: Scalars['Boolean']['input'];
  slippage: Scalars['Float']['input'];
};

export type UserSymbolPreferenceRequest = {
  symbol: Scalars['String']['input'];
};

export type UserSymbolPreferenceResponse = {
  __typename?: 'UserSymbolPreferenceResponse';
  isCross: Scalars['Boolean']['output'];
  isFavorite: Scalars['Boolean']['output'];
  leverage: Scalars['Int']['output'];
  orderUnitInBase: Scalars['Boolean']['output'];
  tpslUnit: Scalars['String']['output'];
};

export type UserTradeHistoryResponse = {
  __typename?: 'UserTradeHistoryResponse';
  histories: Array<TradeHistory>;
};

export type ValidationsRequest = {
  approve: Scalars['Boolean']['input'];
  balance: Scalars['Boolean']['input'];
  fee: Scalars['Boolean']['input'];
};

export type VaultPosition = {
  __typename?: 'VaultPosition';
  amount: Scalars['Float']['output'];
  apy: Scalars['Float']['output'];
  lastUpdated: Scalars['Int']['output'];
  symbol: Scalars['String']['output'];
  value: Scalars['Float']['output'];
};

export type VaultPositionsRequest = {
  address: Scalars['String']['input'];
  chainID: Scalars['Int']['input'];
  userId: Scalars['Int']['input'];
};

export type VaultPositionsResponse = {
  __typename?: 'VaultPositionsResponse';
  positions: Array<VaultPosition>;
};
