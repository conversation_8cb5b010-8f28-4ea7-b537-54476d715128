import { gql } from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateScalar: { input: any; output: any; }
  DateTime: { input: any; output: any; }
  DecimalScalar: { input: any; output: any; }
};

export type AiAnalyticDto = {
  __typename?: 'AiAnalyticDTO';
  address: Scalars['String']['output'];
  avatarAnalytic?: Maybe<Scalars['String']['output']>;
  chainId: Scalars['Int']['output'];
  socialWebsiteAnalytic?: Maybe<Scalars['String']['output']>;
  themeNarrativeAnalytic?: Maybe<Scalars['String']['output']>;
};

export type AiAnalyzedInfoInput = {
  includeAvatarAnalytic?: Scalars['Boolean']['input'];
  includeSocialWebsiteAnalytic?: Scalars['Boolean']['input'];
  includeThemeNarrativeAnalytic?: Scalars['Boolean']['input'];
  lang?: InputMaybe<AiAnalyzedInfoLang>;
  tokenAddress: Scalars['String']['input'];
};

/** Supported languages for AI analysis */
export enum AiAnalyzedInfoLang {
  En = 'EN',
  Hi = 'HI',
  Ja = 'JA',
  Vi = 'VI',
  ZhHans = 'ZH_HANS'
}

export type AllCategoriesInput = {
  chainId: Scalars['Int']['input'];
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type AssetBalanceDto = {
  __typename?: 'AssetBalanceDTO';
  balance: Scalars['DecimalScalar']['output'];
  logoUrl?: Maybe<Scalars['String']['output']>;
  symbol: Scalars['String']['output'];
  tokenAddress: Scalars['String']['output'];
};

export type AssetBalanceInput = {
  hideSmallBalance?: InputMaybe<Scalars['Boolean']['input']>;
  userAddress: Scalars['String']['input'];
};

export type AssetChartItemDto = {
  __typename?: 'AssetChartItemDTO';
  t: Scalars['DateTime']['output'];
  v: Scalars['DecimalScalar']['output'];
};

export type AssetHistoryDto = {
  __typename?: 'AssetHistoryDTO';
  balance: Scalars['DecimalScalar']['output'];
  timestamp: Scalars['Int']['output'];
};

export type AssetHistoryInput = {
  timeframe: AssetHistoryTimeFrame;
  tokenAddress?: InputMaybe<Scalars['String']['input']>;
  type: AssetHistoryType;
  userAddress?: InputMaybe<Scalars['String']['input']>;
};

/** asset history time frame */
export enum AssetHistoryTimeFrame {
  D1 = 'd1',
  M1 = 'm1',
  W1 = 'w1',
  Y1 = 'y1'
}

/** asset history type */
export enum AssetHistoryType {
  Collapse = 'Collapse',
  Expand = 'Expand'
}

export type BooleanOptionInfo = {
  __typename?: 'BooleanOptionInfo';
  description?: Maybe<Scalars['String']['output']>;
  label: Scalars['Boolean']['output'];
  value: Scalars['String']['output'];
};

export type CategoryDto = {
  __typename?: 'CategoryDTO';
  categoryId?: Maybe<Scalars['String']['output']>;
  isEnabled: Scalars['Boolean']['output'];
  marketCap?: Maybe<Scalars['DecimalScalar']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  price1hChangeHistory: Array<Scalars['DecimalScalar']['output']>;
  price24hChange?: Maybe<Scalars['DecimalScalar']['output']>;
  priceDownCount: Scalars['Float']['output'];
  priceUpCount: Scalars['Float']['output'];
  top1TokenAddress?: Maybe<Scalars['String']['output']>;
  top1TokenSymbol?: Maybe<Scalars['String']['output']>;
  topGainers?: Maybe<Array<TopGainers>>;
  volume1hHistory: Array<Scalars['DecimalScalar']['output']>;
  volume24h?: Maybe<Scalars['DecimalScalar']['output']>;
};

export type CategoryPagination = {
  __typename?: 'CategoryPagination';
  data: Array<CategoryDto>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
};

export type CategoryStatisticDto = {
  __typename?: 'CategoryStatisticDTO';
  marketCap?: Maybe<Scalars['DecimalScalar']['output']>;
  price24hChange?: Maybe<Scalars['DecimalScalar']['output']>;
  priceDownCount: Scalars['Float']['output'];
  priceUpCount: Scalars['Float']['output'];
  volume24h?: Maybe<Scalars['DecimalScalar']['output']>;
};

export type CategoryStatisticInput = {
  categoryId: Scalars['String']['input'];
  chainId: Scalars['Int']['input'];
};

/** Supported blockchain types */
export enum ChainType {
  All = 'ALL',
  Evm = 'EVM',
  Solana = 'SOLANA',
  Tron = 'TRON'
}

export type CryptoCurrencyPriceDto = {
  __typename?: 'CryptoCurrencyPriceDTO';
  symbol: Scalars['String']['output'];
  usdPrice: Scalars['String']['output'];
};

export type DailyProfitDto = {
  __typename?: 'DailyProfitDTO';
  pnl: Scalars['Float']['output'];
  timestamp: Scalars['Int']['output'];
};

/** dev actions  */
export enum DevAction {
  AddLiquidity = 'AddLiquidity',
  Burnt = 'Burnt',
  Hold = 'Hold',
  RemoveLiquidity = 'RemoveLiquidity',
  SellAll = 'SellAll'
}

export type DexScreenPoolDto = {
  __typename?: 'DexScreenPoolDTO';
  address: Scalars['String']['output'];
  baseSymbol: Scalars['String']['output'];
  baseToken: Scalars['String']['output'];
  baseTokenLiquidity?: Maybe<Scalars['DecimalScalar']['output']>;
  chainId: Scalars['Int']['output'];
  createdAt: Scalars['String']['output'];
  dex?: Maybe<Scalars['String']['output']>;
  quoteLiquidity?: Maybe<Scalars['DecimalScalar']['output']>;
  quoteSymbol: Scalars['String']['output'];
  quoteToken: Scalars['String']['output'];
  quoteTokenPrice: Scalars['DecimalScalar']['output'];
  usdLiquidity?: Maybe<Scalars['DecimalScalar']['output']>;
};

export type FavoriteToken = {
  __typename?: 'FavoriteToken';
  createdTime?: Maybe<Scalars['DateScalar']['output']>;
  token?: Maybe<Scalars['String']['output']>;
};

export type FollowedTransaction = {
  __typename?: 'FollowedTransaction';
  baseAmount?: Maybe<Scalars['DecimalScalar']['output']>;
  baseToken: Scalars['String']['output'];
  chainId: Scalars['Int']['output'];
  decimals?: Maybe<Scalars['Int']['output']>;
  holderPct?: Maybe<Scalars['String']['output']>;
  isDev: Scalars['Boolean']['output'];
  isFreshWallet: Scalars['Boolean']['output'];
  isHugeValue: Scalars['Boolean']['output'];
  isInsider: Scalars['Boolean']['output'];
  isKOL: Scalars['Boolean']['output'];
  isNativeWallet: Scalars['Boolean']['output'];
  isNewActivity: Scalars['Boolean']['output'];
  isPoolContract: Scalars['Boolean']['output'];
  isSmartMoney: Scalars['Boolean']['output'];
  isTopTrader: Scalars['Boolean']['output'];
  isWhale: Scalars['Boolean']['output'];
  liquidity?: Maybe<Scalars['DecimalScalar']['output']>;
  logIndex?: Maybe<Scalars['Int']['output']>;
  maker: Scalars['String']['output'];
  pair?: Maybe<Scalars['String']['output']>;
  price?: Maybe<Scalars['DecimalScalar']['output']>;
  quoteAmount?: Maybe<Scalars['DecimalScalar']['output']>;
  quoteToken?: Maybe<Scalars['String']['output']>;
  timestamp: Scalars['String']['output'];
  totalSupply?: Maybe<Scalars['DecimalScalar']['output']>;
  tx24h?: Maybe<Scalars['Int']['output']>;
  txHash: Scalars['String']['output'];
  type: Scalars['String']['output'];
  usdAmount?: Maybe<Scalars['DecimalScalar']['output']>;
  usdPrice?: Maybe<Scalars['DecimalScalar']['output']>;
};

export type FollowedTransactionPagination = {
  __typename?: 'FollowedTransactionPagination';
  data: Array<FollowedTransaction>;
  fromTimestamp: Scalars['String']['output'];
};

export type HolderDto = {
  __typename?: 'HolderDTO';
  address?: Maybe<Scalars['String']['output']>;
  avgMarketCap?: Maybe<Scalars['DecimalScalar']['output']>;
  avgPriceUsd?: Maybe<Scalars['DecimalScalar']['output']>;
  balance?: Maybe<Scalars['DecimalScalar']['output']>;
  buys?: Maybe<Scalars['Float']['output']>;
  chainId?: Maybe<Scalars['Int']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  decimal?: Maybe<Scalars['Int']['output']>;
  maxHoldingQty?: Maybe<Scalars['DecimalScalar']['output']>;
  nativeBalance?: Maybe<Scalars['DecimalScalar']['output']>;
  numberTransaction?: Maybe<Scalars['Int']['output']>;
  rawBalance?: Maybe<Scalars['DecimalScalar']['output']>;
  realizedPnL?: Maybe<Scalars['DecimalScalar']['output']>;
  realizedProfit?: Maybe<Scalars['DecimalScalar']['output']>;
  sells?: Maybe<Scalars['Float']['output']>;
  sourceOfFunding?: Maybe<Scalars['String']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
  token?: Maybe<Scalars['String']['output']>;
  tokenAccount?: Maybe<Scalars['String']['output']>;
  totalBuyQty?: Maybe<Scalars['DecimalScalar']['output']>;
  totalBuyUsd?: Maybe<Scalars['DecimalScalar']['output']>;
  totalProfit?: Maybe<Scalars['DecimalScalar']['output']>;
  totalSellQty?: Maybe<Scalars['DecimalScalar']['output']>;
  totalSellUsd?: Maybe<Scalars['DecimalScalar']['output']>;
  totalSupply?: Maybe<Scalars['DecimalScalar']['output']>;
  totalTradedQty?: Maybe<Scalars['DecimalScalar']['output']>;
  totalUsdValue?: Maybe<Scalars['DecimalScalar']['output']>;
  unrealizedProfit?: Maybe<Scalars['DecimalScalar']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type HolderInput = {
  chainId: Scalars['Int']['input'];
  classification?: TransactionClassification;
  holder?: InputMaybe<Scalars['String']['input']>;
  limit?: Scalars['Int']['input'];
  page?: Scalars['Int']['input'];
  sortBy?: InputMaybe<Scalars['String']['input']>;
  token: Scalars['String']['input'];
};

export type HolderPagination = {
  __typename?: 'HolderPagination';
  averageHoldingPerWallet?: Maybe<Scalars['DecimalScalar']['output']>;
  averageHoldingPerWalletHistory?: Maybe<Array<Scalars['DecimalScalar']['output']>>;
  blueChip?: Maybe<Scalars['DecimalScalar']['output']>;
  blueChipHistory?: Maybe<Array<Scalars['DecimalScalar']['output']>>;
  data?: Maybe<Array<HolderDto>>;
  insiderHolding?: Maybe<Scalars['DecimalScalar']['output']>;
  insiderHoldingHistory?: Maybe<Array<Scalars['DecimalScalar']['output']>>;
  limit?: Maybe<Scalars['Int']['output']>;
  numberOfHolderHistory?: Maybe<Array<Scalars['DecimalScalar']['output']>>;
  numberOfHolders?: Maybe<Scalars['DecimalScalar']['output']>;
  page?: Maybe<Scalars['Int']['output']>;
  top10HolderHistory?: Maybe<Array<Scalars['DecimalScalar']['output']>>;
  top10Holders?: Maybe<Scalars['DecimalScalar']['output']>;
};

export type InfluentialTwitterFollowerDto = {
  __typename?: 'InfluentialTwitterFollowerDTO';
  address: Scalars['String']['output'];
  chainId: Scalars['Int']['output'];
  numberOfFollowers: Scalars['Int']['output'];
  url: Scalars['String']['output'];
};

export type LastTransaction = {
  __typename?: 'LastTransaction';
  baseAmount: Scalars['DecimalScalar']['output'];
  chainId: Scalars['String']['output'];
  marketCap: Scalars['DecimalScalar']['output'];
  priceUsd: Scalars['DecimalScalar']['output'];
  quoteAmount?: Maybe<Scalars['DecimalScalar']['output']>;
  symbol: Scalars['String']['output'];
  timestamp: Scalars['Int']['output'];
  token: Scalars['String']['output'];
  transactionType: TransactionType;
  txid: Scalars['String']['output'];
};

export type LastTransactionPagination = {
  __typename?: 'LastTransactionPagination';
  data?: Maybe<Array<LastTransaction>>;
  fromTimestamp: Scalars['String']['output'];
};

/** token launchpad */
export enum Launchpad {
  Moonshot = 'Moonshot',
  Pumpfun = 'Pumpfun'
}

export enum LifecycleStates {
  Completed = 'completed',
  Completing = 'completing',
  NewCreation = 'newCreation',
  Soaring = 'soaring'
}

export type ListInfluentialTwitterFollowerResponse = {
  __typename?: 'ListInfluentialTwitterFollowerResponse';
  data: Array<InfluentialTwitterFollowerDto>;
  pagination: Pagination;
};

export type ListInfluentialTwitterFollowersInput = {
  address: Scalars['String']['input'];
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type ManageTokenCategoryInput = {
  categoryId: Scalars['String']['input'];
  chainId: Scalars['Int']['input'];
  tokenAddress: Scalars['String']['input'];
};

export type MemeDto = {
  __typename?: 'MemeDTO';
  athDate?: Maybe<Scalars['DateScalar']['output']>;
  athPrice?: Maybe<Scalars['DecimalScalar']['output']>;
  atlDate?: Maybe<Scalars['DateScalar']['output']>;
  atlPrice?: Maybe<Scalars['DecimalScalar']['output']>;
  buyTxs1h: Scalars['Float']['output'];
  buyTxs1m: Scalars['Float']['output'];
  buyTxs5m: Scalars['Float']['output'];
  buyTxs6h: Scalars['Float']['output'];
  buyTxs24h: Scalars['Float']['output'];
  categoryIds?: Maybe<Array<Scalars['String']['output']>>;
  chainId: Scalars['Int']['output'];
  circulatingSupply?: Maybe<Scalars['DecimalScalar']['output']>;
  createdTime?: Maybe<Scalars['DateScalar']['output']>;
  devHold?: Maybe<Scalars['Float']['output']>;
  devLaunched?: Maybe<Scalars['Float']['output']>;
  dexes?: Maybe<Array<Scalars['String']['output']>>;
  firstPrice?: Maybe<Scalars['DecimalScalar']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  initLiquidity?: Maybe<Scalars['DecimalScalar']['output']>;
  insider?: Maybe<Scalars['Float']['output']>;
  internalMarketProgress?: Maybe<Scalars['DecimalScalar']['output']>;
  isFavorite: Scalars['Boolean']['output'];
  isHotToken: Scalars['Boolean']['output'];
  launchpad?: Maybe<Scalars['String']['output']>;
  liquidity?: Maybe<Scalars['DecimalScalar']['output']>;
  marketCap5mChangeUsd?: Maybe<Scalars['DecimalScalar']['output']>;
  marketcap?: Maybe<Scalars['DecimalScalar']['output']>;
  marketcap5m?: Maybe<Scalars['DecimalScalar']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  numberOfHolder?: Maybe<Scalars['Int']['output']>;
  numberUniqueAddresses?: Maybe<NumberUniqueAddresses>;
  ohlc?: Maybe<Array<Ohlcdto>>;
  openPrice?: Maybe<Scalars['DecimalScalar']['output']>;
  price?: Maybe<Scalars['DecimalScalar']['output']>;
  price1hAgo?: Maybe<Scalars['DecimalScalar']['output']>;
  price1hChange?: Maybe<Scalars['DecimalScalar']['output']>;
  price1mAgo?: Maybe<Scalars['DecimalScalar']['output']>;
  price1mChange?: Maybe<Scalars['DecimalScalar']['output']>;
  price5mAgo?: Maybe<Scalars['DecimalScalar']['output']>;
  price5mChange?: Maybe<Scalars['DecimalScalar']['output']>;
  price6hChange?: Maybe<Scalars['DecimalScalar']['output']>;
  price24hAgo?: Maybe<Scalars['DecimalScalar']['output']>;
  price24hChange?: Maybe<Scalars['DecimalScalar']['output']>;
  rug?: Maybe<Scalars['Boolean']['output']>;
  rugReason?: Maybe<Scalars['String']['output']>;
  rugTime?: Maybe<Scalars['DateTime']['output']>;
  sameSourceWallet?: Maybe<Scalars['String']['output']>;
  sellTxs1h: Scalars['Float']['output'];
  sellTxs1m: Scalars['Float']['output'];
  sellTxs5m: Scalars['Float']['output'];
  sellTxs6h: Scalars['Float']['output'];
  sellTxs24h: Scalars['Float']['output'];
  smartMoneyPct?: Maybe<Scalars['DecimalScalar']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
  token: Scalars['String']['output'];
  top10Holder?: Maybe<Scalars['DecimalScalar']['output']>;
  topTrending?: Maybe<Scalars['Int']['output']>;
  totalAmount?: Maybe<TotalAmount>;
  totalTransactions?: Maybe<TotalTransactions>;
  trendingScore1h?: Maybe<Scalars['Float']['output']>;
  trendingScore1m?: Maybe<Scalars['Float']['output']>;
  trendingScore5m?: Maybe<Scalars['Float']['output']>;
  trendingScore6h?: Maybe<Scalars['Float']['output']>;
  trendingScore24h?: Maybe<Scalars['Float']['output']>;
  turnoverRate24h?: Maybe<Scalars['DecimalScalar']['output']>;
  tweetId?: Maybe<Scalars['String']['output']>;
  twitterNameChangeCount?: Maybe<Scalars['Int']['output']>;
  twitterUrl?: Maybe<Scalars['String']['output']>;
  txBySniperPct?: Maybe<Scalars['DecimalScalar']['output']>;
  txs1h: Scalars['Float']['output'];
  txs1m: Scalars['Float']['output'];
  txs5m: Scalars['Float']['output'];
  txs6h: Scalars['Float']['output'];
  txs24h: Scalars['Float']['output'];
  volume1h?: Maybe<Scalars['DecimalScalar']['output']>;
  volume1m?: Maybe<Scalars['DecimalScalar']['output']>;
  volume5m?: Maybe<Scalars['DecimalScalar']['output']>;
  volume6h?: Maybe<Scalars['DecimalScalar']['output']>;
  volume24h?: Maybe<Scalars['DecimalScalar']['output']>;
  website?: Maybe<Scalars['String']['output']>;
};

export type MemeInput = {
  chain?: ChainType;
  dex: TokenSource;
  dexes?: InputMaybe<Scalars['String']['input']>;
  internalMarketProgressFrom?: InputMaybe<Scalars['Float']['input']>;
  internalMarketProgressTo?: InputMaybe<Scalars['Float']['input']>;
  launchpad?: InputMaybe<Launchpad>;
  lifecycleStates?: LifecycleStates;
  limit?: InputMaybe<Scalars['Int']['input']>;
  liquidityPoolFrom?: InputMaybe<Scalars['String']['input']>;
  liquidityPoolTo?: InputMaybe<Scalars['String']['input']>;
  marketValueFrom?: InputMaybe<Scalars['String']['input']>;
  marketValueTo?: InputMaybe<Scalars['String']['input']>;
  numberOfHolderFrom?: InputMaybe<Scalars['Int']['input']>;
  numberOfHolderTo?: InputMaybe<Scalars['Int']['input']>;
  openingTimeFrom?: InputMaybe<Scalars['Int']['input']>;
  openingTimeTo?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  sortBy?: InputMaybe<Scalars['String']['input']>;
  timeRange: TokenTimeRange;
  tradingVolume1hFrom?: InputMaybe<Scalars['String']['input']>;
  tradingVolume1hTo?: InputMaybe<Scalars['String']['input']>;
  transaction1hFrom?: InputMaybe<Scalars['Int']['input']>;
  transaction1hTo?: InputMaybe<Scalars['Int']['input']>;
};

export type MemePagination = {
  __typename?: 'MemePagination';
  data: Array<MemeDto>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
};

export type Mutation = {
  __typename?: 'Mutation';
  addToFavorite: Scalars['Boolean']['output'];
  addTokenToCategory: Scalars['Boolean']['output'];
  followWallet: Scalars['Boolean']['output'];
  removeTokenFavorite: Scalars['Boolean']['output'];
  removeTokenFromCategory: Scalars['Boolean']['output'];
  setCategoryName: Scalars['Boolean']['output'];
  toggleCategory: Scalars['Boolean']['output'];
  unFollowWallet: Scalars['Boolean']['output'];
};


export type MutationAddToFavoriteArgs = {
  tokens: Scalars['String']['input'];
};


export type MutationAddTokenToCategoryArgs = {
  input: ManageTokenCategoryInput;
};


export type MutationFollowWalletArgs = {
  input: SmartMoneyFollowInput;
};


export type MutationRemoveTokenFavoriteArgs = {
  tokens: Scalars['String']['input'];
};


export type MutationRemoveTokenFromCategoryArgs = {
  input: ManageTokenCategoryInput;
};


export type MutationSetCategoryNameArgs = {
  input: SetCategoryNameInput;
};


export type MutationToggleCategoryArgs = {
  input: ToggleCategoryInput;
};


export type MutationUnFollowWalletArgs = {
  input: SmartMoneyFollowInput;
};

export type NumberUniqueAddresses = {
  __typename?: 'NumberUniqueAddresses';
  numberOfBuyAddress1h?: Maybe<Scalars['Int']['output']>;
  numberOfBuyAddress5m?: Maybe<Scalars['Int']['output']>;
  numberOfBuyAddress6h?: Maybe<Scalars['Int']['output']>;
  numberOfBuyAddress24h?: Maybe<Scalars['Int']['output']>;
  numberOfSellAddress1h?: Maybe<Scalars['Int']['output']>;
  numberOfSellAddress5m?: Maybe<Scalars['Int']['output']>;
  numberOfSellAddress6h?: Maybe<Scalars['Int']['output']>;
  numberOfSellAddress24h?: Maybe<Scalars['Int']['output']>;
};

export type Ohlcdto = {
  __typename?: 'OHLCDTO';
  chainId: Scalars['Int']['output'];
  close: Scalars['String']['output'];
  high: Scalars['String']['output'];
  low: Scalars['String']['output'];
  open?: Maybe<Scalars['String']['output']>;
  token: Scalars['String']['output'];
  tokenVolume: Scalars['String']['output'];
  ts?: Maybe<Scalars['Int']['output']>;
  usdVolume: Scalars['String']['output'];
};

export type OhlcInput = {
  fromTimeStamp?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  timeframe: Timeframe;
  token: Scalars['String']['input'];
};

export type OnChainDataAnalyticDto = {
  __typename?: 'OnChainDataAnalyticDTO';
  address: Scalars['String']['output'];
  chainId: Scalars['Int']['output'];
  devTokenHoldingPercentage: Scalars['Float']['output'];
  numberOfDevProjectsLaunched: Scalars['Int']['output'];
  numberOfHolder: Scalars['Int']['output'];
  numberOfLinkedWallets: Scalars['Int']['output'];
  numberOfRugPullWareHouseAddresses: Scalars['Int']['output'];
  twitterAccountCreationDate: Scalars['DateScalar']['output'];
  twitterNameChangeCount: Scalars['Int']['output'];
};

export type OnChainDataAnalyticInput = {
  address: Scalars['String']['input'];
};

export enum Pair_Sort_Field {
  Age = 'AGE',
  Holder = 'HOLDER',
  LaunchProgress = 'LAUNCH_PROGRESS',
  Liquidity = 'LIQUIDITY',
  MarketCap = 'MARKET_CAP',
  Txs = 'TXS',
  Volume = 'VOLUME'
}

export enum Pair_Window_Filter {
  Window_1Day = 'WINDOW_1_DAY',
  Window_1Hour = 'WINDOW_1_HOUR',
  Window_1Minute = 'WINDOW_1_MINUTE',
  Window_5Minute = 'WINDOW_5_MINUTE',
  Window_6Hour = 'WINDOW_6_HOUR'
}

export type Pagination = {
  __typename?: 'Pagination';
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
};

export type PairDto = {
  __typename?: 'PairDTO';
  address: Scalars['String']['output'];
  baseToken?: Maybe<Scalars['Int']['output']>;
  chainId: Scalars['Int']['output'];
  createdTime?: Maybe<Scalars['Int']['output']>;
  decimal0?: Maybe<Scalars['Int']['output']>;
  decimal1?: Maybe<Scalars['Int']['output']>;
  factory: Scalars['String']['output'];
  initialLiquidity?: Maybe<Scalars['String']['output']>;
  isShowAlert?: Maybe<Scalars['Boolean']['output']>;
  label?: Maybe<Scalars['String']['output']>;
  liquidity?: Maybe<Scalars['String']['output']>;
  lpAddress?: Maybe<Scalars['String']['output']>;
  reserve0?: Maybe<Scalars['String']['output']>;
  reserve1?: Maybe<Scalars['String']['output']>;
  tags?: Maybe<Array<Scalars['String']['output']>>;
  token0: Scalars['String']['output'];
  token0Info?: Maybe<TokenWithStatisticDto>;
  token1: Scalars['String']['output'];
  token1Info?: Maybe<TokenWithStatisticDto>;
  tokenInfo?: Maybe<TokenWithStatisticDto>;
};

export type PairInput = {
  ageMax?: InputMaybe<Scalars['Float']['input']>;
  ageMin?: InputMaybe<Scalars['Float']['input']>;
  chain?: InputMaybe<Scalars['String']['input']>;
  dex?: Array<Support_Dex>;
  holderMax?: InputMaybe<Scalars['Int']['input']>;
  holderMin?: InputMaybe<Scalars['Int']['input']>;
  launchProgressMin?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  liquidityMax?: InputMaybe<Scalars['Int']['input']>;
  liquidityMin?: InputMaybe<Scalars['Int']['input']>;
  marketCapMin?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  sortField?: Pair_Sort_Field;
  sortType?: Sort_Type;
  timeRange?: Pair_Window_Filter;
  txsMax?: InputMaybe<Scalars['Int']['input']>;
  txsMin?: InputMaybe<Scalars['Int']['input']>;
  volumeMax?: InputMaybe<Scalars['Int']['input']>;
  volumeMin?: InputMaybe<Scalars['Int']['input']>;
};

export type PairPagination = {
  __typename?: 'PairPagination';
  data?: Maybe<Array<PairDto>>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
};

export type PoolDto = {
  __typename?: 'PoolDTO';
  address: Scalars['String']['output'];
  baseToken?: Maybe<Scalars['Int']['output']>;
  chainId?: Maybe<Scalars['Int']['output']>;
  createdTime?: Maybe<Scalars['Float']['output']>;
  decimal0?: Maybe<Scalars['Int']['output']>;
  decimal1?: Maybe<Scalars['Int']['output']>;
  factory?: Maybe<Scalars['String']['output']>;
  label?: Maybe<Scalars['String']['output']>;
  liquidity?: Maybe<Scalars['DecimalScalar']['output']>;
  reserve0?: Maybe<Scalars['String']['output']>;
  reserve1?: Maybe<Scalars['String']['output']>;
  tags?: Maybe<Array<Scalars['String']['output']>>;
  token0: Scalars['String']['output'];
  token0Info?: Maybe<TokenDto>;
  token1: Scalars['String']['output'];
  token1Info?: Maybe<TokenDto>;
};

export type PoolTransactionInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  chainId: Scalars['Int']['input'];
  classification?: TransactionClassification;
  lastTimestamp?: InputMaybe<Scalars['Int']['input']>;
  /** sortBy these fields timestamp, usdAmount, baseAmount, with prefix + is asc, - is desc, example: +timestamp */
  sortBy?: InputMaybe<Scalars['String']['input']>;
  timestampFrom?: InputMaybe<Scalars['Int']['input']>;
  timestampTo?: InputMaybe<Scalars['Int']['input']>;
  token: Scalars['String']['input'];
  transactionUsdAmountFrom?: InputMaybe<Scalars['Float']['input']>;
  transactionUsdAmountTo?: InputMaybe<Scalars['Float']['input']>;
  transactionVolumeFrom?: InputMaybe<Scalars['Float']['input']>;
  transactionVolumeTo?: InputMaybe<Scalars['Float']['input']>;
  type?: TransactionType;
};

export type PoolTransactionPagination = {
  __typename?: 'PoolTransactionPagination';
  data?: Maybe<Array<Transaction>>;
  fromTimestamp: Scalars['String']['output'];
  liquidity?: Maybe<Scalars['DecimalScalar']['output']>;
  numberOfPools?: Maybe<Scalars['Int']['output']>;
};

export type PortfolioDto = {
  __typename?: 'PortfolioDTO';
  avgMarketCap?: Maybe<Scalars['DecimalScalar']['output']>;
  avgPriceUsd?: Maybe<Scalars['DecimalScalar']['output']>;
  chainId?: Maybe<Scalars['Int']['output']>;
  decimals?: Maybe<Scalars['Int']['output']>;
  logoUrl?: Maybe<Scalars['String']['output']>;
  maxHoldingQty?: Maybe<Scalars['DecimalScalar']['output']>;
  price?: Maybe<Scalars['DecimalScalar']['output']>;
  price24hChange?: Maybe<Scalars['DecimalScalar']['output']>;
  realizedPnL?: Maybe<Scalars['DecimalScalar']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
  token?: Maybe<Scalars['String']['output']>;
  totalBaseAmount?: Maybe<Scalars['DecimalScalar']['output']>;
  totalBuyBaseAmount?: Maybe<Scalars['Float']['output']>;
  totalBuyQty?: Maybe<Scalars['DecimalScalar']['output']>;
  totalBuyUsd?: Maybe<Scalars['DecimalScalar']['output']>;
  totalSellBaseAmount?: Maybe<Scalars['Float']['output']>;
  totalSellQty?: Maybe<Scalars['DecimalScalar']['output']>;
  totalSellUsd?: Maybe<Scalars['DecimalScalar']['output']>;
  totalTradedQty?: Maybe<Scalars['DecimalScalar']['output']>;
  totalUsdValue?: Maybe<Scalars['DecimalScalar']['output']>;
  updatedAt?: Maybe<Scalars['DateScalar']['output']>;
  userAddress?: Maybe<Scalars['String']['output']>;
};

export type PortfolioPagination = {
  __typename?: 'PortfolioPagination';
  data: Array<PortfolioDto>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  totalHoldingTokens: Scalars['Int']['output'];
};

export type Query = {
  __typename?: 'Query';
  getAiAnalyzedInfo: AiAnalyticDto;
  getAllCategories: CategoryPagination;
  getAssetBalance: Array<AssetBalanceDto>;
  getAssetChart: Array<AssetChartItemDto>;
  getAssetChartPreview: Array<AssetChartItemDto>;
  getAssetHistory: Array<AssetHistoryDto>;
  getBrowserHistory: Array<TokenBrowserHistoryDto>;
  getCategoryStatistic: CategoryStatisticDto;
  getCryptoCurrencyPrice: Array<CryptoCurrencyPriceDto>;
  getFavoriteToken: TokenStatisticPagination;
  getFollowedHolder: HolderPagination;
  getFollowedPairs: PairPagination;
  getFollowedTransactions: FollowedTransactionPagination;
  getFollowingSmartMoneys: Array<SmartMoneyDto>;
  getFollowingWalletAddressess: Array<Scalars['String']['output']>;
  getHolder: HolderPagination;
  getMemeToken: MemePagination;
  getNewToken: TokenStatisticPagination;
  getOHLC: Array<Ohlcdto>;
  getOnChainDataAnalytic: OnChainDataAnalyticDto;
  getPoolTransactions: PoolTransactionPagination;
  getPopularTokens: Array<TokenPopularDto>;
  getPortfolio: PortfolioPagination;
  getSmartMoneyActions: SmartMoneyActionPaginationDto;
  getTokenCreatedByDev: Array<TokenCreatedByDevDto>;
  getTokenDetail: TokenDetail;
  getTokenOfficialInformation: TokenOfficialInformation;
  getTokenPortrait: TokenPortrait;
  getTokenSniper: TokenSniperDto;
  getTokenTelegram: TokenInfoDto;
  getTokenTrending: TokenStatisticPagination;
  getTradeHistory: TradeHistoryPagination;
  getTradingTransactions: TradingTransactionPagination;
  getWalletBalance: Array<WalletBalanceDto>;
  getWalletInfoOnHover: WalletInfoOnHoverDto;
  lastTransactions: LastTransactionPagination;
  listInfluentialTwitterFollowers: ListInfluentialTwitterFollowerResponse;
  rank: Array<SmartMoneyDto>;
  searchToken: Array<TokenStatisticDto>;
  tokens: TokenPagination;
  tokensByCategory: TokensStatisticByCategoryPagination;
};


export type QueryGetAiAnalyzedInfoArgs = {
  input: AiAnalyzedInfoInput;
};


export type QueryGetAllCategoriesArgs = {
  input: AllCategoriesInput;
};


export type QueryGetAssetBalanceArgs = {
  input: AssetBalanceInput;
};


export type QueryGetAssetChartArgs = {
  input: WalletAssetChartInput;
};


export type QueryGetAssetChartPreviewArgs = {
  input: WalletAssetChartInput;
};


export type QueryGetAssetHistoryArgs = {
  input: AssetHistoryInput;
};


export type QueryGetBrowserHistoryArgs = {
  input: TokenBrowserHistoryInput;
};


export type QueryGetCategoryStatisticArgs = {
  input: CategoryStatisticInput;
};


export type QueryGetFavoriteTokenArgs = {
  input: TokenFilterInput;
};


export type QueryGetFollowedHolderArgs = {
  input: HolderInput;
};


export type QueryGetFollowedPairsArgs = {
  input: PairInput;
};


export type QueryGetFollowedTransactionsArgs = {
  input: TransactionInput;
};


export type QueryGetFollowingSmartMoneysArgs = {
  filter: SmartMoneyFollowFilterInput;
};


export type QueryGetFollowingWalletAddressessArgs = {
  filter: SmartMoneyActionFilterInput;
};


export type QueryGetHolderArgs = {
  input: HolderInput;
};


export type QueryGetMemeTokenArgs = {
  input: MemeInput;
};


export type QueryGetNewTokenArgs = {
  input: TokenFilterInput;
};


export type QueryGetOhlcArgs = {
  input: OhlcInput;
};


export type QueryGetOnChainDataAnalyticArgs = {
  input: OnChainDataAnalyticInput;
};


export type QueryGetPoolTransactionsArgs = {
  input: PoolTransactionInput;
};


export type QueryGetPortfolioArgs = {
  input: SearchPortfolioInput;
};


export type QueryGetSmartMoneyActionsArgs = {
  filter: SmartMoneyActionFilterInput;
};


export type QueryGetTokenCreatedByDevArgs = {
  input: TokenCreatedByDevInput;
};


export type QueryGetTokenDetailArgs = {
  token: TokenDetailInput;
};


export type QueryGetTokenOfficialInformationArgs = {
  address: Scalars['String']['input'];
};


export type QueryGetTokenPortraitArgs = {
  token: TokenDetailInput;
};


export type QueryGetTokenSniperArgs = {
  input: TokenSniperInput;
};


export type QueryGetTokenTelegramArgs = {
  input: TelegramTokenInput;
};


export type QueryGetTokenTrendingArgs = {
  input: TokenTrendingInput;
};


export type QueryGetTradeHistoryArgs = {
  input: TokenTradeHistoryInput;
};


export type QueryGetTradingTransactionsArgs = {
  input: TradingTransactionInput;
};


export type QueryGetWalletBalanceArgs = {
  input: WalletBalanceInput;
};


export type QueryGetWalletInfoOnHoverArgs = {
  input: WalletInfoOnHoverInput;
};


export type QueryLastTransactionsArgs = {
  input: SearchLastTransactionInput;
};


export type QueryListInfluentialTwitterFollowersArgs = {
  input: ListInfluentialTwitterFollowersInput;
};


export type QueryRankArgs = {
  filter: SmartMoneyFilterInput;
};


export type QuerySearchTokenArgs = {
  input: Scalars['String']['input'];
};


export type QueryTokensArgs = {
  token: TokenInput;
};


export type QueryTokensByCategoryArgs = {
  input: TokensByCategoryInput;
};

export enum Sort_Type {
  Asc = 'ASC',
  Desc = 'DESC'
}

export enum Support_Dex {
  Moonshot = 'MOONSHOT',
  Pump = 'PUMP',
  Raydium = 'RAYDIUM'
}

export type SearchLastTransactionInput = {
  lastTimestamp?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  maxAmount?: InputMaybe<Scalars['DecimalScalar']['input']>;
  minAmount?: InputMaybe<Scalars['DecimalScalar']['input']>;
  token: Scalars['String']['input'];
};

export type SearchPortfolioInput = {
  allToken?: InputMaybe<Scalars['Boolean']['input']>;
  hideSmallBalance?: Scalars['Boolean']['input'];
  hideSmallLiquidity?: Scalars['Boolean']['input'];
  hideZeroBalance?: Scalars['Boolean']['input'];
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  token?: InputMaybe<Scalars['String']['input']>;
  userAddress: Scalars['String']['input'];
};

export type SetCategoryNameInput = {
  categoryId: Scalars['String']['input'];
  chainId: Scalars['Int']['input'];
  name: Scalars['String']['input'];
};

export type SmartMoneyActionDto = {
  __typename?: 'SmartMoneyActionDTO';
  address: Scalars['String']['output'];
  baseAmount?: Maybe<Scalars['DecimalScalar']['output']>;
  timestamp: Scalars['Float']['output'];
  token: TokenDto;
  txType: TransactionType;
  usdAmount?: Maybe<Scalars['DecimalScalar']['output']>;
};

export type SmartMoneyActionFilterInput = {
  chain: ChainType;
  limit?: Scalars['Int']['input'];
  minAmountUsd?: InputMaybe<Scalars['Int']['input']>;
  page?: Scalars['Int']['input'];
  transactionType?: InputMaybe<Array<TransactionType>>;
  walletAddresses?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type SmartMoneyActionPaginationDto = {
  __typename?: 'SmartMoneyActionPaginationDTO';
  actions?: Maybe<Array<SmartMoneyActionDto>>;
};

export type SmartMoneyDto = {
  __typename?: 'SmartMoneyDTO';
  address: Scalars['String']['output'];
  avatar?: Maybe<Scalars['String']['output']>;
  avgCost7d: Scalars['Float']['output'];
  dailyProfits?: Maybe<Array<DailyProfitDto>>;
  lastActivityAt?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  pnl1d: Scalars['Float']['output'];
  pnl7d: Scalars['Float']['output'];
  pnl30d: Scalars['Float']['output'];
  tags?: Maybe<Array<Scalars['String']['output']>>;
  totalBuy1d?: Maybe<Scalars['Float']['output']>;
  totalBuy7d?: Maybe<Scalars['Float']['output']>;
  totalBuy30d?: Maybe<Scalars['Float']['output']>;
  winRate7d: Scalars['Float']['output'];
};

export type SmartMoneyFilterInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  chain?: ChainType;
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  type?: InputMaybe<Array<SmartMoneyType>>;
};

export type SmartMoneyFollowFilterInput = {
  chain?: ChainType;
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  sortType?: SmartMoneySortType;
};

export type SmartMoneyFollowInput = {
  chain?: ChainType;
  walletAddress: Scalars['String']['input'];
  name? : InputMaybe<Scalars['String']['input']>;
};

export enum SmartMoneySortType {
  FollowTime = 'FollowTime',
  Pnl7D = 'Pnl7D'
}

export enum SmartMoneyType {
  Fresh = 'Fresh',
  Kol = 'KOL',
  PumpSm = 'PumpSM',
  SmartMoney = 'SmartMoney',
  Sniper = 'Sniper',
  TopTrader = 'TopTrader',
  Whale = 'Whale'
}

export type SocialDetailInfo = {
  __typename?: 'SocialDetailInfo';
  averageCommentsPerPost?: Maybe<Scalars['Float']['output']>;
  averageForwardingPerPost?: Maybe<Scalars['Float']['output']>;
  averageLikesPerPost?: Maybe<Scalars['Float']['output']>;
  averageViewPerPost?: Maybe<Scalars['Float']['output']>;
  nameChanges?: Maybe<Array<Scalars['String']['output']>>;
  numberFollowers?: Maybe<Scalars['String']['output']>;
  numberPosts?: Maybe<Scalars['String']['output']>;
  registrationDate?: Maybe<Scalars['DateTime']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  url?: Maybe<Scalars['String']['output']>;
};

export type SocialInfo = {
  __typename?: 'SocialInfo';
  type?: Maybe<Scalars['String']['output']>;
  url?: Maybe<Scalars['String']['output']>;
};

export type TelegramTokenInput = {
  chain?: ChainType;
  tokenAddress?: InputMaybe<Scalars['String']['input']>;
  walletAddress?: InputMaybe<Scalars['String']['input']>;
};

/** timeframe supported */
export enum Timeframe {
  D1 = 'd1',
  H1 = 'h1',
  H2 = 'h2',
  H4 = 'h4',
  H6 = 'h6',
  M1 = 'm1',
  M5 = 'm5',
  M10 = 'm10',
  M15 = 'm15',
  M30 = 'm30',
  S1 = 's1',
  S30 = 's30',
  W1 = 'w1'
}

export type ToggleCategoryInput = {
  categoryId: Scalars['String']['input'];
  chainId: Scalars['Int']['input'];
  isEnabled: Scalars['Boolean']['input'];
};

export type TokenBrowserHistoryDto = {
  __typename?: 'TokenBrowserHistoryDTO';
  chainId: Scalars['Int']['output'];
  createdTime: Scalars['DateTime']['output'];
  dexes: Array<Scalars['String']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  marketCap: Scalars['DecimalScalar']['output'];
  price24hChange: Scalars['DecimalScalar']['output'];
  symbol: Scalars['String']['output'];
  token: Scalars['String']['output'];
};

export type TokenBrowserHistoryInput = {
  tokenAddresses: Array<Scalars['String']['input']>;
};

export type TokenCreatedByDevDto = {
  __typename?: 'TokenCreatedByDevDTO';
  address: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  marketCap?: Maybe<Scalars['DecimalScalar']['output']>;
  rug?: Maybe<Scalars['Boolean']['output']>;
  rugReason?: Maybe<Scalars['String']['output']>;
  rugTime?: Maybe<Scalars['DateTime']['output']>;
  symbol: Scalars['String']['output'];
};

export type TokenCreatedByDevInput = {
  devAddress: Scalars['String']['input'];
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type TokenDto = {
  __typename?: 'TokenDTO';
  address?: Maybe<Scalars['String']['output']>;
  burnRatio?: Maybe<Scalars['Float']['output']>;
  burnStatus?: Maybe<Scalars['String']['output']>;
  chainId?: Maybe<Scalars['Int']['output']>;
  decimals?: Maybe<Scalars['String']['output']>;
  info?: Maybe<TokenInfo>;
  isBlacklisted?: Maybe<Scalars['Boolean']['output']>;
  isHoneypot?: Maybe<Scalars['Boolean']['output']>;
  mintDisable?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  ratTraderAmountRate?: Maybe<Scalars['Float']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
  tags?: Maybe<Array<Scalars['String']['output']>>;
  top10HolderRate?: Maybe<Scalars['Float']['output']>;
  totalSupply?: Maybe<Scalars['String']['output']>;
};

export type TokenDetail = {
  __typename?: 'TokenDetail';
  address?: Maybe<Scalars['String']['output']>;
  athDate?: Maybe<Scalars['DateScalar']['output']>;
  athPrice?: Maybe<Scalars['DecimalScalar']['output']>;
  atlDate?: Maybe<Scalars['DateScalar']['output']>;
  atlPrice?: Maybe<Scalars['DecimalScalar']['output']>;
  burnRatio?: Maybe<Scalars['Float']['output']>;
  burnStatus?: Maybe<Scalars['String']['output']>;
  buyTxs?: Maybe<Scalars['DecimalScalar']['output']>;
  chainId?: Maybe<Scalars['Int']['output']>;
  circulatingSupply?: Maybe<Scalars['DecimalScalar']['output']>;
  createdTime?: Maybe<Scalars['DateScalar']['output']>;
  decimals?: Maybe<Scalars['String']['output']>;
  devHold?: Maybe<Scalars['Float']['output']>;
  dexes?: Maybe<Array<Scalars['String']['output']>>;
  health?: Maybe<TokenHealth>;
  holders: Scalars['Float']['output'];
  info?: Maybe<TokenInfo>;
  initLiquidity?: Maybe<Scalars['DecimalScalar']['output']>;
  internalMarketProgress?: Maybe<Scalars['DecimalScalar']['output']>;
  isBlacklisted?: Maybe<Scalars['Boolean']['output']>;
  isFavorite: Scalars['Boolean']['output'];
  isHoneypot?: Maybe<Scalars['Boolean']['output']>;
  isHotToken: Scalars['Boolean']['output'];
  liquidity?: Maybe<Scalars['DecimalScalar']['output']>;
  marketCap?: Maybe<Scalars['DecimalScalar']['output']>;
  mintDisable?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  numberProTrader: Scalars['Int']['output'];
  numberUniqueAddresses?: Maybe<NumberUniqueAddresses>;
  openPrice?: Maybe<Scalars['DecimalScalar']['output']>;
  pairs?: Maybe<Array<DexScreenPoolDto>>;
  /** deprecate field, use pairs instead */
  pools?: Maybe<Array<PoolDto>>;
  /** deprecated fields, use query getTokenDetail instead */
  portrait?: Maybe<TokenPortrait>;
  price?: Maybe<Scalars['DecimalScalar']['output']>;
  price24hChange?: Maybe<Scalars['DecimalScalar']['output']>;
  ratTraderAmountRate?: Maybe<Scalars['Float']['output']>;
  sellTxs?: Maybe<Scalars['DecimalScalar']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
  tags?: Maybe<Array<Scalars['String']['output']>>;
  /** deprecated fields, use query getTop10Holder instead */
  top10Holder?: Maybe<Scalars['DecimalScalar']['output']>;
  top10HolderRate?: Maybe<Scalars['Float']['output']>;
  topTrending?: Maybe<Scalars['Int']['output']>;
  totalAmount?: Maybe<TotalAmount>;
  totalSupply?: Maybe<Scalars['String']['output']>;
  totalTransactions?: Maybe<TotalTransactions>;
  turnoverRate24h?: Maybe<Scalars['DecimalScalar']['output']>;
  volume24h?: Maybe<Scalars['DecimalScalar']['output']>;
};

export type TokenDetailInput = {
  address: Scalars['String']['input'];
};

/** token trending direction */
export enum TokenDirection {
  AiAnalysis = 'AiAnalysis',
  Gainer = 'Gainer',
  Loser = 'Loser',
  Popular = 'Popular'
}

export type TokenFilterInput = {
  chain?: ChainType;
  dex: TokenSource;
  dexes?: InputMaybe<Scalars['String']['input']>;
  internalMarketProgressFrom?: InputMaybe<Scalars['Float']['input']>;
  internalMarketProgressTo?: InputMaybe<Scalars['Float']['input']>;
  launchpad?: InputMaybe<Launchpad>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  liquidityPoolFrom?: InputMaybe<Scalars['String']['input']>;
  liquidityPoolTo?: InputMaybe<Scalars['String']['input']>;
  marketValueFrom?: InputMaybe<Scalars['String']['input']>;
  marketValueTo?: InputMaybe<Scalars['String']['input']>;
  numberOfHolderFrom?: InputMaybe<Scalars['Int']['input']>;
  numberOfHolderTo?: InputMaybe<Scalars['Int']['input']>;
  openingTimeFrom?: InputMaybe<Scalars['Int']['input']>;
  openingTimeTo?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  sortBy?: InputMaybe<Scalars['String']['input']>;
  timeRange: TokenTimeRange;
  tradingVolume1hFrom?: InputMaybe<Scalars['String']['input']>;
  tradingVolume1hTo?: InputMaybe<Scalars['String']['input']>;
  transaction1hFrom?: InputMaybe<Scalars['Int']['input']>;
  transaction1hTo?: InputMaybe<Scalars['Int']['input']>;
};

export type TokenHealth = {
  __typename?: 'TokenHealth';
  burnt: Scalars['Boolean']['output'];
  canSell: Scalars['Boolean']['output'];
  noBlackListWhiteListFunction: Scalars['Boolean']['output'];
  noHighTax: Scalars['Boolean']['output'];
  notMint: Scalars['Boolean']['output'];
  top10?: Maybe<Scalars['DecimalScalar']['output']>;
  verifiedSourceCode: Scalars['Boolean']['output'];
};

export type TokenInfo = {
  __typename?: 'TokenInfo';
  bannerUrl?: Maybe<Scalars['String']['output']>;
  dexScreenerBoosts?: Maybe<Scalars['Boolean']['output']>;
  logoUrl?: Maybe<Scalars['String']['output']>;
  socials?: Maybe<Array<SocialInfo>>;
  websites?: Maybe<Array<WebsitesInfo>>;
};

export type TokenInfoDto = {
  __typename?: 'TokenInfoDTO';
  address?: Maybe<Scalars['String']['output']>;
  burnRatio?: Maybe<Scalars['Float']['output']>;
  burnStatus?: Maybe<Scalars['String']['output']>;
  chainId?: Maybe<Scalars['Int']['output']>;
  decimals?: Maybe<Scalars['String']['output']>;
  dexes?: Maybe<Array<Scalars['String']['output']>>;
  info?: Maybe<TokenInfo>;
  isBlacklisted?: Maybe<Scalars['Boolean']['output']>;
  isHoneypot?: Maybe<Scalars['Boolean']['output']>;
  marketCap?: Maybe<Scalars['DecimalScalar']['output']>;
  mintDisable?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  price?: Maybe<Scalars['DecimalScalar']['output']>;
  ratTraderAmountRate?: Maybe<Scalars['Float']['output']>;
  solanaPrice?: Maybe<Scalars['DecimalScalar']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
  tags?: Maybe<Array<Scalars['String']['output']>>;
  top10HolderRate?: Maybe<Scalars['Float']['output']>;
  totalSupply?: Maybe<Scalars['String']['output']>;
  walletToken?: Maybe<WalletToken>;
};

export type TokenInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  chain?: InputMaybe<Scalars['String']['input']>;
  createdAt?: InputMaybe<Scalars['DateTime']['input']>;
  decimal?: InputMaybe<Scalars['Int']['input']>;
  deployedAt?: InputMaybe<Scalars['DateTime']['input']>;
  devStatus?: InputMaybe<Scalars['String']['input']>;
  holder?: InputMaybe<Scalars['Int']['input']>;
  lastTxs?: InputMaybe<Scalars['DateTime']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  logo?: InputMaybe<Scalars['String']['input']>;
  marketCap?: InputMaybe<Scalars['Float']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  oneHourTxs?: InputMaybe<Scalars['Int']['input']>;
  oneHourVol?: InputMaybe<Scalars['Float']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  solanaBalance?: InputMaybe<Scalars['Float']['input']>;
  status?: InputMaybe<Scalars['Float']['input']>;
  symbol?: InputMaybe<Scalars['String']['input']>;
};

export type TokenOfficialInformation = {
  __typename?: 'TokenOfficialInformation';
  addPoolTime?: Maybe<Scalars['DateTime']['output']>;
  balanceProjectPartyDev?: Maybe<Scalars['String']['output']>;
  booleanOptions?: Maybe<Array<BooleanOptionInfo>>;
  contractAddress?: Maybe<Scalars['String']['output']>;
  devEntrepreneurshipHistory?: Maybe<Scalars['String']['output']>;
  openingDate?: Maybe<Scalars['DateTime']['output']>;
  poolAddress?: Maybe<Scalars['String']['output']>;
  poolNativeBalance?: Maybe<Scalars['DecimalScalar']['output']>;
  poolTokenBalance?: Maybe<Scalars['DecimalScalar']['output']>;
  projectPartyDevAddress?: Maybe<Scalars['String']['output']>;
  projectPartyDevHistory?: Maybe<Scalars['String']['output']>;
  socials?: Maybe<Array<SocialDetailInfo>>;
};

export type TokenPagination = {
  __typename?: 'TokenPagination';
  data: Array<TokenDto>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
};

export type TokenPopularDto = {
  __typename?: 'TokenPopularDTO';
  chainId: Scalars['Float']['output'];
  dexes?: Maybe<Array<Scalars['String']['output']>>;
  hot?: Maybe<Scalars['Boolean']['output']>;
  logoUrl?: Maybe<Scalars['String']['output']>;
  marketcap?: Maybe<Scalars['DecimalScalar']['output']>;
  name: Scalars['String']['output'];
  price24hChange?: Maybe<Scalars['DecimalScalar']['output']>;
  symbol: Scalars['String']['output'];
  token: Scalars['String']['output'];
};

export type TokenPortrait = {
  __typename?: 'TokenPortrait';
  abandoned?: Maybe<Scalars['Boolean']['output']>;
  advertisesOnDex?: Maybe<Scalars['Boolean']['output']>;
  ageSinceCreation?: Maybe<Scalars['Boolean']['output']>;
  bannerUrl?: Maybe<Scalars['String']['output']>;
  devAction?: Maybe<Array<DevAction>>;
  devAddLiquidity?: Maybe<Scalars['Boolean']['output']>;
  devStatus?: Maybe<Scalars['Boolean']['output']>;
  explorer?: Maybe<Scalars['String']['output']>;
  launchedOnPump?: Maybe<Scalars['Boolean']['output']>;
  lowLiquidity?: Maybe<Scalars['Boolean']['output']>;
  officialTelegram?: Maybe<Scalars['String']['output']>;
  officialTwitter?: Maybe<Scalars['String']['output']>;
  officialWebsite?: Maybe<Scalars['String']['output']>;
  tweetId?: Maybe<Scalars['String']['output']>;
  twitterNameChangeCount?: Maybe<Scalars['Int']['output']>;
  updatedSocialOnDex?: Maybe<Scalars['Boolean']['output']>;
  walletActive1h: Scalars['Int']['output'];
};

/** TokenSniper */
export enum TokenSniper {
  BuyMore = 'BuyMore',
  Hold = 'Hold',
  SellAll = 'SellAll',
  SellPart = 'SellPart',
  Sniper = 'Sniper'
}

export type TokenSniperDto = {
  __typename?: 'TokenSniperDto';
  currentTotalHolding?: Maybe<Scalars['DecimalScalar']['output']>;
  snipers?: Maybe<Array<TokenSniper>>;
  top10Holders?: Maybe<Scalars['DecimalScalar']['output']>;
  totalBought?: Maybe<Scalars['DecimalScalar']['output']>;
};

export type TokenSniperInput = {
  chainId: Scalars['Int']['input'];
  tokenAddress: Scalars['String']['input'];
};

/** token creation source */
export enum TokenSource {
  Aldrin = 'Aldrin',
  AldrinV2 = 'AldrinV2',
  All = 'All',
  Balansol = 'Balansol',
  BonkSwap = 'BonkSwap',
  Crema = 'Crema',
  Cropper = 'Cropper',
  Cykura = 'Cykura',
  Dexlab = 'Dexlab',
  Dradex = 'Dradex',
  FluxBeam = 'FluxBeam',
  GooseFx = 'GooseFX',
  GooseFxv2 = 'GooseFXV2',
  HeliumNetwork = 'HeliumNetwork',
  Invariant = 'Invariant',
  Lifinity = 'Lifinity',
  LifinityV2 = 'LifinityV2',
  MarcoPolo = 'MarcoPolo',
  Marinade = 'Marinade',
  Mercurial = 'Mercurial',
  Meteora = 'Meteora',
  MeteoraDlmm = 'MeteoraDLMM',
  Moonshot = 'Moonshot',
  ObricV2 = 'ObricV2',
  Openbook = 'Openbook',
  OpenbookV2 = 'OpenbookV2',
  Orca = 'Orca',
  OrcaTokenSwap = 'OrcaTokenSwap',
  Penguin = 'Penguin',
  Perps = 'Perps',
  Phoenix = 'Phoenix',
  PumpSwap = 'PumpSwap',
  Pumpfun = 'Pumpfun',
  Raydium = 'Raydium',
  RaydiumClmm = 'RaydiumCLMM',
  Saber = 'Saber',
  SaberDecimalWrapper = 'SaberDecimalWrapper',
  Saros = 'Saros',
  Sencha = 'Sencha',
  Step = 'Step',
  Stepn = 'Stepn',
  Symmetry = 'Symmetry',
  TokenSwap = 'TokenSwap',
  UnstakeIt = 'UnstakeIt',
  Whirlpool = 'Whirlpool'
}

export type TokenStatisticDto = {
  __typename?: 'TokenStatisticDTO';
  athDate?: Maybe<Scalars['DateScalar']['output']>;
  athPrice?: Maybe<Scalars['DecimalScalar']['output']>;
  atlDate?: Maybe<Scalars['DateScalar']['output']>;
  atlPrice?: Maybe<Scalars['DecimalScalar']['output']>;
  buyTxs1h: Scalars['Float']['output'];
  buyTxs1m: Scalars['Float']['output'];
  buyTxs5m: Scalars['Float']['output'];
  buyTxs6h: Scalars['Float']['output'];
  buyTxs24h: Scalars['Float']['output'];
  categoryIds?: Maybe<Array<Scalars['String']['output']>>;
  chainId: Scalars['Int']['output'];
  circulatingSupply?: Maybe<Scalars['DecimalScalar']['output']>;
  createdTime?: Maybe<Scalars['DateScalar']['output']>;
  devHold?: Maybe<Scalars['Float']['output']>;
  devLaunched?: Maybe<Scalars['Float']['output']>;
  dexes?: Maybe<Array<Scalars['String']['output']>>;
  firstPrice?: Maybe<Scalars['DecimalScalar']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  initLiquidity?: Maybe<Scalars['DecimalScalar']['output']>;
  insider?: Maybe<Scalars['Float']['output']>;
  internalMarketProgress?: Maybe<Scalars['DecimalScalar']['output']>;
  isFavorite: Scalars['Boolean']['output'];
  isHotToken: Scalars['Boolean']['output'];
  launchpad?: Maybe<Scalars['String']['output']>;
  liquidity?: Maybe<Scalars['DecimalScalar']['output']>;
  marketCap5mChangeUsd?: Maybe<Scalars['DecimalScalar']['output']>;
  marketcap?: Maybe<Scalars['DecimalScalar']['output']>;
  marketcap5m?: Maybe<Scalars['DecimalScalar']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  numberOfHolder?: Maybe<Scalars['Int']['output']>;
  numberUniqueAddresses?: Maybe<NumberUniqueAddresses>;
  ohlc?: Maybe<Array<Ohlcdto>>;
  openPrice?: Maybe<Scalars['DecimalScalar']['output']>;
  price?: Maybe<Scalars['DecimalScalar']['output']>;
  price1hAgo?: Maybe<Scalars['DecimalScalar']['output']>;
  price1hChange?: Maybe<Scalars['DecimalScalar']['output']>;
  price1mAgo?: Maybe<Scalars['DecimalScalar']['output']>;
  price1mChange?: Maybe<Scalars['DecimalScalar']['output']>;
  price5mAgo?: Maybe<Scalars['DecimalScalar']['output']>;
  price5mChange?: Maybe<Scalars['DecimalScalar']['output']>;
  price6hChange?: Maybe<Scalars['DecimalScalar']['output']>;
  price24hAgo?: Maybe<Scalars['DecimalScalar']['output']>;
  price24hChange?: Maybe<Scalars['DecimalScalar']['output']>;
  rug?: Maybe<Scalars['Boolean']['output']>;
  rugReason?: Maybe<Scalars['String']['output']>;
  rugTime?: Maybe<Scalars['DateTime']['output']>;
  sameSourceWallet?: Maybe<Scalars['String']['output']>;
  sellTxs1h: Scalars['Float']['output'];
  sellTxs1m: Scalars['Float']['output'];
  sellTxs5m: Scalars['Float']['output'];
  sellTxs6h: Scalars['Float']['output'];
  sellTxs24h: Scalars['Float']['output'];
  smartMoneyPct?: Maybe<Scalars['DecimalScalar']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
  token: Scalars['String']['output'];
  top10Holder?: Maybe<Scalars['DecimalScalar']['output']>;
  topTrending?: Maybe<Scalars['Int']['output']>;
  totalAmount?: Maybe<TotalAmount>;
  totalTransactions?: Maybe<TotalTransactions>;
  trendingScore1h?: Maybe<Scalars['Float']['output']>;
  trendingScore1m?: Maybe<Scalars['Float']['output']>;
  trendingScore5m?: Maybe<Scalars['Float']['output']>;
  trendingScore6h?: Maybe<Scalars['Float']['output']>;
  trendingScore24h?: Maybe<Scalars['Float']['output']>;
  turnoverRate24h?: Maybe<Scalars['DecimalScalar']['output']>;
  tweetId?: Maybe<Scalars['String']['output']>;
  twitterNameChangeCount?: Maybe<Scalars['Int']['output']>;
  twitterUrl?: Maybe<Scalars['String']['output']>;
  txBySniperPct?: Maybe<Scalars['DecimalScalar']['output']>;
  txs1h: Scalars['Float']['output'];
  txs1m: Scalars['Float']['output'];
  txs5m: Scalars['Float']['output'];
  txs6h: Scalars['Float']['output'];
  txs24h: Scalars['Float']['output'];
  volume1h?: Maybe<Scalars['DecimalScalar']['output']>;
  volume1m?: Maybe<Scalars['DecimalScalar']['output']>;
  volume5m?: Maybe<Scalars['DecimalScalar']['output']>;
  volume6h?: Maybe<Scalars['DecimalScalar']['output']>;
  volume24h?: Maybe<Scalars['DecimalScalar']['output']>;
  website?: Maybe<Scalars['String']['output']>;
};

export type TokenStatisticPagination = {
  __typename?: 'TokenStatisticPagination';
  data: Array<TokenStatisticDto>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
};

/** token time range for filtering */
export enum TokenTimeRange {
  H1 = 'h1',
  H6 = 'h6',
  H24 = 'h24',
  M1 = 'm1',
  M5 = 'm5'
}

export type TokenTradeHistoryInput = {
  chain?: ChainType;
  filter?: InputMaybe<TradeHistoryFilterInput>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  tokenAddress: Scalars['String']['input'];
};

export type TokenTrendingInput = {
  chain?: ChainType;
  dex: TokenSource;
  dexes?: InputMaybe<Scalars['String']['input']>;
  direction?: TokenDirection;
  internalMarketProgressFrom?: InputMaybe<Scalars['Float']['input']>;
  internalMarketProgressTo?: InputMaybe<Scalars['Float']['input']>;
  launchpad?: InputMaybe<Launchpad>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  liquidityPoolFrom?: InputMaybe<Scalars['String']['input']>;
  liquidityPoolTo?: InputMaybe<Scalars['String']['input']>;
  marketValueFrom?: InputMaybe<Scalars['String']['input']>;
  marketValueTo?: InputMaybe<Scalars['String']['input']>;
  numberOfHolderFrom?: InputMaybe<Scalars['Int']['input']>;
  numberOfHolderTo?: InputMaybe<Scalars['Int']['input']>;
  openingTimeFrom?: InputMaybe<Scalars['Int']['input']>;
  openingTimeTo?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  sortBy?: InputMaybe<Scalars['String']['input']>;
  timeRange: TokenTimeRange;
  tradingVolume1hFrom?: InputMaybe<Scalars['String']['input']>;
  tradingVolume1hTo?: InputMaybe<Scalars['String']['input']>;
  transaction1hFrom?: InputMaybe<Scalars['Int']['input']>;
  transaction1hTo?: InputMaybe<Scalars['Int']['input']>;
};

export type TokenWithStatisticDto = {
  __typename?: 'TokenWithStatisticDto';
  address?: Maybe<Scalars['String']['output']>;
  burnRatio?: Maybe<Scalars['Float']['output']>;
  burnStatus?: Maybe<Scalars['String']['output']>;
  buyTxs1h: Scalars['Float']['output'];
  buyTxs1m: Scalars['Float']['output'];
  buyTxs5m: Scalars['Float']['output'];
  buyTxs6h: Scalars['Float']['output'];
  buyTxs24h: Scalars['Float']['output'];
  chainId?: Maybe<Scalars['Int']['output']>;
  decimals?: Maybe<Scalars['String']['output']>;
  holders: Scalars['Float']['output'];
  info?: Maybe<TokenInfo>;
  initLiquidity: Scalars['Float']['output'];
  isBlacklisted?: Maybe<Scalars['Boolean']['output']>;
  isHoneypot?: Maybe<Scalars['Boolean']['output']>;
  liquidity: Scalars['Float']['output'];
  marketcap: Scalars['Float']['output'];
  mintDisable?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  price?: Maybe<Scalars['Float']['output']>;
  price1hChange?: Maybe<Scalars['String']['output']>;
  price1mChange?: Maybe<Scalars['String']['output']>;
  price5mChange?: Maybe<Scalars['String']['output']>;
  ratTraderAmountRate?: Maybe<Scalars['Float']['output']>;
  sellTxs1h: Scalars['Float']['output'];
  sellTxs1m: Scalars['Float']['output'];
  sellTxs5m: Scalars['Float']['output'];
  sellTxs6h: Scalars['Float']['output'];
  sellTxs24h: Scalars['Float']['output'];
  symbol?: Maybe<Scalars['String']['output']>;
  tags?: Maybe<Array<Scalars['String']['output']>>;
  top10HolderRate?: Maybe<Scalars['Float']['output']>;
  totalSupply?: Maybe<Scalars['String']['output']>;
  volume1h: Scalars['Float']['output'];
  volume1m: Scalars['Float']['output'];
  volume5m: Scalars['Float']['output'];
  volume6h: Scalars['Float']['output'];
  volume24h: Scalars['Float']['output'];
};

export type TokensByCategoryInput = {
  categoryId: Scalars['String']['input'];
  chainId: Scalars['Int']['input'];
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  sortBy?: TokensByCategorySortFields;
  sortType?: Sort_Type;
};

/** sort fields for tokens by category */
export enum TokensByCategorySortFields {
  MarketCap = 'MarketCap',
  Price = 'Price',
  Price24hChange = 'Price24hChange',
  Volume24h = 'Volume24h'
}

export type TokensStatisticByCategoryDto = {
  __typename?: 'TokensStatisticByCategoryDTO';
  address?: Maybe<Scalars['String']['output']>;
  chainId?: Maybe<Scalars['String']['output']>;
  logoUrl?: Maybe<Scalars['String']['output']>;
  marketCap?: Maybe<Scalars['DecimalScalar']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  price?: Maybe<Scalars['DecimalScalar']['output']>;
  price24hChange?: Maybe<Scalars['DecimalScalar']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
  volume24h?: Maybe<Scalars['DecimalScalar']['output']>;
};

export type TokensStatisticByCategoryPagination = {
  __typename?: 'TokensStatisticByCategoryPagination';
  data: Array<TokensStatisticByCategoryDto>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
};

export type TopGainers = {
  __typename?: 'TopGainers';
  logoUrl?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  price24hChange?: Maybe<Scalars['DecimalScalar']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
};

export type TotalAmount = {
  __typename?: 'TotalAmount';
  totalBuyAmount1h?: Maybe<Scalars['DecimalScalar']['output']>;
  totalBuyAmount5m?: Maybe<Scalars['DecimalScalar']['output']>;
  totalBuyAmount6h?: Maybe<Scalars['DecimalScalar']['output']>;
  totalBuyAmount24h?: Maybe<Scalars['DecimalScalar']['output']>;
  totalSellAmount1h?: Maybe<Scalars['DecimalScalar']['output']>;
  totalSellAmount5m?: Maybe<Scalars['DecimalScalar']['output']>;
  totalSellAmount6h?: Maybe<Scalars['DecimalScalar']['output']>;
  totalSellAmount24h?: Maybe<Scalars['DecimalScalar']['output']>;
};

export type TotalTransactions = {
  __typename?: 'TotalTransactions';
  numberOfPurchases1h?: Maybe<Scalars['Int']['output']>;
  numberOfPurchases5m?: Maybe<Scalars['Int']['output']>;
  numberOfPurchases6h?: Maybe<Scalars['Int']['output']>;
  numberOfPurchases24h?: Maybe<Scalars['Int']['output']>;
  numberOfSales1h?: Maybe<Scalars['Int']['output']>;
  numberOfSales5m?: Maybe<Scalars['Int']['output']>;
  numberOfSales6h?: Maybe<Scalars['Int']['output']>;
  numberOfSales24h?: Maybe<Scalars['Int']['output']>;
};

export type TradeHistoryDto = {
  __typename?: 'TradeHistoryDTO';
  balance: Scalars['DecimalScalar']['output'];
  marketCap: Scalars['DecimalScalar']['output'];
  maxHoldingQty: Scalars['DecimalScalar']['output'];
  price: Scalars['DecimalScalar']['output'];
  quantity: Scalars['DecimalScalar']['output'];
  timestamp: Scalars['String']['output'];
  tradeValue: Scalars['DecimalScalar']['output'];
  type: TransactionType;
  wallet: Scalars['String']['output'];
};

export type TradeHistoryFilterInput = {
  fromTimestamp?: InputMaybe<Scalars['Int']['input']>;
  quantityMax?: InputMaybe<Scalars['DecimalScalar']['input']>;
  quantityMin?: InputMaybe<Scalars['DecimalScalar']['input']>;
  sortType?: Sort_Type;
  toTime?: InputMaybe<Scalars['Int']['input']>;
  tradeValueMax?: InputMaybe<Scalars['DecimalScalar']['input']>;
  tradeValueMin?: InputMaybe<Scalars['DecimalScalar']['input']>;
  type?: TransactionType;
  wallets?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type TradeHistoryPagination = {
  __typename?: 'TradeHistoryPagination';
  data: Array<TradeHistoryDto>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
};

export type TradingTransactionInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  chainId: Scalars['Int']['input'];
  classification?: TransactionClassification;
  filterRobot?: Scalars['Boolean']['input'];
  lastTimestamp?: InputMaybe<Scalars['Int']['input']>;
  /** sortBy these fields timestamp, usdAmount, baseAmount, with prefix + is asc, - is desc, example: +timestamp */
  sortBy?: InputMaybe<Scalars['String']['input']>;
  timestampFrom?: InputMaybe<Scalars['Int']['input']>;
  timestampTo?: InputMaybe<Scalars['Int']['input']>;
  token: Scalars['String']['input'];
  transactionUsdAmountFrom?: InputMaybe<Scalars['Float']['input']>;
  transactionUsdAmountTo?: InputMaybe<Scalars['Float']['input']>;
  transactionVolumeFrom?: InputMaybe<Scalars['Float']['input']>;
  transactionVolumeTo?: InputMaybe<Scalars['Float']['input']>;
  type?: TransactionType;
};

export type TradingTransactionPagination = {
  __typename?: 'TradingTransactionPagination';
  data: Array<FollowedTransaction>;
  fromTimestamp: Scalars['String']['output'];
};

export type Transaction = {
  __typename?: 'Transaction';
  baseAmount?: Maybe<Scalars['DecimalScalar']['output']>;
  baseToken: Scalars['String']['output'];
  chainId: Scalars['Int']['output'];
  logIndex?: Maybe<Scalars['Int']['output']>;
  maker: Scalars['String']['output'];
  pair?: Maybe<Scalars['String']['output']>;
  price?: Maybe<Scalars['DecimalScalar']['output']>;
  quoteAmount?: Maybe<Scalars['DecimalScalar']['output']>;
  quoteToken?: Maybe<Scalars['String']['output']>;
  timestamp: Scalars['String']['output'];
  txHash: Scalars['String']['output'];
  type: Scalars['String']['output'];
  usdAmount?: Maybe<Scalars['DecimalScalar']['output']>;
  usdPrice?: Maybe<Scalars['DecimalScalar']['output']>;
};

/** transaction classification */
export enum TransactionClassification {
  All = 'All',
  Followed = 'Followed',
  Fresh = 'Fresh',
  Insider = 'Insider',
  Kol = 'KOL',
  ProjectParty = 'ProjectParty',
  SameSource = 'SameSource',
  SmartMoney = 'SmartMoney',
  Sniper = 'Sniper',
  Whale = 'Whale'
}

export type TransactionEvent = {
  __typename?: 'TransactionEvent';
  baseAmount: Scalars['DecimalScalar']['output'];
  baseAmountRaw?: Maybe<Scalars['String']['output']>;
  baseToken: Scalars['String']['output'];
  factory?: Maybe<Scalars['String']['output']>;
  isSniper: Scalars['Boolean']['output'];
  label?: Maybe<Scalars['String']['output']>;
  liquidityUsd?: Maybe<Scalars['DecimalScalar']['output']>;
  nativeAmount: Scalars['DecimalScalar']['output'];
  nativePrice: Scalars['DecimalScalar']['output'];
  pair: Scalars['String']['output'];
  price: Scalars['DecimalScalar']['output'];
  quoteAmount: Scalars['DecimalScalar']['output'];
  quoteAmountRaw?: Maybe<Scalars['String']['output']>;
  quoteReserves: Scalars['DecimalScalar']['output'];
  quoteToken: Scalars['String']['output'];
  realTokenReserves: Scalars['DecimalScalar']['output'];
  type: Scalars['String']['output'];
  usdAmount: Scalars['DecimalScalar']['output'];
  usdPrice: Scalars['DecimalScalar']['output'];
};

export type TransactionInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  chainId: Scalars['Int']['input'];
  lastTimestamp?: InputMaybe<Scalars['Int']['input']>;
  /** sortBy these fields timestamp, usdAmount, baseAmount, with prefix + is asc, - is desc, example: +timestamp */
  sortBy?: InputMaybe<Scalars['String']['input']>;
  timestampFrom?: InputMaybe<Scalars['Int']['input']>;
  timestampTo?: InputMaybe<Scalars['Int']['input']>;
  token: Scalars['String']['input'];
  transactionUsdAmountFrom?: InputMaybe<Scalars['Float']['input']>;
  transactionUsdAmountTo?: InputMaybe<Scalars['Float']['input']>;
  transactionVolumeFrom?: InputMaybe<Scalars['Float']['input']>;
  transactionVolumeTo?: InputMaybe<Scalars['Float']['input']>;
  type?: TransactionType;
};

/** type of transaction */
export enum TransactionType {
  AddLiquidity = 'AddLiquidity',
  All = 'All',
  Buy = 'Buy',
  Liquidity = 'Liquidity',
  RemoveLiquidity = 'RemoveLiquidity',
  Sell = 'Sell',
  Trading = 'Trading'
}

export type WalletAssetChartInput = {
  duration: WalletDuration;
  unit: WalletBalanceUnit;
  walletAddress?: InputMaybe<Scalars['String']['input']>;
};

export type WalletBalanceDto = {
  __typename?: 'WalletBalanceDTO';
  balanceChangeNative: Scalars['DecimalScalar']['output'];
  balanceChangeUsd: Scalars['DecimalScalar']['output'];
  duration: WalletDuration;
  nativeTokenAddress: Scalars['String']['output'];
  nativeTokenBalance: Scalars['DecimalScalar']['output'];
  nativeTokenSymbol: Scalars['String']['output'];
  usdBalance: Scalars['DecimalScalar']['output'];
  userId: Scalars['String']['output'];
  walletAddress: Scalars['String']['output'];
  walletType: WalletType;
};

export type WalletBalanceInput = {
  duration: WalletDuration;
  walletAddress?: InputMaybe<Scalars['String']['input']>;
};

export enum WalletBalanceUnit {
  Native = 'Native',
  Usd = 'Usd'
}

export enum WalletDuration {
  D1 = 'd1',
  M1 = 'm1',
  W1 = 'w1',
  Y1 = 'y1'
}

export type WalletInfoOnHoverDto = {
  __typename?: 'WalletInfoOnHoverDTO';
  balance: Scalars['DecimalScalar']['output'];
  buys: Scalars['Float']['output'];
  createdAt: Scalars['DateTime']['output'];
  realizedPnL: Scalars['DecimalScalar']['output'];
  sells: Scalars['Float']['output'];
  totalBuyUsd: Scalars['DecimalScalar']['output'];
  totalSellUsd: Scalars['DecimalScalar']['output'];
  totalUsdValue: Scalars['DecimalScalar']['output'];
};

export type WalletInfoOnHoverInput = {
  chain?: ChainType;
  tokenAddress: Scalars['String']['input'];
  wallet: Scalars['String']['input'];
};

export type WalletToken = {
  __typename?: 'WalletToken';
  address?: Maybe<Scalars['String']['output']>;
  avgMarketCap?: Maybe<Scalars['DecimalScalar']['output']>;
  avgPriceUsd?: Maybe<Scalars['DecimalScalar']['output']>;
  balance?: Maybe<Scalars['DecimalScalar']['output']>;
  buys?: Maybe<Scalars['Float']['output']>;
  chainId?: Maybe<Scalars['Int']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  maxHoldingQty?: Maybe<Scalars['DecimalScalar']['output']>;
  nativeBalance?: Maybe<Scalars['DecimalScalar']['output']>;
  rawBalance?: Maybe<Scalars['DecimalScalar']['output']>;
  realizedPnL?: Maybe<Scalars['DecimalScalar']['output']>;
  sells?: Maybe<Scalars['Float']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
  token?: Maybe<Scalars['String']['output']>;
  tokenAccount?: Maybe<Scalars['String']['output']>;
  totalBuyQty?: Maybe<Scalars['DecimalScalar']['output']>;
  totalBuyUsd?: Maybe<Scalars['DecimalScalar']['output']>;
  totalSellQty?: Maybe<Scalars['DecimalScalar']['output']>;
  totalSellUsd?: Maybe<Scalars['DecimalScalar']['output']>;
  totalTradedQty?: Maybe<Scalars['DecimalScalar']['output']>;
  totalUsdValue?: Maybe<Scalars['DecimalScalar']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export enum WalletType {
  Funding = 'Funding',
  Futures = 'Futures',
  Spot = 'Spot'
}

export type WebsitesInfo = {
  __typename?: 'WebsitesInfo';
  label?: Maybe<Scalars['String']['output']>;
  url?: Maybe<Scalars['String']['output']>;
};
