import { gql } from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  Decimal: { input: any; output: any; }
  JSON: { input: any; output: any; }
  Time: { input: any; output: any; }
};

export type AddUserWalletWhitelistInput = {
  google2FA: Scalars['String']['input'];
  wallets: Array<InputMaybe<WalletItemInput>>;
};

export type Mutation = {
  __typename?: 'Mutation';
  addWalletWhitelist: UserWalletWhitelist;
  modifyWalletWhitelist: UserWalletWhitelist;
  /** Withdraw one token from wallet A to B */
  withdraw: WithdrawRecord;
};


export type MutationAddWalletWhitelistArgs = {
  input: AddUserWalletWhitelistInput;
};


export type MutationModifyWalletWhitelistArgs = {
  input: AddUserWalletWhitelistInput;
};


export type MutationWithdrawArgs = {
  input: WithdrawInput;
};

export enum OnchainNetwork {
  Ethereum = 'ETHEREUM',
  Solana = 'SOLANA'
}

export type Query = {
  __typename?: 'Query';
  getWalletWhitelist: UserWalletWhitelist;
  /** Get user's wallet withdraw history */
  getWithdrawFee: WithdrawFeeResp;
  getWithdrawHistory: Array<Maybe<WithdrawRecord>>;
};


export type QueryGetWithdrawFeeArgs = {
  input: WithdrawInput;
};


export type QueryGetWithdrawHistoryArgs = {
  input: SearchWithdrawHistoryInput;
};

export type SearchWalletWhitelistInput = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
};

export type SearchWithdrawHistoryInput = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<WithdrawStatus>;
  walletAddress: Scalars['String']['input'];
};

export type UserWalletWhitelist = {
  __typename?: 'UserWalletWhitelist';
  createdAt: Scalars['Time']['output'];
  id: Scalars['ID']['output'];
  updatedAt: Scalars['Time']['output'];
  userId: Scalars['String']['output'];
  whitelist: Array<Maybe<WalletItem>>;
};

export type WalletItem = {
  __typename?: 'WalletItem';
  chainId: Scalars['String']['output'];
  walletAddress: Scalars['String']['output'];
};

export type WalletItemInput = {
  chainId: Scalars['String']['input'];
  walletAddress: Scalars['String']['input'];
};

export type WithdrawFeeResp = {
  __typename?: 'WithdrawFeeResp';
  fee: Scalars['Decimal']['output'];
  network: OnchainNetwork;
  unit: Scalars['String']['output'];
};

export type WithdrawInput = {
  amount: Scalars['Decimal']['input'];
  chainId: Scalars['String']['input'];
  fromAddress: Scalars['String']['input'];
  toAddress: Scalars['String']['input'];
  token: Scalars['String']['input'];
};

export type WithdrawRecord = {
  __typename?: 'WithdrawRecord';
  amount: Scalars['Decimal']['output'];
  chainId: Scalars['String']['output'];
  createdAt: Scalars['Time']['output'];
  decimals: Scalars['Int']['output'];
  errorCode?: Maybe<Scalars['String']['output']>;
  errorMessage?: Maybe<Scalars['String']['output']>;
  fromAddress: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  meta?: Maybe<Scalars['String']['output']>;
  network: OnchainNetwork;
  status: WithdrawStatus;
  toAddress: Scalars['String']['output'];
  token: Scalars['String']['output'];
  txid?: Maybe<Scalars['String']['output']>;
  updatedAt: Scalars['Time']['output'];
  userId: Scalars['String']['output'];
};

export enum WithdrawStatus {
  /** The transaction is on-chain and has been confirmed by at least one subsequent block */
  Confirmed = 'Confirmed',
  /** The transaction failed */
  Failed = 'Failed',
  /** The transaction has been accepted by the system and is in the process of being sent to the blockchain */
  Pending = 'Pending',
  /** The transaction has been received by a validator but is not confirmed yet */
  Processed = 'Processed',
  /** The transaction was processed successfully */
  Success = 'Success'
}
