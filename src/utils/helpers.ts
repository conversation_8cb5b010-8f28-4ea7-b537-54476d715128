import { ChainIds } from '@/types/enums.ts'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import i18n from '@/i18n'
import { f } from 'fintech-number'
import { TimeWheelDateType } from '@/redux/modules/tokenDetail.slice.ts'
import { LAUNCHPADS } from '@/lib/constant.ts'

export * from './copy-trade-helper'

dayjs.extend(relativeTime)

/**
 * Defines the mapping of chain IDs to their respective names.
 */
export const BLOCKCHAIN_NAMES: Record<ChainIds, string> = {
  [ChainIds.Ethereum]: 'Ethereum',
  [ChainIds.Bsc]: 'BSC',
  [ChainIds.BscTest]: 'BSC Testnet',
  [ChainIds.Avalanche]: 'Avalanche',
  [ChainIds.FantomOpera]: 'Fantom Opera',
  [ChainIds.Arbitrum]: 'Arbitrum',
  [ChainIds.Polygon]: 'Polygon',
  [ChainIds.Pulse]: 'Pulse',
  [ChainIds.Bitrock]: 'Bitrock',
  [ChainIds.Shibarium]: 'Shibarium',
  [ChainIds.Cybria]: 'Cybria',
  [ChainIds.Base]: 'Base',
  [ChainIds.Solana]: 'Solana',
  [ChainIds.BTC]: 'Bitcoin',
  [ChainIds.TON]: 'TON',
  [ChainIds.TRX]: '',
}

export const BLOCKCHAIN_SHORTNAME: Record<ChainIds, string> = {
  [ChainIds.Ethereum]: 'ETH',
  [ChainIds.Bsc]: 'BSC',
  [ChainIds.BscTest]: 'BSC Testnet',
  [ChainIds.Avalanche]: 'AVAX',
  [ChainIds.FantomOpera]: 'FTM',
  [ChainIds.Arbitrum]: 'ARB',
  [ChainIds.Polygon]: 'MATIC',
  [ChainIds.Pulse]: 'PULSE',
  [ChainIds.Bitrock]: 'BITROCK',
  [ChainIds.Shibarium]: 'SHIB',
  [ChainIds.Cybria]: 'CYB',
  [ChainIds.Base]: 'BASE',
  [ChainIds.Solana]: 'SOL',
  [ChainIds.BTC]: 'BTC',
  [ChainIds.TON]: 'TON',
  [ChainIds.TRX]: 'TRX',
}

export const getBlockChainLogo = (chainId: ChainIds, baseToken: string) => {
  const trustwalletAssetURL = 'https://raw.githubusercontent.com/trustwallet/assets/master/blockchains'

  // To get more chain go to : https://github.com/trustwallet/assets/tree/master/blockchains
  const chainAsset: Record<ChainIds, string> = {
    [ChainIds.Bsc]: 'smartchain',
    [ChainIds.Ethereum]: 'ethereum',
    [ChainIds.Arbitrum]: 'arbitrum',
    [ChainIds.Polygon]: 'polygon',
    [ChainIds.Pulse]: 'pulse',
    [ChainIds.Bitrock]: 'bitrock',
    [ChainIds.Shibarium]: 'shibarium',
    [ChainIds.Cybria]: 'cybria',
    [ChainIds.Solana]: 'solana',
    [ChainIds.Base]: 'base',
    [ChainIds.Avalanche]: '',
    [ChainIds.BscTest]: '',
    [ChainIds.FantomOpera]: '',
    [ChainIds.BTC]: '',
    [ChainIds.TON]: '',
    [ChainIds.TRX]: 'tron',
  }

  return `${trustwalletAssetURL}/${chainAsset[chainId]}/assets/${baseToken}/logo.png`
  // Optional: return ChainIcon
}

export function getBlockchainLogo2(chainId?: ChainIds) {
  if (!chainId) return ''
  const trustwalletAssetURL = 'https://raw.githubusercontent.com/trustwallet/assets/master/blockchains'
  const chainAsset: Record<ChainIds, string> = {
    [ChainIds.Bsc]: 'smartchain',
    [ChainIds.Ethereum]: 'ethereum',
    [ChainIds.Arbitrum]: 'arbitrum',
    [ChainIds.Polygon]: 'polygon',
    [ChainIds.Pulse]: 'pulse',
    [ChainIds.Bitrock]: 'bitrock',
    [ChainIds.Shibarium]: 'shibarium',
    [ChainIds.Cybria]: 'cybria',
    [ChainIds.Solana]: 'solana',
    [ChainIds.Base]: 'base',
    [ChainIds.Avalanche]: 'avalanchec',
    [ChainIds.BscTest]: 'binance',
    [ChainIds.FantomOpera]: 'fantom',
    [ChainIds.BTC]: 'bitcoin',
    [ChainIds.TON]: 'ton',
    [ChainIds.TRX]: 'tron',
  }
  return `${trustwalletAssetURL}/${chainAsset[chainId]}/info/logo.png`
}

export const timeFromNow = (timestamp?: number) => {
  if (!timestamp) return '--'

  if (isNaN(timestamp) || timestamp <= 0) return '--'

  const timestampMs =
    String(timestamp).length === 10
      ? timestamp * 1000 // Convert seconds to milliseconds if needed
      : timestamp

  const past = dayjs(timestampMs)
  if (!past.isValid()) return '--'

  const now = dayjs()
  const seconds = now.diff(past, 'seconds')
  const minutes = now.diff(past, 'minutes')
  const hours = now.diff(past, 'hours')
  const days = now.diff(past, 'days')
  const months = now.diff(past, 'months')
  const years = now.diff(past, 'years')

  if (seconds < 60) return `${seconds}s`
  if (minutes < 60) return `${minutes}m`
  if (hours < 24) return `${hours}h`
  if (days < 30) return `${days} day${days > 1 ? 's' : ''}`
  if (months < 12) return `${months} month${months > 1 ? 's' : ''}`
  return `${years} year${years > 1 ? 's' : ''}`
}

export const formatMoney = (amount?: number, showCurrency: boolean = true, unit?: string) => {
  if (!amount) return '0'

  const absAmount = Math.abs(amount)

  const formatter = (num: number) => parseFloat(num.toFixed(2)).toLocaleString() // Ensures proper formatting

  let formattedAmount: string

  if (absAmount >= 1_000_000_000_000_000) {
    formattedAmount = `${formatter(absAmount / 1_000_000_000_000_000)}Q` // Quadrillions
  } else if (absAmount >= 1_000_000_000_000) {
    formattedAmount = `${formatter(absAmount / 1_000_000_000_000)}T` // Trillions
  } else if (absAmount >= 1_000_000_000) {
    formattedAmount = `${formatter(absAmount / 1_000_000_000)}B` // Billions
  } else if (absAmount >= 1_000_000) {
    formattedAmount = `${formatter(absAmount / 1_000_000)}M` // Millions
  } else if (absAmount >= 1_000) {
    formattedAmount = `${formatter(absAmount / 1_000)}K` // Thousands
  } else if (absAmount < 1) {
    formattedAmount = Number(absAmount)
      .toFixed(8)
      .replace(/\.0+$/, '')
      .replace(/(\.\d+?)0+$/, '$1')
  } else {
    formattedAmount = formatter(parseFloat(`${absAmount}`))
  }

  let result = showCurrency ? `$${formattedAmount}` : formattedAmount
  if (amount < 0) {
    result = `-${result}`
  }

  if (unit) {
    result += unit
  }

  return result
}

// Subscript numbers for small values
const subscript = [
  { number: 0, sub: '₀' },
  { number: 1, sub: '₁' },
  { number: 2, sub: '₂' },
  { number: 3, sub: '₃' },
  { number: 4, sub: '₄' },
  { number: 5, sub: '₅' },
  { number: 6, sub: '₆' },
  { number: 7, sub: '₇' },
  { number: 8, sub: '₈' },
  { number: 9, sub: '₉' },
]

const zeroCountToSubNumber = (zeroCount: number): string => {
  if (zeroCount == 0 || zeroCount == 1) return ''
  if (zeroCount > 9) {
    const subscriptNumbers = zeroCount
      .toString()
      .split('')
      .map((num) => subscript[Number(num)]?.sub)
      .join('')
    return subscriptNumbers
  }
  return subscript[zeroCount]?.sub
}

export const formatSmallNumber = (value?: number, showCurrency: boolean = true, afterSub?: number): string => {
  if (!value) return '--'

  if (value >= 0.01) {
    return showCurrency ? `$${formatMoney(value, false)}` : formatMoney(value, false) // Remove extra $
  }

  const str = value.toFixed(20) // Convert to full decimal representation
  const match = str.match(/^0\.0+(?=\d)/) // Find leading zeros after "0."

  if (!match) {
    return showCurrency ? `$${formatMoney(value, false)}` : formatMoney(value, false) // Ensure formatMoney doesn't add $
  }

  const zeroCount = match[0].length - 2 // Count extra zeros after "0.0"
  const subscript = `${zeroCountToSubNumber(zeroCount)}` // Create subscript for zeros

  // Extract only meaningful digits, ignoring trailing zeros
  const significantPart = str
    .replace(/^0\.0+/, '')
    .replace(/^0+/, '')
    .slice(0, afterSub ?? 4)

  return `${showCurrency ? '$' : ''}0.0${subscript}${significantPart}`
}

export const formatPrice = (value?: number, afterSub?: number) => {
  return value ? formatSmallNumber(value, true, afterSub) : '0'
}
export const symbolFormat = (symbol: string) => {
  return symbol
}

export function getFormatPriceDecimal(price: string) {
  const input = formatDetailPrice(Number(price ?? 0))
  const subMatch = input.match(/<sub[^>]*>(.*?)<\/sub>/)
  if (!subMatch) {
    const parts = input.split('.')
    const dec = parts[1] || ''
    return dec.length
  }

  const subValue = subMatch[1]

  const subNumber = parseInt(subValue, 10)

  const indexSub = input.indexOf('<sub')
  const beforeSub = input.slice(0, indexSub)

  const indexEndSub = input.indexOf('</sub>')
  const trailing = input.slice(indexEndSub + 6)

  // let intPart = ''
  let decPart = ''
  const dotIndex = beforeSub.indexOf('.')
  if (dotIndex !== -1) {
    // intPart = beforeSub.slice(0, dotIndex)
    decPart = beforeSub.slice(dotIndex + 1)
  } else {
    // intPart = beforeSub
  }

  let paddedDecPart = decPart
  if (paddedDecPart.length < subNumber) {
    paddedDecPart = '0'.repeat(subNumber - paddedDecPart.length) + paddedDecPart
  }

  const newDecPart = paddedDecPart + trailing
  // const processed = intPart + "." + newDecPart;

  return newDecPart.length
}

export const formatDetailPrice = (value: number): string => {
  if (value === 0 || !value) return '0'

  // Handle values < 1
  if (value < 1) {
    // Convert to string with many decimal places
    const str = value.toFixed(20)

    // Find the first non-zero digit after decimal
    const match = str.match(/^0\.0*[1-9]/)

    if (!match) return str // Safety check

    // Count zeros after decimal point
    const zeroCount = match[0].length - 3 // -2 for "0." and -1 for the first non-zero digit

    if (zeroCount >= 4) {
      // For numbers with >= 4 zeros after decimal point (e.g., 0.000123)
      const subscript = `<sub class="text-[70%]">${zeroCount}</sub>`

      // Get the first 4 significant digits
      let significantPart = ''
      let significantDigitCount = 0
      let startCounting = false

      // Find the first 4 significant digits
      for (let i = 0; i < str.length && significantDigitCount < 4; i++) {
        const char = str[i]
        if (char !== '0' && char !== '.') {
          startCounting = true
        }
        if (startCounting && /[0-9]/.test(char)) {
          significantPart += char
          significantDigitCount++
        }
      }

      return `0.0${subscript}${significantPart}`
    } else {
      // For numbers like 0.1234, 0.01234, 0.001234
      const precision = 4 + (zeroCount > 0 ? zeroCount : 0)
      return value.toPrecision(precision).replace(/\.?0+$/, '')
    }
  }

  // Handle values >= 1
  const fixed = value.toFixed(4)

  // Remove trailing zeros and decimal point if all decimal digits are zero
  return fixed.replace(/\.0+$/, '').replace(/(\.\d+?)0+$/, '$1')
}

// Super long price handling
export const formatSuperLongPrice = (value: number, maxLength = 10): string => {
  const formatted = formatDetailPrice(value)

  // If HTML is present (has subscript for small numbers), preserve it
  if (formatted.includes('<sub')) {
    return formatted // Already optimized for display
  }

  // For large numbers with many digits
  if (formatted.length > maxLength) {
    // If very long (over maxLength + 5), apply both size reduction and truncation
    if (formatted.length > maxLength + 5) {
      const visiblePart = formatted.slice(0, maxLength)
      const hiddenPart = formatted.slice(maxLength)
      return `<span class="text-[8pt]">${visiblePart}</span><span class="hidden" title="${formatted}">${hiddenPart}</span>...`
    }
    // If moderately long, just reduce the font size
    else {
      return `<span class="text-[8pt]" title="${formatted}">${formatted}</span>`
    }
  }

  return formatted
}

/**
 * Formats a percentage value according to specified rules
 * @param value - The percentage value (already multiplied by 100)
 * @param includeSign - Whether to include + sign for positive values
 * @returns Formatted percentage string
 */
export const formatPercentage = (value: number, includeSign = false): string => {
  // Handle null/undefined case
  if (!value || isNaN(value)) return '--'

  // Handle extremely large values (≥10000T%)
  if (value >= 10000000000000000) {
    return '>9999T%'
  }

  // Handle value conversion with suffixes for large percentages
  if (value >= 1000) {
    let formattedValue: string

    if (value >= 1000000000000) {
      // Trillions
      formattedValue = (value / 1000000000000)
        .toFixed(2)
        .replace(/\.0+$/, '')
        .replace(/(\.\d+?)0+$/, '$1')
      return `${includeSign && value > 0 ? '+' : ''}${formattedValue}T%`
    } else if (value >= 1000000000) {
      // Billions
      formattedValue = (value / 1000000000)
        .toFixed(2)
        .replace(/\.0+$/, '')
        .replace(/(\.\d+?)0+$/, '$1')
      return `${includeSign && value > 0 ? '+' : ''}${formattedValue}B%`
    } else if (value >= 1000000) {
      // Millions
      formattedValue = (value / 1000000)
        .toFixed(2)
        .replace(/\.0+$/, '')
        .replace(/(\.\d+?)0+$/, '$1')
      return `${includeSign && value > 0 ? '+' : ''}${formattedValue}M%`
    } else {
      // Thousands (K)
      formattedValue = (value / 1000)
        .toFixed(2)
        .replace(/\.0+$/, '')
        .replace(/(\.\d+?)0+$/, '$1')
      return `${includeSign && value > 0 ? '+' : ''}${formattedValue}K%`
    }
  }

  // Regular formatting for values < 10K%
  // Round to 2 decimal places
  if (!value) return '--'
  let formattedValue = Number(value)?.toFixed(2)
  // Remove unnecessary zeros
  formattedValue = formattedValue.replace(/\.0+$/, '') // Remove if all decimal places are 0
  formattedValue = formattedValue.replace(/(\.\d+?)0+$/, '$1') // Remove trailing zeros

  // Add sign if needed
  if (includeSign && value > 0) {
    formattedValue = `+${formattedValue}`
  }

  return `${formattedValue}%`
}

/**
 * Calculates percentage change between two values
 * @param currentValue - The latest value
 * @param previousValue - The previous value to compare against
 * @returns Formatted percentage change
 */
export const calculatePercentageChange = (currentValue: number, previousValue: number): string => {
  if (!previousValue) return '--'

  const percentageChange = ((currentValue - previousValue) / previousValue) * 100
  return formatPercentage(percentageChange, true)
}

export const formatSmallLongNumber = (value: number): string => {
  if (value === 0 || !isFinite(value)) return '--'

  const isNegative = value < 0
  const decimals = 20
  const factor = Math.pow(10, decimals)
  const floored = Math.floor(Math.abs(value) * factor) / factor
  const absValueStr = floored.toString().replace(/0+$/, '').replace(/\.$/, '')
  const [_, decimal = ''] = absValueStr.split('.')
  // console.log({ value, floored, absValueStr })

  const zeroMatch = decimal.match(/0{4,}/)
  if (zeroMatch) {
    const zeroStr = zeroMatch[0]
    const zeroStart = decimal.indexOf(zeroStr)
    const afterZeros = decimal.slice(zeroStart + zeroStr.length)
    const nextDigits = afterZeros.slice(0, 3)
    return `${isNegative ? '-' : ''}0.0{${zeroStr.length}}${nextDigits}`
  }

  if (floored < 1) {
    let count = 0
    let result = ''
    for (const digit of decimal) {
      result += digit
      if (digit !== '0') count++
      if (count >= 4) break
    }
    if (!result) return '--'
    return `${isNegative ? '-' : ''}0.${result}`
  } else {
    const factor4 = Math.pow(10, 4)
    const floored4 = Math.floor(floored * factor4) / factor4
    return `${isNegative ? '-' : ''}${floored4.toString().replace(/0+$/, '').replace(/\.$/, '')}`
  }
}

export const getLinkExplorer = (chainId: ChainIds, txHash: string) => {
  const chainExplorer: Record<ChainIds, string> = {
    [ChainIds.Bsc]: 'https://bscscan.com/tx/',
    [ChainIds.Ethereum]: 'https://etherscan.io/tx/',
    [ChainIds.Arbitrum]: 'https://arbiscan.io/tx/',
    [ChainIds.Polygon]: 'https://polygonscan.com/tx/',
    [ChainIds.Pulse]: 'https://pulsechain.explorer.pulsechain.com/tx/',
    [ChainIds.Bitrock]: '',
    [ChainIds.Shibarium]: '',
    [ChainIds.Cybria]: '',
    [ChainIds.Solana]: 'https://solscan.io/tx/',
    [ChainIds.Base]: 'https://basescan.org/tx/',
    [ChainIds.Avalanche]: 'https://snowtrace.io/tx/',
    [ChainIds.BscTest]: 'https://testnet.bscscan.com/tx/',
    [ChainIds.FantomOpera]: 'https://ftmscan.com/tx/',
    [ChainIds.BTC]: '',
    [ChainIds.TON]: '',
    [ChainIds.TRX]: '',
  }

  return `${chainExplorer[chainId]}${txHash}`
}

export function formatTokenPrice(
  value: number,
  options?: {
    roundType?: 'round' | 'floor' | 'ceil'
  },
): {
  integerPart: string
  zeroCount: number
  decimalPart: string
  unit?: string
} {
  const roundType = options?.roundType || 'round'

  const roundFn = (val: number) => {
    switch (roundType) {
      case 'floor':
        return Math.floor(val)
      case 'ceil':
        return Math.ceil(val)
      default:
        return Math.round(val)
    }
  }

  const negative = value < 0
  const absValue = Math.abs(value)

  if (absValue >= 1_000_000_000) {
    const formattedValue = (absValue / 1_000_000_000).toFixed(4)
    return {
      integerPart: `${negative ? '-' : ''}${formattedValue.replace(/\.0+$/, '').replace(/(\.\d+?)0+$/, '1')}`,
      zeroCount: 0,
      decimalPart: '',
      unit: 'B',
    }
  }

  if (absValue < 1) {
    const str = value.toString()
    const [base, exponent] = str.split('e')

    if (exponent) {
      const exponentValue = parseInt(exponent, 10)
      const zerosToAdd = Math.abs(exponentValue) - 1
      const [integerPart, decimalPart] = base.split('.')
      let newDecimalPart = ''
      if (decimalPart) {
        const decimalLength = parseInt(integerPart) >= 1 ? 4 : 3
        const decimalLongerThan4 = decimalPart.length > decimalLength
        const decimal = decimalLongerThan4
          ? roundFn(parseFloat(decimalPart.slice(0, decimalLength)) / 10)
          : parseFloat(decimalPart)
        newDecimalPart = decimalLongerThan4 ? decimal.toString() : decimalPart
      }
      return {
        integerPart: `${negative ? '-' : ''}0`,
        zeroCount: zerosToAdd,
        decimalPart: `${integerPart}${newDecimalPart}`.replace(/0+$/, ''),
      }
    }

    const decimalIndex = str.indexOf('.')
    const decimalPart = str.slice(decimalIndex + 1)
    const zeroMatch = decimalPart.match(/^0{4,}/)

    if (zeroMatch) {
      const zeroStr = zeroMatch[0]
      const zeroStart = decimalPart.indexOf(zeroStr)
      const afterZeros = decimalPart.slice(zeroStart + zeroStr.length)
      const nextDigits = afterZeros.slice(0, 5)
      const rounded = nextDigits.length >= 4 ? roundFn(parseFloat(nextDigits) / 10) : afterZeros
      const zeroCount = zeroStr.length
      return {
        integerPart: `${negative ? '-' : ''}0`,
        zeroCount,
        decimalPart: rounded.toString().replace(/0+$/, ''),
      }
    } else {
      const zeroCount = decimalPart.search(/[^0]/)
      const afterZeros = decimalPart.slice(zeroCount, zeroCount + 5)
      const rounded = afterZeros.length >= 4 ? roundFn(parseFloat(afterZeros) / 10) : afterZeros.toString()
      const zeroString = zeroCount > 0 ? '0'.repeat(zeroCount) : ''
      return {
        integerPart: `${negative ? '-' : ''}0`,
        zeroCount: 0,
        decimalPart: (zeroString + rounded).replace(/0+$/, ''),
      }
    }
  } else {
    const str = value.toString()
    const [integerPart, decimalPart] = str.split('.')
    if (!decimalPart) {
      return {
        integerPart: `${negative ? '-' : ''}${str}`,
        zeroCount: 0,
        decimalPart: '',
      }
    }

    if (decimalPart.length > 4) {
      const zeroCount = decimalPart.search(/[^0]/)
      const rounded = roundFn(parseFloat(decimalPart.slice(0, 5)) / 10)
      const zeroString = zeroCount > 0 ? '0'.repeat(zeroCount) : ''
      return {
        integerPart: `${negative ? '-' : ''}${integerPart}`,
        zeroCount: 0,
        decimalPart: (zeroString + rounded.toString()).replace(/0+$/, ''),
      }
    }
    return {
      integerPart: `${negative ? '-' : ''}${str}`,
      zeroCount: 0,
      decimalPart: '',
    }
  }
}

export const isRealIOSSafari = (): boolean => {
  const userAgent = navigator.userAgent.toLowerCase()
  const isIOS = /iphone|ipad|ipod/.test(userAgent)
  const isSafari = /safari/.test(userAgent) && !/crios|fxios|edgios/.test(userAgent)
  
  const isNotDevTools = !('chrome' in window) && window.self === window.top
  
  return isIOS && isSafari && isNotDevTools
}

export const detectIOSBrowser = () => {
  const userAgent = navigator.userAgent.toLowerCase()
  
  if (!/iphone|ipad|ipod/.test(userAgent)) {
    return { isIOS: false, browser: null }
  }

  // iOS detected, now check which browser
  if (/crios/.test(userAgent)) return { isIOS: true, browser: 'chrome' }
  if (/fxios/.test(userAgent)) return { isIOS: true, browser: 'firefox' }
  if (/edgios/.test(userAgent)) return { isIOS: true, browser: 'edge' }
  if (/safari/.test(userAgent)) return { isIOS: true, browser: 'safari' }
  
  return { isIOS: true, browser: 'unknown' }
}


export function formatNumber(value: number | string): string {
  if (typeof value === 'string') {
    value = parseFloat(value)
  }
  if (isNaN(value)) return '--'
  return value
    .toLocaleString('en-US')
    .replace(/\.0+$/, '')
    .replace(/(\.\d+?)0+$/, '$1')
    .replace(/,/g, '.')
}

export const removeFormatting = (value: string): string => {
  if (!value) return ''
  return value.replace(/,/g, '')
}

export function formatNumberWithCommas(value: string): string {
  if (!value || value === '' || value === '.') return value
  
  const parts = value.split('.')
  const integerPart = parts[0]
  const decimalPart = parts[1]
  
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  
  if (decimalPart !== undefined) {
    return `${formattedInteger}.${decimalPart}`
  }
  
  return formattedInteger
}

export function formatNumberWithUnit(value: number, unit: string): string {
  if (isNaN(value)) return '--'
  return `${value
    .toLocaleString('en-US')
    .replace(/\.0+$/, '')
    .replace(/(\.\d+?)0+$/, '$1')
    .replace(/,/g, '.')} ${unit}`
}

export function formatChartPrice(value: any): string {
  if (isNaN(value)) return '--'
  const absValue = Math.abs(value)
  const moneyFormatted = formatTokenPrice(absValue)
  const { integerPart, zeroCount, decimalPart } = moneyFormatted
  const zeroCountStr = zeroCountToSubNumber(zeroCount)
  const formattedDecimalPart = decimalPart.replace(/0+$/, '').replace('-', '')
  let output = ''
  if (zeroCount > 0) {
    output = decimalPart ? `${f(+integerPart)}.0${zeroCountStr}${formattedDecimalPart}` : f(+integerPart)
  } else {
    output = decimalPart ? `${f(+integerPart)}.${formattedDecimalPart}` : f(+integerPart)
  }
  return `${value < 0 ? '-' : ''}${output}`
}

export function isNumber(value: number | string | undefined): boolean {
  if (typeof value !== 'number') return false
  if (isNaN(value)) return false
  if (!isFinite(value)) return false
  return !(value === Infinity || value === -Infinity)
}

export const tConst = (key: string) => {
  if (!i18n.isInitialized) {
    console.warn('i18n is not initialized yet.')
    return key
  }
  return i18n.t(key)
}

export function getDaysInMonth(year: string, month: string): number {
  return dayjs(`${year}-${month}-01`).daysInMonth()
}

export const isDiffOver1Month = (startDate?: TimeWheelDateType, endDate?: TimeWheelDateType): boolean => {
  if (!startDate || !endDate) return false

  const format = 'YYYY-MM-DD HH:mm'

  const start = dayjs(
    `${startDate.year}-${startDate.month}-${startDate.day} ${startDate.hour}:${startDate.minute}`,
    format,
  )
  const end = dayjs(`${endDate.year}-${endDate.month}-${endDate.day} ${endDate.hour}:${endDate.minute}`, format)

  const diffInMonths = Math.abs(end.diff(start, 'month', true))

  return diffInMonths > 1
}

export const getLaunchpad = (dexes: string[] | undefined) => {
  if (!dexes) return undefined
  for (const dex of dexes) {
    if (LAUNCHPADS.includes(dex)) {
      return dex
    }
  }
  return undefined
}

export function formatSmartTimeDiff(timestamp: number): string {
  const now = dayjs()
  const diffMs = Number(timestamp) - now.valueOf()

  if (isNaN(diffMs) || !isFinite(diffMs)) return '--'

  const diffInSeconds = diffMs / 1000
  if (Math.abs(diffInSeconds) < 60) return `${Math.round(Math.abs(diffInSeconds))}s`

  const diffInMinutes = diffInSeconds / 60
  if (Math.abs(diffInMinutes) < 60) return `${Math.round(Math.abs(diffInMinutes))}m`

  const diffInHours = diffInMinutes / 60
  if (Math.abs(diffInHours) < 24) return `${Math.round(Math.abs(diffInHours))}h`

  const diffInDays = diffInHours / 24
  if (Math.abs(diffInDays) < 30) return `${Math.round(Math.abs(diffInDays))}D`

  const diffInMonths = diffInDays / 30
  if (Math.abs(diffInMonths) < 12) return `${Math.round(Math.abs(diffInMonths))}M`

  const diffInYears = diffInDays / 365
  return `${Math.round(Math.abs(diffInYears))}Y`
}

export const waitTimer = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

const getSubscript = (numStr: string): string => {
  return [...numStr]
    .map((char) => {
      const item = subscript.find((s) => s.number.toString() === char)
      return item?.sub || ''
    })
    .join('')
}

const handleSubscript = (leadingZeros: number, decimals: string, isNegative: boolean): string => {
  const digitsAfterZeros = decimals.slice(leadingZeros)
  const flooredDigits = digitsAfterZeros.slice(0, 4) // floor to 4 digits only
  const sub = getSubscript(leadingZeros.toString())
  const result = `${isNegative ? '-' : ''}0.0${sub}${flooredDigits}`

  // Return 0 if the result is "0.", "0.0", or "0.00"
  if (result === '0.' || result === '0.0' || result === '0.00') {
    return '0'
  }

  return result
}

export function formatLongValue(value: number, is2Decimal: boolean = false, decimalPlaces: number = 6): string {
  const suffixes = ['', 'K', 'M', 'B', 'T']

  if (value === 0 || !isFinite(value)) return '0'

  const isNegative = value < 0
  const absVal = Math.abs(value)
  const decimalsToShow = is2Decimal ? 2 : decimalPlaces

  // Large numbers (>= 1000)
  if (absVal >= 1000) {
    let tier = Math.floor(Math.log10(absVal) / 3)
    tier = Math.min(tier, suffixes.length - 1)
    const scaled = Math.floor(absVal / Math.pow(10, tier * 3))
    return `${isNegative ? '-' : ''}${scaled}${suffixes[tier]}`
  }

  // Numbers between 1 and 999 (with decimals)
  if (absVal >= 1) {
    const decimals = (absVal % 1).toFixed(decimalPlaces).slice(2)
    if (decimals === '0'.repeat(decimalPlaces)) {
      return `${isNegative ? '-' : ''}${Math.floor(absVal)}`
    }

    const match = decimals.match(/^(0+)/)
    const leadingZeros = match ? match[0].length : 0

    if (leadingZeros >= 4) {
      return handleSubscript(leadingZeros, decimals, isNegative)
    } else {
      const cleanDecimal = decimals.replace(/0+$/, '').slice(0, decimalsToShow)
      const result = `${isNegative ? '-' : ''}${Math.floor(absVal)}.${cleanDecimal}`
      if (/^[-]?0\.?0*$/.test(result)) return '0'
      return result
    }
  }

  // Numbers less than 1
  const parts = absVal.toString().split('.')
  const decimals = parts[1] || ''
  const match = decimals.match(/^(0+)/)
  const leadingZeros = match ? match[0].length : 0

  if (leadingZeros >= 4) {
    return handleSubscript(leadingZeros, decimals, isNegative)
  } else {
    const cleanDecimal = decimals.replace(/0+$/, '').slice(0, decimalsToShow)
    const result = `${isNegative ? '-' : ''}0.${cleanDecimal}`
    if (/^[-]?0\.?0*$/.test(result)) return '0'
    return result
  }
}

export function convertTimeWheelToTimestamp(dateObj: TimeWheelDateType): number | null {
  const { year = '1970', month = '1', day = '1', hour = '0', minute = '0' } = dateObj

  const dateStr = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${hour.padStart(2, '0')}:${minute.padStart(2, '0')}`

  return dayjs(dateStr).isValid() ? dayjs(dateStr).valueOf() : null
}

export const formatWalletName = (wallet: string) => {
  if (wallet.length <= 8) return wallet
  return `${wallet.slice(0, 5)}...${wallet.slice(-3)}`
}

export function formatDecimal(value: number): string {
  const fixed = value.toFixed(2)
  return fixed.endsWith('.00') ? parseInt(fixed).toString() : fixed
}

export function formatTimestamp(isoString: string | undefined, showTime: boolean = false): string {
  if (!isoString) return '--'
  const format = showTime ? 'YYYY/MM/DD HH:mm' : 'YYYY/MM/DD'
  return dayjs(isoString).format(format)
}

export function getFirstAndLastFiveChars(input: string): string {
  if (input.length <= 10) return input
  const first = input.slice(0, 5)
  const last = input.slice(-5)
  return `${first}...${last}`
}

export function formatDecimalLongValue(value: number, decimalPlaces: number = 6): string {
  const suffixes = ['', 'K', 'M', 'B', 'T']

  if (value === 0 || !isFinite(value)) return '0'

  const isNegative = value < 0
  const absVal = Math.abs(value)

  // Format large numbers with decimal
  if (absVal >= 1000) {
    let tier = Math.floor(Math.log10(absVal) / 3)
    tier = Math.min(tier, suffixes.length - 1)

    const scale = Math.pow(10, tier * 3)
    const scaled = absVal / scale
    const formatted = scaled.toFixed(decimalPlaces).replace(/\.?0+$/, '')

    return `${isNegative ? '-' : ''}${formatted}${suffixes[tier]}`
  }

  // Numbers between 1 and 999 (with decimals)
  if (absVal >= 1) {
    const decimals = (absVal % 1).toFixed(decimalPlaces).slice(2)
    if (decimals === '0'.repeat(decimalPlaces)) {
      return `${isNegative ? '-' : ''}${Math.floor(absVal)}`
    }

    const match = decimals.match(/^(0+)/)
    const leadingZeros = match ? match[0].length : 0

    if (leadingZeros >= 3) {
      return handleSubscript(leadingZeros, decimals, isNegative)
    } else {
      const cleanDecimal = decimals.replace(/0+$/, '').slice(0, decimalPlaces)
      const result = `${isNegative ? '-' : ''}${Math.floor(absVal)}.${cleanDecimal}`
      if (/^[-]?0\.?0*$/.test(result)) return '0'
      return result
    }
  }

  // Numbers less than 1
  const parts = absVal.toString().split('.')
  const decimals = parts[1] || ''
  const match = decimals.match(/^(0+)/)
  const leadingZeros = match ? match[0].length : 0

  if (leadingZeros >= 3) {
    return handleSubscript(leadingZeros, decimals, isNegative)
  } else {
    const cleanDecimal = decimals.replace(/0+$/, '').slice(0, decimalPlaces)
    const result = `${isNegative ? '-' : ''}0.${cleanDecimal}`
    if (/^[-]?0\.?0*$/.test(result)) return '0'
    return result
  }
}
