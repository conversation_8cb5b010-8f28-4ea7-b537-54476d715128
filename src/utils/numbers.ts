export function formatNumber(num: number | string, decimal = 2) {
  if (num === undefined || num === null) return '--'
  if (typeof num === 'string') {
    num = parseFloat(num)
  }
  if (isNaN(num)) return '--'
  if (num === 0) return '0'
  const absNum = Math.abs(num)
  if (absNum < 1e3) {
    return num.toFixed(decimal)
  } else if (absNum < 1e6) {
    return `${(num / 1e3).toFixed(decimal)}K`
  } else if (absNum < 1e9) {
    return `${(num / 1e6).toFixed(decimal)}M`
  } else if (absNum < 1e12) {
    return `${(num / 1e9).toFixed(decimal)}B`
  } else {
    return `${(num / 1e12).toFixed(decimal)}T`
  }
}


export function formatPercent(num: number | string, decimal = 2, defaultValue = '--') {
  if (num === undefined || num === null || num == 'Infinity' || num == '-Infinity') return defaultValue
  if (typeof num === 'string') {
    num = parseFloat(num)
  }
  if (isNaN(num)) return defaultValue
  // if (num > 0 && num <= 10) {
  //   num = num * 100
  // }
  let numStr = num.toFixed(decimal)
  if (numStr.indexOf('.00') > -1) {
    numStr = numStr.replace('.00', '')
  }

  return `${formatNumber(numStr)}%`
}

