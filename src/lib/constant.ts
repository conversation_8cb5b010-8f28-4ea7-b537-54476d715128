import { ChainIds } from '@/types/enums.ts'

export const LaunchPlatformOptions = [
  {
    value: 'All',
    label: '全部',
    icon: '/images/icons/icon-category-2.svg',
  },
  {
    value: 'Pumpfun',
    label: 'Pump',
    icon: '/images/icons/pump-icon.svg',
  },
  {
    value: 'Moonshot',
    label: 'Moonshot',
    icon: '/images/icons/icon-moonshot.svg',
  },
  {
    value: 'Raydium',
    label: 'Raydium',
    icon: '/images/icons/icon-raydium.svg',
  },
  {
    value: 'PumpSwap',
    label: 'PumpSwap',
    icon: '/images/icons/icon-pumpswap.webp',
  },
  {
    value: 'Mercurial',
    label: 'Mercurial',
    icon: '/images/icons/mercurial.png',
  },
  {
    value: 'Meteora',
    label: 'Meteora',
    icon: '/images/icons/meteora.svg',
  },
  {
    value: 'GooseFX',
    label: 'GooseFX',
    icon: '/images/icons/goosefx.svg',
  },
  {
    value: 'Aldrin',
    label: 'Aldrin',
    icon: '/images/icons/aldrin.png',
  },
  {
    value: 'Step',
    label: 'Step',
    icon: '/images/icons/step.png',
  },
  {
    value: 'Saros',
    label: 'Saros',
    icon: '/images/icons/saros.png',
  },
  // {
  //   value: 'Stepn',
  //   label: 'Stepn',
  //   icon: '/images/icons/icon-stepn.svg',
  // },
  // {
  //   value: 'Dradex',
  //   label: 'Dradex',
  //   icon: '/images/icons/icon-dradex.svg',
  // },
  // {
  //   value: 'ObricV2',
  //   label: 'ObricV2',
  //   icon: '/images/icons/icon-obric-v2.svg',
  // },
  {
    value: 'Lifinity',
    label: 'Lifinity',
    icon: '/images/icons/lifinity.svg',
  },
  {
    value: 'LifinityV2',
    label: 'LifinityV2',
    icon: '/images/icons/lifinity.svg',
  },
  {
    value: 'Orca',
    label: 'Orca',
    icon: '/images/icons/orca.png',
  },
  {
    value: 'FluxBeam',
    label: 'FluxBeam',
    icon: '/images/icons/fluxbeam.png',
  },
  // {
  //   value: 'Whirlpool',
  //   label: 'Whirlpool',
  //   icon: '/images/icons/icon-whirlpool.svg',
  // },
  {
    value: 'SolFi',
    label: 'SolFi',
    icon: '/images/icons/solfi.png',
  },
]

// TODO: add constant app router
export const APP_PATH = {
  // crypto paths
  FUTURES_DISCOVER: '/futures/discover',
  FUTURES: '/futures',
  TRANSACTION_HISTORY: '/futures/transaction-history',
  FUTURES_MARKET: '/futures/market',
  FUTURES_ASSET: '/futures/assets',
  FUTURES_TRANSFER: '/futures/transfer',

  // meme paths
  MEME_NEW_PAIRS_DEMO: '/meme/new-pairs-demo',
  MEME_DISCOVER: '/meme/discover',
  MEME_DISCOVER_OLD: '/meme/discover-old',
  MEME_DISCOVER_NEW: '/meme/discover-new',
  MEME_TOKEN: '/meme/token',
  MEME_TOKEN_DETAIL: '/meme/:chain/token/:address',
  MEME_TREND: '/meme/trend/:id',
  MEME_TELEGRAM_AUTH: '/telegram-auth',
  MEME_ASSETS: '/meme/assets',
  MEME_MONITORING: '/meme/monitoring',
  MEME_SMART_MONEY: '/meme/smart-money',
  MEME_POSITION: '/meme/position',
  MEME_ASSETS_TOKEN: '/meme/assets/token',
  MEME_ASSETS_WALLET: '/meme/assets/wallet',
  MEME_ASSETS_DEPOSIT: '/meme/assets/deposit',
  MEME_SETTINGS_GOOGLE_AUTH: '/meme/settings/google-authenticator',
  MEME_SETTINGS_RESET_GOOGLE_AUTH: '/meme/settings/reset-google-authenticator',
  MEME_SETTINGS_CONNECT_GOOGLE_AUTH: '/meme/settings/connect-google-authenticator',
  MEME_SETTINGS_VERIFY_GOOGLE_AUTH: '/meme/settings/verify-google-authenticator',
  MEME_SETTINGS_WHITELIST_GOOGLE_AUTH: '/meme/settings/whitelist-google-authenticator',
  MEME_ASSETS_DEPOSIT_EXTERNAL: '/meme/assets/deposit/external',
  MEME_SETTINGS_ABOUT_US: '/meme/settings/about-us',
  MEME_SETTINGS_LANGUAGE: '/meme/settings/language',
  MEME_COLORS_SETTINGS: '/meme/settings/colors',
  MEME_NOTIFICATION_SETTINGS: '/meme/settings/notification',
  MEME_SETTINGS_FUNDING_HISTORY: '/meme/settings/funding-history',
  MEME_SETTINGS_BROWSING_HISTORY: '/meme/settings/browsing-history',
  MEME_SETTINGS_USER_FEEDBACK: '/meme/settings/user-feedback',
  MEME_SETTINGS_USER_FEEDBACK_PROGRESS: '/meme/settings/user-feedback/progress',
  MEME_TRANSFER: '/meme/assets/transfer',
  MEME_CROSS_CHAIN_BRIDGE: '/meme/cross-chain-bridge',
  MEME_CROSS_CHAIN_BRIDGE_STATUS: '/meme/cross-chain-bridge/tx',
  MEME_NOTIFICATIONS: '/meme/notifications',

  // category paths
  CATEGORY: '/category',
  CATEGORY_DETAIL: '/category/:category',
  // copy trading paths
  COPY_TRADING_WALLET_SETTINGS: '/wallet-copy/settings',

  // debug paths
  DEBUG: '/debug',

  // webview
  WEBVIEW_PRICE_CHART: '/webview/price-chart',
  WEBVIEW_ASSET_CHART: '/webview/asset-chart',

  // wallet copy
  WALLET_COPY: '/wallet-copy',

  // terms of use
  TERMS_OF_USE: '/terms-of-use',
  PRIVACY_POLICY: '/privacy-policy',

  // withdrawal
  WITHDRAWAL: '/withdrawal/:accountType',
  GOOGLE_AUTH: '/google-auth',
  // transfer
  CRYPTO_DEPOSIT: '/crypto-deposit',
  // transfer
  CRYPTO_DEPOSIT_STATUS: '/crypto-transfer-status',
}

export const COLORS = {
  SEL_RISE_COLOR: '0,255,180',
  UNSEL_RISE_COLOR: '5,72,53',
  SEL_FALL_COLOR: '171,87,255',
  UNSEL_FALL_COLOR: '70,26,102',
}

export const LAUNCHPADS = ['Pumpfun', 'Moonshot'] as string[]

export const CHAIN_SYMBOLS: Record<number, string> = {
  [ChainIds.Solana]: 'sol',
  [ChainIds.Ethereum]: 'eth',
  [ChainIds.Arbitrum]: 'arb',
}

export const LAUNCHPAD_LOGOS: Record<string, string> = {
  Pumpfun: '/images/icons/pump-icon.svg',
  Moonshot: '/images/icons/icon-moonshot.svg',
}

export const IMAGES_CONSTANTS = {
  DEFAULT: {
    FALLBACK: '/images/icons/icon-fallback.svg',
  },
}

export const CHAIN_EXPLORER_URLS: Record<number, string> = {
  [ChainIds.Solana]: 'https://solscan.io',
  [ChainIds.Ethereum]: 'https://etherscan.io',
  [ChainIds.Arbitrum]: 'https://arbiscan.io',
}

export const CHAIN_EXPLORER_TX_URLS: Record<number, string> = {
  [ChainIds.Solana]: 'https://solscan.io/tx',
  [ChainIds.Ethereum]: 'https://etherscan.io/tx',
  [ChainIds.Arbitrum]: 'https://arbiscan.io/tx',
}

export const CHAIN_EXPLORER_ADDRESS_URLS: Record<number, string> = {
  [ChainIds.Solana]: 'https://solscan.io/account',
  [ChainIds.Ethereum]: 'https://etherscan.io/address',
  [ChainIds.Arbitrum]: 'https://arbiscan.io/address',
}
