import { WalletAdapterNetwork } from '@solana/wallet-adapter-base'
import { <PERSON>WalletName } from './wallets/BossWalletAdapter'
import { OKXWalletName } from './wallets/OKXWalletAdapter'
import { MetaMaskWalletName } from './wallets/MetaMaskWalletAdaper'
import { BitgetWalletName } from './wallets/BitgetWalletAdapter'
import { WalletConnectWalletName } from './wallets/WalletConnectWalletAdapter'
import { Configs } from '@/const/configs'
import { Connection } from '@solana/web3.js'
import { ChainIds } from '@/types/enums'

// EVM Chain IDs
export const CHAINS_ID = {
  ETHEREUM: 11155111,
  SOLANA: 901, //mainnet: 900
  BINANCE: 97,
  TRON: 1001,
}

// Get EVM chain name from chain ID
export const getChainName = (chainId: number): string => {
  switch (chainId) {
    case CHAINS_ID.ETHEREUM:
      return 'Ethereum'
    case CHAINS_ID.BINANCE:
      return 'Binance Smart Chain'
    case CHAINS_ID.SOLANA:
      return 'Solana'
    default:
      return 'Unknown Chain'
  }
}

export const getIconChain = (chain: string): string => {
  switch (chain) {
    case TYPE_CHAIN.SOLANA:
      return '/images/icons/icon-sol.svg'
    case TYPE_CHAIN.ETH:
      return '/images/ether.svg'
    case TYPE_CHAIN.ARB:
      return '/images/arbitrum.svg'
    default:
      return '/images/icons/icon-sol.svg'
  }
}

export const enum TYPE_CHAIN {
  ETH = 'eth',
  SOLANA = 'sol',
  ARB = 'arb',
}

export const enum TYPE_ACCOUNT {
  CHAIN = 'chain',
  TELEGRAM = 'telegram',
}

export const CHAIN_DEFAULT = TYPE_CHAIN.SOLANA

export const LIST_CHAIN_SUPPORTED = [
  {
    value: TYPE_CHAIN.SOLANA,
    chain_id: ChainIds.Solana,
    label: 'Solana',
    img: '/images/icons/icon-sol.svg',
  },
  {
    value: TYPE_CHAIN.ETH,
    chain_id: ChainIds.Ethereum,
    label: 'Ethereum',
    img: '/images/ether.svg',
  },
  {
    value: TYPE_CHAIN.ARB,
    chain_id: ChainIds.Arbitrum,
    label: 'Arbitrum',
    img: '/images/arbitrum.svg',
  },
]

export const getChainId = (chain: string) => {
  switch (chain) {
    case TYPE_CHAIN.SOLANA:
      return ChainIds.Solana
    case TYPE_CHAIN.ETH:
      return ChainIds.Ethereum
    case TYPE_CHAIN.ARB:
      return ChainIds.Arbitrum
    default:
      return ChainIds.Solana
  }
}

export const telegramBotConfig = {
  botName: Configs.telegramBot,
}

export const message_to_sign = (address: string, nonce: string) => {
  let message = 'Welcome to Xbit!\n\n'
  message += 'Click to sign in and accept the Xbit Terms of Service https://xbit.com/terms-of-use\n\n'
  message += 'This request will not trigger a blockchain transaction or cost any gas fees.\n\n'
  message += `Wallet address:\n\n${address}\n\n`
  message += `Nonce:\n${nonce}`
  return message
}

export const getImgFromNameWallet = (name: string) => {
  switch (name) {
    case BossWalletName:
      return '/images/wallets/ic-boss-wallet.svg'
    case OKXWalletName:
      return '/images/wallets/ic-okx-wallet-1.svg'
    case MetaMaskWalletName:
      return '/images/wallets/ic-metamask-wallet.svg'
    case BitgetWalletName:
      return '/images/wallets/ic-bitget-wallet.svg'
    // case PhantomWalletName:
    //   wallet = new PhantomWalletAdapter()
    //   break
    case WalletConnectWalletName:
      return '/images/wallets/ic-connect-wallet.svg'
    // break
    default:
      return '/images/logo-xbit.webp'
  }
}

//Sol config
export const networkSolana = WalletAdapterNetwork.Mainnet
export const MIN_BALANCE_SOL = 0.03
export const FRIST_FEE_SOL = 0.002
export const SOL_ADDRESS = 'So11111111111111111111111111111111111111112'
export const PLATFORM_FEE_SOL = 0.01
export const BASIC_FEE = 0.00005
//RPC: https://www.helius.dev/
export const RPC_SOL = 'https://mainnet.helius-rpc.com/?api-key=a905e00e-5a98-4979-8ed6-8a7ac38a214c'
export const solanaConnection = new Connection(RPC_SOL, 'confirmed')

export const convertChainNameToNativeToken = (str: string) => {
  switch (str) {
    case 'EVM':
      return 'ETH'
    case 'SOLANA':
      return 'SOL'
    case 'ARB':
      return 'ETH'
    default:
      return str
  }
}

export const isValidSolAddress = (address: string): boolean => {
  const solAddressRegex = /^[1-9A-HJ-NP-Za-km-z]{43,44}$/
  return solAddressRegex.test(address)
}
