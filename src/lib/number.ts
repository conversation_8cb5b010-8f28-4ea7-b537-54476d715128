import { f } from 'fintech-number'
import { formatPercentageChange } from '@/lib/format.ts'

export function fShortenNumber(value: number, decimals: number = 2, option?: any): string {
  if (value < 1000) return f(value, { decimal: decimals, ...option }) // No need to shorten

  const suffixes = ['', 'K', 'M', 'B', 'T', 'Q']
  const tier = Math.floor(Math.log10(value) / 3) // Determine the tier (thousands, millions, etc.)

  if (tier === 0) return value.toString() // Less than 1000, return as is

  const suffix = suffixes[tier]
  const scale = Math.pow(10, tier * 3)
  const scaledValue = value / scale

  return scaledValue.toFixed(decimals).replace(/0+$/, '').replace(/\.$/, '') + suffix
}

export const formatBalanceWallet = ({ balance, decimal = 6 }: { balance: number; decimal?: number }) => {
  return f(balance, {
    decimal: decimal,
    round: 'down',
  })
}

export const formatInputValue = (val: number, decimal: number = 6) => {
  return val
    .toFixed(decimal)
    .replace(/\.?0+$/, '')
    .toString()
}

export const formatPriceChange = (value: number) => {
  const formatted = formatPercentageChange(value)
  return formatted.label.replace(/0+$/, '')
}

export const formatCurrency = (value: number) => {
  return f(value, { decimal: 2, tinySupport: 6 })
}

export const parseNumber = (value: string): number => {
  const parsedValue = parseFloat(value)
  return isNaN(parsedValue) ? 0 : parsedValue
}

export const formatSmallPrice = (num: number): string => {
  if (num === 0) return '0'

  // Case: number < 1
  if (num < 1 && num > 0) {
    const decimal = num?.toFixed(20).split('.')[1]
    let result = '0.'
    let nonZeroCount = 0

    for (let i = 0; i < decimal.length; i++) {
      const digit = decimal[i]
      result += digit
      if (digit !== '0') nonZeroCount++
      if (nonZeroCount >= 4) break
    }

    // Trim trailing zeros but preserve leading zeros
    return result.replace(/(\d*?[1-9])0+$/, '$1').replace(/\.$/, '')
  }

  // Case: number >= 1
  const rounded = num?.toFixed(10) // prevent floating-point junk
  const [intPart, decPartRaw] = rounded.split('.')
  const decPart = decPartRaw.replace(/0+$/, '').slice(0, 3) // trim trailing zeros, then limit to 3 digits
  return decPart.length ? `${intPart}.${decPart}` : intPart
}
