import { fShortenNumber } from '@/lib/number.ts'

export function formatDuration(seconds: number) {
  if (seconds < 60) {
    return { label: `${seconds}s`, color: 'rgba(0, 255, 180, 0.7)' }
  } else if (seconds < 3600) {
    return { label: `${Math.floor(seconds / 60)}m`, color: 'rgba(0, 255, 180, 0.7)' }
  } else if (seconds < 86400) {
    return { label: `${Math.floor(seconds / 3600)}h`, color: 'rgba(0, 255, 180, 0.7)' }
  } else {
    return { label: `${Math.floor(seconds / 86400)}d`, color: 'rgba(255, 255, 255, 0.7)' }
  }
}

export function formatPercentageChange(change: number) {
  if (typeof change === undefined) return { label: '--', cls: '' }

  if (Math.abs(change) <= 0.01) return { label: '0%', cls: 'text-neutral' }

  let formattedChange = change.toLocaleString('en', { maximumFractionDigits: 2 }).replace(/\.0+$/, '')

  if (change >= 1e16)
    return {
      label: '>9999T%',
      cls: 'text-rise',
    }
  if (change >= 1e12)
    return {
      label: (change / 1e12).toFixed(2).replace(/\.?0+$/, '') + 'T%',
      cls: 'text-rise',
    }
  if (change >= 1e9)
    return {
      label: (change / 1e9).toFixed(2).replace(/\.?0+$/, '') + 'B%',
      cls: 'text-rise',
    }
  if (change >= 1e6)
    return {
      label: (change / 1e6).toFixed(2).replace(/\.?0+$/, '') + 'M%',
      cls: 'text-rise',
    }
  if (change >= 1e3)
    return {
      label: (change / 1e3).toFixed(2).replace(/\.?0+$/, '') + 'K%',
      cls: 'text-rise',
    }

  let label = formattedChange + '%'
  let cls = ''

  if (change > 0) {
    cls = 'text-rise'
  } else if (change === 0) {
    cls = 'text-neutral'
  } else {
    cls = 'text-fall'
  }
  return { label, cls }
}

export function formatHolders(count: number) {
  if (count < 1000) return count.toString()

  let unit = ''
  let value = count

  if (count >= 1e9) {
    unit = 'B'
    value = count / 1e9
  } else if (count >= 1e6) {
    unit = 'M'
    value = count / 1e6
  } else if (count >= 1e3) {
    unit = 'K'
    value = count / 1e3
  }

  return value.toFixed(2).replace(/\.?0+$/, '') + unit
}
// market and Transaction amount
export function formatMarketValue(value?: number, currency: string = '') {
  if (!value) return '--'

  if (value < 1000) return currency + fShortenNumber(value)

  const units = ['K', 'M', 'B', 'T']
  let unitIndex = -1
  let formattedValue = value

  while (formattedValue >= 1000 && unitIndex < units.length - 1) {
    formattedValue /= 1000
    unitIndex++
  }

  if (unitIndex === units.length - 1 && formattedValue >= 10000) {
    return `>${currency}9999T`
  }

  return currency + formattedValue?.toFixed(2)?.replace(/\.?0+$/, '') + units[unitIndex]
}

export function formatTradeCount(tradeCount: number) {
  if (tradeCount < 100000) {
    return tradeCount.toString()
  }

  const units = ['K', 'M', 'B', 'T']
  let unitIndex = 0
  let formattedCount = tradeCount

  while (formattedCount >= 1000 && unitIndex < units.length) {
    formattedCount /= 1000
    unitIndex++
  }

  formattedCount = Math.round(formattedCount * 100) / 100

  if (formattedCount % 1 === 0) {
    return `${formattedCount.toFixed(0)}${units[unitIndex - 1]}`
  } else if ((formattedCount * 10) % 1 === 0) {
    return `${formattedCount.toFixed(1)}${units[unitIndex - 1]}`
  } else {
    return `${formattedCount.toFixed(2)}${units[unitIndex - 1]}`
  }
}

export function formatVolume(volume: number) {
  if (volume > 1e15) return '>9999T'
  return fShortenNumber(volume)
}

export const formatLiquidity = (liquidity: number | undefined) => {
  if (liquidity === undefined) return '--'
  if (liquidity > 1e15) return '>$9999T'
  return '$' + fShortenNumber(liquidity)
}
