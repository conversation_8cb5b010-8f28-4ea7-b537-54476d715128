import Container from '@/components/common/Container'
import ListCoinCrypto from '@/components/futuresDiscover/list-coin-crypto'
import SearchBar from '@/components/futuresDiscover/search-bar'
import { useState } from 'react'

const FuturesDiscover = () => {
  const [search, setSearch] = useState('')

  return (
    <div className="relative overflow-hidden bg-[#19191E]">
      <Container>
        <SearchBar setSearch={setSearch} />
      </Container>
      <ListCoinCrypto search={search} />
    </div>
  )
}

export default FuturesDiscover
