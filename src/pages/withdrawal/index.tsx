import HeaderWithBack from '@/components/header/HeaderWithBack'
import { useParams, useSearchParams } from 'react-router-dom'
import { BorderGradientCard } from '@components/withdrawal/BorderGradientCard.tsx'
import { Button } from '@components/ui/button.tsx'
import { useEffect, useMemo, useRef, useState } from 'react'
import { WithdrawalResultDrawer, WithdrawalResultDrawerHandle } from '@components/withdrawal/WithdrawalResultDrawer.tsx'
import DetailTransaction from '@components/withdrawal/DetailTransaction.tsx'
import { TelegramAccountDestination } from '@components/withdrawal/TelegramAccountDestination.tsx'
import { useAppSelector } from '@/redux/store'
import { priceChain } from '@/redux/modules/price.slice'
import { TYPE_ACCOUNT } from '@/lib/blockchain.ts'
import { WalletAccountDestination } from '@components/withdrawal/WalletAccountDestination.tsx'
import { useTranslation } from 'react-i18next'
import { BLOCKCHAIN_NAMES } from '@/utils/helpers.ts'
import { useMultiChainWallet } from '@hooks/useMultiChainWallet.ts'
import { getWithdrawFee, withdraw } from '@/services/wallet.service.ts'
import { walletClient } from '@/lib/gql/apollo-client'
import { SOL_ADDRESS } from '@/hooks/useCreateOrder'
import MoneyFormatted from '@components/common/MoneyFormatted.tsx'
import { formatMoney } from '@/utils/helpers'

const Withdrawal = () => {
  const { activeWallet } = useMultiChainWallet({})
  const activeAccount = useAppSelector((state) => state.wallet.activeAccount)
  const priceSol = useAppSelector(priceChain('SOL'))
  const params = useParams()
  const [searchParams] = useSearchParams()
  const chainId = searchParams.get('chainId') || ''
  const tokenAddress = searchParams.get('tokenAddress') || ''
  const tokenName = searchParams.get('tokenName') || ''

  const fromAddress = activeWallet?.walletId || ''
  const availableBalance = activeWallet?.balance?.formatted || 0
  const [toAddress, setToAddress] = useState<string>('')

  const [value, setValue] = useState<string>()
  const [withdrawFee, setWithdrawFee] = useState<{
    fee: string
    unit: string
  }>({
    fee: '--',
    unit: 'SOL',
  })
  const { accountType } = params
  const ref = useRef<WithdrawalResultDrawerHandle>(null)

  const [loading, setLoading] = useState(false)

  const { t } = useTranslation()

  const title =
    accountType === 'futures' ? t('assets.withdraw') : t('assets.withdrawal.withdrawToken', { token: 'SOL' })
  const amount = useMemo(() => {
    if (value === undefined) return 0
    return parseFloat(value)
  }, [value])

  const [detailTransactionOpen, setDetailTransactionOpen] = useState(false)
  const [transaction, setTransaction] = useState<any>(null)

  const fetchWithdrawFee = async () => {
    try {
      const { data } = await walletClient.query({
        query: getWithdrawFee,
        variables: {
          input: {
            fromAddress,
            toAddress,
            chainId,
            token: SOL_ADDRESS, // tokenAddress
            amount: Number(amount),
          },
        },
      })
      if (data?.getWithdrawFee) {
        setWithdrawFee({ ...data.getWithdrawFee })
      } else {
        setWithdrawFee({ fee: '--', unit: 'SOL' })
      }
    } catch (error) {
      console.error('Error fetching withdrawal fee:', error)
    }
  }

  const handleWithdraw = async () => {
    setLoading(true)
    try {
      const { data } = await walletClient.mutate({
        mutation: withdraw,
        variables: {
          input: {
            fromAddress,
            toAddress,
            chainId,
            token: SOL_ADDRESS, // tokenAddress
            amount: Number(amount),
          },
        },
      })
      setLoading(false)
      ref.current?.show({
        type: 'success',
        title: t('assets.withdrawal.requestWithdrawSuccess'),
        message: t('assets.withdrawal.transactionInprogress'),
        amount: amount,
        unit: 'SOL',
        buttonText: t('assets.withdrawal.checkTheDetails'),
        onClick: function (): void {
          setTransaction(data.withdraw)
          setDetailTransactionOpen(true)
        },
      })
    } catch (error) {
      setLoading(false)
      ref.current?.show({
        type: 'failed',
        title: t('assets.withdrawal.withdrawFailed'),
        message: error[0]?.message || t('assets.withdrawal.withdrawFailedMessage'),
        amount: amount,
        unit: 'SOL',
        buttonText: t('assets.withdrawal.tryAgain'),
        onClick: function (): void {},
      })
    }
  }

  useEffect(() => {
    if (!amount || amount <= 0) return
    fetchWithdrawFee()
  }, [amount])

  return (
    <div className="flex max-h-screen h-screen pt-16 flex-col overflow-hidden bg-[#111] bg-[url('/images/walletCopy/bg_setting.png')] bg-cover bg-center text-white">
      <HeaderWithBack
        title={title}
        className="justify-center bg-transparent fixed top-0 left-0 right-0 max-w-[768px] mx-auto"
        titleClassName="ml-0"
        right={t('assets.withdrawal.history')}
      />
      <div className="px-5 pt-4">
        {activeAccount === TYPE_ACCOUNT.TELEGRAM ? (
          <TelegramAccountDestination
            chainId={chainId}
            tokenName={tokenName}
            toAddress={toAddress}
            setToAddress={setToAddress}
          />
        ) : (
          <WalletAccountDestination />
        )}
        <BorderGradientCard className="mb-4">
          <div className="text-[#FFFFFFB2] text-[calc(14rem/16)] mb-3">{t('assets.withdrawal.amount')}</div>
          <div className="bg-[#141414] rounded-[8px] px-3 py-2 flex items-center mb-2">
            <div className="flex flex-col flex-1">
              <input
                placeholder="0"
                value={value}
                onChange={(e) => {
                  let newValue = e.target.value.replace(/[^0-9.]/g, '')
                  if (newValue.includes('.')) {
                    const parts = newValue.split('.')
                    newValue = parts[0] + '.' + parts[1]
                  }
                  setValue(newValue)
                }}
              />
              <span className="text-[#FFFFFF80] text-[calc(12rem/16)]">
                ≈
                <MoneyFormatted value={amount ? amount * priceSol : '0'} />
              </span>
            </div>
            <div className="text-[calc(16rem/16)] text-[#FFFFFFCC] mr-2.5">{'SOL'}</div>
            <button
              className="text-[calc(14rem/16)] text-[#00FFB4]"
              onClick={() => {
                setValue(availableBalance.toString())
              }}
            >
              {t('assets.withdrawal.withdrawAll')}
            </button>
          </div>
          <div className="flex justify-between">
            <div className="text-[calc(12rem/16)] text-[#FFFFFFB2]">{t('assets.withdrawal.availableBalance')}</div>
            <div className="text-[calc(13rem/16)]">
              <MoneyFormatted value={availableBalance} unit={'SOL'} />
            </div>
          </div>
          {amount > availableBalance && (
            <div className="text-[calc(12rem/16)] text-[#FF353C]">{t('assets.withdrawal.insufficientBalance')}</div>
          )}
        </BorderGradientCard>

        <div className="bg-[linear-gradient(90deg,rgba(255,0,0,0.1)_0%,rgba(205,0,48,0.1)_100%)] px-3.5 py-2 flex items-center gap-1.5 rounded-[8px]">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M8.00325 9.83318C7.72992 9.83327 7.50318 9.60667 7.5031 9.33333L7.5021 6C7.50202 5.72667 7.72862 5.49993 8.00195 5.49985C8.27529 5.49977 8.50202 5.72637 8.5021 5.9997L8.5031 9.33303C8.50318 9.60637 8.27659 9.8331 8.00325 9.83318Z"
              fill="#FF353C"
            />
            <path
              d="M8.00349 11.9998C7.96349 11.9999 7.91682 11.9932 7.87016 11.9865C7.83015 11.9799 7.79015 11.9666 7.75014 11.9466C7.71014 11.9333 7.67013 11.9133 7.63013 11.8866C7.59678 11.86 7.56344 11.8333 7.5301 11.8066C7.41006 11.68 7.33668 11.5067 7.33663 11.3334C7.33657 11.16 7.40986 10.9867 7.52982 10.86C7.56314 10.8333 7.59647 10.8066 7.62979 10.78C7.66979 10.7533 7.70978 10.7333 7.74978 10.7199C7.78977 10.6999 7.82977 10.6866 7.86976 10.6799C7.95642 10.6599 8.04976 10.6598 8.12976 10.6798C8.17643 10.6865 8.21644 10.6998 8.25644 10.7198C8.29645 10.7331 8.33645 10.7531 8.37646 10.7797C8.4098 10.8064 8.44314 10.833 8.47648 10.8597C8.59652 10.9863 8.66991 11.1596 8.66996 11.333C8.67001 11.5063 8.59673 11.6797 8.47677 11.8064C8.44344 11.833 8.41012 11.8597 8.37679 11.8864C8.3368 11.9131 8.29681 11.9331 8.25681 11.9464C8.21682 11.9664 8.17682 11.9798 8.13016 11.9865C8.09016 11.9931 8.04349 11.9998 8.00349 11.9998Z"
              fill="#FF353C"
            />
            <path
              d="M12.0445 14.7718L3.96453 14.7742C2.66453 14.7746 1.67106 14.3016 1.16414 13.4484C0.66388 12.5952 0.730217 11.4952 1.36321 10.355L5.40103 3.08715C6.06734 1.88695 6.98714 1.22667 8.00047 1.22637C9.01381 1.22606 9.934 1.88579 10.601 3.08559L14.6432 10.3577C15.2769 11.4975 15.3505 12.5908 14.8441 13.451C14.3377 14.2978 13.3445 14.7714 12.0445 14.7718ZM8.00077 2.22637C7.37411 2.22655 6.76092 2.70674 6.27451 3.57355L2.24336 10.8481C1.79027 11.6616 1.71716 12.4083 2.03065 12.9482C2.34415 13.4881 3.03757 13.7812 3.9709 13.7809L12.0509 13.7785C12.9842 13.7782 13.6708 13.4847 13.9907 12.9446C14.3105 12.4045 14.2303 11.6645 13.7767 10.8446L9.72784 3.57251C9.24092 2.70599 8.62744 2.22618 8.00077 2.22637Z"
              fill="#FF353C"
            />
          </svg>
          <div className="flex-1 text-[calc(11rem/16)]">
            {activeAccount === TYPE_ACCOUNT.TELEGRAM ? (
              <>
                {t('assets.withdrawal.warningWithdrawTele')}{' '}
                <button className="text-[#00FFF6]">{t('assets.withdrawal.learnMore')}</button>
              </>
            ) : (
              <>{t('assets.withdrawal.warningWithdrawWallet')}</>
            )}
          </div>
        </div>

        <div className="absolute bottom-0 left-0 right-0 flex justify-between items-center pb-10 px-5">
          <div>
            <div className="text-[calc(12rem/16)] text-[#FFFFFF80]">{t('assets.withdrawal.receivingAmount')}</div>
            <div className="font-medium">
              {amount ? formatMoney(amount - Number(withdrawFee.fee), false) : '--'}
              {' SOL'}
            </div>
            <div className="text-[calc(12rem/16)] text-[#FFFFFFB2]">
              {activeAccount === TYPE_ACCOUNT.TELEGRAM ? t('assets.withdrawal.fee') : t('assets.withdrawal.gasFee')}{' '}
              <span>
                {withdrawFee.fee} {withdrawFee.unit}
              </span>
            </div>
          </div>
          <div className="w-36 text-right">
            <Button
              variant="gradient"
              className="rounded-full w-full text-[#1A1A1A] "
              disabled={!amount || amount <= 0 || !toAddress || loading}
              onClick={handleWithdraw}
              isLoading={loading}
            >
              {loading ? '' : t('assets.withdrawal.confirmWithdraw')}
            </Button>
          </div>
        </div>
      </div>

      <WithdrawalResultDrawer ref={ref} />
      <DetailTransaction 
        open={detailTransactionOpen}
        setOpen={() => setDetailTransactionOpen(false)}
        transaction={transaction}
      />
    </div>
  )
}

export default Withdrawal
