import Container from '@components/common/Container.tsx'
import Chart from '@components/chart/index.tsx'
import OrderForm from '@components/orderForm'
import MovingLineTabs from '@components/common/MovingLineTabs.tsx'
import { UITab } from '@/types/uiTabs.ts'
import DetailInfo from '@components/detailInfo'
// import DetailHeader from '@components/detailHeader'
import DetailStatistic from '@components/detailStatistic'
import OrderBook from '@components/orderBook'
import { useNavigate, useParams } from 'react-router-dom'
import { useQuery } from '@apollo/client'
import { getTokenDetail } from '@/services/tokens.service'
import { useEffect, useState } from 'react'
import { TYPE_CHAIN } from '@/lib/blockchain'
import WalletSolanaConnectModal from '@/components/header/wallet-connect'
import { useAppDispatch, useAppSelector } from '@/redux/store'
import { useTranslation } from 'react-i18next'
import CurrentOrdersList from '@/components/currentOrdersList'
import DetailToken from '@components/detailToken'
import { setCurrentToken } from '@/redux/modules/holding.slice.ts'
import { APP_PATH, CHAIN_SYMBOLS } from '@/lib/constant'
import AIAnalytics from '@/components/aiAnalytics'
import LastTransactionSubscription from '@components/LastTransactionSubscription.tsx'
import { TokenAlert } from '@components/detailInfo/TokenAlert.tsx'
import HoldingTab from '@components/myPositions/HoldingTab.tsx'
import DetailBanner from '@components/detailBanner'
import PairDetail from '@components/pairDetail'
import { useMultiChainWallet } from '@/hooks/useMultiChainWallet'
import { ServiceConfig } from '@/lib/gql/service-config'
import DetailTokenTabs from '@components/detailTokenTabs'
import DetailListIcon from '@components/detailListIcon'
import { getPath } from '@/lib/utils.ts'
import DetailTokenTable from '@components/detaiTokenTable'
import DetailHolderTab from '@components/DetailHolderTab'
import TokenPageTitle from '@components/TokenPageTitle'
import ButtonLogin from '@/components/common/LoginSection/ButtonLogin'
import useSignWallet from '@/hooks/useSignWallet'
import { browsingHistoryActions } from '@/redux/modules/browsingHistory.slice.ts'
import { countPendingOrders } from '@services/order.service.ts'
import { tradingClient } from '@/lib/gql/apollo-client.ts'
import eventBus from '@/lib/eventBus.ts'
import { REFETCH_PENDING_ORDERS } from '@/lib/eventMessages.ts'
import DetailPoolTab from '@/components/detailPoolTab'

const DEFAULT_MEME_TOKEN = import.meta.env.VITE_DEFAULT_MEME_TOKEN
const DEFAULT_MEME_TOKEN_CHAIN_ID = import.meta.env.VITE_DEFAULT_MEME_CHAIN_ID

const MemeDetailPage = () => {
  const { t } = useTranslation()
  const { address } = useParams()
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const chain = useAppSelector((state) => state.wallet.activeChain)
  const { activeWallet } = useMultiChainWallet({})
  const { handleSignMessage } = useSignWallet({ isAutoConnect: false })

  const { data } = useQuery(getTokenDetail, {
    skip: !address,
    variables: {
      input: {
        address: address,
      },
    },
  })

  const userAddress = activeWallet?.walletId
  const { data: dataCountPendingOrders, refetch: reCountPendingOrders } = useQuery(countPendingOrders, {
    skip: !userAddress,
    client: tradingClient,
    variables: {
      input: {
        userAddress,
      },
    },
  })

  const pendingOrdersCount = dataCountPendingOrders?.getPendingOrders?.total
  const pendingOrdersCountString = pendingOrdersCount > 99 ? '99+' : pendingOrdersCount

  const navTabs: UITab[] = [
    {
      value: 'trading',
      label: t('detail.tabs.trading'),
    },
    {
      value: 'info',
      label: t('detail.tabs.information'),
    },
    {
      value: 'AI',
      label: t('detail.tabs.aiAnalysis'),
    },
  ]

  const numsHolders = 0

  const currentListTabs: UITab[] = [
    {
      value: 'followed',
      label: t('detail.tabs.followed'),
    },
    {
      value: 'trading',
      label: t('detail.tabs.trading'),
    },
    {
      value: 'holders',
      label: t('detail.tabs.holders', { total: numsHolders > 0 ? `(${numsHolders})` : '' }),
    },
    {
      value: 'pool',
      label: t('detail.tabs.pool'),
    },
    {
      value: 'order',
      label: pendingOrdersCount
        ? t('detail.tabs.currentCommissionWithCount', { total: pendingOrdersCountString })
        : t('detail.tabs.currentCommission'),
    },
    {
      value: 'holding',
      label: t('detail.tabs.myPositions'),
    },
  ]

  const [currentNavTab, setCurrentNavTab] = useState<string>(navTabs[0].value)
  const [currentTab, setCurrentTab] = useState<string>(currentListTabs[5].value)

  const handleChangeTab = (tab: string) => {
    setCurrentTab(tab)
  }
  const handleRenderTab = (tab: string) => {
    switch (tab) {
      case currentListTabs[0].value:
        return <DetailTokenTabs />
      case currentListTabs[1].value:
        return <DetailTokenTable symbol={tokenData?.symbol} price={tokenData?.price} />
      case currentListTabs[2].value:
        return <DetailHolderTab />
      case currentListTabs[3].value:
        return <DetailPoolTab token={tokenData?.address} chainId={tokenData?.chainId} />
      case currentListTabs[4].value:
        return <CurrentOrdersList currentToken={address} />
      case currentListTabs[5].value:
      default:
        return <HoldingTab />
    }
  }

  const ButtonConnectWallet = () => {
    return (
      <Container className="mt-[10px] h-40">
        <div className="flex items-center gap-2 flex-col justify-center text-[14px] text-[#999999] mt-10">
          <p>
            {t('login.notLogined', {
              name: 'XBIT',
            })}
          </p>
          <ButtonLogin onClick={() => handleSignMessage()} className="hover-scale">
            <img src="/images/icons/icon-wallet.svg" className="w-[1rem] h-[calc(1rem*(13.43/16))]" alt="" />
            {t('wallet.connectGuide')}
          </ButtonLogin>
        </div>
      </Container>
    )
  }

  const tokenData = data?.getTokenDetail

  useEffect(() => {
    if (tokenData) {
      dispatch(setCurrentToken(tokenData?.address))
    }
  }, [tokenData])

  // const { recentToken, updateRecentToken } = useRecentToken()

  useEffect(() => {
    if (!address) {
      const recentToken = sessionStorage.getItem('recentToken')
        ? JSON.parse(sessionStorage.getItem('recentToken')!)
        : null
      if (recentToken) {
        navigate(getPath(APP_PATH.MEME_TOKEN_DETAIL, { address: recentToken.token, chain: recentToken.chain }))
        return
      }

      navigate(
        getPath(APP_PATH.MEME_TOKEN_DETAIL, {
          address: DEFAULT_MEME_TOKEN,
          chain: CHAIN_SYMBOLS[+DEFAULT_MEME_TOKEN_CHAIN_ID],
        }),
      )
      return
    }

    sessionStorage.setItem('recentToken', JSON.stringify({ token: address, chain: chain }))
  }, [address])

  useEffect(() => {
    if (!address) return

    // Save to history
    dispatch(browsingHistoryActions.addTokenToHistory(address))
  }, [address])

  useEffect(() => {
    if (!address) return

    // Save to history
    dispatch(browsingHistoryActions.addTokenToHistory(address))
  }, [address])

  useEffect(() => {
    eventBus.on(REFETCH_PENDING_ORDERS, (data: any) => {
      const needRefetch = data?.data?.needRefetch
      if (needRefetch) {
        reCountPendingOrders()
      }
    })
    return () => {
      eventBus.remove(REFETCH_PENDING_ORDERS)
    }
  }, [])

  return (
    <>
      <LastTransactionSubscription baseAddress={address} />
      <TokenPageTitle address={address} symbol={tokenData?.symbol} defaultPrice={tokenData?.price} />
      <div className="relative">
        {/* sticky header*/}
        <div className="sticky top-0 z-20 bg-[#111111]">
          {chain === TYPE_CHAIN.SOLANA && <WalletSolanaConnectModal />}
          {/*<DetailHeader tokenData={tokenData} />*/}
          <div className="flex items-center justify-between w-full py-1.5 border-b-[0.6px] border-b-[#ECECED14]">
            <MovingLineTabs
              tabs={navTabs}
              defaultTab={navTabs[0].value}
              onTabChange={(tab: string) => {
                setCurrentNavTab(tab)
              }}
              containerClassName="bg-[none] after:hidden"
              tabsClassName="w-full"
            />
            <DetailListIcon tokenData={tokenData} />
          </div>
        </div>
        <div id="tab-trading" className={currentNavTab === navTabs[0].value ? 'block' : 'hidden'}>
          <TokenAlert tokenDetail={tokenData} />
          <DetailInfo tokenData={tokenData} />
          <Chart tokenData={tokenData} />
          <DetailStatistic />
          <Container className="flex gap-[4px] mt-[8px] mb-[8px] pb-[10px] relative">
            <OrderBook tokenDetail={tokenData} />
            <OrderForm tokenDetail={tokenData} />
          </Container>
          <MovingLineTabs
            tabs={currentListTabs}
            onTabChange={handleChangeTab}
            defaultTab={currentListTabs[5].value}
            itemClassName="!px-[7.1px] font-normal"
          />
          {activeWallet?.isConnected && ServiceConfig.token ? handleRenderTab(currentTab) : ButtonConnectWallet()}
        </div>
        <div id="tab-info" className={currentNavTab === navTabs[1].value ? 'block' : 'hidden'}>
          <TokenAlert tokenDetail={tokenData} />
          <DetailBanner tokenData={tokenData} />
          <DetailToken tokenData={tokenData} />
          <PairDetail tokenData={tokenData} />
        </div>
        <div id="tab-ai" className={currentNavTab === navTabs[2].value ? 'block' : 'hidden'}>
          <AIAnalytics address={address} />
        </div>
      </div>
    </>
  )
}

export default MemeDetailPage
