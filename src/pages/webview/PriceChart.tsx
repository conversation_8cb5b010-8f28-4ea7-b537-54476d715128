import { useLocation } from 'react-router-dom'
import { useQuery } from '@apollo/client'
import { getTokenDetail } from '@/services/tokens.service'
import { useRef, useMemo, useState } from 'react'
import TvChart from '@/components/chart/TvChart'

// token:
// period:
// 1S, 30S, 1, 5, 15, 30, 60, 240, 1D, 1W
// chartType:
// 0: Bars
// 1: Candles
// 2: Line
// 3: Area
// 8: Heikin Ashi
// 9: Hollow candles

const DEFAULT_PERIOD = '1'
const DEFAULT_CANDLE_TYPE = 1

const PriceChart = () => {
  const { search } = useLocation()
  const params = useMemo(() => new URLSearchParams(search), [search])

  const token = params.get('token') ?? ''
  const period = params.get('period') ?? DEFAULT_PERIOD
  const chartType = Number(params.get('type')) || DEFAULT_CANDLE_TYPE
  const rawTypeOHLC = params.get('typeOHLC')
  const typeOHLC = rawTypeOHLC === 'marketCap' ? 'marketCap' : 'price'

  const tvChartRef = useRef(null)
  const [isLoading, setIsLoading] = useState<boolean>(true)

  const { data } = useQuery(getTokenDetail, {
    variables: { input: { address: token } },
    skip: !token, // Skip query if token is empty
  })

  const handleChartReady = () => {
    setIsLoading(false)
  }

  const tokenData = data?.getTokenDetail

  if (!tokenData) return null

  return (
    <div className="relative h-screen">
      <div className="containerLoadingSpinner h-full" style={{ display: isLoading ? 'block' : 'none' }} />
      <div style={{ display: !isLoading && tokenData?.name ? 'block' : 'none' }}>
        <TvChart
          ref={tvChartRef}
          tokenData={tokenData}
          period={period}
          chartType={chartType}
          typeOHLC={typeOHLC}
          onChartReady={handleChartReady}
          isWebview
        />
      </div>
    </div>
  )
}

export default PriceChart
