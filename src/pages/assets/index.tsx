import MovingLineTabs from '@components/common/MovingLineTabs.tsx'
import { UITab } from '@/types/uiTabs.ts'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { TabOverview } from '@components/assets/overview/TabOverview.tsx'
import { useAppSelector } from '@/redux/store'
import { TYPE_CHAIN } from '@/lib/blockchain'
import WalletSolanaConnectModal from '@/components/header/wallet-connect'
import Funding from '@/components/assets/funding/Funding'
import Futures from '@/components/assets/futures/Futures'
import Spot from '@/components/assets/spot/Spot'
import { AssetOverviewContextState, AssetOverviewProvider } from '@components/assets/overview/AssetOverviewContext.tsx'
import { useSearchParams } from 'react-router-dom'
import { OverviewHeader } from '@components/assets/overview/OverviewHeader.tsx'
import { SelectAccountDrawer, SelectAccountDrawerHandle } from '@components/withdrawal/SelectAccountDrawer.tsx'
import ChooseAccountPopup from '@components/assets/deposit/ChooseAccountPopup.tsx'
import ChooseTokenPopup from '@components/assets/deposit/ChooseTokenPopup.tsx'
import { FuturesHeader } from '@components/assets/futures/FuturesHeader.tsx'
import { FundingHeader } from '@components/assets/funding/FundingHeader.tsx'
import { SpotHeader } from '@components/assets/spot/SpotHeader.tsx'
import { getWalletBalance } from '@services/assets.service.ts'
import { WalletBalanceDto } from '@/@generated/gql/graphql-core.ts'
import { useActiveWallet } from '@hooks/useActiveWallet.ts'
import { useNavigate } from 'react-router-dom'
import { gqlClient } from '@/lib/gql/apollo-client.ts'
import { Splash } from '@components/assets/Splash.tsx'
import { ServiceConfig } from '@/lib/gql/service-config'

const renderTabContent = (tab: string) => {
  if (tab === 'Overview') {
    return <TabOverview />
  }
  if (tab === 'Funding') {
    return <Funding />
  }
  if (tab === 'Futures') {
    return <Futures />
  }
  if (tab === 'Spot') {
    return <Spot />
  }
}

const AssetsHeader = (props: { tab: string; onItemClick: (key: string) => void; wallets?: WalletBalance }) => {
  const { tab, onItemClick, wallets } = props
  if (tab === 'Overview') {
    const fundingBalance = wallets?.funding?.reduce((acc, wallet) => acc + parseFloat(wallet.usdBalance || '0'), 0) || 0
    const futuresBalance = wallets?.futures?.reduce((acc, wallet) => acc + parseFloat(wallet.usdBalance || '0'), 0) || 0
    const spotBalance = wallets?.spot?.reduce((acc, wallet) => acc + parseFloat(wallet.usdBalance || '0'), 0) || 0
    const totalBalance = fundingBalance + futuresBalance + spotBalance
    return <OverviewHeader onItemClick={onItemClick} balance={totalBalance} />
  }
  if (tab === 'Futures') {
    const usdBalance = wallets?.futures?.reduce((acc, wallet) => acc + parseFloat(wallet.usdBalance || '0'), 0) || 0
    return <FuturesHeader onItemClick={onItemClick} balance={usdBalance} />
  }
  if (tab === 'Funding') {
    const usdBalance = wallets?.funding?.reduce((acc, wallet) => acc + parseFloat(wallet.usdBalance || '0'), 0) || 0
    return <FundingHeader onItemClick={onItemClick} balance={usdBalance} />
  }
  if (tab === 'Spot') {
    const usdBalance = wallets?.spot?.reduce((acc, wallet) => acc + parseFloat(wallet.usdBalance || '0'), 0) || 0
    return <SpotHeader onItemClick={onItemClick} balance={usdBalance} />
  }
  return undefined
}

interface TabItem extends UITab {
  slug: string
}

const headerTabs: TabItem[] = [
  {
    value: 'Overview',
    label: 'assets.overview.title',
    slug: 'overview',
  },
  {
    value: 'Funding',
    label: 'assets.funding.title',
    slug: 'funding',
  },
  {
    value: 'Futures',
    label: 'assets.futures.title',
    slug: 'futures',
  },
  // {
  //   value: 'Spot',
  //   label: 'assets.spot.title',
  //   slug: 'spot',
  // },
]

const extractTabFromUrl = () => {
  const href = window.location.href
  const url = new URL(href)
  const tab = url.searchParams.get('tab')
  if (tab) {
    const foundTab = headerTabs.find((item) => item.slug === tab)
    return foundTab ? foundTab.value : headerTabs[0].value
  }
  return headerTabs[0].value
}

interface WalletBalance {
  funding: WalletBalanceDto[] | undefined
  futures: WalletBalanceDto[] | undefined
  spot: WalletBalanceDto[] | undefined
}

const AssetsPage = () => {
  const { t } = useTranslation()
  const chain = useAppSelector((state) => state.wallet.activeChain)
  const [currentTab, setCurrentTab] = useState(() => extractTabFromUrl())
  const [hideBalance, setHideBalance] = useState(false)
  const selectAccountDrawerRef = useRef<SelectAccountDrawerHandle>(null)

  const [openChooseAccountPopup, setOpenChooseAccountPopup] = useState(false)
  const [openChooseTokenPopup, setOpenChooseTokenPopup] = useState(false)
  const [data, setData] = useState<WalletBalance>({
    funding: undefined,
    futures: undefined,
    spot: undefined,
  })
  const [walletToDeposit, setWalletToDeposit] = useState<string | undefined>(undefined)

  const tabs: UITab[] = headerTabs.map((tab) => ({
    value: tab.value,
    label: t(tab.label),
  }))

  const contextValue: AssetOverviewContextState = useMemo(() => {
    return {
      hideBalance,
      toggleHideBalance: setHideBalance,
    }
  }, [hideBalance, setHideBalance])

  const [params] = useSearchParams()

  useEffect(() => {
    const currentTab = headerTabs.find((item) => item.slug === params.get('tab'))
    if (currentTab) {
      setCurrentTab(currentTab.value)
    } else {
      setCurrentTab(headerTabs[0].value)
    }
  }, [params])

  const navigate = useNavigate()

  const handleTabChanged = (tab: string) => {
    setCurrentTab(tab)
    const slug = headerTabs.find((item) => item.value === tab)?.slug
    const newSearchParams = new URLSearchParams(window.location.search)
    newSearchParams.set('tab', slug || '')
    if (slug) {
      navigate({ search: newSearchParams.toString() }, { replace: true })
    }
  }

  const handleOnItemClicked = (key: string) => {
    switch (key) {
      case 'deposit':
        setOpenChooseAccountPopup(true)
        break
      case 'withdraw':
        selectAccountDrawerRef.current?.show()
        break
      case 'transfer':
        // Handle transfer action
        break
      default:
        break
    }
  }

  const activeWallet = useActiveWallet()

  useEffect(() => {
    const fetchWalletBalance = async () => {
      if (!activeWallet?.walletId) return

      try {
        const { data } = await gqlClient.query({
          query: getWalletBalance,
          variables: {
            input: {
              duration: 'd1',
              walletAddress: currentTab !== 'Overview' ? activeWallet?.walletId : undefined,
            },
          },
        })
        const wallets = data.getWalletBalance as WalletBalanceDto[]
        setData({
          funding: wallets.filter((wallet) => wallet.walletType === 'Funding'),
          futures: wallets.filter((wallet) => wallet.walletType === 'Futures'),
          spot: wallets.filter((wallet) => wallet.walletType === 'Spot'),
        })
      } catch (error) {
        console.error('Error fetching wallet balance:', error)
        setData({
          funding: undefined,
          futures: undefined,
          spot: undefined,
        })
      }
    }
    if (ServiceConfig.token) {
      fetchWalletBalance()
    }
  }, [activeWallet, currentTab, ServiceConfig.token])

  if (!activeWallet || !activeWallet.isConnected) return <Splash />

  return (
    <AssetOverviewProvider value={contextValue}>
      <div className="relative bg-[#111111] min-h-[calc(100vh-75px)] -mb-5 overflow-y-hidden">
        <div className="max-h-full overflow-y-auto no-scrollbar">
          <div className=" sticky top-0 z-20 pt-3 bg-[#111111]">
            <MovingLineTabs
              tabs={tabs}
              disabledTabs={[]}
              defaultTab={currentTab}
              onTabChange={(tab) => handleTabChanged(tab)}
              containerClassName="justify-start bg-transparent"
            />
          </div>
          <AssetsHeader tab={currentTab} onItemClick={handleOnItemClicked} wallets={data} />
          <div>{renderTabContent(currentTab)}</div>
          {chain === TYPE_CHAIN.SOLANA && <WalletSolanaConnectModal />}
          <ChooseAccountPopup
            open={openChooseAccountPopup}
            setOpen={setOpenChooseAccountPopup}
            setOpenChooseTokenPopup={(open: boolean, setWalletAccountSelect) => {
              setOpenChooseTokenPopup(open)
              if (setWalletAccountSelect) {
                setWalletToDeposit(setWalletAccountSelect)
              }
            }}
          />
          <ChooseTokenPopup
            open={openChooseTokenPopup}
            setOpen={setOpenChooseTokenPopup}
            walletToDeposit={walletToDeposit}
          />
        </div>
      </div>
      <SelectAccountDrawer ref={selectAccountDrawerRef} />
    </AssetOverviewProvider>
  )
}

export default AssetsPage
