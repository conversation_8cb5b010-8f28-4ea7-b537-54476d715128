import HeaderWithBack from '@components/header/HeaderWithBack.tsx'
import { Switch } from '@components/ui/switch.tsx'
import { ReactNode, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'
import { IconDollarCircle } from '@components/icon/gradient/IconDollarCircle.tsx'
import { IconEmergency } from '@components/icon/gradient/IconEmergency.tsx'
import { IconBell } from '@components/icon/gradient/IconBell.tsx'
import { useNotificationsEnabled } from '@hooks/useNotificationsEnabled.ts'
import { EnableManuallyGuide } from '@/components/settings/EnableManuallyGuide'
import { useAppSelector } from '@/redux/store'
import { NotificationPreference } from '@/redux/modules/userSettings.slice.ts'
import { NotificationTypeCategoryCode } from '@/@generated/gql/graphql-user.ts'
import { useMutation } from '@apollo/client'
import { updateNotificationPreferences } from '@services/userSettings.service.ts'
import { userGqlClient } from '@/lib/gql/apollo-client.ts'
import { IconMessageNotificationGradient } from '@components/icon'

const NotificationsEnabledChecker = () => {
  const { notificationsEnabled, requestNotificationPermission } = useNotificationsEnabled({
    onDenied: () => toast.custom((id) => <EnableManuallyGuide id={id} />, { duration: 3000 }),
  })
  const { t } = useTranslation()

  const handleClick = () => {
    requestNotificationPermission()
  }

  if (notificationsEnabled) return null
  return (
    <div className="bg-black border border-[#ECECED1F] rounded-[8px] px-3 py-4 flex">
      <div className="flex-1">
        <div className="flex items-center gap-1.5 mb-2.5">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M8.00065 1.33301C4.32732 1.33301 1.33398 4.32634 1.33398 7.99967C1.33398 11.673 4.32732 14.6663 8.00065 14.6663C11.674 14.6663 14.6673 11.673 14.6673 7.99967C14.6673 4.32634 11.674 1.33301 8.00065 1.33301ZM7.50065 5.33301C7.50065 5.05967 7.72732 4.83301 8.00065 4.83301C8.27398 4.83301 8.50065 5.05967 8.50065 5.33301V8.66634C8.50065 8.93967 8.27398 9.16634 8.00065 9.16634C7.72732 9.16634 7.50065 8.93967 7.50065 8.66634V5.33301ZM8.61398 10.9197C8.58065 11.0063 8.53398 11.073 8.47398 11.1397C8.40732 11.1997 8.33398 11.2463 8.25398 11.2797C8.17398 11.313 8.08732 11.333 8.00065 11.333C7.91398 11.333 7.82732 11.313 7.74732 11.2797C7.66732 11.2463 7.59398 11.1997 7.52732 11.1397C7.46732 11.073 7.42065 11.0063 7.38732 10.9197C7.35398 10.8397 7.33398 10.753 7.33398 10.6663C7.33398 10.5797 7.35398 10.493 7.38732 10.413C7.42065 10.333 7.46732 10.2597 7.52732 10.193C7.59398 10.133 7.66732 10.0863 7.74732 10.053C7.90732 9.98634 8.09398 9.98634 8.25398 10.053C8.33398 10.0863 8.40732 10.133 8.47398 10.193C8.53398 10.2597 8.58065 10.333 8.61398 10.413C8.64732 10.493 8.66732 10.5797 8.66732 10.6663C8.66732 10.753 8.64732 10.8397 8.61398 10.9197Z"
              fill="#FF6E27"
            />
          </svg>
          <span className="text-white font-medium text-[calc(14rem/16)] leading-3.5">
            {t('appSettings.notifications.enablePushNotifications')}
          </span>
        </div>
        <div className="text-white text-[calc(12rem/16)] leading-3">
          {t('appSettings.notifications.enablePushNotificationsSubtitle')}
        </div>
      </div>
      <div className="flex items-center gap-1.5 cursor-pointer" onClick={handleClick}>
        <span className="text-white text-[calc(13rem/16)]">{t('appSettings.notifications.enableBtn')}</span>
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M6.61935 12.0465L10.666 7.99979L6.61935 3.95312"
            stroke="#878787"
            strokeWidth="1.5"
            strokeMiterlimit="10"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </div>
    </div>
  )
}

type Item = {
  key: 'smartMoneyAlert' | 'tokenPrice' | 'tradingSignal'
  title: string
  subTitle: string
  icon: ReactNode
  disabled: boolean
}

const useItems = () => {
  const { t } = useTranslation()
  return useMemo(() => {
    return [
      {
        key: 'smartMoneyAlert',
        title: t('appSettings.notifications.smartMoneyTitle'),
        subTitle: t('appSettings.notifications.smartMoneySubtitle'),
        icon: <IconDollarCircle />,
        disabled: false,
      },
      {
        key: 'tokenPrice',
        title: t('appSettings.notifications.priceAlertTitle'),
        subTitle: t('appSettings.notifications.priceAlertSubtitle'),
        icon: <IconEmergency />,
        disabled: true, // Disable this item
      },
      {
        key: 'tradingSignal',
        title: t('appSettings.notifications.tradingTitle'),
        subTitle: t('appSettings.notifications.tradingSubtitle'),
        icon: <IconBell />,
        disabled: true, // Disable this item
      },
      {
        key: 'others',
        title: t('appSettings.notifications.othersTitle'),
        subTitle: t('appSettings.notifications.othersSubtitle'),
        icon: <IconMessageNotificationGradient />,
        disabled: true,
      },
    ] as Item[]
  }, [t])
}

const mapTypeCode: Record<string, NotificationTypeCategoryCode> = {
  smartMoneyAlert: NotificationTypeCategoryCode.SmartMoneyActivity,
  tokenPrice: NotificationTypeCategoryCode.PriceChange,
  tradingSignal: NotificationTypeCategoryCode.FuturesSignal,
  others: NotificationTypeCategoryCode.Others,
}

export const NotificationsSettingsPage = () => {
  const [settings, setSettings] = useState({
    smartMoneyAlert: false,
    tokenPrice: false,
    tradingSignal: false,
    others: false,
  })
  const { t } = useTranslation()

  const items = useItems()
  const [mutate] = useMutation(updateNotificationPreferences, {
    client: userGqlClient,
  })

  const handleChange = (key: string, newValue: boolean) => {
    setSettings((prev) => ({ ...prev, [key]: newValue }))
    mutate({
      variables: {
        input: {
          notificationTypeCode: mapTypeCode[key],
          isEnabled: newValue,
        },
      },
    }).then(() => {})
  }

  const notificationSettings = useAppSelector(
    (state) => state.userSettings.notificationPreferences as NotificationPreference[],
  )

  useEffect(() => {
    if (!notificationSettings) return
    setSettings({
      smartMoneyAlert:
        notificationSettings.find(
          (item) => item.notificationTypeCode === NotificationTypeCategoryCode.SmartMoneyActivity,
        )?.isEnabled ?? false,
      tokenPrice:
        notificationSettings.find((item) => item.notificationTypeCode === NotificationTypeCategoryCode.PriceChange)
          ?.isEnabled ?? false,
      tradingSignal:
        notificationSettings.find((item) => item.notificationTypeCode === NotificationTypeCategoryCode.FuturesSignal)
          ?.isEnabled ?? false,
      others:
        notificationSettings.find((item) => item.notificationTypeCode === NotificationTypeCategoryCode.Others)
          ?.isEnabled ?? false,
    })
  }, [notificationSettings])

  return (
    <div className="w-full h-dvh flex flex-col">
      <HeaderWithBack title={t('appSettings.notifications.title')} className="bg-transparent" />
      <div className="px-3 py-6 space-y-4">
        <NotificationsEnabledChecker />
        <div>
          {items.map((item) => (
            <div
              key={item.key}
              className={`flex items-center justify-between px-3 py-4 border-b border-[#ECECED14] last:border-b-0 ${
                item.disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
              }`}
              onClick={() => {
                if (item.disabled) return // Prevent interaction if disabled
                handleChange(item.key, !settings[item.key])
              }}
            >
              <div className="size-9 flex items-center justify-center bg-[#ECECED14] mr-2 rounded-full">
                {item.icon}
              </div>
              <div className="flex flex-col flex-1">
                <span className="text-[calc(15rem/16)] text-[#FFFFFF]">{item.title}</span>
                <span className="text-[calc(14rem/16)] text-[#FFFFFFCC]">{item.subTitle}</span>
              </div>
              <Switch checked={settings[item.key]} disabled={item.disabled} />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
