import { UITab } from '@/types/uiTabs'

export interface CryptoData {
  id: string
  symbol: string
  pair: string
  leverage: string
  marketCap: string
  price: string
  volume: string
  priceChange: number
}

export const headerTabs: UITab[] = [
  {
    value: '自选',
    label: '自选', // FavoriteList
  },
  {
    value: '热门',
    label: '热门', //VolumeList
  },
  {
    value: '分类',
    label: '分类',
  },
  {
    value: '涨幅榜',
    label: '涨幅榜',
  },
  {
    value: '跌幅榜',
    label: '跌幅榜',
  },
  {
    value: '成交额',
    label: '成交额',
  },
  {
    value: '持仓额',
    label: '持仓额',
  },
  {
    value: '市值',
    label: '市值',
  },
]

export interface ISymbolList {
  changPxPercent: number
  currentPrice: number
  marketCap: number
  maxLeverage: number
  symbol: string
  volume: string
  isFavorite?: boolean
}
