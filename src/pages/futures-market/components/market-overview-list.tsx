import MovingLineTabs from '@/components/common/MovingLineTabs'
import { useState } from 'react'
import { headerTabs } from '../type'
import ClassificationList from './classification-list'
import FavoriteList from './favorite-list'
import GianerList from './gianer-list'
import LoserList from './loser-list'
import MarketCapList from './market-cap-list'
import PositionList from './position-list'
import Transactions from './transactions'
import TrendingTable from './trending-table'
import VolumeList from './volume-list'
import useSymbolListSubscription from '../hooks/useSymbolListSubscription'
import SearchBar from './search-bar'
import Container from '@/components/common/Container'

const MarketOverviewList = () => {
  const [currentTab, setCurrentTab] = useState<string>(headerTabs[1].value)
  const { symbolData } = useSymbolListSubscription({
    shouldSkip: false,
  })

  const handleTabChange = (tab: string) => {
    setCurrentTab(tab)
  }

  const handleRenderTab = (tab: string) => {
    switch (tab) {
      case headerTabs[0].value:
        return <FavoriteList symbolData={symbolData} search={search} />
      case headerTabs[1].value:
        return <VolumeList symbolData={symbolData} search={search} />
      case headerTabs[2].value:
        return <ClassificationList symbolData={symbolData} search={search} />
      case headerTabs[3].value:
        return <GianerList symbolData={symbolData} search={search} />
      case headerTabs[4].value:
        return <LoserList symbolData={symbolData} search={search} />
      case headerTabs[5].value:
        return <Transactions symbolData={symbolData} search={search} />
      case headerTabs[6].value:
        return <PositionList symbolData={symbolData} search={search} />
      case headerTabs[7].value:
        return <MarketCapList symbolData={symbolData} search={search} />

      default:
        return <TrendingTable />
    }
  }
  const [search, setSearch] = useState('')
  return (
    <div className="relative overflow-hidden bg-[#19191E]">
      {/* <div
        className="absolute inset-0 w-full h-screen bg-[url(/images/listCoinCrypto/充值bg.png)] bg-no-repeat bg-cover z-0 opacity-80"
        style={{
          backgroundPositionY: '-15px',
        }}
      /> */}

      <div className="w-full max-w-[768px] overflow-hidden h-screen">
        <Container className="">
          <SearchBar setSearch={setSearch} />
        </Container>
        <MovingLineTabs
          tabs={headerTabs}
          defaultTab={headerTabs[1].value}
          onTabChange={handleTabChange}
          containerClassName="after:hidden w-full gradient-border-buttom rounded-t-[8px] bg-inherit z-1 relative"
          tabsClassName="w-full"
          wrapperClassName="z-1"
          itemClassName="px-3.5"
          tabsListClassName="px-0"
        />
        <div className="w-full overflow-hidden relative z-1">
          <div className="z-1 relative">{handleRenderTab(currentTab)}</div>
          {/* <div className="h-[95px]" /> */}
        </div>
      </div>
    </div>
  )
}

export default MarketOverviewList
