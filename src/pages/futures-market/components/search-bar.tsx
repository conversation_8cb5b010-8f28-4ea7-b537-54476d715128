import Text from '@/components/common/Text'
import { TokenSearchDrawer, TokenSearchDrawerType } from '@/components/futuresDetails/tokenSearchDrawer'
import useDebounceValue from '@/hooks/useDebounceValue'
import { Search } from 'lucide-react'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'

const SearchBar = ({ setSearch }: { setSearch: Dispatch<SetStateAction<string>> }) => {
  const [value, setValue] = useState<string>('')
  const debounceValue = useDebounceValue(value, 500)
  const [openTokenSearch, setOpenTokenSearch] = useState(false)

  useEffect(() => {
    setSearch(debounceValue)
  }, [debounceValue, setSearch])

  return (
    <div className="py-3">
      <div className="relative w-full z-1" onClick={() => setOpenTokenSearch(true)}>
        <div
          className="w-full 
                   bg-[#ECECED14] 
                     text-[calc(1rem*(14/16))]
                     pl-[14px] 
                     pr-[10px] 
                     h-[36px]
                     rounded-full 
                     border-[0.5px] 
                     border-[#ECECED14] 
                     focus:outline-none 
                     focus:border-[#444444]
                     flex 
                     cursor-pointer 
                     items-center
                    "
        >
          <Text text="搜索币种名称" fontSize={14} fontWeight="regular" color="#FFFFFFB2" className="" />
        </div>
        <Search className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 size-[18px]" />
      </div>
      <TokenSearchDrawer
        open={openTokenSearch}
        setOpen={setOpenTokenSearch}
        type={TokenSearchDrawerType.CRYPTO}
        allowShowList
      />
    </div>
  )
}

export default SearchBar
