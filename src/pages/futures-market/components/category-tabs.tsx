import React, { memo, useState } from 'react'
import { cn } from '@/lib/utils'

interface CategoryTabsProps {
  tabs?: string[]
  activeTab?: string
  onTabChange?: (tabValue: string) => void
  className?: string
  isLoadingCategory?: boolean
}

const CategoryTabs: React.FC<CategoryTabsProps> = ({
  tabs = [],
  activeTab: controlledActiveTab,
  onTabChange,
  className,
  isLoadingCategory,
}) => {
  const [internalActiveTab, setInternalActiveTab] = useState(tabs[0] || 'all')

  // Use controlled or uncontrolled state
  const activeTab = controlledActiveTab ?? internalActiveTab

  const handleTabClick = (tabValue: string) => {
    if (onTabChange) {
      onTabChange(tabValue)
    } else {
      setInternalActiveTab(tabValue)
    }
  }

  return (
    <div className={cn('gap-3 flex items-center px-3.5 pt-1 w-fit', className)}>
      {isLoadingCategory ? (
        <div className="relative pb-4">
          <div className="gap-3 flex items-center px-2 pt-1 w-fit mb-4">
            {Array.from({ length: 7 }).map((_, index) => (
              <div key={index} className="h-6 w-16 bg-primary/10 rounded animate-pulse" />
            ))}
          </div>
        </div>
      ) : (
        tabs.map((tab) => (
          <button
            key={tab}
            onClick={() => handleTabClick(tab)}
            className={cn(
              ' py-2 transition-all duration-200 ease-in-out whitespace-nowrap app-font-regular  text-[calc(1rem*(12/16))]',
              'hover:text-white',
              activeTab === tab ? 'text-white' : 'text-[#FFFFFFB2]',
            )}
          >
            {tab}
          </button>
        ))
      )}
    </div>
  )
}

export default memo(CategoryTabs)
