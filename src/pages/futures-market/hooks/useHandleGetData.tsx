import { symbolDexClient } from '@/lib/gql/apollo-client'
import { GET_CATEGORY_LIST, GET_FAVORITE_SYMBOLS, GET_SYMBOL_LIST } from '@/services/symbol.dex.service'
import { useEffect, useMemo, useState } from 'react'
import { ISymbolList } from '../type'

interface IPUseHandleGetData {
  condition: 'gainer' | 'volume' | 'loser' | 'trend' | 'marketCap' | 'openInterest' | 'category'
  isFavorite?: boolean
  isCategory?: boolean
  isDisabledNomalList?: boolean
}

export interface UpsertFavoriteSymbolResponse {
  upsertFavoriteSymbol: {
    error: string | null
    status: boolean
  }
}

export const useSearchFilter = (data: ISymbolList[], searchValue: string) => {
  return useMemo(() => {
    if (!searchValue || !searchValue.trim()) {
      return data
    }

    const searchTerm = searchValue.toLowerCase().trim()

    return data.filter((item) => {
      const symbolMatch = item.symbol?.toLowerCase().includes(searchTerm)
      return symbolMatch
    })
  }, [data, searchValue])
}

const CATEGORY_ALL = '全部'

const useHandleGetData = ({ condition, isFavorite, isCategory, isDisabledNomalList }: IPUseHandleGetData) => {
  const [symbolList, setSymbolList] = useState<ISymbolList[]>([])
  const [symbolsFavorite, setSymbolsFavorite] = useState<ISymbolList[]>([])
  const [isLoadingSymbol, setIsLoadingSymbol] = useState(true)
  const [isLoadingFavorite, setIsLoadingFavorite] = useState(true)
  const [categoryList, setCategoryList] = useState<string[]>([])
  const [isLoadingCategory, setIsLoadingCategory] = useState(false)

  const handleGetCategoryList = async () => {
    try {
      setIsLoadingCategory(true)
      const { data } = await symbolDexClient.query({
        query: GET_CATEGORY_LIST,
      })
      setCategoryList([CATEGORY_ALL, ...(data?.getCategory?.categories || [])])
    } catch (error) {
      console.error('Error fetching category list:', error)
    } finally {
      // setIsLoadingCategory(false)
    }
  }

  const handleGetSymbolList = async (categoryArgs?: string) => {
    try {
      let input: { condition: IPUseHandleGetData['condition']; category?: string } = {
        condition: condition === 'category' && categoryArgs === CATEGORY_ALL ? 'volume' : condition,
      }

      if (condition === 'category' && categoryArgs && categoryArgs !== CATEGORY_ALL) {
        input = {
          condition: 'category',
          category: categoryArgs,
        }
      }
      setIsLoadingSymbol(true)
      const { data, loading } = await symbolDexClient.query({
        query: GET_SYMBOL_LIST,
        variables: { input },
      })
      setSymbolList(data?.getSymbolList?.list || [])
      setIsLoadingSymbol(loading)
    } catch (error) {
      console.error('Error fetching symbol list:', error)
    } finally {
      setIsLoadingSymbol(false)
      setIsLoadingCategory(false)
    }
  }

  const getFavoriteSymbols = async () => {
    try {
      setIsLoadingFavorite(true)
      const { data, loading } = await symbolDexClient.query({
        query: GET_FAVORITE_SYMBOLS,
      })
      setSymbolsFavorite(data?.getFavoriteSymbols?.list || [])
      setIsLoadingFavorite(loading)
    } catch (error) {
      console.error('Error fetching favorite symbols:', error)
    } finally {
      setIsLoadingFavorite(false)
    }
  }

  useEffect(() => {
    if (isFavorite) {
      getFavoriteSymbols()
    }

    if (isDisabledNomalList) {
      return
    }

    if (!isCategory) {
      handleGetSymbolList()
    } else {
      handleGetCategoryList()
    }
  }, [])

  return {
    symbolList,
    isLoading: isLoadingSymbol,
    symbolsFavorite,
    isLoadingFavorite,
    categoryList,
    isLoadingCategory,
    getFavoriteSymbols,
    handleGetCategoryList,
    handleGetSymbolList,
  }
}

export default useHandleGetData
