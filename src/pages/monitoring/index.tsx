import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { UITab } from '@/types/uiTabs.ts'
import { useSearchParams } from 'react-router-dom'
import MovingLineTabs from '@components/common/MovingLineTabs.tsx'
// import TabRealTimeTransactions from '@components/monitoring/TabRealTimeTransactions.tsx'
// import TabFollowing from '@components/monitoring/TabFollowing.tsx'
import TabSmartMoney from '@components/listCoin/TabSmartMoney.tsx'
import TokenDetailSmartMoney from '@components/tokenDetailSmartMoney'

const MonitoringPage = () => {
  const { t } = useTranslation()
  const [searchParams, setSearchParams] = useSearchParams()

  const navTabs: UITab[] = [
    {
      value: 'realTimeTransactions',
      label: t('monitoring.realTimeTransactions'),
    },
    {
      value: 'following',
      label: t('monitoring.following'),
    },
  ]

  const [currentNavTab, setCurrentNavTab] = useState<string>(searchParams.get('tab') || navTabs[0].value)

  const handleChangeTab = (tab: string) => {
    setCurrentNavTab(tab)
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev)
      newParams.set('tab', tab)
      return newParams
    })
  }

  const handleRenderTab = (tab: string) => {
    switch (tab) {
      case navTabs[0].value:
        return <TokenDetailSmartMoney />
        case navTabs[1].value:
        return <TabSmartMoney />
      default:
        return <TokenDetailSmartMoney />
    }
  }

  return (
    <div className="@container relative mx-auto mt-2 -mb-20 ">
      <MovingLineTabs
        tabs={navTabs}
        defaultTab={currentNavTab}
        onTabChange={handleChangeTab}
        containerClassName="bg-[none] pb-[5px] relative before:absolute before:bottom-0 before:left-0 before:right-0 before:h-[0.6px] before:bg-[linear-gradient(64.61deg,_rgba(63,_45,_123,_0.6)_9.27%,_rgba(0,_107,_81,_0.6)_67.81%)]"
        tabsClassName="w-full"
        itemClassName="font-normal"
      />

      {handleRenderTab(currentNavTab)}
    </div>
  )
}

export default MonitoringPage
