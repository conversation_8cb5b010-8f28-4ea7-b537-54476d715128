import { Skeleton } from '@/components/ui/skeleton'
import DeleteCopyButton from '@/components/walletCopyDetails/DeleteCopyButton'
import HeaderTitle from '@/components/walletCopyDetails/HeaderTitle'
import { tradingClient } from '@/lib/gql/apollo-client'
import { getCopyTradeConfigById } from '@/services/copytrade.service'
import { ChainIds } from '@/types/enums'
import { useQuery } from '@apollo/client'
import HeaderWithBack from '@components/header/HeaderWithBack.tsx'
import Information from '@components/walletCopyDetails/Information.tsx'
import OrderTrackingList from '@components/walletCopyDetails/OrderTrackingList.tsx'
import ResultDataStatistics from '@components/walletCopyDetails/ResultDataStatistics.tsx'
import { get } from 'lodash-es'
import { useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'

export default function CopyTradingDetailsPage() {
  const [listAddress, setlistAddress] = useState<string[]>([]);
  const navigate = useNavigate()
  const { address } = useParams();
  const { data, loading } = useQuery(getCopyTradeConfigById, {
    variables: {
      input: {
        id: address
      }
    },
    client: tradingClient,
  })
  useEffect(() => {
    if (data) {
      const copyTradeConfig = get(data, 'getCopyTradeConfig', null);
      if (copyTradeConfig) {
        setlistAddress(copyTradeConfig.statistic.copyTradeTokenHoldingStatistics.map((item: { baseAddress: string }) => item.baseAddress));
      }
    }
  }, [data]);
  const getCopyTradeConfig = get(data, 'getCopyTradeConfig', {
    id: '',
    status: 'Active',
    userAddress: '',
    leaderAddress: '',
    leaderNickname: '',
    chainId: ChainIds.Solana,
    createdAt: '',
    leaderTags: [],
  })
  const { status, createdAt } = getCopyTradeConfig
  const onBack = () => {
    navigate(-1)
  }
  return (
    <div className="flex flex-col bg-[#111111] no-scrollbar">
      <HeaderWithBack
        title={<HeaderTitle status={status} id={address || ''} />}
        right={<DeleteCopyButton id={address || ''} />}
        onBack={onBack}
        className="bg-transparent p-2.5 h-12"
      />
      <div className="max-h-[calc(100vh_-_45px)] overflow-x-hidden overflow-y-auto no-scrollbar px-2.5 pb-7">
        {/* <Information address={address} status={status} tradeConfig={getCopyTradeConfig} tags={leaderTags ? leaderTags : []} token={{ chainId: ChainIds.Solana, token: userAddress }} walletName={listCoinHelper.formatWalletNameCustom(leaderNickname || leaderAddress)} /> */}
        {loading ? <Skeleton /> : <Information address={address} tradeConfig={getCopyTradeConfig} />}
        {
          address && !loading ?
            <ResultDataStatistics
              createdAt={createdAt}
              listAddress={listAddress}
              loading={loading}
              data={get(getCopyTradeConfig, 'statistic.copyTradeTokenHoldingStatistics', [])}
            /> :
            null
        }
        {address ? <OrderTrackingList id={address} /> : null}
      </div>
    </div>
  )
}
