import WalletSettingsForm from '@/components/copy-trading/wallet-settings/WalletSettingsForm'
import Header<PERSON>ithBack from '@components/header/HeaderWithBack'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate, useParams } from 'react-router-dom'

/**
 * CopyTradeWalletSettings component displays the wallet settings for copy trading
 */
const CopyTradeWalletSettings: React.FC = () => {
  const navigate = useNavigate()
  const { t } = useTranslation()

  const handleBack = () => {
    navigate(-1)
  }

  const handleCancel = () => {
    navigate(-1)
  }

  return (
    <div className="@container mx-auto mt-2 flex flex-col items-stretch overflow-x-hidden overscroll-none min-h-[calc(100dvh-139px)] overflow-y-auto no-scrollbar">
      <div className="flex flex-col bg-[#111] bg-[url('/images/walletCopy/bg_setting.png')] bg-cover bg-center text-white">
        <HeaderWithBack
          title={t('walletCopy.settings.title')}
          onBack={handleBack}
          // right={<button className="text-sm font-medium capitalize">{t('walletCopy.settings.tutorial')}</button>}
          className="bg-transparent p-2.5"
        />
        <div className="px-2.5">
          <WalletSettingsForm
            onCancel={handleCancel}
            initialValues={
              {
                // You can provide initial values here if needed
              }
            }
          />
        </div>
      </div>
    </div>
  )
}

export default CopyTradeWalletSettings
