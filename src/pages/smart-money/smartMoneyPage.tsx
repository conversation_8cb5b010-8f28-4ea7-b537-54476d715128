import { useTranslation } from 'react-i18next'
import { UITab } from '@/types/uiTabs.ts'
import MovingLineTabs from '@components/common/MovingLineTabs.tsx'
import { useState } from 'react'
// import TokenDetailSmartMoney from '@components/tokenDetailSmartMoney'
import TabCopyTrade from '@components/listCoin/TabCopyTrade.tsx'
// import TabSmartMoney from '@components/listCoin/TabSmartMoney.tsx'
import { useSearchParams } from 'react-router-dom'

const SmartMoneyPage = () => {
  const { t } = useTranslation()
  const [searchParams, setSearchParams] = useSearchParams();

  const navTabs: UITab[] = [
    {
      value: 'topTalents',
      label: t('smartMoney.tabs.topTalents'),
    },
    {
      value: 'walletCopy',
      label: t('smartMoney.tabs.walletCopy'),
    },
    // {
    //   value: 'smartMoney',
    //   label: t('smartMoney.tabs.smartMoney'),
    // },
    // {
    //   value: 'activities',
    //   label: t('smartMoney.tabs.Activities'),
    // },
  ]

  const [currentNavTab, setCurrentNavTab] = useState<string>(searchParams.get('tab') || navTabs[0].value)

  const handleRenderTab = (tab: string) => {
    switch (tab) {
      case navTabs[0].value:
        return <TabCopyTrade.TopTraders />
      case navTabs[1].value:
        return <TabCopyTrade.WalletCopyTrade />
      // case navTabs[2].value:
      //   return <TabSmartMoney />
      // case navTabs[3].value:
      //   return <TokenDetailSmartMoney />
      default:
        return <TabCopyTrade.TopTraders />
    }
  }

  const handleChangeTab = (tab: string) => {
    setCurrentNavTab(tab)
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev)
      newParams.set('tab', tab)
      return newParams
    });
  }

  return (
    <div className="@container relative mx-auto mt-2 -mb-20">
      <MovingLineTabs
        tabs={navTabs}
        defaultTab={currentNavTab}
        onTabChange={handleChangeTab}
        containerClassName="bg-[none] pb-[5px] relative before:absolute"
        tabsClassName="w-full"
        itemClassName="font-normal"
      />
      {handleRenderTab(currentNavTab)}
    </div>
  )
}

export default SmartMoneyPage