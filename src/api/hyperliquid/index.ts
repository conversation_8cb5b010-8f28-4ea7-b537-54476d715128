import { useMutation } from '@tanstack/react-query'
import request from './request'
import { IUserFunding, IUserHistoricalOrder, PerpetualsMetaResponse } from '@/api/hyperliquid/types'
import { toast } from 'sonner'

export function getPerpetualsMetadata(): Promise<PerpetualsMetaResponse> {
  return request<PerpetualsMetaResponse>({
    url: '/info',
    method: 'post',
    data: {
      type: 'spotMeta',
    },
  })
}

export function getCandleSnapshot(data: any) {
  return request({
    url: '/info',
    method: 'post',
    data: {
      req: data,
      type: 'candleSnapshot',
    },
  })
}

export function getPredictedFundings() {
  return request({
    url: '/info',
    method: 'post',
    data: {
      type: 'predictedFundings',
    },
  })
}

export function getUserAssests(user: string) {
  return request({
    url: '/info',
    method: 'post',
    data: {
      type: 'clearinghouseState',
      user: user,
    },
  })
}

export function getPerpMetadata() {
  return request({
    url: '/info',
    method: 'post',
    data: {
      type: 'meta',
    },
  })
}

export function getPerpMetaAndAssetCtxs() {
  return request({
    url: '/info',
    method: 'post',
    data: {
      type: 'metaAndAssetCtxs',
    },
  })
}



// Retrieve a user's funding history
export function getUserFunding() {
  return useMutation({
    mutationFn: (user: string) => {
      return request<IUserFunding[]>({
        url: '/info',
        method: 'post',
        data: {
          type: 'userFunding',
          user: user,
        },
      })
    },
    mutationKey: ['userFunding'],
    onError: (error) => {
      console.log('error', error)
      toast.error(error.message)
    },
  })
}

// Retrieve a user's historical orders
export function getUserHistoricalOrders() {
  return useMutation({
    mutationFn: (user: string) => {
      return request<IUserHistoricalOrder[]>({
        url: '/info',
        method: 'post',
        data: {
          type: 'historicalOrders',
          user: user,
        },
      })
    },
    mutationKey: ['userHistoricalOrders'],
    onError: (error) => {
      console.log('error', error)
      toast.error(error.message)
    },
  })
}

export function getPerpUserHistoryOrders(user: string) {
  return request({
    url: '/info',
    method: 'post',
    data: {
      "type": "historicalOrders",
      "user": user
    }
  });
}


export function getPerpUserHistoryTrades (user: string) {
  return request({
    url: '/info',
    method: "post",
    data: {
      "aggregateByTime": true,
      "type": "userFills",
      "user": user
    }
  });

}

export function getClearinghouseState (user: string) {
  return request({
    url: '/info',
    method: "post",
    data: {
      "type": "clearinghouseState",
      "user": user
    }
  });

}

// Retrieve mids for all coins
export function getgetAllMids() {
  return useMutation({
    mutationFn: (specificDex?: string) => {
      return request<Record<string, string>>({
        url: '/info',
        method: 'post',
        data: {
          type: 'allMids',
          dex: specificDex,
        },
      })
    },
    mutationKey: ['userHistoricalOrders'],
    onError: (error) => {
      console.log('error', error)
      toast.error(error.message)
    },
  })
}


export function fetchOrderBookSnapshot(data: {
  type: string;
  coin: string;
  nSigFigs?: number | null;
  mantissa?: number | null;
}) {
  return request({
    url: '/info',
    method: 'post',
    data,
  });
}