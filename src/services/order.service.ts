import { gql } from '@apollo/client'

export const createOrderMutation = gql`
  mutation createOrder($input: CreateOrderInput!) {
    createOrder(input: $input) {
      id
      createdAt
      updatedAt
      deletedAt
      transactionType
      type
      baseAddress
      quoteAddress
      userAddress
      limitPrice
      baseAmount
      quoteAmount
      exit
      tp
      sl
      exitAt
      status
      txid
      chainId
      baseDecimal
      baseSymbol
      quoteSymbol
      slippage
      marketCap
    }
  }
`

export const getNetworkFeeQuery = gql`
  query getNetworkFee($input: NetworkFeeInput!) {
    getNetworkFee(input: $input) {
      solana {
        priorityFeePrice {
          medium
          high
          veryHigh
        }
        maxComputeUnits
      }
      ethereum {
        low {
          suggestedMaxPriorityFeePerGas
          suggestedMaxFeePerGas
          minWaitTimeEstimate
          maxWaitTimeEstimate
        }
        medium {
          suggestedMaxPriorityFeePerGas
          suggestedMaxFeePerGas
          minWaitTimeEstimate
          maxWaitTimeEstimate
        }
        high {
          suggestedMaxPriorityFeePerGas
          suggestedMaxFeePerGas
          minWaitTimeEstimate
          maxWaitTimeEstimate
        }
        estimatedBaseFee
      }
    }
  }
`

export const countPendingOrders = gql`
  query getPendingOrders($input: SearchOrderInput!) {
    getPendingOrders(input: $input) {
      total
    }
  }
`

export const getCurrentOrdersQuery = gql`
  query orders($input: SearchOrderInput!) {
    orders(input: $input) {
      id
      chainId
      baseAddress
      quoteAddress
      createdAt
      openPrice
      baseAmount
      quoteAmount
      marketCap
      doublePrincipalAfterPurchase
      transactionType
      baseSymbol
      tp
      sl
      limitPrice
      type
      quoteSymbol
      slippage
      triggerPrice
      callbackRate
      trailingOrderTriggered
      triggerAt
      txid
      mevProtect
      priorityFeePrice
      status
      triggerPrice
      openQuoteUsdRate
      callbackRate
    }
  }
`
export const getOrdersHistoryQuery = gql`
  query orderHistory($input: SearchOrderInput!) {
    orderHistory(input: $input) {
      id
      createdAt
      updatedAt
      deletedAt
      transactionType
      type
      baseAddress
      quoteAddress
      userAddress
      limitPrice
      openPrice
      baseAmount
      quoteAmount
      exit
      tp
      sl
      openSubmitMeta
      closeSubmitMeta
      filledAt
      exitReason
      exitAt
      status
      txId
      openTxId
      closeTxId
      chainId
      baseDecimal
      baseSymbol
      quoteSymbol
      slippage
      triggerPrice
      callbackRate
      trailingOrderTriggered
      triggerAt
      mevProtect
      priorityFeePrice
      doublePrincipalAfterPurchase
    }
  }
`

export const getTransactionHistory = gql`
  query getTransactions($input: SearchOrderInput!) {
    getTransactions(input: $input) {
      id
      createdAt
      transactionType
      baseAddress
      baseAmount
      quoteAmount
      txid
      chainId
      baseDecimal
      baseSymbol
      slippage
      pnl
      closePriceQuote
      closePriceUsd
      slippageLossAmount
      platformFeeAmount
      antiMevFeeAmount
      gasFeeAmount
      pumpFeeAmount
      antiMevFeeAmount
      platformFeeAmount
      priorityFeeAmount
      marketCap
    }
  }
`

export const getTransactionTokens = gql`
  query transactionTokens($input: TransactionTokenInput!) {
    transactionTokens(input: $input) {
      address
      symbol
      chainId
    }
  }
`

export const modifyOrderMutation = gql`
  mutation modifyOrder($input: ModifyOrderInput!) {
    modifyOrder(input: $input) {
      id
    }
  }
`

export const getHistoryStatistic = gql`
  query historyStatistic($input: SearchOrderInput!) {
    historyStatistic(input: $input) {
      totalOrder
      totalBuyQuote
      totalBuyUsd
      totalSellQuote
      totalSellUsd
    }
  }
`
export const cancelOrderMutation = gql`
  mutation cancelOrder($id: ID!) {
    cancelOrder(id: $id) {
      id
    }
  }
`

export const getPortfolioQuery = gql`
  query portfolio($input: SearchPortfolioInput!) {
    portfolio(input: $input) {
      userAddress
      token
      totalBaseAmount
      totalQuoteAmount
      avgPriceUsd
      avgPriceQuote
      totalBuyBaseAmount
      totalBuyQuoteAmount
      totalSellBaseAmount
      totalSellQuoteAmount
    }
  }
`

export const createOrderByWeb3Mutation = gql`
  mutation saveWeb3Order($input: SaveWeb3OrderInput!) {
    saveWeb3Order(input: $input) {
      id
    }
  }
`

export const getLastTransactions = gql`
  query lastTransactions($input: SearchLastTransactionInput!) {
    lastTransactions(input: $input) {
      data {
        token
        symbol
        chainId
        transactionType
        priceUsd
        baseAmount
        quoteAmount
        txid
        marketCap
        timestamp
      }
      fromTimestamp
    }
  }
`
