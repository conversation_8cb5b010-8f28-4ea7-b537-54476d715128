import { gql, TypedDocumentNode } from '@apollo/client'
import { GetAllAssetHistoryResponse, GetAssetChartResponse } from '@/types/responses.ts'
import { GetAllAssetHistoryInput } from '@/types/requests.ts'
import { QueryGetAssetChartArgs } from '@/@generated/gql/graphql-core.ts'

export const getAssetHistory = gql`
  query GetAssetHistory($input: AssetHistoryInput!) {
    getAssetHistory(input: $input) {
      timestamp
      balance
    }
  }
`
export const getWalletBalance = gql`
  query GetWalletBalance($input: WalletBalanceInput!) {
    getWalletBalance(input: $input) {
      balanceChangeUsd
      usdBalance
      walletAddress
      walletType
      userId
      nativeTokenSymbol
      nativeTokenBalance
    }
  }
`

export const getAllAssetHistory: TypedDocumentNode<GetAllAssetHistoryResponse, GetAllAssetHistoryInput> = gql`
  query GetAllAssetHistory($collapseInput: AssetHistoryInput!, $expandInput: AssetHistoryInput!) {
    collapse: getAssetHistory(input: $collapseInput) {
      timestamp
      balance
    }
    expand: getAssetHistory(input: $expandInput) {
      timestamp
      balance
    }
  }
`

export const getAssetChart: TypedDocumentNode<GetAssetChartResponse, QueryGetAssetChartArgs> = gql`
  query GetAssetChartPreview($input: WalletAssetChartInput!) {
    getAssetChartPreview(input: $input) {
      t
      v
    }
    getAssetChart(input: $input) {
      t
      v
    }
  }
`
