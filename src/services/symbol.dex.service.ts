import { gql } from '@apollo/client'

export const GET_FAVORITE_SYMBOLS = gql`
  query GetFavoriteSymbols {
    getFavoriteSymbols {
      list {
        symbol
        maxLeverage
        marketCap
        volume
        changPxPercent
        currentPrice
      }
    }
  }
`

export const GET_SYMBOL_LIST = gql`
  query GetSymbolList($input: SymbolListRequest!) {
    getSymbolList(input: $input) {
      list {
        symbol
        maxLeverage
        marketCap
        volume
        changPxPercent
        currentPrice
      }
    }
  }
`

export const UPSERT_FAVORITE_SYMBOL = gql`
  mutation UpsertFavoriteSymbol($input: UpsertFavoriteSymbolRequest!) {
    upsertFavoriteSymbol(input: $input) {
      error
      status
    }
  }
`

export const GET_CATEGORY_LIST = gql`
  query GetCategory {
    getCategory {
      categories
    }
  }
`
export const getUserSymbolPreference = gql`
  query GetUserSymbolPreference($input: UserSymbolPreferenceRequest!) {
    getUserSymbolPreference(input: $input) {
      isFavorite
      leverage
      isCross
    }
  }
`

export const mutationUserSymbolPreference = gql`
  mutation UpdateUserSymbolPreference($input: UpdateUserSymbolPreferenceRequest!) {
    updateUserSymbolPreference(input: $input) {
      isFavorite
      leverage
      isCross
    }
  }
`

export const SEARCH_SYMBOL_ENDPOINT = gql`
  query SearchSymbol($input: SearchSymbolRequest!) {
    searchSymbol(input: $input) {
      list {
        symbol
        maxLeverage
        marketCap
        volume
        changPxPercent
        currentPrice
      }
    }
  }
`

export const GET_POPULAR_SYMBOLS = gql`
  query GetPopularSymbol($input: PopularSymbolRequest!) {
    getPopularSymbol(input: $input) {
      list {
            symbol
            maxLeverage
            marketCap
            volume
            changPxPercent
            openInterest
            currentPrice
        }
    }
  }
`

export const GET_NEW_SYMBOLS = gql`
  query GetNewSymbol {
    getNewSymbol {
      list
    }
  }
`