import { gql, TypedDocumentNode } from '@apollo/client'
import {
  GetAiAnalyzedInfoResponse,
  GetAllCategoriesInput,
  GetAllCategoriesResponse,
  GetCategoryStatisticsResponse,
  GetTokensByCategoryResponse,
  GetTokenSnipersResponse,
} from '@/types/responses.ts'
import { GeneralInput, GetCategoryStatisticsInput, GetTokensByCategoryInput } from '@/types/requests.ts'
import { Query, QueryGetTradingTransactionsArgs, TokenSniperInput } from '@/@generated/gql/graphql-core.ts'
import { AiAnalyzedInfoInput } from '@/@generated/gql/graphql-core.ts'

export const getTrendingTokens = gql`
  query GetTokenTrending($input: TokenTrendingInput!) {
    getTokenTrending(input: $input) {
      limit
      page
      data {
        chainId
        token
        name
        image
        symbol
        firstPrice
        price
        price1mAgo
        price5mAgo
        price1hAgo
        price1mChange
        price5mChange
        price6hChange
        price1hChange
        price24hChange
        price24hAgo
        txs1h
        txs1m
        txs24h
        txs5m
        txs6h
        buyTxs1m
        buyTxs5m
        buyTxs1h
        buyTxs6h
        buyTxs24h
        sellTxs1m
        sellTxs5m
        sellTxs1h
        sellTxs6h
        sellTxs24h
        volume1m
        volume5m
        volume1h
        volume6h
        volume24h
        marketcap
        liquidity
        initLiquidity
        internalMarketProgress
        isFavorite
        numberOfHolder
        dexes
        createdTime
        tweetId
        twitterNameChangeCount
        twitterUrl
        website
        trendingScore1h
        trendingScore1m
        trendingScore24h
        trendingScore5m
        trendingScore6h
        ohlc {
          ts
          token
          open
          usdVolume
        }
      }
    }
  }
`

export const getNewTokens = gql`
  query GetNewToken($input: TokenFilterInput!) {
    getNewToken(input: $input) {
      page
      limit
      data {
        chainId
        token
        price
        price1mChange
        price5mChange
        price1hChange
        price6hChange
        price24hChange
        buyTxs1m
        buyTxs5m
        buyTxs1h
        buyTxs6h
        buyTxs24h
        sellTxs1m
        sellTxs5m
        sellTxs1h
        sellTxs6h
        sellTxs24h
        volume1m
        volume5m
        volume1h
        volume6h
        volume24h
        marketcap
        liquidity
        internalMarketProgress
        isFavorite
        numberOfHolder
        dexes
        createdTime
        name
        image
        symbol
        ohlc {
          ts
          open
          usdVolume
        }
      }
    }
  }
`

export const getTokens = gql`
  query Tokens($input: TokenInput!) {
    tokens(token: $input) {
      data {
        address
        chainId
        symbol
        name
        decimals
        logo
        totalSupply
        tags
        mintDisable
        isBlacklisted
        isHoneypot
        burnRatio
        burnStatus
        top10HolderRate
        ratTraderAmountRate
      }
      page
      limit
    }
  }
`

export const getTokenPriceAndMarketCap = gql`
  query GetTokenDetail($input: TokenDetailInput!) {
    getTokenDetail(token: $input) {
      price
      marketCap
    }
  }
`

export const getTokenDetail = gql`
  query GetTokenDetail($input: TokenDetailInput!) {
    getTokenDetail(token: $input) {
      address
      chainId
      symbol
      name
      decimals
      totalSupply
      tags
      mintDisable
      isBlacklisted
      isHoneypot
      isFavorite
      isHotToken
      burnRatio
      burnStatus
      top10HolderRate
      ratTraderAmountRate
      volume24h
      marketCap
      buyTxs
      sellTxs
      price
      price24hChange
      createdTime
      dexes
      internalMarketProgress
      liquidity
      turnoverRate24h
      circulatingSupply
      openPrice
      athPrice
      atlPrice
      marketCap
      holders
      topTrending
      numberProTrader
      turnoverRate24h
      circulatingSupply
      openPrice
      athPrice
      atlPrice
      marketCap
      holders
      topTrending
      info {
        logoUrl
        bannerUrl
        dexScreenerBoosts
        socials {
          url
          type
        }
        websites {
          url
          label
        }
      }
      pools {
        address
        chainId
        token0
        token1
        factory
        decimal0
        decimal1
        baseToken
        label
        liquidity
        token0Info {
          address
          chainId
          symbol
          name
          decimals
          totalSupply
          tags
          mintDisable
          isBlacklisted
          isHoneypot
          burnRatio
          burnStatus
          top10HolderRate
          ratTraderAmountRate
        }
        token1Info {
          address
          chainId
          symbol
          name
          decimals
          totalSupply
          tags
          mintDisable
          isBlacklisted
          isHoneypot
          burnRatio
          burnStatus
          top10HolderRate
          ratTraderAmountRate
        }
        createdTime
        reserve0
        reserve1
        tags
      }
      pairs {
        address
        chainId
        baseToken
        createdAt
        quoteToken
        baseTokenLiquidity
        quoteLiquidity
        usdLiquidity
        quoteSymbol
        baseSymbol
        usdLiquidity
        dex
        quoteTokenPrice
      }
      health {
        noBlackListWhiteListFunction
        notMint
        burnt
        top10
      }
      portrait {
        walletActive1h
        launchedOnPump
        lowLiquidity
        devAction
        abandoned
        ageSinceCreation
        advertisesOnDex
        updatedSocialOnDex
        officialTwitter
        officialTelegram
        officialWebsite
        explorer
        tweetId
        twitterNameChangeCount
      }
      totalTransactions {
        numberOfPurchases5m
        numberOfPurchases1h
        numberOfPurchases6h
        numberOfPurchases24h
        numberOfSales5m
        numberOfSales1h
        numberOfSales6h
        numberOfSales24h
      }
      totalAmount {
        totalBuyAmount5m
        totalBuyAmount1h
        totalBuyAmount6h
        totalBuyAmount24h
        totalSellAmount5m
        totalSellAmount1h
        totalSellAmount6h
        totalSellAmount24h
      }
      numberUniqueAddresses {
        numberOfBuyAddress5m
        numberOfBuyAddress1h
        numberOfBuyAddress6h
        numberOfBuyAddress24h
        numberOfSellAddress5m
        numberOfSellAddress1h
        numberOfSellAddress6h
        numberOfSellAddress24h
      }
    }
  }
`

export const getMemeTokens = gql`
  query GetMemeToken($input: MemeInput!) {
    getMemeToken(input: $input) {
      page
      limit
      data {
        chainId
        token
        price1mChange
        price5mChange
        price1hChange
        price6hChange
        price24hChange
        buyTxs1m
        buyTxs5m
        buyTxs1h
        buyTxs6h
        buyTxs24h
        sellTxs1m
        sellTxs5m
        sellTxs1h
        sellTxs6h
        sellTxs24h
        volume1m
        volume5m
        volume1h
        volume6h
        volume24h
        marketcap
        liquidity
        internalMarketProgress
        isFavorite
        numberOfHolder
        dexes
        createdTime
        name
        image
        symbol
        marketCap5mChangeUsd
        top10Holder
        txBySniperPct
        devHold
        tweetId
        twitterNameChangeCount
        twitterUrl
        website
        sameSourceWallet
        smartMoneyPct
        devLaunched
        insider
        ohlc {
          ts
          open
          usdVolume
        }
      }
    }
  }
`

export const getFavoriteTokens = gql`
  query GetFavoriteToken($input: TokenFilterInput!) {
    getFavoriteToken(input: $input) {
      page
      limit
      data {
        chainId
        token
        price
        price1mChange
        price5mChange
        price1hChange
        price6hChange
        price24hChange
        buyTxs1m
        buyTxs5m
        buyTxs1h
        buyTxs6h
        buyTxs24h
        sellTxs1m
        sellTxs5m
        sellTxs1h
        sellTxs6h
        sellTxs24h
        volume1m
        volume5m
        volume1h
        volume6h
        volume24h
        marketcap
        liquidity
        internalMarketProgress
        isFavorite
        numberOfHolder
        dexes
        createdTime
        name
        image
        symbol
        isFavorite
        ohlc {
          ts
          open
          usdVolume
        }
      }
    }
  }
`

export const addTokenToFavorite = gql`
  mutation AddToFavorite($token: String!) {
    addToFavorite(tokens: $token)
  }
`

export const removeTokenFromFavorite = gql`
  mutation RemoveTokenFavorite($token: String!) {
    removeTokenFavorite(tokens: $token)
  }
`

export const searchTokens = gql`
  query SearchToken($input: String!) {
    searchToken(input: $input) {
      chainId
      token
      price
      price24hChange
      txs24h
      volume24h
      marketcap
      liquidity
      numberOfHolder
      isFavorite
      dexes
      createdTime
      name
      image
      symbol
    }
  }
`

export const getPortfolio = gql`
  query getPortfolio($input: SearchPortfolioInput!) {
    getPortfolio(input: $input) {
      page
      limit
      data {
        userAddress
        token
        symbol
        decimals
        totalBaseAmount
        avgPriceUsd
        totalBuyBaseAmount
        totalSellBaseAmount
        avgMarketCap
        totalUsdValue
        totalBuyQty
        totalBuyUsd
        totalSellQty
        totalSellUsd
        totalTradedQty
        chainId
        updatedAt
        realizedPnL
        logoUrl
        maxHoldingQty
        price
        price24hChange
      }
      totalHoldingTokens
    }
  }
`

export const getOHLC = gql`
  query GetOHLC($input: OHLCInput!) {
    getOHLC(input: $input) {
      open
      ts
      usdVolume
    }
  }
`

export const getPopularTokens = gql`
  query GetPopularTokens {
    getPopularTokens {
      token
      chainId
      symbol
      name
      logoUrl
      hot
    }
  }
`

export const getCategories: TypedDocumentNode<GetAllCategoriesResponse, GetAllCategoriesInput> = gql`
  query GetAllCategories($input: AllCategoriesInput!) {
    getAllCategories(input: $input) {
      data {
        categoryId
        marketCap
        volume24h
        price24hChange
        priceUpCount
        priceDownCount
        name
        topGainers {
          logoUrl
          name
          symbol
          price24hChange
        }
        top1TokenSymbol
        top1TokenAddress
        volume1hHistory
        price1hChangeHistory
      }
      limit
      page
    }
  }
`

export const getCategoryStatistics: TypedDocumentNode<GetCategoryStatisticsResponse, GetCategoryStatisticsInput> = gql`
  query GetCategoryStatistic($input: CategoryStatisticInput!) {
    getCategoryStatistic(input: $input) {
      marketCap
      volume24h
      price24hChange
      priceUpCount
      priceDownCount
    }
  }
`

export const getTokensByCategory: TypedDocumentNode<GetTokensByCategoryResponse, GetTokensByCategoryInput> = gql`
  query TokensByCategory($input: TokensByCategoryInput!) {
    tokensByCategory(input: $input) {
      page
      limit
      data {
        address
        marketCap
        volume24h
        price24hChange
        chainId
        name
        logoUrl
        price
        symbol
      }
    }
  }
`

export const getTradeHistory = gql`
  query GetTradeHistory($input: TokenTradeHistoryInput!) {
    getTradeHistory(input: $input) {
      page
      limit
      data {
        timestamp
        type
        tradeValue
        price
        quantity
        wallet
        maxHoldingQty
        balance
        marketCap
      }
    }
  }
`

export const getSmartMoneys = gql`
  query GetSmartMoneyActions($filter: SmartMoneyActionFilterInput!) {
    getSmartMoneyActions(filter: $filter) {
      actions {
        timestamp
        address
        baseAmount
        token {
          address
          name
          totalSupply
          info {
            logoUrl
          }
        }
        txType
        usdAmount
      }
    }
  }
`

export const getSmartMoneyTradeHistories = gql`
  query getSmartMoneyTradeHistories($req: SmartMoneyTradeHistoryReq!) {
    getSmartMoneyTradeHistories(req: $req) {
      address
      timestamp
      type
      amount
      usdAmount
      usdPrice
      token {
        logo
        address
        totalSupply
      }
    }
  }
`

export const getAIAnalyzedInfo = gql`
  query GetAiAnalyzedInfo($input: AiAnalyzedInfoInput!) {
    getAiAnalyzedInfo(input: $input) {
      chainId
      address
      themeNarrativeAnalytic
      socialWebsiteAnalytic
      avatarAnalytic
    }
  }
`

export const getTokenOfficialInformation = gql`
  query GetTokenOfficialInformation($address: String!) {
    getTokenOfficialInformation(address: $address) {
      openingDate
      contractAddress
      poolAddress
      addPoolTime
      projectPartyDevAddress
      balanceProjectPartyDev
      devEntrepreneurshipHistory
      projectPartyDevHistory
      socials {
        url
        type
        registrationDate
        numberFollowers
        numberPosts
        averageViewPerPost
        averageCommentsPerPost
        averageLikesPerPost
        averageForwardingPerPost
        nameChanges
      }
    }
  }
`

export const getTokenSnipers: TypedDocumentNode<GetTokenSnipersResponse, GeneralInput<TokenSniperInput>> = gql`
  query GetTokenSniper($input: TokenSniperInput!) {
    getTokenSniper(input: $input) {
      snipers
      totalBought
      currentTotalHolding
      top10Holders
    }
  }
`

export const getTransactions = gql`
  query GetTransactions($input: TransactionInput!) {
    getTransactions(input: $input) {
      fromTimestamp
      data {
        timestamp
        chainId
        txHash
        logIndex
        baseToken
        quoteToken
        pair
        type
        maker
        baseAmount
        quoteAmount
        price
        usdAmount
        usdPrice
        liquidity
        holderPct
        isInsider
        isNativeWallet
      }
    }
  }
`

export const getTrending24hTokens = gql`
  query GetTokenTrending($input: TokenTrendingInput!) {
    getTokenTrending(input: $input) {
      data {
        image
        symbol
        createdTime
        token
        marketcap
        price
        price24hChange
        isFavorite
        chainId
      }
    }
  }
`

export const getAiAnalyzedInfo: TypedDocumentNode<GetAiAnalyzedInfoResponse, GeneralInput<AiAnalyzedInfoInput>> = gql`
  query GetAiAnalyzedInfo($input: AiAnalyzedInfoInput!) {
    getAiAnalyzedInfo(input: $input) {
      address
      avatarAnalytic
      chainId
      socialWebsiteAnalytic
      themeNarrativeAnalytic
    }
  }
`

export const getFollowedHolder = gql`
  query GetFollowedHolder($input: HolderInput!) {
    getFollowedHolder(input: $input) {
      page
      limit
      data {
        address
        token
        tokenAccount
        balance
        buys
        sells
        rawBalance
        createdAt
        updatedAt
        chainId
        avgPriceUsd
        avgMarketCap
        symbol
        totalUsdValue
        totalBuyQty
        totalBuyUsd
        totalSellQty
        totalSellUsd
        totalTradedQty
        maxHoldingQty
        realizedPnL
        nativeBalance
        realizedProfit
        unrealizedProfit
        totalProfit
        numberTransaction
        sourceOfFunding
        totalSupply
        decimal
      }
    }
  }
`

export const getHolder = gql`
  query GetHolder($input: HolderInput!) {
    getHolder(input: $input) {
      page
      limit
      data {
        address
        token
        tokenAccount
        balance
        buys
        sells
        rawBalance
        createdAt
        updatedAt
        chainId
        avgPriceUsd
        avgMarketCap
        symbol
        totalUsdValue
        totalBuyQty
        totalBuyUsd
        totalSellQty
        totalSellUsd
        totalTradedQty
        maxHoldingQty
        realizedPnL
        nativeBalance
        realizedProfit
        unrealizedProfit
        totalProfit
        numberTransaction
        sourceOfFunding
        totalSupply
        decimal
      }
    }
  }
`

export const getFollowedTransactions = gql`
  query GetFollowedTransactions($input: TransactionInput!) {
    getFollowedTransactions(input: $input) {
      fromTimestamp
      data {
        timestamp
        chainId
        txHash
        logIndex
        baseToken
        quoteToken
        pair
        type
        maker
        baseAmount
        quoteAmount
        price
        usdAmount
        usdPrice
        liquidity
        holderPct
        isInsider
        isNativeWallet
      }
    }
  }
`

export const getTradingTransactions: TypedDocumentNode<
  Pick<Query, 'getTradingTransactions'>,
  QueryGetTradingTransactionsArgs
> = gql`
  query GetTradingTransactions($input: TradingTransactionInput!) {
    getTradingTransactions(input: $input) {
      data {
        timestamp
        chainId
        txHash
        logIndex
        baseToken
        quoteToken
        pair
        type
        maker
        baseAmount
        quoteAmount
        price
        usdAmount
        usdPrice
        liquidity
        totalSupply
        decimals
        isHugeValue
        isWhale
        isDev
        isFreshWallet
        isInsider
        isNativeWallet
        isNewActivity
        holderPct
        isPoolContract
        isKOL
        isSmartMoney
        isTopTrader
        tx24h
      }
    }
  }
`

export const getPoolTransactions = gql`
  query GetPoolTransactions($input: PoolTransactionInput!) {
    getPoolTransactions(input: $input) {
      data {
        timestamp
        chainId
        txHash
        logIndex
        baseToken
        quoteToken
        pair
        type
        maker
        baseAmount
        quoteAmount
        price
        usdAmount
        usdPrice
      }
      numberOfPools
      fromTimestamp
      liquidity
    }
  }
`

export const getWalletStatistics = gql`
  query GetWalletStatistic($input: WalletStatisticInput!) {
    getWalletStatistic(input: $input) {
      totalBuyTxs
      totalSellTxs
      totalUsdBuyAmount
      totalUsdSellAmount
      maxHolding
      currentHolding
      holdingDuration
    }
  }
`
