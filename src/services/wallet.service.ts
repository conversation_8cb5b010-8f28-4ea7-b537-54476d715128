import {gql} from '@apollo/client';

export const getWithdrawFee = gql`
  query getWithdrawFee($input: WithdrawInput!) {
    getWithdrawFee(input: $input) {
      network
      fee
      unit
    }
  }
`;

export const getWithdrawHistory = gql`
  query getWithdrawHistory($input: SearchWithdrawHistoryInput!) {
    getWithdrawHistory(input: $input) {
      createdAt
      updatedAt
      id
      userId
      fromAddress
      toAddress
      token
      chainId
      decimals
      amount
      status
      network
      txid
      errorCode
      errorMessage
      meta
    }
  }
`;

export const getWalletWhitelist = gql`
  query getWalletWhitelist {
    getWalletWhitelist {
      id
      userId
      whitelist {
        chainId
        walletAddress
      }
      createdAt
      updatedAt
    }
  }
`;

export const withdraw = gql`
  mutation withdraw($input: WithdrawInput!) {
    withdraw(input: $input) {
      createdAt
      updatedAt
      id
      userId
      fromAddress
      toAddress
      token
      chainId
      decimals
      amount
      status
      network
      txid
      errorCode
      errorMessage
      meta
    }
  }
`;
export const followWallet = gql`
  mutation followWallet($input: SmartMoneyFollowInput!){
    followWallet(input: $input)
  }
`

export const unFollowWallet = gql`
  mutation unFollowWallet($input: SmartMoneyFollowInput!){
    unFollowWallet(input: $input)
  }
`

