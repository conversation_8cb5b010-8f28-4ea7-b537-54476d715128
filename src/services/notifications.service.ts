import { gql, TypedDocumentNode } from '@apollo/client'
import { GeneralResponse } from '@/types/responses.ts'
import { ListNotificationInput, Notification, RegisterDeviceTokenInput } from '@/@generated/gql/graphql-notification.ts'
import { GeneralInput } from '@/types/requests.ts'

export const getNotificationsList: TypedDocumentNode<
  GeneralResponse<'ListNotification', Notification[]>,
  GeneralInput<ListNotificationInput>
> = gql`
  query ListNotification($input: ListNotificationInput!) {
    ListNotification(input: $input) {
      id
      avatar
      title
      description
      type
      metadata
      read
    }
  }
`

export const registerTokenDevice: TypedDocumentNode<any, GeneralInput<RegisterDeviceTokenInput>> = gql`
  mutation RegisterDeviceToken($input: RegisterDeviceTokenInput!) {
    registerDeviceToken(input: $input)
  }
`

export const getUnreadNotificationCount: TypedDocumentNode<GeneralResponse<'getUnreadNotificationCount', number>> = gql`
  query GetUnreadNotificationCount {
    getUnreadNotificationCount
  }
`
