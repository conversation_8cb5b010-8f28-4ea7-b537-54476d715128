(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[9488],{53310:e=>{e.exports={en:["Re"],tr:["Tekrar"]}},85119:e=>{e.exports={en:["Dark"],tr:["Koyu"]}},96870:e=>{e.exports={en:["Light"],tr:["Aydınlık"]}},85886:e=>{e.exports={en:["d"],tr:["g"]}},44634:e=>{e.exports={en:["h"],tr:["s"]}},5977:e=>{e.exports={en:["m"],tr:["a"]}},21492:e=>{e.exports={en:["s"]}},97559:e=>{e.exports={en:["{title} copy"],tr:["{title} kopyala"]}},38691:e=>{e.exports={en:["D"],tr:["G"]}},77995:e=>{e.exports={en:["M"],tr:["A"]}},93934:e=>{e.exports={en:["R"],tr:["A"]}},82901:e=>{e.exports={en:["T"],tr:["Z"]}},7408:e=>{e.exports={en:["W"],tr:["H"]}},38048:e=>{e.exports={en:["h"],tr:["s"]}},68430:e=>{e.exports={en:["m"],tr:["d"]}},68823:e=>{e.exports={en:["s"]}},2696:e=>{e.exports={en:["C"],tr:["K"]}},43253:e=>{e.exports={en:["H"],tr:["Y"]}},61372:e=>{e.exports={en:["HL2"]}},55096:e=>{e.exports={en:["HLC3"]}},94174:e=>{e.exports={en:["OHLC4"]}},89923:e=>{e.exports={en:["L"],tr:["D"]}},46728:e=>{e.exports={en:["O"],tr:["A"]}},32856:e=>{e.exports=Object.create(null),e.exports["%D_input"]={en:["%D"]},e.exports["%K_input"]={en:["%K"]},e.exports["%R_input"]={en:["%R"]},e.exports["+DI_input"]={en:["+DI"]},e.exports["-0.236 Level Color_input"]={en:["-0.236 Level Color"],tr:["-0.236 Seviye Rengi"]},e.exports["-0.382 Level Color_input"]={en:["-0.382 Level Color"],tr:["-0.382 Seviye Rengi"]},e.exports["-0.618 Level Color_input"]={en:["-0.618 Level Color"],tr:["-0.618 Seviye Rengi"]},e.exports["-0.65 Level Color_input"]={en:["-0.65 Level Color"],tr:["-0.65 Seviye Rengi"]},e.exports["-DI_input"]={en:["-DI"]},e.exports["0 Level Color_input"]={en:["0 Level Color"],tr:["0 Seviye Rengi"]},e.exports["0.236 Level Color_input"]={en:["0.236 Level Color"],tr:["0.236 Seviye Rengi"]},e.exports["0.382 Level Color_input"]={en:["0.382 Level Color"],tr:["0.382 Seviye Rengi"]},e.exports["0.5 Level Color_input"]={en:["0.5 Level Color"],tr:["0.5 Seviye Rengi"]},e.exports["0.618 Level Color_input"]={en:["0.618 Level Color"],tr:["0.618 Seviye Rengi"]},e.exports["0.65 Level Color_input"]={en:["0.65 Level Color"],tr:["0.65 Seviye Rengi"]},e.exports["0.786 Level Color_input"]={en:["0.786 Level Color"],tr:["0.786 Seviye Rengi"]},e.exports["1 Level Color_input"]={en:["1 Level Color"],tr:["1 Seviye Rengi"]},e.exports["1.272 Level Color_input"]={en:["1.272 Level Color"],tr:["1.272 Seviye Rengi"]},e.exports["1.414 Level Color_input"]={en:["1.414 Level Color"],tr:["1.414 Seviye Rengi"]},e.exports["1.618 Level Color_input"]={en:["1.618 Level Color"],tr:["1.618 Seviye Rengi"]},e.exports["1.65 Level Color_input"]={en:["1.65 Level Color"],tr:["1.65 Seviye Rengi"]},e.exports["1st Period_input"]={en:["1st Period"],tr:["1. Period"]},e.exports["2.618 Level Color_input"]={en:["2.618 Level Color"],tr:["2.618 Seviye Rengi"]},e.exports["2.65 Level Color_input"]={en:["2.65 Level Color"],tr:["2.65 Seviye Rengi"]},e.exports["2nd Period_input"]={en:["2nd Period"],tr:["2. Period"]},e.exports["3.618 Level Color_input"]={en:["3.618 Level Color"],tr:["3.618 Seviye Rengi"]},
e.exports["3.65 Level Color_input"]={en:["3.65 Level Color"],tr:["3.65 Seviye Rengi"]},e.exports["3rd Period_input"]={en:["3rd Period"],tr:["3. Period"]},e.exports["4.236 Level Color_input"]={en:["4.236 Level Color"],tr:["4.236 Seviye Rengi"]},e.exports["4th Period_input"]={en:["4th Period"],tr:["4. Period"]},e.exports["5th Period_input"]={en:["5th Period"],tr:["5. Period"]},e.exports["6th Period_input"]={en:["6th Period"],tr:["6. Period"]},e.exports.ADR_B_input={en:["ADR_B"]},e.exports["ADX Smoothing_input"]={en:["ADX Smoothing"],tr:["ADX Düzleştirilmiş"]},e.exports["ADX smoothing_input"]={en:["ADX smoothing"],tr:["ADX düzleştirilmiş"]},e.exports.ADX_input={en:["ADX"]},e.exports["ATR Mult_input"]={en:["ATR Mult"]},e.exports["ATR length_input"]={en:["ATR length"],tr:["ATR uzunluğu"]},e.exports["ATR({atrValue})_input"]={en:["ATR({atrValue})"]},e.exports.ATR_input={en:["ATR"]},e.exports["Accumulation/Distribution_input"]={en:["Accumulation/Distribution"],tr:["Birikim/Dağıtım"]},e.exports["All items_combobox_input"]={en:["All items"],tr:["Tüm kalemler"]},e.exports.All_input={en:["All"],tr:["Hepsi"]},e.exports["Anchor Period_input"]={en:["Anchor Period"],tr:["Çapa Dönemi"]},e.exports["Another symbol_input"]={en:["Another symbol"],tr:["Başka sembol"]},e.exports["Aroon Down_input"]={en:["Aroon Down"],tr:["Aroon Alt"]},e.exports["Aroon Up_input"]={en:["Aroon Up"],tr:["Aroon Yükseliş"]},e.exports.Average_input={en:["Average"],tr:["Ortalama"]},e.exports["Averaging Periods_input"]={en:["Averaging Periods"],tr:["Ortalama Dönemler"]},e.exports.Back_input={en:["Back"],tr:["Geri"]},e.exports["Bands style_input"]={en:["Bands style"],tr:["Bant stili"]},e.exports.Bar_input={en:["Bar"],tr:["Çubuk"]},e.exports["Base Line Periods_input"]={en:["Base Line Periods"],tr:["Temel Çizgi Periyotları"]},e.exports["Base Line_input"]={en:["Base Line"],tr:["Temel Çizgi"]},e.exports.Basis_input={en:["Basis"],tr:["Temel"]},e.exports["Bollinger Bands %B_input"]={en:["Bollinger Bands %B"],tr:["Bollinger Bantları %B"]},e.exports["Bollinger Bands Width_input"]={en:["Bollinger Bands Width"],tr:["Bollinger Bantları Genişliği"]},e.exports.Borders_input={en:["Borders"],tr:["Kenar"]},e.exports["Box size assignment method_input"]={en:["Box size assignment method"],tr:["Kutu boyutu atama yöntemi"]},e.exports["Box size_input"]={en:["Box size"],tr:["Kutu Büyüklüğü"]},e.exports.CCI_input={en:["CCI"]},e.exports.CHOP_input={en:["CHOP"]},e.exports.Cancel_input={en:["Cancel"],tr:["İptal"]},e.exports.Candles_input={en:["Candles"],tr:["Mum"]},e.exports.Centered_input={en:["Centered"],tr:["Ortalanmış"]},e.exports.Century_input={en:["Century"],tr:["Yüzyıl"]},e.exports["Chaikin Oscillator_input"]={en:["Chaikin Oscillator"],tr:["Chaikin Osilatörü"]},e.exports["Chande MO_input"]={en:["Chande MO"]},e.exports.Close_input={en:["Close"],tr:["Kapat"]},e.exports["Color 0_input"]={en:["Color 0"],tr:["Renk 0"]},e.exports["Color 1_input"]={en:["Color 1"],tr:["Renk 1"]},e.exports["Color 2_input"]={en:["Color 2"],tr:["Renk 2"]},e.exports["Color 3_input"]={en:["Color 3"],tr:["Renk 3"]},
e.exports["Color 4_input"]={en:["Color 4"],tr:["Renk 4"]},e.exports["Color 5_input"]={en:["Color 5"],tr:["Renk 5"]},e.exports["Color 6_input"]={en:["Color 6"],tr:["Renk 6"]},e.exports["Color 7_input"]={en:["Color 7"],tr:["Renk 7"]},e.exports["Color 8_input"]={en:["Color 8"],tr:["Renk 8"]},e.exports["Color bars based on previous close_input"]={en:["Color bars based on previous close"],tr:["Önceki kapanışa göre renk çubukları"]},e.exports["Color based on previous close_input"]={en:["Color based on previous close"],tr:["Önceki kapanışa göre çubuk rengi"]},e.exports["Conversion Line Periods_input"]={en:["Conversion Line Periods"],tr:["Dönüş Çizgisi Periyodu"]},e.exports["Conversion Line_input"]={en:["Conversion Line"],tr:["Dönüş Çizgisi"]},e.exports.Correlation_input={en:["Correlation"],tr:["Korelasyon"]},e.exports.Count_input={en:["Count"],tr:["Sayım"]},e.exports.Crosses_input={en:["Crosses"],tr:["Kesişmeler"]},e.exports.Custom_input={en:["Custom"],tr:["Özel"]},e.exports.DEMA_input={en:["DEMA"],tr:["İÜHO"]},e.exports["DI Length_input"]={en:["DI Length"],tr:["YG Uzunluğu"]},e.exports.DPO_input={en:["DPO"],tr:["DFO"]},e.exports.D_input={en:["D"]},e.exports.Day_input={en:["Day"],tr:["Gün"]},e.exports["Days Per Year_input"]={en:["Days Per Year"],tr:["Yıla Göre Günler"]},e.exports.Decade_input={en:["Decade"],tr:["10 yıl"]},e.exports.Delta_input={en:["Delta"]},e.exports.Depth_input={en:["Depth"],tr:["Derinlik"]},e.exports["Detrended Price Oscillator_input"]={en:["Detrended Price Oscillator"],tr:["Karşılaştırılamayan Fiyat Osilatörü"]},e.exports["Developing Poc_input"]={en:["Developing Poc"],tr:["Gelişen Yts"]},e.exports["Deviation (%)_input"]={en:["Deviation (%)"],tr:["Sapma (%)"]},e.exports.Deviation_input={en:["Deviation"],tr:["Sapma"]},e.exports.Divisor_input={en:["Divisor"],tr:["Bölen"]},e.exports["Down Volume_input"]={en:["Down Volume"],tr:["Düşen Hacim"]},e.exports["Down bars_input"]={en:["Down bars"],tr:["Aşağı çubuklar"]},e.exports["Down color_input"]={en:["Down color"],tr:["Aşağı renk"]},e.exports["Down fractals_input"]={en:["Down fractals"],tr:["Aşağı fraktallar"]},e.exports.EOM_input={en:["EOM"]},e.exports["Each (pre-market, market, post-market)_input"]={en:["Each (pre-market, market, post-market)"],tr:["Her biri (piyasa öncesi, piyasa, piyasa sonrası)"]},e.exports["Elder's Force Index_input"]={en:["Elder's Force Index"],tr:["Elder Kuvvet Endeksi"]},e.exports["Equality Line_input"]={en:["Equality Line"],tr:["Eşitlik Çizgisi"]},e.exports.Exponential_input={en:["Exponential"],tr:["Üstel"]},e.exports["Extend POC Right_input"]={en:["Extend POC Right"],tr:["YTS Sağa Genişlet"]},e.exports["Extend Right_input"]={en:["Extend Right"],tr:["Sağa Uzat"]},e.exports["Extend VAH Right_input"]={en:["Extend VAH Right"],tr:["VAH'ı Sağa Uzat"]},e.exports["Extend VAL Right_input"]={en:["Extend VAL Right"],tr:["VAL'ı Sağa Uzat"]},e.exports["Extend to last bar_input"]={en:["Extend to last bar"],tr:["Son çubuğa kadar uzat"]},e.exports.Falling_input={en:["Falling"],tr:["Düşüş"]},e.exports["Fast Length_input"]={en:["Fast Length"],
tr:["Hızlı Uzunluk"]},e.exports["Fast length_input"]={en:["Fast length"],tr:["Hızlı uzunluk"]},e.exports.Fill_input={en:["Fill"],tr:["Dolgu"]},e.exports.Fisher_input={en:["Fisher"]},e.exports.Growing_input={en:["Growing"],tr:["Büyüyen"]},e.exports["HLC bars_input"]={en:["HLC bars"],tr:["HLC barları"]},e.exports.HV_input={en:["HV"]},e.exports["Histogram Box_input"]={en:["Histogram Box"],tr:["Histogram Kutusu"]},e.exports.Histogram_input={en:["Histogram"]},e.exports["Hull MA_input"]={en:["Hull MA"],tr:["Hull HO"]},e.exports.Increment_input={en:["Increment"],tr:["Artış"]},e.exports.Indicator_input={en:["Indicator"],tr:["Gösterge"]},e.exports["Instrument 1_input"]={en:["Instrument 1"],tr:["Enstrüman 1"]},e.exports["Instrument 2_input"]={en:["Instrument 2"],tr:["Enstrüman 2"]},e.exports["Investor EMA 1 length_input"]={en:["Investor EMA 1 length"],tr:["Yatırımcı EMA 1 uzunluğu"]},e.exports["Investor EMA 2 length_input"]={en:["Investor EMA 2 length"],tr:["Yatırımcı EMA 2 uzunluğu"]},e.exports["Investor EMA 3 length_input"]={en:["Investor EMA 3 length"],tr:["Yatırımcı EMA 3 uzunluğu"]},e.exports["Investor EMA 4 length_input"]={en:["Investor EMA 4 length"],tr:["Yatırımcı EMA 4 uzunluğu"]},e.exports["Investor EMA 5 length_input"]={en:["Investor EMA 5 length"],tr:["Yatırımcı EMA 5 uzunluğu"]},e.exports["Investor EMA 6 length_input"]={en:["Investor EMA 6 length"],tr:["Yatırımcı EMA 6 uzunluğu"]},e.exports["Jaw Length_input"]={en:["Jaw Length"],tr:["Çene Uzunluğu"]},e.exports["Jaw Offset_input"]={en:["Jaw Offset"],tr:["Çene Aralığı"]},e.exports.Jaw_input={en:["Jaw"],tr:["Çene"]},e.exports.KST_input={en:["KST"]},e.exports.K_input={en:["K"]},e.exports["Labels Position_input"]={en:["Labels Position"],tr:["Etiketlerin Pozisyonu"]},e.exports["Labels on price scale_input"]={en:["Labels on price scale"],tr:["Fiyat ölçeğindeki etiketler"]},e.exports["Lagging Span Periods_input"]={en:["Lagging Span Periods"],tr:["Geciken Açıklık Dönemleri"]},e.exports["Lagging Span_input"]={en:["Lagging Span"],tr:["Gecikme Aralığı"]},e.exports["Leading Shift Periods_input"]={en:["Leading Shift Periods"],tr:["Öncü Vardiya Dönemleri"]},e.exports["Leading Span A_input"]={en:["Leading Span A"],tr:["Öndeki Açıklık A"]},e.exports["Leading Span B_input"]={en:["Leading Span B"],tr:["Öndeki Açıklık B"]},e.exports["Leading Span Periods_input"]={en:["Leading Span Periods"],tr:["Öncü Açıklık Dönemleri"]},e.exports["Length EMA_input"]={en:["Length EMA"],tr:["ÜHO Uzunluğu"]},e.exports["Length MA_input"]={en:["Length MA"],tr:["HO Uzunluğu"]},e.exports.Length1_input={en:["Length1"],tr:["Uzunluk1"]},e.exports.Length2_input={en:["Length2"],tr:["Uzunluk2"]},e.exports.Length3_input={en:["Length3"],tr:["Uzunluk3"]},e.exports.Length_input={en:["Length"],tr:["Uzunluk"]},e.exports.Level_input={en:["Level"],tr:["Seviye"]},e.exports["Levels Format_input"]={en:["Levels Format"],tr:["Seviyelerin Formatı"]},e.exports.Limit_input={en:["Limit"]},e.exports.Line_input={en:["Line"],tr:["Çizgi"]},e.exports["Lips Length_input"]={en:["Lips Length"],tr:["Dudak Uzunluğu"]},
e.exports["Lips Offset_input"]={en:["Lips Offset"],tr:["Dudak Aralığı"]},e.exports.Lips_input={en:["Lips"],tr:["Dudaklar"]},e.exports["Long Length_input"]={en:["Long Length"],tr:["Uzun Uzunluğu"]},e.exports["Long RoC Length_input"]={en:["Long RoC Length"],tr:["Uzun KVE Uzunluğu"]},e.exports["Long length_input"]={en:["Long length"],tr:["Uzun uznuluğu"]},e.exports["Long period_input"]={en:["Long period"],tr:["Uzun süre"]},e.exports.Long_input={en:["Long"],tr:["Uzun"]},e.exports["Lower Band_input"]={en:["Lower Band"],tr:["Alt Bant"]},e.exports["Lower Deviation_input"]={en:["Lower Deviation"],tr:["Alt Sapma"]},e.exports["Lower Percentage_input"]={en:["Lower Percentage"],tr:["Düşük Yüzdelik"]},e.exports.LowerLimit_input={en:["LowerLimit"],tr:["AltLimit"]},e.exports.Lower_input={en:["Lower"],tr:["Alt"]},e.exports["MA Length_input"]={en:["MA Length"],tr:["HO Uzunluğu"]},e.exports.MACD_input={en:["MACD"]},e.exports.MA_input={en:["MA"],tr:["HO"]},e.exports.MF_input={en:["MF"],tr:["HF"]},e.exports.MM_month_input={en:["MM"],tr:["AA"]},e.exports.MOM_input={en:["MOM"]},e.exports["Main chart symbol_input"]={en:["Main chart symbol"],tr:["Temel grafik sembolü"]},e.exports["Market Closed Percentage_input"]={en:["Market Closed Percentage"],tr:["Piyasa Kapalı Yüzdesi"]},e.exports["Market only_input"]={en:["Market only"],tr:["Sadece piyasa"]},e.exports["Max value_input"]={en:["Max value"],tr:["Max değer"]},e.exports.Median_input={en:["Median"],tr:["Medyan"]},e.exports.Method_input={en:["Method"],tr:["Yöntem"]},e.exports.Middle_input={en:["Middle"],tr:["Orta"]},e.exports.Minimize_input={en:["Minimize"],tr:["Küçült"]},e.exports.Month_input={en:["Month"],tr:["Ay"]},e.exports.Move_input={en:["Move"],tr:["Hareket"]},e.exports["Multi timeframe_input"]={en:["Multi timeframe"],tr:["Çoklu zaman dilimi"]},e.exports.Multiplier_input={en:["Multiplier"],tr:["Çarpan"]},e.exports.NV_input={en:["NV"]},e.exports["Nothing selected_combobox_input"]={en:["Nothing selected"],tr:["Seçili birşey yok"]},e.exports["Number Of Rows_input"]={en:["Number Of Rows"],tr:["Satır Sayısı"]},e.exports["Number of line_input"]={en:["Number of line"],tr:["Satır sayısı"]},e.exports.OSC_input={en:["OSC"]},e.exports.Offset_input={en:["Offset"],tr:["Uzantı"]},e.exports.OnBalanceVolume_input={en:["OnBalanceVolume"],tr:["DengeİşlemHacmi"]},e.exports["One step back building_input"]={en:["One step back building"],tr:["Bir adım geri oluşum"]},e.exports.Oscillator_input={en:["Oscillator"],tr:["Osilatör"]},e.exports.Overbought_input={en:["Overbought"],tr:["Fazla alınmış"]},e.exports.Oversold_input={en:["Oversold"],tr:["Fazla satılmış"]},e.exports.POC_input={en:["POC"],tr:["YTS(Yoğun Takas Seviyesi)"]},e.exports.PVT_input={en:["PVT"]},e.exports.P_input={en:["P"]},e.exports.ParabolicSAR_input={en:["ParabolicSAR"]},e.exports.Percent_input={en:["Percent"],tr:["Yüzde"]},e.exports["Percentage LTP({percentageLTPValue}%)_input"]={en:["Percentage LTP({percentageLTPValue}%)"],tr:["LTP Yüzdesi({percentageLTPValue}%)"]},e.exports["Percentage LTP_input"]={en:["Percentage LTP"],tr:["Yüzde LTP"]},
e.exports.Percentage_input={en:["Percentage"],tr:["Yüzde"]},e.exports.Period_input={en:["Period"],tr:["Periyot"]},e.exports.Periods_input={en:["Periods"],tr:["Periyotlar"]},e.exports["Phantom bars_input"]={en:["Phantom bars"],tr:["Hayalet çubuklar"]},e.exports.Placement_input={en:["Placement"],tr:["Yerleştirme"]},e.exports.Plot_input={en:["Plot"],tr:["Çizim"]},e.exports["Plots Background_input"]={en:["Plots Background"],tr:["Parselin Arkaplanı"]},e.exports["Post-market only_input"]={en:["Post-market only"],tr:["Sadece Piyasa sonrası"]},e.exports["Pre-market only_input"]={en:["Pre-market only"],tr:["Sadece piyasa öncesi"]},e.exports["Price source_input"]={en:["Price source"],tr:["Fiyat kaynağı"]},e.exports.Price_input={en:["Price"],tr:["Fiyat"]},e.exports["Projection down bars_input"]={en:["Projection down bars"],tr:["Oluşumdaki aşağı çubuklar"]},e.exports["Projection down color_input"]={en:["Projection down color"],tr:["Oluşumdaki düşüş rengi"]},e.exports["Projection up bars_input"]={en:["Projection up bars"],tr:["Oluşumdaki yukarı çubuklar"]},e.exports["Projection up color_input"]={en:["Projection up color"],tr:["Oluşumdaki yükseliş rengi"]},e.exports.Q_input={en:["Q"]},e.exports["ROC Length_input"]={en:["ROC Length"],tr:["RoC Uzunluğu"]},e.exports.ROCLen1_input={en:["ROCLen1"],tr:["ROCUzun1"]},e.exports.ROCLen2_input={en:["ROCLen2"],tr:["ROCUzun2"]},e.exports.ROCLen3_input={en:["ROCLen3"],tr:["ROCUzun3"]},e.exports.ROCLen4_input={en:["ROCLen4"],tr:["ROCUzun4"]},e.exports.ROC_input={en:["ROC"]},e.exports["RSI Length_input"]={en:["RSI Length"],tr:["RSI Uzunluğu"]},e.exports["RSI Source_input"]={en:["RSI Source"],tr:["RSI Kaynağı"]},e.exports.RSI_input={en:["RSI"]},e.exports.RVGI_input={en:["RVGI"]},e.exports.RVI_input={en:["RVI"]},e.exports.Range_input={en:["Range"],tr:["Aralık"]},e.exports["Rate of Change Lookback_input"]={en:["Rate of Change Lookback"],tr:["Geçmişe Dönük Değişim Oranı"]},e.exports["Reversal amount_input"]={en:["Reversal amount"],tr:["Ters dönüş miktarı"]},e.exports["Rolling Period_input"]={en:["Rolling Period"],tr:["Değişen Dönem"]},e.exports["Row Size_input"]={en:["Row Size"],tr:["Satır sayısı"]},e.exports["Rows Layout_input"]={en:["Rows Layout"],tr:["Satır Düzeni"]},e.exports.SMALen1_input={en:["SMALen1"],tr:["BHOUzun1"]},e.exports.SMALen2_input={en:["SMALen2"],tr:["BHOUzun2"]},e.exports.SMALen3_input={en:["SMALen3"],tr:["BHOUzun3"]},e.exports.SMALen4_input={en:["SMALen4"],tr:["BHOUzun4"]},e.exports["SMI Ergodic Oscillator_input"]={en:["SMI Ergodic Oscillator"],tr:["SMI Ergodic Osilatörü"]},e.exports.SMI_input={en:["SMI"]},e.exports.Session_input={en:["Session"],tr:["Seans"]},e.exports.Sessions_input={en:["Sessions"],tr:["Oturumlar"]},e.exports.Shapes_input={en:["Shapes"],tr:["Şekiller"]},e.exports["Short Length_input"]={en:["Short Length"],tr:["Kısa Uzunluk"]},e.exports["Short RoC Length_input"]={en:["Short RoC Length"],tr:["Kısa RoC Uzunluğu"]},e.exports["Short length_input"]={en:["Short length"],tr:["Kısa uzunluk"]},e.exports["Short period_input"]={en:["Short period"],tr:["Kısa periyot"]},
e.exports.Short_input={en:["Short"],tr:["Kısa"]},e.exports["Show real prices on price scale (instead of Heikin-Ashi price)_input"]={en:["Show real prices on price scale (instead of Heikin-Ashi price)"],tr:["Fiyat ölçeğinde gerçek fiyatları göster (Heikin-Ashi fiyatı yerine)"]},e.exports.SigLen_input={en:["SigLen"],tr:["SinUzun"]},e.exports.Sig_input={en:["Sig"],tr:["Sin"]},e.exports.Sigma_input={en:["Sigma"]},e.exports["Signal Length_input"]={en:["Signal Length"],tr:["Sinyal Uzunluğu"]},e.exports["Signal line period_input"]={en:["Signal line period"],tr:["Sinyal çizgisi periyodu"]},e.exports["Signal smoothing_input"]={en:["Signal smoothing"],tr:["Sinyal belirginleştirme"]},e.exports.Signal_input={en:["Signal"],tr:["Sinyal"]},e.exports["Simple ma(oscillator)_input"]={en:["Simple ma(oscillator)"],tr:["Basit ort(osilatör)"]},e.exports["Simple ma(signal line)_input"]={en:["Simple ma(signal line)"],tr:["Basit ort(sinyal çizgisi)"]},e.exports.Simple_input={en:["Simple"],tr:["Basit"]},e.exports["Slow Length_input"]={en:["Slow Length"],tr:["Yavaş Uzunluk"]},e.exports["Slow length_input"]={en:["Slow length"],tr:["Yavaş uzunluk"]},e.exports["Smoothing Length_input"]={en:["Smoothing Length"],tr:["Yumuşatma Uzunluğu"]},e.exports["Smoothing Line_input"]={en:["Smoothing Line"],tr:["Yumuşatma Çizgisi"]},e.exports.Smoothing_input={en:["Smoothing"],tr:["Belirginleştirme"]},e.exports.Source_input={en:["Source"],tr:["Kaynak"]},e.exports["Standard Errors_input"]={en:["Standard Errors"],tr:["Standart Hatalar"]},e.exports.Start_input={en:["Start"],tr:["Başlat"]},e.exports.StdDev_input={en:["StdDev"],tr:["StdSapma"]},e.exports["Stochastic Length_input"]={en:["Stochastic Length"],tr:["Stokastik Uzunluk"]},e.exports.Style_input={en:["Style"],tr:["Stil"]},e.exports.Symbol_input={en:["Symbol"],tr:["Sembol"]},e.exports.TEMA_input={en:["TEMA"]},e.exports.TRIX_input={en:["TRIX"]},e.exports["Teeth Length_input"]={en:["Teeth Length"],tr:["Diş Uzunluğu"]},e.exports["Teeth Offset_input"]={en:["Teeth Offset"],tr:["Diş Aralığı"]},e.exports.Teeth_input={en:["Teeth"],tr:["Diş"]},e.exports["Ticks Per Row_input"]={en:["Ticks Per Row"],tr:["Satır başına fiyat adımı sayısı"]},e.exports.Timeframe_input={en:["Timeframe"],tr:["Zaman aralığı"]},e.exports.Total_input={en:["Total"],tr:["Toplam"]},e.exports["Trader EMA 1 length_input"]={en:["Trader EMA 1 length"],tr:["İşlem yapan EMA 1 uzunluğu"]},e.exports["Trader EMA 2 length_input"]={en:["Trader EMA 2 length"],tr:["İşlem yapan EMA 2 uzunluğu"]},e.exports["Trader EMA 3 length_input"]={en:["Trader EMA 3 length"],tr:["İşlem yapan EMA 3 uzunluğu"]},e.exports["Trader EMA 4 length_input"]={en:["Trader EMA 4 length"],tr:["İşlemci EMA 4 uzunluğu"]},e.exports["Trader EMA 5 length_input"]={en:["Trader EMA 5 length"],tr:["İşlemci EMA 5 uzunluğu"]},e.exports["Trader EMA 6 length_input"]={en:["Trader EMA 6 length"],tr:["İşlemci EMA 6 uzunluğu"]},e.exports.Traditional_input={en:["Traditional"],tr:["Geleneksel"]},e.exports.Trigger_input={en:["Trigger"],tr:["Tetik"]},e.exports.Type_input={en:["Type"],tr:["Tip"]},
e.exports.UO_input={en:["UO"]},e.exports["Up Volume_input"]={en:["Up Volume"],tr:["Artan Hacim"]},e.exports["Up bars_input"]={en:["Up bars"],tr:["Yukarı çubuklar"]},e.exports["Up color_input"]={en:["Up color"],tr:["Yukarı renk"]},e.exports["Up fractals_input"]={en:["Up fractals"],tr:["Yükselen fraktal"]},e.exports["Up/Down_input"]={en:["Up/Down"],tr:["Yukarı/Aşağı"]},e.exports["UpDown Length_input"]={en:["UpDown Length"],tr:["UpDown Uzunluğu"]},e.exports["Upper Band_input"]={en:["Upper Band"],tr:["Üst Bant"]},e.exports["Upper Deviation_input"]={en:["Upper Deviation"],tr:["Üst Sapma"]},e.exports["Upper Percentage_input"]={en:["Upper Percentage"],tr:["Üst Yüzdelik"]},e.exports.UpperLimit_input={en:["UpperLimit"],tr:["ÜstLimit"]},e.exports.Upper_input={en:["Upper"],tr:["Üst"]},e.exports["Use Lower Deviation_input"]={en:["Use Lower Deviation"],tr:["Alt Sapma Kullan"]},e.exports["Use Upper Deviation_input"]={en:["Use Upper Deviation"],tr:["Üst Sapma Kullan"]},e.exports["VI +_input"]={en:["VI +"]},e.exports["VI -_input"]={en:["VI -"]},e.exports.VWAP_input={en:["VWAP"],tr:["HAOF"]},e.exports.VWMA_input={en:["VWMA"]},e.exports["Value Area Down_input"]={en:["Value Area Down"],tr:["Değer Alanı Aşağı"]},e.exports["Value Area Up_input"]={en:["Value Area Up"],tr:["Değer Alanı Yukarı"]},e.exports["Value Area Volume_input"]={en:["Value Area Volume"],tr:["Değer Alanı Hacmi"]},e.exports["Value Area volume_input"]={en:["Value Area volume"],tr:["Takas Aralığı hacmi"]},e.exports["Value Area_input"]={en:["Value Area"],tr:["Değer Alanı"]},e.exports.Value_input={en:["Value"],tr:["Değer"]},e.exports["Values in status line_input"]={en:["Values in status line"],tr:["Durum satırındaki değerler"]},e.exports.Volume_input={en:["Volume"],tr:["Hacim"]},e.exports["WMA Length_input"]={en:["WMA Length"],tr:["WMA Uzunluğu"]},e.exports["Wait for timeframe closes_input"]={en:["Wait for timeframe closes"],tr:["Zaman aralığının kapanmasını bekleyin"]},e.exports.Week_input={en:["Week"],tr:["Hafta"]},e.exports.Weighted_input={en:["Weighted"],tr:["Ağırlıklı"]},e.exports.Wick_input={en:["Wick"],tr:["Fitil"]},e.exports.Wicks_input={en:["Wicks"],tr:["Fitil"]},e.exports["Wilder's Smoothing_input"]={en:["Wilder's Smoothing"],tr:["Wilder'ın Belirginleştirmesi"]},e.exports["Window Size_input"]={en:["Window Size"],tr:["Pencere Genişliği"]},e.exports.X_input={en:["X"]},e.exports.YY_year_input={en:["YY"]},e.exports.Year_input={en:["Year"],tr:["Yıl"]},e.exports["Zero Line_input"]={en:["Zero Line"],tr:["Sıfır Çizgisi"]},e.exports.Zero_input={en:["Zero"],tr:["Sıfır"]},e.exports.fastLength_input={en:["fastLength"],tr:["hızlıUzunluk"]},e.exports.from_input={en:["from"],tr:["'dan/'den"]},e.exports.increment_input={en:["increment"],tr:["artım"]},e.exports.isCentered_input={en:["isCentered"],tr:["Merkezde mi"]},e.exports.len_input={en:["len"],tr:["uzn"]},e.exports.length14_input={en:["length14"],tr:["uzunluk14"]},e.exports.length28_input={en:["length28"],tr:["uzunluk28"]},e.exports.length7_input={en:["length7"],tr:["uzunluk7"]},e.exports.lengthRSI_input={en:["lengthRSI"],
tr:["uzunlukRSI"]},e.exports.lengthStoch_input={en:["lengthStoch"],tr:["uzunlukStok"]},e.exports.length_input={en:["length"],tr:["uzunluk"]},e.exports.long_input={en:["long"],tr:["uzun"]},e.exports.longlen_input={en:["longlen"]},e.exports.maximum_input={en:["maximum"],tr:["maksimum"]},e.exports.mult_input={en:["mult"],tr:["çarpan"]},e.exports.p_input={en:["p"]},e.exports.q_input={en:["q"]},e.exports.resolution_input={en:["resolution"],tr:["çözünürlük"]},e.exports.roclen1_input={en:["roclen1"],tr:["rocuznlk1"]},e.exports.roclen2_input={en:["roclen2"],tr:["rocuznlk2"]},e.exports.roclen3_input={en:["roclen3"],tr:["rocuznlk3"]},e.exports.roclen4_input={en:["roclen4"],tr:["rocuznlk4"]},e.exports.short_input={en:["short"],tr:["kısa"]},e.exports.shortlen_input={en:["shortlen"]},e.exports["show MA_input"]={en:["show MA"],tr:["HO göster"]},e.exports.siglen_input={en:["siglen"]},e.exports.signalLength_input={en:["signalLength"],tr:["sinyalUzunluk"]},e.exports.slowLength_input={en:["slowLength"],tr:["yavaşUzunluk"]},e.exports.smalen1_input={en:["smalen1"],tr:["bhoUznlk1"]},e.exports.smalen2_input={en:["smalen2"],tr:["bhoUznlk2"]},e.exports.smalen3_input={en:["smalen3"],tr:["bhoUznlk3"]},e.exports.smalen4_input={en:["smalen4"],tr:["bhoUznlk4"]},e.exports.smoothD_input={en:["smoothD"]},e.exports.smoothK_input={en:["smoothK"]},e.exports.start_input={en:["start"],tr:["başlangıç"]},e.exports.sym_input={en:["sym"],tr:["smbl"]},e.exports.to_input={en:["to"],tr:["'ya/'ye"]},e.exports.useTrueRange_input={en:["useTrueRange"],tr:["TrueRangeKullan"]},e.exports.x_input={en:["x"]},e.exports["yay Color 0_input"]={en:["yay Color 0"],tr:["yay Renk 0"]},e.exports["yay Color 1_input"]={en:["yay Color 1"],tr:["yay Renk 1"]},e.exports["{number} item_combobox_input"]={en:["{number} item","{number} items"],tr:["{number} araç","{number} araç"]}},50873:e=>{e.exports={en:["ATR({atrValue})"]}},73425:e=>{e.exports={en:["Percentage LTP({percentageLTPValue}%)"],tr:["LTP Yüzdesi({percentageLTPValue}%)"]}},40566:e=>{e.exports={en:["Traditional"],tr:["Geleneksel"]}},35359:e=>{e.exports={en:["Post"],tr:["Sonrası"]}},93866:e=>{e.exports={en:["Pre"],tr:["Öncesi"]}},75163:e=>{e.exports={en:["Invert scale"],tr:["Ölçeği Ters Çevir"]}},35210:e=>{e.exports={en:["Indexed to 100"],tr:["100'e Endeksli"]}},31340:e=>{e.exports={en:["Logarithmic"],tr:["Logaritmik"]}},19405:e=>{e.exports={en:["No overlapping labels"],tr:["Örtüşen Etiketleri Gösterme"]}},34954:e=>{e.exports={en:["Percent"],tr:["Yüzde"]}},55300:e=>{e.exports={en:["Regular"],tr:["Normal"]}},8029:e=>{e.exports={en:["ETH"]}},34647:e=>{e.exports={en:["Electronic trading hours"],tr:["Elektronik işlem saatleri"]}},36862:e=>{e.exports={en:["Extended trading hours"],tr:["Genişletilmiş işlem saatleri"]}},7807:e=>{e.exports={en:["POST"],tr:["SONRA"]}},46273:e=>{e.exports={en:["PRE"],tr:["ÖNCE"]}},50434:e=>{e.exports={en:["Postmarket"],tr:["Piyasa-sonrası"]}},59330:e=>{e.exports={en:["Premarket"],tr:["Piyasa-öncesi"]}},35342:e=>{e.exports={en:["RTH"]}},84246:e=>{e.exports={en:["Regular trading hours"],
tr:["Normal işlem saatleri"]}},13132:e=>{e.exports={en:["May"]}},83477:e=>{e.exports=Object.create(null),e.exports["*All Candlestick Patterns*_study"]={en:["*All Candlestick Patterns*"],tr:["*Tüm Mum Çubuğu Formasyonları*"]},e.exports["24-hour Volume_study"]={en:["24-hour Volume"],tr:["24 saatlik Hacim"]},e.exports["52 Week High/Low_study"]={en:["52 Week High/Low"],tr:["52 Hafta En Yüksek/Düşük"]},e.exports.ASI_study={en:["ASI"]},e.exports["Abandoned Baby - Bearish_study"]={en:["Abandoned Baby - Bearish"],tr:["Terk Edilmiş Bebek - Ayı"]},e.exports["Abandoned Baby - Bullish_study"]={en:["Abandoned Baby - Bullish"],tr:["Terk Edilmiş Bebek - Boğa"]},e.exports["Accelerator Oscillator_study"]={en:["Accelerator Oscillator"],tr:["Hızlandırıcı Osilatörü"]},e.exports["Accounts payable_study"]={en:["Accounts payable"],tr:["Ödenebilir hesaplar"]},e.exports["Accounts receivable - trade, net_study"]={en:["Accounts receivable - trade, net"],tr:["Aktarılabilecek hesaplar - işlem, net"]},e.exports["Accounts receivables, gross_study"]={en:["Accounts receivables, gross"],tr:["Alacak hesapları, brüt"]},e.exports.Accruals_study={en:["Accruals"],tr:["Tahakkuklar"]},e.exports["Accrued payroll_study"]={en:["Accrued payroll"],tr:["Tahakkuk eden maaş bordrosu"]},e.exports["Accumulated depreciation, total_study"]={en:["Accumulated depreciation, total"],tr:["Birikmiş amortisman, toplam"]},e.exports["Accumulation/Distribution_study"]={en:["Accumulation/Distribution"],tr:["Birikim/Dağıtım"]},e.exports["Accumulative Swing Index_study"]={en:["Accumulative Swing Index"],tr:["Biriktirici Sallanma Endeksi"]},e.exports["Additional paid-in capital/Capital surplus_study"]={en:["Additional paid-in capital/Capital surplus"],tr:["Ek ödenmiş sermaye/Sermaye fazlası"]},e.exports["Advance Decline Line_study"]={en:["Advance Decline Line"],tr:["Yükseliş Düşüş Çizgisi"]},e.exports["Advance Decline Ratio_study"]={en:["Advance Decline Ratio"],tr:["Yükseliş Düşüş Oranı"]},e.exports["Advance/Decline Ratio (Bars)_study"]={en:["Advance/Decline Ratio (Bars)"],tr:["Yükseliş/Düşüş Oranı (Çubuk grafiği)"]},e.exports["Advance/Decline_study"]={en:["Advance/Decline"],tr:["Yükseliş/Düşüş"]},e.exports["After tax other income/expense_study"]={en:["After tax other income/expense"],tr:["Vergi sonrası diğer gelir/gider"]},e.exports["All Chart Patterns_study"]={en:["All Chart Patterns"],tr:["Tüm Grafik Desenleri"]},e.exports["Altman Z-score_study"]={en:["Altman Z-score"],tr:["Altman Z-skoru"]},e.exports["Amortization of deferred charges_study"]={en:["Amortization of deferred charges"],tr:["Ertelenmiş bedellerin amortisman edilmesi"]},e.exports["Amortization of intangibles_study"]={en:["Amortization of intangibles"],tr:["Maddi Olmayan Varlıkların Amortisman edilmesi"]},e.exports.Amortization_study={en:["Amortization"],tr:["Amortisman"]},e.exports["Anchored Volume Profile_study"]={en:["Anchored Volume Profile"],tr:["Sabitlenmiş Hacim Profili"]},e.exports["Arnaud Legoux Moving Average_study"]={en:["Arnaud Legoux Moving Average"],tr:["Arnaud Legoux Hareketli Ortalama"]},
e.exports.Aroon_study={en:["Aroon"]},e.exports["Asset turnover_study"]={en:["Asset turnover"],tr:["Varlık cirosu"]},e.exports["Auto Anchored Volume Profile_study"]={en:["Auto Anchored Volume Profile"],tr:["Otomatik Sabitlenmiş Hacim Profili"]},e.exports["Auto Fib Extension_study"]={en:["Auto Fib Extension"],tr:["Otomatik Fib Uzatma"]},e.exports["Auto Fib Retracement_study"]={en:["Auto Fib Retracement"],tr:["Oto Fib Düzeltmesi"]},e.exports["Auto Pitchfork_study"]={en:["Auto Pitchfork"],tr:["Otomatik Pitchfork"]},e.exports["Average Daily Range_study"]={en:["Average Daily Range"],tr:["Ortalama Günlük Aralık"]},e.exports["Average Day Range_study"]={en:["Average Day Range"],tr:["Ortalama Gün Aralığı"]},e.exports["Average Directional Index_study"]={en:["Average Directional Index"],tr:["Ortalama Yönsel Endeks(ADX)"]},e.exports["Average Price_study"]={en:["Average Price"],tr:["Ortalama Fiyat"]},e.exports["Average True Range_study"]={en:["Average True Range"],tr:["Ortalama Gerçek Aralık"]},e.exports["Average basic shares outstanding_study"]={en:["Average basic shares outstanding"],tr:["Tedavüldeki ortalama temel hisse"]},e.exports["Awesome Oscillator_study"]={en:["Awesome Oscillator"],tr:["Müthiş Osilatör"]},e.exports["Bad debt / Doubtful accounts_study"]={en:["Bad debt / Doubtful accounts"],tr:["Kötü borç / Şüpheli hesaplar"]},e.exports["Balance of Power_study"]={en:["Balance of Power"],tr:["Güç Dengesi"]},e.exports["BarUpDn Strategy_study"]={en:["BarUpDn Strategy"],tr:["BarUpDn Stratejisi"]},e.exports["Basic EPS_study"]={en:["Basic EPS"],tr:["Temel EPS"]},e.exports["Basic earnings per share (Basic EPS)_study"]={en:["Basic earnings per share (Basic EPS)"],tr:["Hisse başına temel kazanç (Temel EPS)"]},e.exports["Bearish Flag Chart Pattern_study"]={en:["Bearish Flag Chart Pattern"],tr:["Ayı Bayrağı Grafik Formasyonu"]},e.exports["Bearish Pennant Chart Pattern_study"]={en:["Bearish Pennant Chart Pattern"],tr:["Flama Ayı Grafik Deseni"]},e.exports["Beneish M-score_study"]={en:["Beneish M-score"],tr:["Beneish M-skoru"]},e.exports["Bollinger Bands %B_study"]={en:["Bollinger Bands %B"],tr:["Bollinger Bantları %B"]},e.exports["Bollinger Bands Strategy directed_study"]={en:["Bollinger Bands Strategy directed"],tr:["Bollinger Bantlar Stratejisi doğrultusunda"]},e.exports["Bollinger Bands Strategy_study"]={en:["Bollinger Bands Strategy"],tr:["Bollinger Bantları Strateji"]},e.exports["Bollinger Bands Width_study"]={en:["Bollinger Bands Width"],tr:["Bollinger Bantları Genişliği"]},e.exports["Bollinger Bands_study"]={en:["Bollinger Bands"],tr:["Bollinger Bantları"]},e.exports["Book value per share_study"]={en:["Book value per share"],tr:["Hisse başına defter değeri"]},e.exports["Bull Bear Power_study"]={en:["Bull Bear Power"],tr:["Boğa Ayı Gücü"]},e.exports["Bullish Flag Chart Pattern_study"]={en:["Bullish Flag Chart Pattern"],tr:["Boğa Bayrağı Grafik Deseni"]},e.exports["Bullish Pennant Chart Pattern_study"]={en:["Bullish Pennant Chart Pattern"],tr:["Flama Boğa Grafik Formasyonu"]},e.exports["Buyback yield %_study"]={
en:["Buyback yield %"],tr:["Geri alım getirisi %"]},e.exports["COGS to revenue ratio_study"]={en:["COGS to revenue ratio"],tr:["COGS/gelir oranı"]},e.exports.CRSI_study={en:["CRSI"]},e.exports["Capital and operating lease obligations_study"]={en:["Capital and operating lease obligations"],tr:["Sermaye ve faaliyet kiralaması yükümlülükleri"]},e.exports["Capital expenditures - fixed assets_study"]={en:["Capital expenditures - fixed assets"],tr:["Sermaye harcamaları - sabit kıymetler"]},e.exports["Capital expenditures - other assets_study"]={en:["Capital expenditures - other assets"],tr:["Sermaye harcamaları - diğer varlıklar"]},e.exports["Capital expenditures_study"]={en:["Capital expenditures"],tr:["Sermaye harcamaları"]},e.exports["Capitalized lease obligations_study"]={en:["Capitalized lease obligations"],tr:["Aktifleştirilen kiralama yükümlülükleri"]},e.exports["Cash & equivalents_study"]={en:["Cash & equivalents"],tr:["Nakit benzerleri"]},e.exports["Cash and short term investments_study"]={en:["Cash and short term investments"],tr:["Nakit ve kısa vadeli yatırımlar"]},e.exports["Cash conversion cycle_study"]={en:["Cash conversion cycle"],tr:["Nakit dönüştürme döngüsü"]},e.exports["Cash from financing activities_study"]={en:["Cash from financing activities"],tr:["Finansman Faaliyetlerinden Gelen Nakit"]},e.exports["Cash from investing activities_study"]={en:["Cash from investing activities"],tr:["Yatırım Faaliyetlerinden Gelen Nakit"]},e.exports["Cash from operating activities_study"]={en:["Cash from operating activities"],tr:["İşletme Faaliyetlerinden Gelen Nakit"]},e.exports["Cash to debt ratio_study"]={en:["Cash to debt ratio"],tr:["Nakit/borç oranı"]},e.exports["Chaikin Money Flow_study"]={en:["Chaikin Money Flow"],tr:["Chaikin Para Akışı"]},e.exports["Chaikin Oscillator_study"]={en:["Chaikin Oscillator"],tr:["Chaikin Osilatörü"]},e.exports["Chaikin Volatility_study"]={en:["Chaikin Volatility"],tr:["Chaikin Volatilitesi"]},e.exports["Chande Kroll Stop_study"]={en:["Chande Kroll Stop"],tr:["Chande Kroll Durdurması"]},e.exports["Chande Momentum Oscillator_study"]={en:["Chande Momentum Oscillator"],tr:["Chande Momentum Osilatörü"]},e.exports["Change in accounts payable_study"]={en:["Change in accounts payable"],tr:["Borç hesaplarında değişiklik"]},e.exports["Change in accounts receivable_study"]={en:["Change in accounts receivable"],tr:["Alacak hesaplarındaki değişiklik"]},e.exports["Change in accrued expenses_study"]={en:["Change in accrued expenses"],tr:["Tahakkuk eden giderlerdeki değişiklik"]},e.exports["Change in inventories_study"]={en:["Change in inventories"],tr:["Envanterde değişiklik"]},e.exports["Change in other assets/liabilities_study"]={en:["Change in other assets/liabilities"],tr:["Diğer varlıklarda/yükümlülüklerde değişiklik"]},e.exports["Change in taxes payable_study"]={en:["Change in taxes payable"],tr:["Ödenecek vergilerdeki değişiklik"]},e.exports["Changes in working capital_study"]={en:["Changes in working capital"],tr:["İşletme Sermayesinde Değişiklikler"]},
e.exports.ChannelBreakOutStrategy_study={en:["ChannelBreakOutStrategy"],tr:["KanaldanÇıkışStratejisi"]},e.exports["Chop Zone_study"]={en:["Chop Zone"],tr:["Kaşe Alanı"]},e.exports["Choppiness Index_study"]={en:["Choppiness Index"],tr:["Dalgalılık Endeksi"]},e.exports["Commodity Channel Index_study"]={en:["Commodity Channel Index"],tr:["Emtia Kanal Endeksi(CCI)"]},e.exports["Common dividends paid_study"]={en:["Common dividends paid"],tr:["Ödenen ortak temettüler"]},e.exports["Common equity, total_study"]={en:["Common equity, total"],tr:["Ortak sermaye, toplam"]},e.exports["Common stock par/Carrying value_study"]={en:["Common stock par/Carrying value"],tr:["Ortak hisse senedi /Taşıma değeri"]},e.exports.Compare_study={en:["Compare"],tr:["Kıyasla"]},e.exports["Conditional Expressions_study"]={en:["Conditional Expressions"],tr:["Koşullu İfadeler"]},e.exports["Connors RSI_study"]={en:["Connors RSI"]},e.exports.ConnorsRSI_study={en:["ConnorsRSI"]},e.exports["Consecutive Up/Down Strategy_study"]={en:["Consecutive Up/Down Strategy"],tr:["Ardışık Yükseliş/Düşüş Stratejisi"]},e.exports["Coppock Curve_study"]={en:["Coppock Curve"],tr:["Coppock Eğrisi"]},e.exports["Correlation - Log_study"]={en:["Correlation - Log"],tr:["Korelasyon - Log"]},e.exports["Correlation Coefficient_study"]={en:["Correlation Coefficient"],tr:["Korelasyon Katsayısı"]},e.exports["Cost of goods sold_study"]={en:["Cost of goods sold"],tr:["Satılan malların maliyeti"]},e.exports["Cost of goods_study"]={en:["Cost of goods"],tr:["Malların maliyeti"]},e.exports["Cumulative Volume Index_study"]={en:["Cumulative Volume Index"],tr:["Kümülatif Hacim Endeksi"]},e.exports["Cup and Handle Chart Pattern_study"]={en:["Cup and Handle Chart Pattern"],tr:["Kupa ve Kulp Grafik Deseni"]},e.exports["Current portion of LT debt and capital leases_study"]={en:["Current portion of LT debt and capital leases"],tr:["LT borcunun cari kısmı ve sermaye kiralaması"]},e.exports["Current ratio_study"]={en:["Current ratio"],tr:["Şimdiki oran"]},e.exports.DMI_study={en:["DMI"]},e.exports["Dark Cloud Cover - Bearish_study"]={en:["Dark Cloud Cover - Bearish"],tr:["Kara Bulut Örtüsü - Ayı"]},e.exports["Days inventory_study"]={en:["Days inventory"],tr:["Günlük envanter"]},e.exports["Days payable_study"]={en:["Days payable"],tr:["Ödenebilecek gün sayısı"]},e.exports["Days sales outstanding_study"]={en:["Days sales outstanding"],tr:["Gün satışları olağanüstü"]},e.exports["Debt to EBITDA ratio_study"]={en:["Debt to EBITDA ratio"],tr:["Borç/FAVÖK oranı"]},e.exports["Debt to assets ratio_study"]={en:["Debt to assets ratio"],tr:["Borç / aktif varlık oranı"]},e.exports["Debt to equity ratio_study"]={en:["Debt to equity ratio"],tr:["Borç/öz sermaye oranı"]},e.exports["Debt to revenue ratio_study"]={en:["Debt to revenue ratio"],tr:["Borç/gelir oranı"]},e.exports["Deferred income, current_study"]={en:["Deferred income, current"],tr:["Ertelenmiş gelir, cari"]},e.exports["Deferred income, non-current_study"]={en:["Deferred income, non-current"],tr:["Ertelenmiş gelir, cari olmayan"]},
e.exports["Deferred tax assets_study"]={en:["Deferred tax assets"],tr:["Ertelenmiş vergi varlıkları"]},e.exports["Deferred tax liabilities_study"]={en:["Deferred tax liabilities"],tr:["Ertelenmiş vergi yükümlülükleri"]},e.exports["Deferred taxes (cash flow)_study"]={en:["Deferred taxes (cash flow)"],tr:["Ertelenmiş vergiler (nakit akışı)"]},e.exports["Deprecation and amortization_study"]={en:["Deprecation and amortization"],tr:["Değer düşürme ve amortisman"]},e.exports["Depreciation & amortization (cash flow)_study"]={en:["Depreciation & amortization (cash flow)"],tr:["Değer düşürme ve amortisman (nakit akışı)"]},e.exports["Depreciation/depletion_study"]={en:["Depreciation/depletion"],tr:["Amortisman/tükenme"]},e.exports.Depreciation_study={en:["Depreciation"],tr:["Amortisman"]},e.exports["Detrended Price Oscillator_study"]={en:["Detrended Price Oscillator"],tr:["Trend Azaltma Fiyat Osilatörü"]},e.exports["Diluted EPS_study"]={en:["Diluted EPS"],tr:["Seyreltilmiş HBK"]},e.exports["Diluted earnings per share (Diluted EPS)_study"]={en:["Diluted earnings per share (Diluted EPS)"],tr:["Hisse başına seyreltilmiş kazanç (Seyreltilmiş EPS)"]},e.exports["Diluted net income available to common stockholders_study"]={en:["Diluted net income available to common stockholders"],tr:["Ortak hissedarlara sunulan seyreltilmiş net gelir"]},e.exports["Diluted shares outstanding_study"]={en:["Diluted shares outstanding"],tr:["Seyreltilmiş hisseler ödenmemiş"]},e.exports["Dilution adjustment_study"]={en:["Dilution adjustment"],tr:["Seyreltme ayarı"]},e.exports["Directional Movement Index_study"]={en:["Directional Movement Index"],tr:["Yönsel Hareket Endeksi"]},e.exports["Directional Movement_study"]={en:["Directional Movement"],tr:["Yönsel Hareket"]},e.exports["Discontinued operations_study"]={en:["Discontinued operations"],tr:["Durdurulan faaliyetler"]},e.exports["Divergence Indicator_study"]={en:["Divergence Indicator"],tr:["Iraksama Göstergesi"]},e.exports["Dividend payout ratio %_study"]={en:["Dividend payout ratio %"],tr:["Temettü ödeme oranı %"]},e.exports["Dividend yield %_study"]={en:["Dividend yield %"],tr:["Temettü verimi %"]},e.exports["Dividends payable_study"]={en:["Dividends payable"],tr:["Ödenecek borç"]},e.exports["Dividends per share - common stock primary issue_study"]={en:["Dividends per share - common stock primary issue"],tr:["Hisse başına temettü - ortak hisse senedi birincil ihraç"]},e.exports["Doji Star - Bearish_study"]={en:["Doji Star - Bearish"],tr:["Doji Yıldızı - Ayı"]},e.exports["Doji Star - Bullish_study"]={en:["Doji Star - Bullish"],tr:["Doji Yıldızı - Boğa"]},e.exports.Doji_study={en:["Doji"]},e.exports["Donchian Channels_study"]={en:["Donchian Channels"],tr:["Donchian Kanalları"]},e.exports["Double Bottom Chart Pattern_study"]={en:["Double Bottom Chart Pattern"],tr:["Çift Dipli Grafik Deseni"]},e.exports["Double EMA_study"]={en:["Double EMA"],tr:["DEMA"]},e.exports["Double Top Chart Pattern_study"]={en:["Double Top Chart Pattern"],tr:["Çift Tepe Grafik Deseni"]},
e.exports["Downside Tasuki Gap - Bearish_study"]={en:["Downside Tasuki Gap - Bearish"],tr:["Aşağı Yönlü Tasuki Boşluğu - Ayı"]},e.exports["Dragonfly Doji - Bullish_study"]={en:["Dragonfly Doji - Bullish"],tr:["Yusufçuk Doji - Boğa"]},e.exports["EBITDA margin %_study"]={en:["EBITDA margin %"],tr:["FAVÖK marjı %"]},e.exports.EBITDA_study={en:["EBITDA"],tr:["FAVÖK"]},e.exports.EBIT_study={en:["EBIT"],tr:["FVÖK"]},e.exports["EMA Cross_study"]={en:["EMA Cross"],tr:["ÜHO Kesişme"]},e.exports["EPS basic one year growth_study"]={en:["EPS basic one year growth"],tr:["EPS temel bir yıllık büyüme"]},e.exports["EPS diluted one year growth_study"]={en:["EPS diluted one year growth"],tr:["EPS seyreltilmiş bir yıllık büyüme"]},e.exports["EPS estimates_study"]={en:["EPS estimates"],tr:["EPS tahminleri"]},e.exports["Earnings yield_study"]={en:["Earnings yield"],tr:["Kazanç Verimi"]},e.exports["Ease Of Movement_study"]={en:["Ease Of Movement"],tr:["Hareket Kolaylığı"]},e.exports["Ease of Movement_study"]={en:["Ease of Movement"],tr:["Hareket Kolaylığı"]},e.exports["Effective interest rate on debt %_study"]={en:["Effective interest rate on debt %"],tr:["Borç üzerindeki efektif faiz oranı %"]},e.exports["Elder Force Index_study"]={en:["Elder Force Index"],tr:["Elder Kuvvet Endeksi"]},e.exports["Elder's Force Index_study"]={en:["Elder's Force Index"],tr:["Elder Güç Endeksi"]},e.exports["Elders Force Index_study"]={en:["Elders Force Index"],tr:["Yaşlılar Gücü Endeksi"]},e.exports["Elliott Wave Chart Pattern_study"]={en:["Elliott Wave Chart Pattern"],tr:["Elliott Dalga Grafik Formasyonu"]},e.exports["Engulfing - Bearish_study"]={en:["Engulfing - Bearish"],tr:["Yutan - Ayı"]},e.exports["Engulfing - Bullish_study"]={en:["Engulfing - Bullish"],tr:["Yutan - Boğa"]},e.exports["Enterprise value to EBIT ratio_study"]={en:["Enterprise value to EBIT ratio"],tr:["Kurumsal değerin FVÖK oranı"]},e.exports["Enterprise value to EBITDA ratio_study"]={en:["Enterprise value to EBITDA ratio"],tr:["Kurumsal değer / FAVÖK oranı"]},e.exports["Enterprise value to revenue ratio_study"]={en:["Enterprise value to revenue ratio"],tr:["Kurumsal değerin gelire oranı"]},e.exports["Enterprise value_study"]={en:["Enterprise value"],tr:["Kuruluş değeri"]},e.exports.Envelope_study={en:["Envelope"],tr:["Zarf"]},e.exports.Envelopes_study={en:["Envelopes"],tr:["Zarflar"]},e.exports["Equity in earnings_study"]={en:["Equity in earnings"],tr:["Kazançlarda öz sermaye"]},e.exports["Equity to assets ratio_study"]={en:["Equity to assets ratio"],tr:["Öz sermaye/varlık oranı"]},e.exports["Evening Doji Star - Bearish_study"]={en:["Evening Doji Star - Bearish"],tr:["Akşam Doji Yıldızı - Ayı"]},e.exports["Evening Star - Bearish_study"]={en:["Evening Star - Bearish"],tr:["Akşam Yıldızı - Ayı"]},e.exports["Falling Three Methods - Bearish_study"]={en:["Falling Three Methods - Bearish"],tr:["Düşen Üç Yöntem - Ayı"]},e.exports["Falling Wedge Chart Pattern_study"]={en:["Falling Wedge Chart Pattern"],tr:["Düşen Kama Grafik Formasyonu"]},e.exports["Falling Window - Bearish_study"]={
en:["Falling Window - Bearish"],tr:["Düşen Pencere - Ayı"]},e.exports["Financing activities – other sources_study"]={en:["Financing activities – other sources"],tr:["Finansman faaliyetleri – diğer kaynaklar"]},e.exports["Financing activities – other uses_study"]={en:["Financing activities – other uses"],tr:["Finansman faaliyetleri – diğer kullanımlar"]},e.exports["Fisher Transform_study"]={en:["Fisher Transform"],tr:["Fisher Dönüşümü"]},e.exports["Fixed Range Volume Profile_study"]={en:["Fixed Range Volume Profile"],tr:["Sabit Aralıklı Hacim Profili"]},e.exports["Fixed Range_study"]={en:["Fixed Range"],tr:["Sabit Aralık"]},e.exports["Float shares outstanding_study"]={en:["Float shares outstanding"],tr:["Öne çıkan döner sermayeli hisseler"]},e.exports["Free cash flow margin %_study"]={en:["Free cash flow margin %"],tr:["Serbest nakit akışı marjı %"]},e.exports["Free cash flow_study"]={en:["Free cash flow"],tr:["Serbest Nakit Akışı"]},e.exports["Fulmer H factor_study"]={en:["Fulmer H factor"],tr:["Fulmer H faktörü"]},e.exports["Funds from operations_study"]={en:["Funds from operations"],tr:["Faaliyetlerden Sağlanan Fonlar"]},e.exports.Gaps_study={en:["Gaps"],tr:["Boşluklar"]},e.exports["Goodwill to assets ratio_study"]={en:["Goodwill to assets ratio"],tr:["İyi niyet/varlık oranı"]},e.exports["Goodwill, net_study"]={en:["Goodwill, net"],tr:["İyi niyet, net"]},e.exports["Graham's number_study"]={en:["Graham's number"],tr:["Graham'ın numarası"]},e.exports["Gravestone Doji - Bearish_study"]={en:["Gravestone Doji - Bearish"],tr:["Mezar Taşı Doji - Ayı"]},e.exports["Greedy Strategy_study"]={en:["Greedy Strategy"],tr:["Açgözlü Strateji"]},e.exports["Gross margin %_study"]={en:["Gross margin %"],tr:["Brüt kar marjı %"]},e.exports["Gross profit to assets ratio_study"]={en:["Gross profit to assets ratio"],tr:["Brüt kârın varlık oranı"]},e.exports["Gross profit_study"]={en:["Gross profit"],tr:["Brüt Kar"]},e.exports["Gross property/plant/equipment_study"]={en:["Gross property/plant/equipment"],tr:["Brüt mülk/tesis/ekipman"]},e.exports["Guppy Multiple Moving Average_study"]={en:["Guppy Multiple Moving Average"],tr:["Guppy Çoklu Hareketli Ortalama"]},e.exports["Hammer - Bullish_study"]={en:["Hammer - Bullish"],tr:["Çekiç - Boğa"]},e.exports["Hanging Man - Bearish_study"]={en:["Hanging Man - Bearish"],tr:["Asılı Adam - Ayı"]},e.exports["Harami - Bearish_study"]={en:["Harami - Bearish"],tr:["Harami - Ayı"]},e.exports["Harami - Bullish_study"]={en:["Harami - Bullish"],tr:["Harami - Boğa"]},e.exports["Harami Cross - Bearish_study"]={en:["Harami Cross - Bearish"],tr:["Harami Çaprazı - Ayı"]},e.exports["Harami Cross - Bullish_study"]={en:["Harami Cross - Bullish"],tr:["Harami Çaprazı - Boğa"]},e.exports["Head and Shoulders Chart Pattern_study"]={en:["Head and Shoulders Chart Pattern"],tr:["Baş ve Omuz Grafik Deseni"]},e.exports["Historical Volatility_study"]={en:["Historical Volatility"],tr:["Tarihi Volatilite"]},e.exports["Hull Moving Average_study"]={en:["Hull Moving Average"],tr:["Hull Hareketli Ortalaması"]},
e.exports["Ichimoku Cloud_study"]={en:["Ichimoku Cloud"],tr:["Ichimoku Bulutu"]},e.exports.Ichimoku_study={en:["Ichimoku"]},e.exports.Impairments_study={en:["Impairments"],tr:["Bozukluklar"]},e.exports["InSide Bar Strategy_study"]={en:["InSide Bar Strategy"],tr:["Çubuk İçi Stratejisi"]},e.exports["Income Tax Credits_study"]={en:["Income Tax Credits"],tr:["Gelir Vergisi Kredileri"]},e.exports["Income Tax, current - foreign_study"]={en:["Income Tax, current - foreign"],tr:["Gelir Vergisi, cari - yabancı"]},e.exports["Income tax payable_study"]={en:["Income tax payable"],tr:["Ödenecek gelir vergisi"]},e.exports["Income tax, current - domestic_study"]={en:["Income tax, current - domestic"],tr:["Gelir vergisi, cari - yerel"]},e.exports["Income tax, current_study"]={en:["Income tax, current"],tr:["Gelir vergisi, cari"]},e.exports["Income tax, deferred - domestic_study"]={en:["Income tax, deferred - domestic"],tr:["Gelir vergisi, ertelenmiş - yurtiçi"]},e.exports["Income tax, deferred - foreign_study"]={en:["Income tax, deferred - foreign"],tr:["Gelir vergisi, ertelenmiş - yabancı"]},e.exports["Income tax, deferred_study"]={en:["Income tax, deferred"],tr:["Gelir vergisi, ertelenmiş"]},e.exports["Interest capitalized_study"]={en:["Interest capitalized"],tr:["Aktifleştirilmiş faiz"]},e.exports["Interest coverage_study"]={en:["Interest coverage"],tr:["Faiz kapsamı"]},e.exports["Interest expense on debt_study"]={en:["Interest expense on debt"],tr:["Borç faiz gideri"]},e.exports["Interest expense, net of interest capitalized_study"]={en:["Interest expense, net of interest capitalized"],tr:["Faiz gideri, aktifleştirilen faiz net"]},e.exports["Inventories - finished goods_study"]={en:["Inventories - finished goods"],tr:["Stoklar - bitmiş ürünler"]},e.exports["Inventories - progress payments & other_study"]={en:["Inventories - progress payments & other"],tr:["Stoklar - hakedişler ve diğer"]},e.exports["Inventories - raw materials_study"]={en:["Inventories - raw materials"],tr:["Envanter - hammaddeler"]},e.exports["Inventories - work in progress_study"]={en:["Inventories - work in progress"],tr:["Envanter - devam eden çalışma"]},e.exports["Inventory to revenue ratio_study"]={en:["Inventory to revenue ratio"],tr:["Envanter/gelir oranı"]},e.exports["Inventory turnover_study"]={en:["Inventory turnover"],tr:["Envanter/gelir oranı"]},e.exports["Inverted Cup and Handle Chart Pattern_study"]={en:["Inverted Cup and Handle Chart Pattern"],tr:["Ters Kupa ve Kulp Grafik Deseni"]},e.exports["Inverted Hammer - Bullish_study"]={en:["Inverted Hammer - Bullish"],tr:["Ters Çekiç - Boğa"]},e.exports["Inverted Head and Shoulders Chart Pattern_study"]={en:["Inverted Head and Shoulders Chart Pattern"],tr:["Ters Baş ve Omuz Grafik Deseni"]},e.exports["Investing activities – other sources_study"]={en:["Investing activities – other sources"],tr:["Yatırım faaliyetleri – diğer kaynaklar"]},e.exports["Investing activities – other uses_study"]={en:["Investing activities – other uses"],tr:["Yatırım faaliyetleri – diğer kullanımlar"]},
e.exports["Investments in unconsolidated subsidiaries_study"]={en:["Investments in unconsolidated subsidiaries"],tr:["Konsolide edilmeyen bağlı ortaklıklardaki yatırımlar"]},e.exports["Issuance of long term debt_study"]={en:["Issuance of long term debt"],tr:["Uzun vadeli borç ihracı"]},e.exports["Issuance/retirement of debt, net_study"]={en:["Issuance/retirement of debt, net"],tr:["Borç ihracı/ortadan kalkma, net"]},e.exports["Issuance/retirement of long term debt_study"]={en:["Issuance/retirement of long term debt"],tr:["Uzun vadeli borç ihracı/ortadan kalkma"]},e.exports["Issuance/retirement of other debt_study"]={en:["Issuance/retirement of other debt"],tr:["Diğer borcun ihracı/ödenmesi"]},e.exports["Issuance/retirement of short term debt_study"]={en:["Issuance/retirement of short term debt"],tr:["Kısa vadeli borç ihracı/ödenmesi"]},e.exports["Issuance/retirement of stock, net_study"]={en:["Issuance/retirement of stock, net"],tr:["Hisse senedi ihracı/ödenme, net"]},e.exports["KZ index_study"]={en:["KZ index"],tr:["KZ indeksi"]},e.exports["Keltner Channel Strategy_study"]={en:["Keltner Channel Strategy"],tr:["Keltner Kanalları Stratejisi"]},e.exports["Keltner Channels Strategy_study"]={en:["Keltner Channels Strategy"],tr:["Keltner Kanal Stratejisi"]},e.exports["Keltner Channels_study"]={en:["Keltner Channels"],tr:["Keltner Kanalları"]},e.exports["Key stats_study"]={en:["Key stats"],tr:["Temel istatistikler"]},e.exports["Kicking - Bearish_study"]={en:["Kicking - Bearish"],tr:["Tekmeleme - Ayı"]},e.exports["Kicking - Bullish_study"]={en:["Kicking - Bullish"],tr:["Tekmeleme - Boğa"]},e.exports["Klinger Oscillator_study"]={en:["Klinger Oscillator"],tr:["Klinger Osilatörü"]},e.exports["Know Sure Thing_study"]={en:["Know Sure Thing"]},e.exports["Least Squares Moving Average_study"]={en:["Least Squares Moving Average"],tr:["En Küçük Kareler Hareketli Ortalaması"]},e.exports["Legal claim expense_study"]={en:["Legal claim expense"],tr:["Yasal talep gideri"]},e.exports["Linear Regression Channel_study"]={en:["Linear Regression Channel"],tr:["Doğrusal Regresyon Kanalı"]},e.exports["Linear Regression Curve_study"]={en:["Linear Regression Curve"],tr:["Doğrusal Regresyon Eğrisi"]},e.exports["Linear Regression Slope_study"]={en:["Linear Regression Slope"],tr:["Doğrusal Regresyon Eğrisi"]},e.exports["Linear Regression_study"]={en:["Linear Regression"],tr:["Doğrusal Regresyon"]},e.exports["Liquidity ratios_study"]={en:["Liquidity ratios"],tr:["Likidite oranları"]},e.exports["Long Lower Shadow - Bullish_study"]={en:["Long Lower Shadow - Bullish"],tr:["Uzun Alt Gölge - Boğa"]},e.exports["Long Upper Shadow - Bearish_study"]={en:["Long Upper Shadow - Bearish"],tr:["Uzun Üst Gölge - Ayı"]},e.exports["Long term debt excl. lease liabilities_study"]={en:["Long term debt excl. lease liabilities"],tr:["Uzun vadeli borç hariç kira borçları"]},e.exports["Long term debt to total assets ratio_study"]={en:["Long term debt to total assets ratio"],tr:["Uzun vadeli borcun toplam aktiflere oranı"]},
e.exports["Long term debt to total equity ratio_study"]={en:["Long term debt to total equity ratio"],tr:["Uzun vadeli borçların toplam özkaynaklara oranı"]},e.exports["Long term debt_study"]={en:["Long term debt"],tr:["Uzun Vadeli Borç"]},e.exports["Long term investments_study"]={en:["Long term investments"],tr:["Uzun vadeli yatırımlar"]},e.exports["MA Cross_study"]={en:["MA Cross"],tr:["HO Cross"]},e.exports["MA with EMA Cross_study"]={en:["MA with EMA Cross"],tr:["HO ve ÜHO Kesişmesi"]},e.exports["MA/EMA Cross_study"]={en:["MA/EMA Cross"],tr:["HO/ÜHO Kesişmesi"]},e.exports["MACD Strategy_study"]={en:["MACD Strategy"],tr:["MACD Strateji"]},e.exports.MACD_study={en:["MACD"]},e.exports["Majority Rule_study"]={en:["Majority Rule"],tr:["Çoğunluk Kuralı"]},e.exports["Market capitalization_study"]={en:["Market capitalization"],tr:["Piyasa Değeri"]},e.exports["Marubozu Black - Bearish_study"]={en:["Marubozu Black - Bearish"],tr:["Marubozu Siyah - Ayı"]},e.exports["Marubozu White - Bullish_study"]={en:["Marubozu White - Bullish"],tr:["Marubozu Siyah - Ayı"]},e.exports["Mass Index_study"]={en:["Mass Index"],tr:["Kütle Endeksi"]},e.exports["McGinley Dynamic_study"]={en:["McGinley Dynamic"],tr:["McGinley Dinamik"]},e.exports["Median Price_study"]={en:["Median Price"],tr:["Medyan Fiyat"]},e.exports.Median_study={en:["Median"],tr:["Medyan"]},e.exports["Minority interest_study"]={en:["Minority interest"],tr:["Azınlık Faizi"]},e.exports["Miscellaneous non-operating expense_study"]={en:["Miscellaneous non-operating expense"],tr:["Çeşitli faaliyet dışı giderler"]},e.exports["Momentum Strategy_study"]={en:["Momentum Strategy"],tr:["Momentum Stratejisi"]},e.exports.Momentum_study={en:["Momentum"]},e.exports["Money Flow Index_study"]={en:["Money Flow Index"],tr:["Para Akışı Endeksi"]},e.exports["Money Flow_study"]={en:["Money Flow"],tr:["Para Akışı"]},e.exports["Moon Phases_study"]={en:["Moon Phases"],tr:["Ayın Evreleri"]},e.exports["Morning Doji Star - Bullish_study"]={en:["Morning Doji Star - Bullish"],tr:["Sabah Doji Yıldızı - Boğa"]},e.exports["Morning Star - Bullish_study"]={en:["Morning Star - Bullish"],tr:["Sabah Yıldızı - Boğa"]},e.exports["Moving Average Adaptive_study"]={en:["Moving Average Adaptive"],tr:["Uyarlamalı Hareketli Ortalama"]},e.exports["Moving Average Channel_study"]={en:["Moving Average Channel"],tr:["Hareketli Ortalama Kanalı"]},e.exports["Moving Average Convergence Divergence_study"]={en:["Moving Average Convergence Divergence"],tr:["Hareketli Ortalama Yakınsama Iraksama"]},e.exports["Moving Average Convergence/Divergence_study"]={en:["Moving Average Convergence/Divergence"],tr:["Hareketli Ortalama Yakınsama/Iraksama"]},e.exports["Moving Average Double_study"]={en:["Moving Average Double"],tr:["İkili Hareketli Ortalama"]},e.exports["Moving Average Exponential_study"]={en:["Moving Average Exponential"],tr:["Üstel Hareketli Ortalama EMA"]},e.exports["Moving Average Hamming_study"]={en:["Moving Average Hamming"],tr:["Aşırı Hareketli Ortalama"]},e.exports["Moving Average Modified_study"]={
en:["Moving Average Modified"],tr:["Değiştirilmiş Hareketli Ortalama"]},e.exports["Moving Average Multiple_study"]={en:["Moving Average Multiple"],tr:["Çoklu Hareketli Ortalama"]},e.exports["Moving Average Ribbon_study"]={en:["Moving Average Ribbon"],tr:["Hareketli Ortalama Şeridi"]},e.exports["Moving Average Simple_study"]={en:["Moving Average Simple"],tr:["Hareketli Ortalama Basit"]},e.exports["Moving Average Triple_study"]={en:["Moving Average Triple"],tr:["Üçlü Hareketli Ortalama"]},e.exports["Moving Average Weighted_study"]={en:["Moving Average Weighted"],tr:["Ağırlıklı Hareketli Ortalama"]},e.exports["Moving Average_study"]={en:["Moving Average"],tr:["Hareketli Ortalama"]},e.exports["MovingAvg Cross_study"]={en:["MovingAvg Cross"],tr:["HareketliOrt Kesişme"]},e.exports["MovingAvg2Line Cross_study"]={en:["MovingAvg2Line Cross"],tr:["HareketliOrt2Çizgi Kesişmesi"]},e.exports["Multi-Time Period Charts_study"]={en:["Multi-Time Period Charts"],tr:["Çoklu-Zaman Aralığı Grafikleri"]},e.exports["Net Volume_study"]={en:["Net Volume"],tr:["Net Hacim"]},e.exports["Net current asset value per share_study"]={en:["Net current asset value per share"],tr:["Hisse başına net cari varlık değeri"]},e.exports["Net debt_study"]={en:["Net debt"],tr:["Net borç"]},e.exports["Net income (cash flow)_study"]={en:["Net income (cash flow)"],tr:["Net gelir (nakit akışı)"]},e.exports["Net income before discontinued operations_study"]={en:["Net income before discontinued operations"],tr:["Durdurulan faaliyetler öncesi net gelir"]},e.exports["Net income per employee_study"]={en:["Net income per employee"],tr:["Çalışan başına net gelir"]},e.exports["Net income_study"]={en:["Net income"],tr:["Net gelir"]},e.exports["Net intangible assets_study"]={en:["Net intangible assets"],tr:["Net maddi olmayan duran varlıklar"]},e.exports["Net margin %_study"]={en:["Net margin %"],tr:["Net marj %"]},e.exports["Net property/plant/equipment_study"]={en:["Net property/plant/equipment"],tr:["Net mülk/tesis/ekipman"]},e.exports["Non-cash items_study"]={en:["Non-cash items"],tr:["Nakit olmayan kalemler"]},e.exports["Non-controlling/minority interest_study"]={en:["Non-controlling/minority interest"],tr:["Kontrol gücü olmayan/azınlık payı"]},e.exports["Non-operating income, excl. interest expenses_study"]={en:["Non-operating income, excl. interest expenses"],tr:["Faaliyet dışı gelir, faiz giderleri hariç"]},e.exports["Non-operating income, total_study"]={en:["Non-operating income, total"],tr:["Faaliyet dışı gelir, toplam"]},e.exports["Non-operating interest income_study"]={en:["Non-operating interest income"],tr:["Faaliyet dışı faiz geliri"]},e.exports["Note receivable - long term_study"]={en:["Note receivable - long term"],tr:["Alacak senedi - uzun vadeli"]},e.exports["Notes payable_study"]={en:["Notes payable"],tr:["Ödenecek notlar"]},e.exports["Number of employees_study"]={en:["Number of employees"],tr:["Çalışan Sayısı"]},e.exports["Number of shareholders_study"]={en:["Number of shareholders"],tr:["Hissedar sayısı"]},e.exports["On Balance Volume_study"]={
en:["On Balance Volume"],tr:["Denge İşlem Hacmi"]},e.exports["On Neck - Bearish_study"]={en:["On Neck - Bearish"],tr:["Boyun Üzerinde - Ayı"]},e.exports["Open Interest_study"]={en:["Open Interest"],tr:["Açık Pozisyon"]},e.exports["Operating earnings yield %_study"]={en:["Operating earnings yield %"],tr:["Faaliyet kazanç getirisi %"]},e.exports["Operating expenses (excl. COGS)_study"]={en:["Operating expenses (excl. COGS)"],tr:["İşletme giderleri (COGS hariç)"]},e.exports["Operating income_study"]={en:["Operating income"],tr:["Faaliyet gelirleri"]},e.exports["Operating lease liabilities_study"]={en:["Operating lease liabilities"],tr:["Faaliyet kiralaması yükümlülükleri"]},e.exports["Operating margin %_study"]={en:["Operating margin %"],tr:["Faaliyet kar marjı %"]},e.exports["Other COGS_study"]={en:["Other COGS"],tr:["Diğer COGS"]},e.exports["Other common equity_study"]={en:["Other common equity"],tr:["Diğer ortak hisse senedi"]},e.exports["Other cost of goods sold_study"]={en:["Other cost of goods sold"],tr:["Satılan ürünlerin diğer maliyeti"]},e.exports["Other current assets, total_study"]={en:["Other current assets, total"],tr:["Diğer dönen varlıklar, toplam"]},e.exports["Other current liabilities_study"]={en:["Other current liabilities"],tr:["Diğer mevcut yükümlülükler"]},e.exports["Other exceptional charges_study"]={en:["Other exceptional charges"],tr:["Diğer istisnai ücretler"]},e.exports["Other financing cash flow items, total_study"]={en:["Other financing cash flow items, total"],tr:["Diğer finansman nakit akışı kalemleri, toplam"]},e.exports["Other intangibles, net_study"]={en:["Other intangibles, net"],tr:["Diğer maddi olmayan duran varlıklar, net"]},e.exports["Other investing cash flow items, total_study"]={en:["Other investing cash flow items, total"],tr:["Diğer yatırımların nakit akış kalemleri, toplam"]},e.exports["Other investments_study"]={en:["Other investments"],tr:["Diğer yatırımlar"]},e.exports["Other liabilities, total_study"]={en:["Other liabilities, total"],tr:["Diğer yükümlülükler, toplam"]},e.exports["Other long term assets, total_study"]={en:["Other long term assets, total"],tr:["Diğer uzun vadeli varlıklar, toplam"]},e.exports["Other non-current liabilities, total_study"]={en:["Other non-current liabilities, total"],tr:["Diğer cari olmayan yükümlülükler, toplam"]},e.exports["Other operating expenses, total_study"]={en:["Other operating expenses, total"],tr:["Diğer işletme giderleri, toplam"]},e.exports["Other receivables_study"]={en:["Other receivables"],tr:["Diğer alacaklar"]},e.exports["Other short term debt_study"]={en:["Other short term debt"],tr:["Diğer kısa vadeli borçlar"]},e.exports["OutSide Bar Strategy_study"]={en:["OutSide Bar Strategy"],tr:["Çubuk Dışı Stratejisi"]},e.exports.Overlay_study={en:["Overlay"],tr:["Katman"]},e.exports["PEG ratio_study"]={en:["PEG ratio"],tr:["PEG oranı"]},e.exports["Paid in capital_study"]={en:["Paid in capital"],tr:["Ödenmiş sermaye"]},e.exports["Parabolic SAR Strategy_study"]={en:["Parabolic SAR Strategy"],tr:["Parabolik SAR Stratejisi"]},
e.exports["Parabolic SAR_study"]={en:["Parabolic SAR"],tr:["Parabolik SAR"]},e.exports["Periodic Volume Profile_study"]={en:["Periodic Volume Profile"],tr:["Periyodik Hacim Profili"]},e.exports["Piercing - Bullish_study"]={en:["Piercing - Bullish"],tr:["Piercing - Boğa"]},e.exports["Piotroski F-score_study"]={en:["Piotroski F-score"],tr:["Piotroski F-skoru"]},e.exports["Pivot Extension Strategy_study"]={en:["Pivot Extension Strategy"],tr:["Pivot Uzatma Stratejisi"]},e.exports["Pivot Points High Low_study"]={en:["Pivot Points High Low"],tr:["Pivot Noktaları Yüksek Düşük"]},e.exports["Pivot Points Standard_study"]={en:["Pivot Points Standard"],tr:["Pivot Noktalar Standartı"]},e.exports["Pivot Reversal Strategy_study"]={en:["Pivot Reversal Strategy"],tr:["Pivot Dönüş Stratejisi"]},e.exports["Preferred dividends paid_study"]={en:["Preferred dividends paid"],tr:["Tercih edilen temettüler ödenen"]},e.exports["Preferred dividends_study"]={en:["Preferred dividends"],tr:["Tercih edilen temettüler"]},e.exports["Preferred stock, carrying value_study"]={en:["Preferred stock, carrying value"],tr:["Tercih edilen hisse senedi, taşınan değeri"]},e.exports["Prepaid expenses_study"]={en:["Prepaid expenses"],tr:["Önceden ödenmiş giderler"]},e.exports["Pretax equity in earnings_study"]={en:["Pretax equity in earnings"],tr:["Kazançlarda vergi öncesi öz sermaye"]},e.exports["Pretax income_study"]={en:["Pretax income"],tr:["Vergi öncesi gelir"]},e.exports["Price Channel Strategy_study"]={en:["Price Channel Strategy"],tr:["Fiyat Kanalı Stratejisi"]},e.exports["Price Channel_study"]={en:["Price Channel"],tr:["Fiyat Kanalı"]},e.exports["Price Oscillator_study"]={en:["Price Oscillator"],tr:["Fiyat Osilatörü"]},e.exports["Price Volume Trend_study"]={en:["Price Volume Trend"],tr:["Fiyat Hacim Trendi"]},e.exports["Price earnings ratio forward_study"]={en:["Price earnings ratio forward"],tr:["İleriye dönük fiyat kazanç oranı"]},e.exports["Price sales ratio forward_study"]={en:["Price sales ratio forward"],tr:["Fiyat satış oranı ileri"]},e.exports["Price to book ratio_study"]={en:["Price to book ratio"],tr:["Fiyatın deftere oranı"]},e.exports["Price to cash flow ratio_study"]={en:["Price to cash flow ratio"],tr:["Fiyatın nakit akışa oranı"]},e.exports["Price to earnings ratio_study"]={en:["Price to earnings ratio"],tr:["Fiyatın kazanca oranı"]},e.exports["Price to free cash flow ratio_study"]={en:["Price to free cash flow ratio"],tr:["Fiyat-serbest nakit akışı oranı"]},e.exports["Price to sales ratio_study"]={en:["Price to sales ratio"],tr:["Fiyatın satışa oranı"]},e.exports["Price to tangible book ratio_study"]={en:["Price to tangible book ratio"],tr:["Maddi kitap oranı fiyat"]},e.exports["Profitability ratios_study"]={en:["Profitability ratios"],tr:["Karlılık oranları"]},e.exports["Provision for risks & charge_study"]={en:["Provision for risks & charge"],tr:["Riskler ve ücret karşılığı"]},e.exports["Purchase of investments_study"]={en:["Purchase of investments"],tr:["Yatırımların satın alınması"]},
e.exports["Purchase/acquisition of business_study"]={en:["Purchase/acquisition of business"],tr:["İşletmenin satın alınması/satın alınması"]},e.exports["Purchase/sale of business, net_study"]={en:["Purchase/sale of business, net"],tr:["İşletme Alım/satımı, net"]},e.exports["Purchase/sale of investments, net_study"]={en:["Purchase/sale of investments, net"],tr:["Yatırım Alım/satımı, net"]},e.exports["Quality ratio_study"]={en:["Quality ratio"],tr:["Kalite oranı"]},e.exports["Quick ratio_study"]={en:["Quick ratio"],tr:["Hızlı oran"]},e.exports["RSI Strategy_study"]={en:["RSI Strategy"],tr:["RSI Stratejisi"]},e.exports["Rate Of Change_study"]={en:["Rate Of Change"],tr:["Değişim Oranı"]},e.exports["Rectangle Chart Pattern_study"]={en:["Rectangle Chart Pattern"],tr:["Dikdörtgen Grafik Deseni"]},e.exports["Reduction of long term debt_study"]={en:["Reduction of long term debt"],tr:["Uzun vadeli borcun azaltılması"]},e.exports["Relative Strength Index_study"]={en:["Relative Strength Index"],tr:["Göreceli Güç Endeksi(RSI)"]},e.exports["Relative Vigor Index_study"]={en:["Relative Vigor Index"],tr:["Göreceli Vigor Endeksi"]},e.exports["Relative Volatility Index_study"]={en:["Relative Volatility Index"],tr:["Göreceli Volatilite Endeksi"]},e.exports["Relative Volume at Time_study"]={en:["Relative Volume at Time"],tr:["Zamandaki Göreli Hacim"]},e.exports["Repurchase of common & preferred stock_study"]={en:["Repurchase of common & preferred stock"],tr:["Adi ve imtiyazlı hisse senedi geri alımı"]},e.exports["Research & development to revenue ratio_study"]={en:["Research & development to revenue ratio"],tr:["Araştırma ve geliştirmenin gelire oranı"]},e.exports["Research & development_study"]={en:["Research & development"],tr:["Araştırma & geliştirme"]},e.exports["Restructuring charge_study"]={en:["Restructuring charge"],tr:["Yeniden yapılandırma ücreti"]},e.exports["Retained earnings_study"]={en:["Retained earnings"],tr:["Dağıtılmamış kârlar"]},e.exports["Return on assets %_study"]={en:["Return on assets %"],tr:["Varlık getirisi %"]},e.exports["Return on equity %_study"]={en:["Return on equity %"],tr:["Özkaynak kârlılığı %"]},e.exports["Return on equity adjusted to book value %_study"]={en:["Return on equity adjusted to book value %"],tr:["Defter değerine göre özkaynak kârlılığı %"]},e.exports["Return on invested capital %_study"]={en:["Return on invested capital %"],tr:["Yatırılan sermayenin getirisi %"]},e.exports["Return on tangible assets %_study"]={en:["Return on tangible assets %"],tr:["Maddi duran varlıkların getirisi %"]},e.exports["Return on tangible equity %_study"]={en:["Return on tangible equity %"],tr:["Maddi özkaynak kârlılığı %"]},e.exports["Revenue estimates_study"]={en:["Revenue estimates"],tr:["Gelir tahminleri"]},e.exports["Revenue one year growth_study"]={en:["Revenue one year growth"],tr:["Gelir bir yıllık büyüme"]},e.exports["Revenue per employee_study"]={en:["Revenue per employee"],tr:["Çalışan başına gelir"]},e.exports["Rising Three Methods - Bullish_study"]={en:["Rising Three Methods - Bullish"],
tr:["Yükselen Üç Yöntem - Boğa"]},e.exports["Rising Wedge Chart Pattern_study"]={en:["Rising Wedge Chart Pattern"],tr:["Yükselen Kama Grafik Deseni"]},e.exports["Rising Window - Bullish_study"]={en:["Rising Window - Bullish"],tr:["Yükselen Pencere - Boğa"]},e.exports["Rob Booker - ADX Breakout_study"]={en:["Rob Booker - ADX Breakout"],tr:["Rob Booker - ADX Kırılması"]},e.exports["Rob Booker - Intraday Pivot Points_study"]={en:["Rob Booker - Intraday Pivot Points"],tr:["Rob Booker - Gün İçi Pivot Noktaları"]},e.exports["Rob Booker - Knoxville Divergence_study"]={en:["Rob Booker - Knoxville Divergence"],tr:["Rob Booker - Knoxville Uyumsuzluğu"]},e.exports["Rob Booker - Missed Pivot Points_study"]={en:["Rob Booker - Missed Pivot Points"],tr:["Rob Booker - Kaçırılan Pivot Noktaları"]},e.exports["Rob Booker - Reversal_study"]={en:["Rob Booker - Reversal"],tr:["Rob Booker - Tersine Dönüş"]},e.exports["Rob Booker - Ziv Ghost Pivots_study"]={en:["Rob Booker - Ziv Ghost Pivots"],tr:["Rob Booker - Ziv Hayalet Pivotları"]},e.exports["SMI Ergodic Indicator/Oscillator_study"]={en:["SMI Ergodic Indicator/Oscillator"],tr:["SMI Ergodik Gösterge/Osilatörü"]},e.exports["SMI Ergodic Indicator_study"]={en:["SMI Ergodic Indicator"],tr:["SMI Ergodik Gösterge"]},e.exports["SMI Ergodic Oscillator_study"]={en:["SMI Ergodic Oscillator"],tr:["SMI Ergodik Osilatör"]},e.exports["Sale of common & preferred stock_study"]={en:["Sale of common & preferred stock"],tr:["Adi ve imtiyazlı hisse satışı"]},e.exports["Sale of fixed assets & businesses_study"]={en:["Sale of fixed assets & businesses"],tr:["Sabit kıymetlerin ve işletmelerin satışı"]},e.exports["Sale/maturity of investments_study"]={en:["Sale/maturity of investments"],tr:["Yatırımların satışı/vadesi"]},e.exports["Selling/general/admin expenses, other_study"]={en:["Selling/general/admin expenses, other"],tr:["Satış/genel/yönetici giderleri, diğer"]},e.exports["Selling/general/admin expenses, total_study"]={en:["Selling/general/admin expenses, total"],tr:["Satış/genel/yönetici giderleri, toplam"]},e.exports["Session Volume HD_study"]={en:["Session Volume HD"],tr:["HD Seans Hacmi"]},e.exports["Session Volume Profile HD_study"]={en:["Session Volume Profile HD"],tr:["Seans Hacmi Profili HD"]},e.exports["Session Volume Profile_study"]={en:["Session Volume Profile"],tr:["Seans Hacmi Profili"]},e.exports["Session Volume_study"]={en:["Session Volume"],tr:["Seans Hacmi"]},e.exports["Shareholders' equity_study"]={en:["Shareholders' equity"],tr:["Ortak sermaye"]},e.exports["Shares buyback ratio %_study"]={en:["Shares buyback ratio %"],tr:["Hisse geri alım oranı %"]},e.exports["Shooting Star - Bearish_study"]={en:["Shooting Star - Bearish"],tr:["Kayan Yıldız - Ayı"]},e.exports["Short term debt excl. current portion of LT debt_study"]={en:["Short term debt excl. current portion of LT debt"],tr:["Kısa vadeli borç hariç LT borcunun mevcut kısmı"]},e.exports["Short term debt_study"]={en:["Short term debt"],tr:["Kısa vadeli borç"]},e.exports["Short term investments_study"]={en:["Short term investments"],
tr:["Kısa vadeli yatırımlar"]},e.exports["Sloan ratio %_study"]={en:["Sloan ratio %"],tr:["Sloan oranı %"]},e.exports["Smoothed Moving Average_study"]={en:["Smoothed Moving Average"],tr:["Yuvarlatılmış Hareketli Ortalama"]},e.exports["Solvency ratios_study"]={en:["Solvency ratios"],tr:["Ödeme gücü oranları"]},e.exports["Spinning Top Black_study"]={en:["Spinning Top Black"],tr:["Dönen Üst Siyah"]},e.exports["Spinning Top White_study"]={en:["Spinning Top White"],tr:["Dönen Üst Beyaz"]},e.exports["Springate score_study"]={en:["Springate score"],tr:["Springate puanı"]},e.exports["Standard Deviation_study"]={en:["Standard Deviation"],tr:["Standart Sapma"]},e.exports["Standard Error Bands_study"]={en:["Standard Error Bands"],tr:["Standart Hata Bandı"]},e.exports["Standard Error_study"]={en:["Standard Error"],tr:["Standart Hata"]},e.exports.Stoch_study={en:["Stoch"],tr:["Stokastik"]},e.exports["Stochastic Momentum Index_study"]={en:["Stochastic Momentum Index"],tr:["Stokastik Momentum Endeksi"]},e.exports["Stochastic RSI_study"]={en:["Stochastic RSI"],tr:["Stokastik RSI"]},e.exports["Stochastic Slow Strategy_study"]={en:["Stochastic Slow Strategy"],tr:["Stokastik Yavaş Strateji"]},e.exports.Stochastic_study={en:["Stochastic"],tr:["Stokastik"]},e.exports.SuperTrend_study={en:["SuperTrend"]},e.exports["Supertrend Strategy_study"]={en:["Supertrend Strategy"],tr:["Supertrend Stratejisi"]},e.exports.Supertrend_study={en:["Supertrend"]},e.exports["Sustainable growth rate_study"]={en:["Sustainable growth rate"],tr:["Sürdürülebilir büyüme oranı"]},e.exports.TRIX_study={en:["TRIX"]},e.exports["Tangible book value per share_study"]={en:["Tangible book value per share"],tr:["Hisse başına maddi defter değeri"]},e.exports["Tangible common equity ratio_study"]={en:["Tangible common equity ratio"],tr:["Maddi ortak hisse senedi oranı"]},e.exports.Taxes_study={en:["Taxes"],tr:["Vergiler"]},e.exports["Technical Ratings Strategy_study"]={en:["Technical Ratings Strategy"],tr:["Teknik Derecelendirme Stratejisi"]},e.exports["Technical Ratings_study"]={en:["Technical Ratings"],tr:["Teknik Değerlendirmeler"]},e.exports.Technicals_study={en:["Technicals"],tr:["Teknikler"]},e.exports["Three Black Crows - Bearish_study"]={en:["Three Black Crows - Bearish"],tr:["Üç Kara Karga - Ayı"]},e.exports["Three White Soldiers - Bullish_study"]={en:["Three White Soldiers - Bullish"],tr:["Üç Beyaz Asker - Boğa"]},e.exports["Time Weighted Average Price_study"]={en:["Time Weighted Average Price"],tr:["Zaman Ağırlıklı Ortalama Fiyat"]},e.exports["Tobin's Q (approximate)_study"]={en:["Tobin's Q (approximate)"],tr:["Tobin'in Q'su (yaklaşık)"]},e.exports["Total assets_study"]={en:["Total assets"],tr:["Toplam Aktifler"]},e.exports["Total cash dividends paid_study"]={en:["Total cash dividends paid"],tr:["Ödenen Toplam Nakit Temettü"]},e.exports["Total common shares outstanding_study"]={en:["Total common shares outstanding"],tr:["Toplam Ödenmemiş Ortak Hisse Senetleri"]},e.exports["Total current assets_study"]={en:["Total current assets"],tr:["Toplam Cari Aktifler"]},
e.exports["Total current liabilities_study"]={en:["Total current liabilities"],tr:["Toplam Cari Yükümlülükler"]},e.exports["Total debt_study"]={en:["Total debt"],tr:["Toplam Borç"]},e.exports["Total equity_study"]={en:["Total equity"],tr:["Toplam Sermaye"]},e.exports["Total inventory_study"]={en:["Total inventory"],tr:["Toplam envanter"]},e.exports["Total liabilities & shareholders' equities_study"]={en:["Total liabilities & shareholders' equities"],tr:["Toplam yükümlülükler ve özkaynaklar"]},e.exports["Total liabilities_study"]={en:["Total liabilities"],tr:["Toplam Mesuliyetler"]},e.exports["Total non-current assets_study"]={en:["Total non-current assets"],tr:["Sabit kıymetler toplamı"]},e.exports["Total non-current liabilities_study"]={en:["Total non-current liabilities"],tr:["Toplam Cari Olmayan Borçlar"]},e.exports["Total operating expenses_study"]={en:["Total operating expenses"],tr:["Toplam faaliyet giderleri"]},e.exports["Total receivables, net_study"]={en:["Total receivables, net"],tr:["Toplam alacaklar, net"]},e.exports["Total revenue_study"]={en:["Total revenue"],tr:["Toplam Gelir"]},e.exports["Trading Sessions_study"]={en:["Trading Sessions"],tr:["İşlem Seansları"]},e.exports["Treasury stock - common_study"]={en:["Treasury stock - common"],tr:["Hazine stoğu - ortak"]},e.exports["Trend Strength Index_study"]={en:["Trend Strength Index"],tr:["Trend Gücü Endeksi"]},e.exports["Tri-Star - Bearish_study"]={en:["Tri-Star - Bearish"],tr:["Tri-Star - Ayı"]},e.exports["Tri-Star - Bullish_study"]={en:["Tri-Star - Bullish"],tr:["Tri-Star - Boğa"]},e.exports["Triangle Chart Pattern_study"]={en:["Triangle Chart Pattern"],tr:["Üçgen Grafik Deseni"]},e.exports["Triple Bottom Chart Pattern_study"]={en:["Triple Bottom Chart Pattern"],tr:["Üçlü Dip Grafik Formasyonu"]},e.exports["Triple EMA_study"]={en:["Triple EMA"],tr:["Üçlü ÜHO"]},e.exports["Triple Top Chart Pattern_study"]={en:["Triple Top Chart Pattern"],tr:["Üçlü Üst Grafik Formasyonu"]},e.exports["True Strength Index_study"]={en:["True Strength Index"],tr:["Gerçek Güç Endeksi"]},e.exports["True Strength Indicator_study"]={en:["True Strength Indicator"],tr:["Gerçek Güç Göstergesi"]},e.exports["Tweezer Top - Bearish_study"]={en:["Tweezer Top - Bearish"],tr:["Cımbız Üstü - Ayı"]},e.exports["Typical Price_study"]={en:["Typical Price"],tr:["Tipik Fiyat"]},e.exports["Ultimate Oscillator_study"]={en:["Ultimate Oscillator"],tr:["Nihai Osilatör(UO)"]},e.exports["Unrealized gain/loss_study"]={en:["Unrealized gain/loss"],tr:["Gerçekleşmemiş kazanç/zarar"]},e.exports["Unusual income/expense_study"]={en:["Unusual income/expense"],tr:["Olağandışı gelir/gider"]},e.exports["Up/Down Volume_study"]={en:["Up/Down Volume"],tr:["Yukarı/Aşağı Ses Seviyesi"]},e.exports["Upside Tasuki Gap - Bullish_study"]={en:["Upside Tasuki Gap - Bullish"],tr:["Cımbız Üstü - Boğa"]},e.exports["VWAP Auto Anchored_study"]={en:["VWAP Auto Anchored"],tr:["VWAP Otomatik Sabitlenmiş"]},e.exports.VWAP_study={en:["VWAP"],tr:["HAOF"]},e.exports.VWMA_study={en:["VWMA"],tr:["HAHO"]},
e.exports["Valuation ratios_study"]={en:["Valuation ratios"],tr:["Değerleme oranları"]},e.exports["Visible Average Price_study"]={en:["Visible Average Price"],tr:["Görünür Ortalama Fiyat"]},e.exports["Visible Range Volume Profile_study"]={en:["Visible Range Volume Profile"],tr:["Görünür Aralık Hacim Profili"]},e.exports["Visible Range_study"]={en:["Visible Range"],tr:["Görünür Aralık"]},e.exports.Vol_study={en:["Vol"],tr:["Hacim"]},e.exports["Volatility Close-to-Close_study"]={en:["Volatility Close-to-Close"],tr:["Volatilite Kapanıştan-Kapanışa"]},e.exports["Volatility Index_study"]={en:["Volatility Index"],tr:["Volatilite Endeksi"]},e.exports["Volatility O-H-L-C_study"]={en:["Volatility O-H-L-C"],tr:["Volatilite O-H-L-C"]},e.exports["Volatility Stop_study"]={en:["Volatility Stop"],tr:["Volatilite Durdurması"]},e.exports["Volatility Zero Trend Close-to-Close_study"]={en:["Volatility Zero Trend Close-to-Close"],tr:["Volatilite Sıfır Trendi Kapanıştan-Kapanışa"]},e.exports["Volty Expan Close Strategy_study"]={en:["Volty Expan Close Strategy"],tr:["Hareketlilik Genişleme Kapatma Stratejisi"]},e.exports["Volume Oscillator_study"]={en:["Volume Oscillator"],tr:["İşlem Hacmi Osilatörü"]},e.exports["Volume Weighted Average Price_study"]={en:["Volume Weighted Average Price"],tr:["Hacim Ağırlıklı Ortalama Fiyat"]},e.exports["Volume Weighted Moving Average_study"]={en:["Volume Weighted Moving Average"],tr:["Hacim Ağırlıklı Hareketli Ortalama"]},e.exports.Volume_study={en:["Volume"],tr:["Hacim"]},e.exports["Vortex Indicator_study"]={en:["Vortex Indicator"],tr:["Vortex Göstergesi"]},e.exports["Williams %R_study"]={en:["Williams %R"]},e.exports["Williams Alligator_study"]={en:["Williams Alligator"],tr:["Williams Gator"]},e.exports["Williams Fractal_study"]={en:["Williams Fractal"],tr:["Williams Fraktalı"]},e.exports["Williams Fractals_study"]={en:["Williams Fractals"],tr:["Williams Fraktalları"]},e.exports["Williams Percent Range_study"]={en:["Williams Percent Range"],tr:["Williams Yüzde Aralığı"]},e.exports["Woodies CCI_study"]={en:["Woodies CCI"],tr:["Woodie CCI"]},e.exports["Zig Zag_study"]={en:["Zig Zag"]},e.exports["Zmijewski score_study"]={en:["Zmijewski score"],tr:["Zmijewski puanı"]}},89633:e=>{e.exports={en:["Anchored Volume Profile"],tr:["Sabitlenmiş Hacim Profili"]}},25705:e=>{e.exports={en:["Fixed Range Volume Profile"],tr:["Sabit Aralıklı Hacim Profili"]}},24261:e=>{e.exports={en:["Vol"],tr:["Hacim"]}},67736:e=>{e.exports={en:["Minor"],tr:["Minör"]}},922:e=>{e.exports={en:["Minute"],tr:["Dakika"]}},91405:e=>{e.exports={en:["Text"],tr:["Metin"]}},78972:e=>{e.exports={en:["Couldn't copy"],tr:["Kopyalanamadı"]}},10615:e=>{e.exports={en:["Couldn't cut"],tr:["Kesilemedi"]}},81518:e=>{e.exports={en:["Couldn't paste"],tr:["Yapıştırılamadı"]}},83140:e=>{e.exports={en:["Countdown to bar close"],tr:["Çubuğun Kapanışına Gerisayım"]}},10871:e=>{e.exports={en:["Colombo"],tr:["Kolombo"]}},55761:e=>{e.exports={en:["Columns"],tr:["Sütunlar"]}},9818:e=>{e.exports={en:["Comment"],tr:["Yorum"]}},53942:e=>{e.exports={
en:["Compare or Add Symbol"],tr:["Kıyasla veya Sembol Ekle"]}},12086:e=>{e.exports={en:["Compilation error"],tr:["Derleme hatası"]}},71692:e=>{e.exports={en:["Confirm to remove locked drawings"],tr:["Kilitli çizimleri silmek için onaylayın"]}},48141:e=>{e.exports={en:["Confirm Inputs"],tr:["Girişleri Onayla"]}},81605:e=>{e.exports={en:["Confirm Remove Study Tree"],tr:["Nesneler Ağacı Kaldırmayı Onayla"]}},38917:e=>{e.exports={en:["Copenhagen"],tr:["Kopenhag"]}},49680:e=>{e.exports={en:["Copy"],tr:["Kopyala"]}},66134:e=>{e.exports={en:["Copy Chart Layout"],tr:["Grafik Yerleşimini Kopyala"]}},63553:e=>{e.exports={en:["Copy price"],tr:["Fiyatı kopyala"]}},65736:e=>{e.exports={en:["Cairo"],tr:["Kâhire"]}},25381:e=>{e.exports={en:["Callout"],tr:["Belirtme"]}},45054:e=>{e.exports={en:["Candles"],tr:["Mum Grafikler"]}},30948:e=>{e.exports={en:["Caracas"],tr:["Karakas"]}},70409:e=>{e.exports={en:["Casablanca"],tr:["Kazablanka"]}},37276:e=>{e.exports={en:["Change"],tr:["Değişim"]}},85124:e=>{e.exports={en:["Change Symbol"],tr:["Sembolu Değiştir"]}},2569:e=>{e.exports={en:["Change interval"],tr:["Zaman Aralığını Değiştir"]}},9687:e=>{e.exports={en:["Change interval. Press number or comma"],tr:["Aralığı değiştirin. Number veya virgül tuşuna basın"]}},36332:e=>{e.exports={en:["Change symbol. Start typing symbol name"],tr:["Sembolü değiştir. Sembol adını yazmaya başlayın"]}},48566:e=>{e.exports={en:["Change scale currency"],tr:["Ölçek para birimini değiştir"]}},85110:e=>{e.exports={en:["Change scale unit"],tr:["Ölçek birimini değiştir"]}},56275:e=>{e.exports={en:["Chart #{index}"],tr:["Grafik #{index}"]}},39950:e=>{e.exports={en:["Chart Properties"],tr:["Grafik Özellikleri"]}},98856:e=>{e.exports={en:["Chart by TradingView"],tr:["Grafik TradingView tarafından"]}},1136:e=>{e.exports={en:["Chart for {symbol}, {interval}"],tr:["Grafik {symbol}, {interval}"]}},69804:e=>{e.exports={en:["Chart image copied to clipboard {emoji}"],tr:["Panoya kopyalanan grafik görüntüsü {emoji}"]}},6655:e=>{e.exports={en:["Chart image embed code copied to clipboard {emoji}"],tr:["Panoya kopyalanan grafik resmi yerleştirme kodu {emoji}"]}},36549:e=>{e.exports={en:["Chatham Islands"],tr:["Chatham Adaları"]}},72452:e=>{e.exports={en:["Chicago"],tr:["Şikago"]}},50349:e=>{e.exports={en:["Chongqing"]}},91944:e=>{e.exports={en:["Circle"],tr:["Daire"]}},14985:e=>{e.exports={en:["Click to set a point"],tr:["Nokta belirlemek için tıkla"]}},12537:e=>{e.exports={en:["Clone"],tr:["Klonla"]}},62578:e=>{e.exports={en:["Close"],tr:["Kapanış"]}},264:e=>{e.exports={en:["Create limit order"],tr:["Limit Emri Oluştur"]}},6969:e=>{e.exports={en:["Cross"],tr:["Artı"]}},74334:e=>{e.exports={en:["Cross Line"],tr:["Kesişen Çizgiler"]}},59396:e=>{e.exports={en:["Currencies"],tr:["Döviz"]}},20177:e=>{e.exports={en:["Current interval and above"],tr:["Mevcut aralık ve üzeri"]}},494:e=>{e.exports={en:["Current interval and below"],tr:["Mevcut aralık ve altı"]}},60668:e=>{e.exports={en:["Current interval only"],tr:["Sadece mevcut aralık"]}},78609:e=>{e.exports={en:["Curve"],tr:["Eğri"]
}},87380:e=>{e.exports={en:["Cycle"],tr:["Birkaç Yıllık"]}},84031:e=>{e.exports={en:["Cyclic Lines"],tr:["Periyodik Çizgiler"]}},93191:e=>{e.exports={en:["Cypher Pattern"],tr:["Açarsöz Formasyonu"]}},7219:e=>{e.exports={en:["A layout with that name already exists"],tr:["Bu isimle bir yerleşim zaten mevcut"]}},67635:e=>{e.exports={en:["A layout with that name already exists. Do you want to overwrite it?"],tr:["Bu isimle bir yerleşim zaten mevcut. Üstüne yazmak istiyor musunuz?"]}},46712:e=>{e.exports={en:["ABCD Pattern"],tr:["ABCD Formasyonu"]}},36485:e=>{e.exports={en:["Amsterdam"]}},24185:e=>{e.exports={en:["Anchor"],tr:["Sabitle"]}},42630:e=>{e.exports={en:["Anchorage"],tr:["Ankraj"]}},42669:e=>{e.exports={en:["Anchored Text"],tr:["Sabitlenmiş Metin"]}},84541:e=>{e.exports={en:["Anchored VWAP"],tr:["Sabit VWAP"]}},77401:e=>{e.exports={en:["Access error"],tr:["Erişim hatası"]}},46501:e=>{e.exports={en:["Add Symbol"],tr:["Sembol Ekle"]}},2439:e=>{e.exports={en:["Add financial metric for {instrumentName}"],tr:["{instrumentName} için finansal metrik ekle"]}},35088:e=>{e.exports={en:["Add indicator/strategy on {studyTitle}"],tr:["{studyTitle} 'a indikatör/strateji ekle"]}},35679:e=>{e.exports={en:["Add this financial metric to entire layout"],tr:["Bu finansalı tüm yerleşime ekle"]}},10996:e=>{e.exports={en:["Add this financial metric to favorites"],tr:["Bu finansal ölçümü favorilere ekle"]}},26090:e=>{e.exports={en:["Add this indicator to entire layout"],tr:["Bu İndikatörü Tüm Yerleşime Ekle"]}},92957:e=>{e.exports={en:["Add this indicator to favorites"],tr:["Bu göstergeyi favorilere ekle"]}},95754:e=>{e.exports={en:["Add this strategy to entire layout"],tr:["Bu Stratejiyi Tüm Yerleşime Ekle"]}},39010:e=>{e.exports={en:["Add this symbol to entire layout"],tr:["Bu sembolü tüm yerleşime ekle"]}},426:e=>{e.exports={en:["Adelaide"]}},40452:e=>{e.exports={en:["Always invisible"],tr:["Daima görünmez"]}},36299:e=>{e.exports={en:["Always visible"],tr:["Daima görünür"]}},58026:e=>{e.exports={en:["All intervals"],tr:["Tüm aralıklar"]}},78358:e=>{e.exports={en:["Apply default"],tr:["Varsayılanı Uygula"]}},22437:e=>{e.exports={en:["Apply these indicators to entire layout"],tr:["Bu göstergeleri tüm yerleşime uygula"]}},27072:e=>{e.exports={en:["Apr"],tr:["Nis"]}},59324:e=>{e.exports={en:["Arc"],tr:["Yay"]}},34456:e=>{e.exports={en:["Area"],tr:["Alan"]}},11858:e=>{e.exports={en:["Arrow"],tr:["Ok İşareti"]}},34247:e=>{e.exports={en:["Arrow Down"],tr:["Aşağı Ok"]}},36352:e=>{e.exports={en:["Arrow Marker"],tr:["Ok İşaretleyici"]}},73193:e=>{e.exports={en:["Arrow Mark Down"],tr:["Aşağı Ok İşareti"]}},1949:e=>{e.exports={en:["Arrow Mark Left"],tr:["Sola Ok İşareti"]}},86275:e=>{e.exports={en:["Arrow Mark Right"],tr:["Sağa Ok İşareti"]}},62453:e=>{e.exports={en:["Arrow Mark Up"],tr:["Yukarı Ok İşareti"]}},77231:e=>{e.exports={en:["Arrow Up"],tr:["Yukarı Ok"]}},98128:e=>{e.exports={en:["Astana"]}},63627:e=>{e.exports={en:["Ashgabat"],tr:["Aşkabad"]}},72445:e=>{e.exports={en:["At close"],tr:["Kapanışta"]}},73702:e=>{e.exports={en:["Athens"],
tr:["Atina"]}},21469:e=>{e.exports={en:["Auto"],tr:["Otomatik"]}},24157:e=>{e.exports={en:["Auto (fits data to screen)"],tr:["Oto (verileri ekrana sığdırır)"]}},46450:e=>{e.exports={en:["Aug"],tr:["Ağu"]}},21841:e=>{e.exports={en:["Average close price label"],tr:["Ortalama kapanış fiyat etiketi"]}},16138:e=>{e.exports={en:["Average close price line"],tr:["Ortalama kapanış fiyat çizgisi"]}},73025:e=>{e.exports={en:["Avg"],tr:["Ort"]}},87580:e=>{e.exports={en:["Azores"]}},73905:e=>{e.exports={en:["Bogota"]}},90594:e=>{e.exports={en:["Bahrain"],tr:["Bahreyn"]}},70540:e=>{e.exports={en:["Balloon"],tr:["Balon"]}},47045:e=>{e.exports={en:["Bangkok"]}},92101:e=>{e.exports={en:["Bar Replay isn't available for this chart type. Want to exit Bar Replay?"],tr:["Çubuk Tekrar Oynatma bu grafik türü için kullanılamaz. Çubuk Tekrar Oynatmadan çıkmak mı istiyorsunuz?"]}},32482:e=>{e.exports={en:["Bar Replay isn't available for this symbol. Want to exit Bar Replay?"],tr:["Çubuk Tekrarı bu sembol için mevcut değil. Çubuk Tekrarı işlevinden çıkmak istiyor musunuz?"]}},85902:e=>{e.exports={en:["Bar Replay isn't available for this time interval. Want to exit Bar Replay?"],tr:["Çubuk Tekrarı bu zaman aralığında kullanılamaz. Çubuk Tekrar Oynatmadan çıkmak istiyor musunuz?"]}},27377:e=>{e.exports={en:["Bars"],tr:["Çubuk Grafikler"]}},81994:e=>{e.exports={en:["Bars Pattern"],tr:["Çubuk Modeli"]}},59213:e=>{e.exports={en:["Baseline"],tr:["Temel Çizgi"]}},71797:e=>{e.exports={en:["Belgrade"],tr:["Belgrat"]}},64313:e=>{e.exports={en:["Berlin"]}},43539:e=>{e.exports={en:["Brush"],tr:["Fırça"]}},91499:e=>{e.exports={en:["Brussels"],tr:["Brüksel"]}},70876:e=>{e.exports={en:["Bratislava"]}},55481:e=>{e.exports={en:["Bring forward"],tr:["Öne getir"]}},17293:e=>{e.exports={en:["Bring to front"],tr:["En öne getir"]}},79336:e=>{e.exports={en:["Brisbane"]}},33672:e=>{e.exports={en:["Bucharest"],tr:["Bükreş"]}},20313:e=>{e.exports={en:["Budapest"],tr:["Budapeşte"]}},25282:e=>{e.exports={en:["Buenos Aires"]}},46768:e=>{e.exports={en:["By TradingView"],tr:["TradingView'den"]}},54280:e=>{e.exports={en:["Go to date"],tr:["Tarihe git"]}},74975:e=>{e.exports={en:["Go to {lineToolName}"],tr:["Şuna git: {lineToolName}"]}},15462:e=>{e.exports={en:["Got it"],tr:["Anlaşıldı"]}},47460:e=>{e.exports={en:["Gann Box"],tr:["Gann Kutusu"]}},48683:e=>{e.exports={en:["Gann Fan"],tr:["Gann Fanı"]}},44763:e=>{e.exports={en:["Gann Square"],tr:["Gann Karesi"]}},60707:e=>{e.exports={en:["Gann Square Fixed"],tr:["Gann Karesi Yeni"]}},46808:e=>{e.exports={en:["Ghost Feed"],tr:["Hayalet Çizgiler"]}},57726:e=>{e.exports={en:["Grand supercycle"],tr:["Birkaç on yıllık"]}},15096:e=>{e.exports={en:["Do you really want to delete indicator template '{name}' ?"],tr:["'{name}' gösterge şablonunu gerçekten silmek istiyor musunuz?"]}},77174:e=>{e.exports={en:["Do you really want to delete study and all of it's children?"],tr:["Çalışmayı ve tüm bağlı olanları silmek istediğinizden emin misiniz?"]}},77125:e=>{e.exports={en:["Double Curve"],tr:["Çift Eğri"]}},9430:e=>{e.exports={
en:["Double-click any edge to reset layout grid"],tr:["Düzen ızgarasını sıfırlamak için herhangi bir kenarı çift tıklayın"]}},75296:e=>{e.exports={en:["Double-click to finish Path"],tr:["Yolu bitirmek için çift tıklayın"]}},17409:e=>{e.exports={en:["Double-click to finish Polyline"],tr:["Polyline'ı bitirmek için çift tıklayın"]}},36539:e=>{e.exports={en:["Double-tap any edge to reset layout grid"],tr:["Yerleşim ızgarasını sıfırlamak için herhangi bir kenara çift dokunun"]}},57131:e=>{e.exports={en:["Data Provided by"],tr:["Verileri Sağlayan"]}},22677:e=>{e.exports={en:["Date"],tr:["Tarih"]}},85444:e=>{e.exports={en:["Date Range"],tr:["Tarih Aralığı"]}},47017:e=>{e.exports={en:["Date and Price Range"],tr:["Tarih ve Fiyat Aralığı"]}},32084:e=>{e.exports={en:["Dec"],tr:["Ara"]}},23403:e=>{e.exports={en:["Degree"],tr:["Derece"]}},27358:e=>{e.exports={en:["Denver"]}},24959:e=>{e.exports={en:["Dhaka"]}},15179:e=>{e.exports={en:["Diamond"],tr:["Elmas"]}},91544:e=>{e.exports={en:["Disjoint Channel"],tr:["Ayrık Kanal"]}},70132:e=>{e.exports={en:["Displacement"],tr:["Ayrıştırma"]}},93864:e=>{e.exports={en:["Drawings toolbar"],tr:["Çizim araç çubuğu"]}},56916:e=>{e.exports={en:["Draw Horizontal Line at {price}"],tr:["{price}'a Yatay Çizgi Çizin"]}},23650:e=>{e.exports={en:["Dubai"]}},79716:e=>{e.exports={en:["Dublin"]}},73456:e=>{e.exports={en:["Emoji"]}},9541:e=>{e.exports={en:["Enter a new chart layout name"],tr:["Grafik yerleşiminin yeni adını yazın"]}},80943:e=>{e.exports={en:["Elliott Correction Wave (ABC)"],tr:["Elliott Düzeltme Dalgası (ABC)"]}},75112:e=>{e.exports={en:["Elliott Double Combo Wave (WXY)"],tr:["Elliott İkili Kombo Dalgası (WXY)"]}},61114:e=>{e.exports={en:["Elliott Impulse Wave (12345)"],tr:["Elliott İtki Dalgası (12345)"]}},72359:e=>{e.exports={en:["Elliott Triangle Wave (ABCDE)"],tr:["Elliott Üçgen Dalgası (ABCDE)"]}},76129:e=>{e.exports={en:["Elliott Triple Combo Wave (WXYXZ)"],tr:["Elliott Üçlü Kombo Dalgası (WXYXZ)"]}},78996:e=>{e.exports={en:["Ellipse"],tr:["Elips"]}},52788:e=>{e.exports={en:["Extended Line"],tr:["Çizgiyi Uzat"]}},86905:e=>{e.exports={en:["Exchange"],tr:["Borsa"]}},19271:e=>{e.exports={en:["Existing pane above"],tr:["Üst panosuna getir"]}},46545:e=>{e.exports={en:["Existing pane below"],tr:["Alt Panosuna Getir"]}},20138:e=>{e.exports={en:["Forecast"],tr:["Tahmin"]}},2507:e=>{e.exports={en:["Feb"],tr:["Şub"]}},59005:e=>{e.exports={en:["Fib Channel"],tr:["Fib Kanalı"]}},82330:e=>{e.exports={en:["Fib Circles"],tr:["Fib Çemberleri"]}},55986:e=>{e.exports={en:["Fib Retracement"],tr:["Fib Düzeltmesi"]}},33880:e=>{e.exports={en:["Fib Speed Resistance Arcs"],tr:["Fib Hız Direnç Yayları"]}},2395:e=>{e.exports={en:["Fib Speed Resistance Fan"],tr:["Fib Hız Direnç Fanı"]}},39014:e=>{e.exports={en:["Fib Spiral"],tr:["Fib Spiralı"]}},30622:e=>{e.exports={en:["Fib Time Zone"],tr:["Fib Saat Dilimi"]}},85042:e=>{e.exports={en:["Fib Wedge"],tr:["Fib Takozu"]}},33885:e=>{e.exports={en:["Flag"],tr:["Bayrak"]}},14600:e=>{e.exports={en:["Flag Mark"],tr:["Bayrak"]}},45051:e=>{e.exports={
en:["Flat Top/Bottom"],tr:["Flat Üst/Alt"]}},39643:e=>{e.exports={en:["Fraction part is invalid."],tr:["Ondalık kısmı geçerisiz."]}},24077:e=>{e.exports={en:["Fundamental studies are no longer available on charts"],tr:["Grafikler üzerinde artık Temel çalışmalar bulunmuyor"]}},31561:e=>{e.exports={en:["Kolkata"],tr:["Kalküta"]}},54533:e=>{e.exports={en:["Kathmandu"]}},83490:e=>{e.exports={en:["Kagi"]}},70913:e=>{e.exports={en:["Karachi"],tr:["Karaçi"]}},76614:e=>{e.exports={en:["Kuwait"],tr:["Kuveyt"]}},38561:e=>{e.exports={en:["Kuala Lumpur"]}},99906:e=>{e.exports={en:["HLC area"],tr:["HLC alanı"]}},886:e=>{e.exports={en:["HLC bars"],tr:["HLC Barları"]}},34491:e=>{e.exports={en:["Ho Chi Minh"]}},13459:e=>{e.exports={en:["Hollow candles"],tr:["İçi Boş Mumlar"]}},48861:e=>{e.exports={en:["Hong Kong"]}},79668:e=>{e.exports={en:["Honolulu"]}},21795:e=>{e.exports={en:["Horizontal Line"],tr:["Yatay Çizgi"]}},25487:e=>{e.exports={en:["Horizontal Ray"],tr:["Yatay Işın"]}},21928:e=>{e.exports={en:["Head and Shoulders"],tr:["Omuz Baş Omuz"]}},63876:e=>{e.exports={en:["Heikin Ashi"]}},48203:e=>{e.exports={en:["Helsinki"]}},27298:e=>{e.exports={en:["Hide"],tr:["Gizle"]}},47074:e=>{e.exports={en:["Hide all"],tr:["Tümünü gizle"]}},52563:e=>{e.exports={en:["Hide all drawings"],tr:["Tüm çizimleri gizle"]}},90763:e=>{e.exports={en:["Hide all drawings and indicators"],tr:["Tüm çizimleri ve göstergeleri gizle"]}},18216:e=>{e.exports={en:["Hide all drawings, indicators, positions & orders"],tr:["Tüm çizimleri, göstergeleri, pozisyonları ve emirleri gizle"]}},78525:e=>{e.exports={en:["Hide all indicators"],tr:["Tüm göstergeleri gizle"]}},42164:e=>{e.exports={en:["Hide all positions & orders"],tr:["Tüm pozisyonları ve emirleri gizle"]}},3217:e=>{e.exports={en:["Hide drawings"],tr:["Çizimleri gizle"]}},97878:e=>{e.exports={en:["Hide events on chart"],tr:["Grafikte olayları gizle"]}},72351:e=>{e.exports={en:["Hide indicators"],tr:["Göstergeleri gizle"]}},28345:e=>{e.exports={en:["Hide marks on bars"],tr:["Çubuklardaki İşaretleri Gizle"]}},92226:e=>{e.exports={en:["Hide positions & orders"],tr:["Pozisyonları ve emirleri gizle"]}},78254:e=>{e.exports={en:["High"],tr:["Yüksek"]}},98236:e=>{e.exports={en:["High-low"],tr:["Yüksek-düşük"]}},99479:e=>{e.exports={en:["High and low price labels"],tr:["Yüksek ve düşük fiyat etiketleri"]}},33766:e=>{e.exports={en:["High and low price lines"],tr:["Yüksek ve düşük fiyat çizgileri"]}},69476:e=>{e.exports={en:["Highlighter"],tr:["Öne Çıkarıcı"]}},72819:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."],tr:["Histogram çok büyük, lütfen ayarlar penceresinde {boldHighlightStart}Satır Boyutu{boldHighlightEnd} öğesini artırın."]}},94966:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Ticks Per Row{boldHighlightEnd} in the settings window."],tr:["Histogram çok büyük, lütfen ayarlar penceresinde {boldHighlightStart}Satır Başına Fiyat Adımı{boldHighlightEnd} öğesini artırın."]}},66751:e=>{e.exports={
en:["Histogram is too large, please reduce {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."],tr:["Histogram çok büyük, lütfen ayarlar penceresindeki {boldHighlightStart}Satır Boyutu{boldHighlightEnd} öğesini küçültün."]}},68065:e=>{e.exports={en:["Image"],tr:["Resim"]}},80185:e=>{e.exports={en:["Intervals less than {resolution} are not supported for {ticker}."],tr:["{resolution} değerinden küçük aralıklar {ticker} için desteklenmez."]}},10268:e=>{e.exports={en:["Intermediate"],tr:["Birkaç Hafta-Aylık"]}},14285:e=>{e.exports={en:["Invalid Symbol"],tr:["Geçersiz Sembol"]}},52969:e=>{e.exports={en:["Invalid symbol"],tr:["Hatalı sembol"]}},37189:e=>{e.exports={en:["Invert scale"],tr:["Ölçeği Ters Çevir"]}},89999:e=>{e.exports={en:["Indexed to 100"],tr:["100'e Endeksli"]}},46850:e=>{e.exports={en:["Indicators value labels"],tr:["Gösterge değer etiketleri"]}},54418:e=>{e.exports={en:["Indicators name labels"],tr:["Göstergelerin ad etiketleri"]}},40490:e=>{e.exports={en:["Indicators, Metrics and Strategies. Press slash"],tr:["Göstergeler, Metrikler ve Stratejiler. Eğik çizgiye basın"]}},15992:e=>{e.exports={en:["Info Line"],tr:["Bilgi Çizgisi"]}},87829:e=>{e.exports={en:["Insert indicator"],tr:["Gösterge ekle"]}},41686:e=>{e.exports={en:["Inside Pitchfork"],tr:["İç Dirgen"]}},37913:e=>{e.exports={en:["Icon"],tr:["İkon"]}},78326:e=>{e.exports={en:["Istanbul"]}},39585:e=>{e.exports={en:["Johannesburg"]}},14995:e=>{e.exports={en:["Jakarta"],tr:["Cakarta"]}},62310:e=>{e.exports={en:["Jan"],tr:["Oca"]}},36057:e=>{e.exports={en:["Jerusalem"],tr:["Kudüs"]}},53786:e=>{e.exports={en:["Jul"],tr:["Tem"]}},429:e=>{e.exports={en:["Jun"],tr:["Haz"]}},67560:e=>{e.exports={en:["Juneau"]}},62329:e=>{e.exports={en:["On the left"],tr:["Solda"]}},55813:e=>{e.exports={en:["On the right"],tr:["Sağda"]}},64818:e=>{e.exports={en:["Only {availableResolutions} intervals are supported for {ticker}."],tr:["{ticker} için yalnızca {availableResolutions} aralığı desteklenmektedir."]}},21064:e=>{e.exports={en:["Oops!"],tr:["Eyvah!"]}},51221:e=>{e.exports={en:["Object Tree"],tr:["Nesnelerin Ağacı"]}},12179:e=>{e.exports={en:["Oct"],tr:["Eki"]}},16610:e=>{e.exports={en:["Open"],tr:["Açılış"]}},27884:e=>{e.exports={en:["Open layout. Press period"],tr:["Yerleşimi aç. Noktaya bas"]}},75722:e=>{e.exports={en:["Oslo"]}},65318:e=>{e.exports={en:["Low"],tr:["Düşük"]}},51077:e=>{e.exports={en:["Lock"],tr:["Kilitle"]}},79777:e=>{e.exports={en:["Lock/unlock"],tr:["Kilitle/kilidi aç"]}},66005:e=>{e.exports={en:["Lock vertical cursor line by time"],tr:["Dikey imleç çizgisini zamana göre kilitleme"]}},14017:e=>{e.exports={en:["Lock price to bar ratio"],tr:["Fiyat Çubuk Oranını Sabitle"]}},16170:e=>{e.exports={en:["Logarithmic"],tr:["Logaritmik"]}},19439:e=>{e.exports={en:["London"],tr:["Londra"]}},74832:e=>{e.exports={en:["Long Position"],tr:["Alış Pozisyonu"]}},28733:e=>{e.exports={en:["Los Angeles"]}},85924:e=>{e.exports={en:["Label Down"],tr:["Aşağı Etiket"]}},52402:e=>{e.exports={en:["Label Up"],tr:["Yukarı Etiket"]}},5119:e=>{e.exports={
en:["Labels"],tr:["Etiketler"]}},19931:e=>{e.exports={en:["Lagos"]}},63815:e=>{e.exports={en:["Last day change"],tr:["Son gün değişimi"]}},59444:e=>{e.exports={en:["Lima"]}},3554:e=>{e.exports={en:["Line"],tr:["Çizgi"]}},9394:e=>{e.exports={en:["Line with markers"],tr:["İşaretli çizgi"]}},43588:e=>{e.exports={en:["Line break"],tr:["Çizgi kesme"]}},56982:e=>{e.exports={en:["Lines"],tr:["Çizgiler"]}},37367:e=>{e.exports={en:["Link to the chart image copied to clipboard {emoji}"],tr:["Panoya kopyalanan grafik resmine bağlantı {emoji}"]}},53375:e=>{e.exports={en:["Lisbon"],tr:["Lizbon"]}},81038:e=>{e.exports={en:["Luxembourg"],tr:["Lüksemburg"]}},60663:e=>{e.exports={en:["Move the point to position the anchor then tap to place"],tr:["Çapayı konumlandırmak için noktayı hareket ettirin ve ardından yerleştirmek için dokunun"]}},35049:e=>{e.exports={en:["Move to"],tr:["Taşı"]}},26493:e=>{e.exports={en:["Move scale to left"],tr:["Ölçeği sola taşı"]}},40789:e=>{e.exports={en:["Move scale to right"],tr:["Ölçeği sağa taşı"]}},57681:e=>{e.exports={en:["Modified Schiff Pitchfork"],tr:["Değiştirilmiş Schiff Dirgeni"]}},93907:e=>{e.exports={en:["More settings"],tr:["Daha fazla ayar"]}},64039:e=>{e.exports={en:["Moscow"],tr:["Moskova"]}},52066:e=>{e.exports={en:["Madrid"]}},38365:e=>{e.exports={en:["Malta"]}},48991:e=>{e.exports={en:["Manila"]}},92767:e=>{e.exports={en:["Mar"]}},73332:e=>{e.exports={en:["Mexico City"],tr:["Meksiko"]}},88314:e=>{e.exports={en:["Merge all scales into one"],tr:["Tüm ölçekleri tek birinde birleştir"]}},54215:e=>{e.exports={en:["Mixed"],tr:["Karışık"]}},24866:e=>{e.exports={en:["Micro"],tr:["Mikro"]}},87957:e=>{e.exports={en:["Millennium"],tr:["Binyıl"]}},14724:e=>{e.exports={en:["Minuette"],tr:["Birkaç Saatlık"]}},78273:e=>{e.exports={en:["Minuscule"],tr:["Ufacık"]}},9865:e=>{e.exports={en:["Muscat"],tr:["Muskat"]}},96935:e=>{e.exports={en:["N/A"],tr:["U/D"]}},36252:e=>{e.exports={en:["No data here"],tr:["Burada veri yok"]}},11254:e=>{e.exports={en:["No scale (fullscreen)"],tr:["Ölçek yok (Tam Ekran)"]}},9140:e=>{e.exports={en:["No sync"],tr:["Senkronizasyon yok"]}},50910:e=>{e.exports={en:["No volume data"],tr:["Hacim verisi yok"]}},99024:e=>{e.exports={en:["No, keep them"],tr:["Hayır, silme"]}},94389:e=>{e.exports={en:["Note"],tr:["Not"]}},26899:e=>{e.exports={en:["Nov"],tr:["Kas"]}},67891:e=>{e.exports={en:["Norfolk Island"],tr:["Norfolk Adaları"]}},40977:e=>{e.exports={en:["Nairobi"]}},40544:e=>{e.exports={en:["New York"]}},66103:e=>{e.exports={en:["New Zealand"],tr:["Yeni Zelanda"]}},15512:e=>{e.exports={en:["New pane above"],tr:["Yukarıda yeni pano"]}},52160:e=>{e.exports={en:["New pane below"],tr:["Aşağıda yeni pano"]}},94600:e=>{e.exports={en:["Nicosia"],tr:["Lefkoşa"]}},73013:e=>{e.exports={en:["Something went wrong"],tr:["Birşeyler ters gitti"]}},83524:e=>{e.exports={en:["Something went wrong when creating the indicator."],tr:["Gösterge oluşturulurken bir şeyler yanlış gitti."]}},4509:e=>{e.exports={en:["Something went wrong. Please try again later."],
tr:["Bir şeyler ters gitti. Lütfen daha sonra tekrar deneyiniz."]}},43047:e=>{e.exports={en:["Save New Chart Layout"],tr:["Yeni Grafik Yerleşimini Sakla"]}},76266:e=>{e.exports={en:["Save as"],tr:["Yeni adla sakla"]}},55502:e=>{e.exports={en:["San Salvador"]}},30231:e=>{e.exports={en:["Santiago"]}},91912:e=>{e.exports={en:["Sao Paulo"]}},43931:e=>{e.exports={en:["Scale currency"],tr:["Ölçek para birimi"]}},43758:e=>{e.exports={en:["Scale price chart only"],tr:["Sadece fiyat grafiğini ölçeklendir"]}},40012:e=>{e.exports={en:["Scale unit"],tr:["Ölçek birimi"]}},42608:e=>{e.exports={en:["Schiff Pitchfork"],tr:["Schiff Dirgeni"]}},76078:e=>{e.exports={en:["Script may be not updated if you leave the page."],tr:["Bu sayfadan ayrıldığınızda komut güncellenmeyebilir"]}},32514:e=>{e.exports={en:["Settings"],tr:["Ayarlar"]}},70784:e=>{e.exports={en:["Second fraction part is invalid."],tr:["İkinci ondalık kısmı geçersiz."]}},75594:e=>{e.exports={en:["Security info"],tr:["Sembol bilgisi"]}},21973:e=>{e.exports={en:["Send to back"],tr:["Geri gönder"]}},71179:e=>{e.exports={en:["Send backward"],tr:["Geriye gönder"]}},26820:e=>{e.exports={en:["Seoul"],tr:["Seul"]}},6816:e=>{e.exports={en:["Sep"],tr:["Eyl"]}},94031:e=>{e.exports={en:["Session"],tr:["Seans"]}},83298:e=>{e.exports={en:["Session volume profile"],tr:["Seans hacmi profili"]}},66707:e=>{e.exports={en:["Session breaks"],tr:["Seans araları"]}},1852:e=>{e.exports={en:["Shanghai"],tr:["Şangay"]}},8075:e=>{e.exports={en:["Short Position"],tr:["Satış Pozisyonu"]}},98334:e=>{e.exports={en:["Show"],tr:["Göster"]}},85891:e=>{e.exports={en:["Show all drawings"],tr:["Tüm çizimleri göster"]}},25881:e=>{e.exports={en:["Show all drawings and indicators"],tr:["Tüm çizimleri ve göstergeleri göster"]}},86738:e=>{e.exports={en:["Show all drawings, indicators, positions & orders"],tr:["Tüm çizimleri, göstergeleri, pozisyonları ve emirleri göster"]}},98753:e=>{e.exports={en:["Show all indicators"],tr:["Tüm göstergeleri göster"]}},55418:e=>{e.exports={en:["Show all ideas"],tr:["Tüm fikirleri göster"]}},20506:e=>{e.exports={en:["Show all positions & orders"],tr:["Tüm pozisyonları ve emirleri göster"]}},33158:e=>{e.exports={en:["Show continuous contract switch"],tr:["Sürekli sözleşme anahtarını göster"]}},81465:e=>{e.exports={en:["Show contract expiration"],tr:["Sözleşmenin bitiş tarihini göster"]}},29449:e=>{e.exports={en:["Show dividends"],tr:["Temettüleri göster"]}},37113:e=>{e.exports={en:["Show earnings"],tr:["Kazançları göster"]}},10261:e=>{e.exports={en:["Show ideas of followed users"],tr:["Takip edilen üyenin fikirlerini göster"]}},62986:e=>{e.exports={en:["Show latest news"],tr:["Son haberleri göster"]}},44020:e=>{e.exports={en:["Show my ideas only"],tr:["Sadece benim fikirlerimi göster"]}},50849:e=>{e.exports={en:["Show splits"],tr:["Bölünmeleri göster"]}},67751:e=>{e.exports={en:["Signpost"],tr:["Levha"]}},77377:e=>{e.exports={en:["Singapore"],tr:["Singapur"]}},39090:e=>{e.exports={en:["Sine Line"],tr:["Sinüs Çizgisi"]}},66205:e=>{e.exports={en:["Square"],tr:["Kare"]}},86146:e=>{
e.exports={en:["Studies limit exceeded: {number} studies per layout.\nPlease, remove some studies."],tr:["Grafik başına {number} çalışma limiti aşıldı. Lütfen bazı çalışmaları kaldırın."]}},92516:e=>{e.exports={en:["Style"],tr:["Stil"]}},61507:e=>{e.exports={en:["Stack on the left"],tr:["Sola istifle"]}},97800:e=>{e.exports={en:["Stack on the right"],tr:["Sağa istifle"]}},85166:e=>{e.exports={en:["Start using keyboard navigation mode. Press {shortcut}"],tr:["Klavye navigasyon modunu kullanmaya başlayın. {shortcut} tuşuna basın"]}},4035:e=>{e.exports={en:["Stay in drawing mode"],tr:["Çizim modunda kal"]}},69217:e=>{e.exports={en:["Step line"],tr:["Adım çizgisi"]}},43114:e=>{e.exports={en:["Sticker"]}},86716:e=>{e.exports={en:["Stockholm"],tr:["Stokholm"]}},1145:e=>{e.exports={en:["Submicro"],tr:["Mikroaltı"]}},63375:e=>{e.exports={en:["Submillennium"],tr:["Sub-binyıllık"]}},30585:e=>{e.exports={en:["Subminuette"],tr:["Birkaç Dakikalık"]}},67948:e=>{e.exports={en:["Supercycle"],tr:["Birkaç On Yıllık"]}},3348:e=>{e.exports={en:["Supermillennium"],tr:["Super-binyılık"]}},18905:e=>{e.exports={en:["Switch to {resolution}"],tr:["{resolution} çözünürlüğüne geç"]}},31622:e=>{e.exports={en:["Sydney"],tr:["Sidney"]}},70963:e=>{e.exports={en:["Symbol Error"],tr:["Sembol Hatası"]}},32390:e=>{e.exports={en:["Symbol name label"],tr:["Sembol ismi etiketi"]}},10127:e=>{e.exports={en:["Symbol last price label"],tr:["Sembol son fiyat etiketi"]}},39079:e=>{e.exports={en:["Sync globally"],tr:["Global eşitleme"]}},46607:e=>{e.exports={en:["Sync in layout"],tr:["Tüm Grafiklerle Senkronize Et"]}},76519:e=>{e.exports={en:["Point & figure"],tr:["Nokta & şekil"]}},39949:e=>{e.exports={en:["Polyline"],tr:["Çoklu çizgi"]}},371:e=>{e.exports={en:["Path"],tr:["Dosya konumu"]}},59256:e=>{e.exports={en:["Parallel Channel"],tr:["Paralel Kanal"]}},61879:e=>{e.exports={en:["Paris"]}},35140:e=>{e.exports={en:["Paste"],tr:["Yapıştır"]}},6919:e=>{e.exports={en:["Percent"],tr:["Yüzde"]}},24436:e=>{e.exports={en:["Perth"]}},14055:e=>{e.exports={en:["Phoenix"]}},34156:e=>{e.exports={en:["Pitchfan"],tr:["Basamak Fanı"]}},19634:e=>{e.exports={en:["Pitchfork"],tr:["Dirgen"]}},86631:e=>{e.exports={en:["Pin"],tr:["İğne"]}},33110:e=>{e.exports={en:["Pin to new left scale"],tr:["Yeni Sol Ölçeğe Bağla"]}},28280:e=>{e.exports={en:["Pin to new right scale"],tr:["Yeni Sağ Ölçeğe Bağla"]}},14115:e=>{e.exports={en:["Pin to left scale"],tr:["Sol ölçeğe bağla"]}},72046:e=>{e.exports={en:["Pin to left scale (hidden)"],tr:["Sol Ölçeğe Bağla (Gizli)"]}},81054:e=>{e.exports={en:["Pin to right scale"],tr:["Sağ ölçeğe sabitle"]}},16986:e=>{e.exports={en:["Pin to right scale (hidden)"],tr:["Sağ Ölçeğe Bağla (Gizli)"]}},60035:e=>{e.exports={en:["Pin to scale (now left)"],tr:["Ölçeğe bağla (Şimdi Sola)"]}},94210:e=>{e.exports={en:["Pin to scale (now no scale)"],tr:["Ölçeğe Bağla (Şimdi Ölçeksiz)"]}},10761:e=>{e.exports={en:["Pin to scale (now right)"],tr:["Ölçeğe Bağla (Şimdi Sağa)"]}},76150:e=>{e.exports={en:["Pin to scale (now {label})"],tr:["Ölçeğe bağla (şimdi {label})"]}},
29436:e=>{e.exports={en:["Pin to scale {label}"],tr:["{label} ölçeğe bağla"]}},2165:e=>{e.exports={en:["Pin to scale {label} (hidden)"],tr:["{label} Ölçeğe Bağla (Gizli)"]}},90095:e=>{e.exports={en:["Pinned to left scale"],tr:["Sol Ölçeğe Bağlı"]}},32538:e=>{e.exports={en:["Pinned to left scale (hidden)"],tr:["Sol ölçeğe bağlı (gizli)"]}},44579:e=>{e.exports={en:["Pinned to right scale"],tr:["Sağ ölçeğe bağlı"]}},94559:e=>{e.exports={en:["Pinned to right scale (hidden)"],tr:["Sağ Ölçeğe Bağla (Gizli)"]}},12645:e=>{e.exports={en:["Pinned to scale {label}"],tr:["{label} ölçeğe bağlı"]}},3564:e=>{e.exports={en:["Pinned to scale {label} (hidden)"],tr:["{label} Ölçeğe Bağlı (Gizli)"]}},71566:e=>{e.exports={en:["Plus button"],tr:["Artı Butonu"]}},28298:e=>{e.exports={en:["Please give us a clipboard writing permission in your browser or press {keystroke}"],tr:["Lütfen bize tarayıcınızda panoya yazma izni verin veya {keystroke} tuşuna basın"]}},81248:e=>{e.exports={en:["Prague"],tr:["Prag"]}},81712:e=>{e.exports={en:["Press and hold {key} while zooming to maintain the chart position"],tr:["Grafik konumunu korumak için yakınlaştırma yaparken {key} tuşunu basılı tutun"]}},91282:e=>{e.exports={en:["Price Label"],tr:["Fiyat Etiketi"]}},97512:e=>{e.exports={en:["Price Note"],tr:["Fiyat Notu"]}},68941:e=>{e.exports={en:["Price Range"],tr:["Fiyat Aralığı"]}},66123:e=>{e.exports={en:["Price format is invalid."],tr:["Fiyat biçimi geçersiz."]}},72926:e=>{e.exports={en:["Price line"],tr:["Fiyat Çizgisi"]}},59189:e=>{e.exports={en:["Primary"],tr:["Birkaç Aylık"]}},75747:e=>{e.exports={en:["Projection"],tr:["Projeksiyon"]}},55801:e=>{e.exports={en:["Published on {customer}, {date}"],tr:["{customer}, {date} tarihinde yayınlandı"]}},14568:e=>{e.exports={en:["Q1"],tr:["Ç1"]}},13534:e=>{e.exports={en:["Q2"],tr:["Ç2"]}},14530:e=>{e.exports={en:["Q3"],tr:["Ç3"]}},3762:e=>{e.exports={en:["Q4"],tr:["Ç4"]}},28756:e=>{e.exports={en:["Qatar"],tr:["Katar"]}},57959:e=>{e.exports={en:["Quick search. Press {shortcut}"],tr:["Hızlı arama. {shortcut} tuşuna basın"]}},56820:e=>{e.exports={en:["Rotated Rectangle"],tr:["Döndürülmüş Dikdörtgen"]}},52961:e=>{e.exports={en:["Rome"],tr:["Roma"]}},50318:e=>{e.exports={en:["Ray"],tr:["Işın"]}},55169:e=>{e.exports={en:["Range"],tr:["Aralık"]}},13386:e=>{e.exports={en:["Reykjavik"],tr:["Reykavik"]}},26001:e=>{e.exports={en:["Rectangle"],tr:["Dikdörtgen"]}},48236:e=>{e.exports={en:["Redo"],tr:["Yinele"]}},2460:e=>{e.exports={en:["Regression Trend"],tr:["Regresyon Trendi"]}},67410:e=>{e.exports={en:["Remove"],tr:["Kaldır"]}},3061:e=>{e.exports={en:["Remove this financial metric from favorites"],tr:["Bu finansal ölçümü favorilerden kaldırın"]}},58764:e=>{e.exports={en:["Remove this indicator from favorites"],tr:["Bu göstergeyi favorilerden kaldırın"]}},86285:e=>{e.exports={en:["Remove {drawings}"],tr:["{drawings}'i kaldır"]}},87796:e=>{e.exports={en:["Remove {drawings} & {indicators}"],tr:["{drawings} ve {indicators}'i kaldırın"]}},87797:e=>{e.exports={en:["Remove {indicators}"],tr:["{indicators}'i kaldır"]}},
22584:e=>{e.exports={en:["Rename Chart Layout"],tr:["Grafik Yerleşimine Yeni Ad Ver"]}},88130:e=>{e.exports={en:["Renko"]}},75246:e=>{e.exports={en:["Reset chart view"],tr:["Grafik görünümünü sıfırla"]}},88853:e=>{e.exports={en:["Reset points"],tr:["Noktaları sıfırla"]}},15332:e=>{e.exports={en:["Reset price scale"],tr:["Fiyat ölçeğini sıfırla"]}},54170:e=>{e.exports={en:["Reset time scale"],tr:["Zaman ölçeğini sıfırla"]}},37974:e=>{e.exports={en:["Riyadh"],tr:["Riyad"]}},94022:e=>{e.exports={en:["Riga"]}},60630:e=>{e.exports={en:["Runtime error"],tr:["Runtime hatası"]}},66719:e=>{e.exports={en:["Warning"],tr:["Dikkat"]}},5959:e=>{e.exports={en:["Warsaw"],tr:["Varşova"]}},98549:e=>{e.exports={en:["Tokelau"]}},69122:e=>{e.exports={en:["Tokyo"]}},10095:e=>{e.exports={en:["Toronto"]}},17981:e=>{e.exports={en:["Table"],tr:["Tablo"]}},11034:e=>{e.exports={en:["Taipei"]}},79995:e=>{e.exports={en:["Tallinn"],tr:["Talin"]}},6686:e=>{e.exports={en:["Tehran"],tr:["Tahran"]}},93553:e=>{e.exports={en:["Template"],tr:["Şablon"]}},81657:e=>{e.exports={en:["The data vendor doesn't provide volume data for this symbol."],tr:["Veri satıcısı bu sembol için hacim verisi sağlamıyor."]}},49947:e=>{e.exports={en:["The publication preview could not be loaded. Please disable your browser extensions and try again."],tr:["Yayın öncesi önizleme yüklenemiyor. Lütfen tarayıcı eklentilerinizi devre dışı bırakın ve tekrar deneyin."]}},99274:e=>{e.exports={en:["The request took too long to process. Ensure you have a stable internet connection. If the issue persists, try decreasing the length of the requested time interval."],tr:["İsteğin işlenmesi çok uzun sürdü. Sabit bir internet bağlantınız olduğundan emin olun. Sorun devam ederse, istenen zaman aralığının uzunluğunu azaltmayı deneyin."]}},43716:e=>{e.exports={en:["There's no data for your selected period and chart timeframe."],tr:["Seçtiğiniz dönem ve grafik zaman aralığı için veri yok."]}},93738:e=>{e.exports={en:["This file is too big. Max size is {value}."],tr:["Bu dosya çok büyük. Maksimum boyut: {value}."]}},59519:e=>{e.exports={en:["This indicator cannot be applied to another indicator."],tr:["Bu göstergeyi başka göstergeye uygulamazsınız"]}},18260:e=>{e.exports={en:["This script contains an error. Please contact its author."],tr:["Bu komut dosyası bir hata içeriyor. Lütfen yazarıyla iletişime geçin."]}},76989:e=>{e.exports={en:["This script is invite-only. To request access, please contact its author."],tr:["Bu komut dosyası yalnızca davetle kullanılabilir. Erişim talebi için lütfen yazarıyla iletişime geçin."]}},47773:e=>{e.exports={en:["This symbol is only available on {linkStart}TradingView{linkEnd}."],tr:["Sembol sadece {linkStart}TradingView'de{linkEnd} bulunabilir."]}},46982:e=>{e.exports={en:["Three Drives Pattern"],tr:["Üç Sürücü Deseni"]}},80254:e=>{e.exports={en:["Tick-based intervals are not available for {ticker}."],tr:["Fiyat adımı bazlı aralıklar {ticker} için kullanılamaz."]}},12806:e=>{e.exports={en:["Time"],tr:["Zaman"]}},20909:e=>{e.exports={en:["Time zone"],
tr:["Saat dilimi"]}},46852:e=>{e.exports={en:["Time Cycles"],tr:["Zaman Döngüleri"]}},17809:e=>{e.exports={en:["Time Price Opportunity"],tr:["Zaman Fiyat Fırsatı"]}},66823:e=>{e.exports={en:["Trade"],tr:["İşlem"]}},7697:e=>{e.exports={en:["TradingView is interactive and has commands to use with a screen reader. The following is a list of keyboard commands available to interact on the platform"],tr:["Tradingview etkileşimlidir ve ekran okuyucu ile kullanılabilecek komutlara sahiptir. Aşağıda, platformda etkileşim kurmak için kullanılabilecek klavye komutlarının bir listesi verilmiştir"]}},35757:e=>{e.exports={en:["Trend Angle"],tr:["Trend Açısı"]}},97339:e=>{e.exports={en:["Trend Line"],tr:["Trend Çizgisi"]}},80583:e=>{e.exports={en:["Trend-Based Fib Extension"],tr:["Trend Tabanlı Fib Uzantısı"]}},72159:e=>{e.exports={en:["Trend-Based Fib Time"],tr:["Trend Tabanlı Fib Uzantısı"]}},1671:e=>{e.exports={en:["Triangle"],tr:["Üçgen"]}},76152:e=>{e.exports={en:["Triangle Down"],tr:["Alçalan Üçgen"]}},90148:e=>{e.exports={en:["Triangle Pattern"],tr:["Üçgen Formasyonu"]}},21236:e=>{e.exports={en:["Triangle Up"],tr:["Yükselen Üçgen"]}},21007:e=>{e.exports={en:["Tunis"],tr:["Tunus"]}},1833:e=>{e.exports={en:["UTC"]}},14804:e=>{e.exports={en:["Undo"],tr:["Geri al"]}},56815:e=>{e.exports={en:["Unexpected error in Deep Backtesting mode. Contact support for more information."],tr:["Derin Geriye Dönük Test modunda beklenmeyen hata. Daha fazla bilgi için desteğe başvurun."]}},15432:e=>{e.exports={en:["Units"],tr:["Birim"]}},11768:e=>{e.exports={en:["Unknown error"],tr:["Bilinmeyen hata"]}},99894:e=>{e.exports={en:["Unlock"],tr:["Kilidi aç"]}},75546:e=>{e.exports={en:["Unsupported interval"],tr:["Desteklenmeyen aralık"]}},8580:e=>{e.exports={en:["User-defined error"],tr:["Kullanıcı tanımlı hata"]}},40693:e=>{e.exports={en:["Volume Profile Fixed Range"],tr:["Birim Profili Sabit Aralığı"]}},39903:e=>{e.exports={en:["Volume Profile indicator available only on our upgraded plans."],tr:["Hacim Profili göstergesi yalnızca yükseltilmiş planlarımızda mevcuttur."]}},93722:e=>{e.exports={en:["Volume candles"],tr:["Hacimli mumlar"]}},69156:e=>{e.exports={en:["Volume data is not provided in BIST MIXED data plan."],tr:["BIST KARMA (MIXED) veri planında hacim verisi sağlanmamaktadır."]}},92763:e=>{e.exports={en:["Volume footprint"],tr:["Hacim ayak izi"]}},32838:e=>{e.exports={en:["Vancouver"]}},29535:e=>{e.exports={en:["Vertical Line"],tr:["Dikey Çizgi"]}},23160:e=>{e.exports={en:["Vienna"],tr:["Viyana"]}},60534:e=>{e.exports={en:["Vilnius"]}},40091:e=>{e.exports={en:["Visibility"],tr:["Görünürlük"]}},54853:e=>{e.exports={en:["Visibility on intervals"],tr:["Aralıklarda görünürlük"]}},58302:e=>{e.exports={en:["Visible on tap"],tr:["Dokunma ile görülebilir"]}},10309:e=>{e.exports={en:["Visible on mouse over"],tr:["Fare geldiğinde görünür"]}},4077:e=>{e.exports={en:["Visual order"],tr:["Görsel Sıra"]}},11316:e=>{e.exports={en:["X Cross"],tr:["X Kesişim"]}},42231:e=>{e.exports={en:["XABCD Pattern"],tr:["XABCD Formasyonu"]}},25059:e=>{e.exports={
en:["You cannot see this pivot timeframe on this resolution"],tr:["Bu çözünürlükte pivot zaman dilimini göremezsiniz"]}},41019:e=>{e.exports={en:["You have locked drawings on this symbol. Do you want to remove the locked drawings too?"],tr:["Bu sembol üzerinde kilitli çizimleriniz var. Kilitli çizimleri de kaldırmak istiyor musunuz?"]}},53168:e=>{e.exports={en:["Yangon"]}},93123:e=>{e.exports={en:["Yes, remove them"],tr:["Evet, sil"]}},62859:e=>{e.exports={en:["Zurich"],tr:["Zürih"]}},47977:e=>{e.exports={en:["change Elliott degree"],tr:["Elliott derecesini değiştir"]}},61557:e=>{e.exports={en:["change no overlapping labels"],tr:["çakışan etiketleri değiştir"]}},76852:e=>{e.exports={en:["change average close price label visibility"],tr:["ortalama kapanış fiyat etiketi görünürlüğünü değiştir"]}},1022:e=>{e.exports={en:["change average close price line visibility"],tr:["ortalama kapanış fiyat çizgisi görünürlüğünü değiştir"]}},69362:e=>{e.exports={en:["change bid and ask labels visibility"],tr:["al sat etiketlerin görünürlüğününü değiştir"]}},52919:e=>{e.exports={en:["change bid and ask lines visibility"],tr:["al sat çizgi görünürlüğünü değiştir"]}},32302:e=>{e.exports={en:["change currency"],tr:["para birimi değiştir"]}},68846:e=>{e.exports={en:["change chart layout to {title}"],tr:["grafik düzenini {title} değiştir"]}},18867:e=>{e.exports={en:["change continuous contract switch visibility"],tr:["sürekli sözleşme anahtarı görünürlüğünü değiştir"]}},39383:e=>{e.exports={en:["change countdown to bar close visibility"],tr:["yakın görünürlüğü engellemek için geri sayımı değiştir"]}},16979:e=>{e.exports={en:["change date range"],tr:["tarih aralığını değiştir"]}},53929:e=>{e.exports={en:["change dividends visibility"],tr:["temettü görünürlüğünü değiştir"]}},6119:e=>{e.exports={en:["change events visibility on chart"],tr:["grafikteki olayların görünürlüğünü değiştir"]}},6819:e=>{e.exports={en:["change earnings visibility"],tr:["kazanç görünürlüğünü değiştir"]}},85532:e=>{e.exports={en:["change futures contract expiration visibility"],tr:["vadeli işlem sözleşmesi vade sonu görünürlüğünü değiştir"]}},24226:e=>{e.exports={en:["change high and low price labels visibility"],tr:["yüksek ve düşük fiyat etiketlerinin görünürlüğünü değiştir"]}},80692:e=>{e.exports={en:["change high and low price lines visibility"],tr:["yüksek ve düşük fiyat çizgilerinin görünürlüğünü değiştir"]}},24893:e=>{e.exports={en:["change indicators name labels visibility"],tr:["göstergelerin isim etiketleri görünürlüğünü değiştir"]}},64729:e=>{e.exports={en:["change indicators value labels visibility"],tr:["göstergeleri değer etiketleri görünürlüğünü değiştir"]}},50243:e=>{e.exports={en:["change latest news and Minds visibility"],tr:["en son haberleri ve Görüşler görünürlüğünü değiştirin"]}},88849:e=>{e.exports={en:["change linking group"],tr:["bağlantı grubunu değiştir"]}},14691:e=>{e.exports={en:["change pane height"],tr:["bölme yüksekliğini değiştir"]}},96379:e=>{e.exports={en:["change plus button visibility"],tr:["artı düğme görünürlüğünü değiştir"]}},
76660:e=>{e.exports={en:["change point"],tr:["değişim noktası"]}},30870:e=>{e.exports={en:["change pre/post market price label visibility"],tr:["piyasa öncesi/sonrası fiyat etiketi görünürlüğünü değiştir"]}},11718:e=>{e.exports={en:["change pre/post market price line visibility"],tr:["piyasa öncesi/sonrası fiyat çizgisi görünürlüğünü değiştir"]}},58419:e=>{e.exports={en:["change previous close price line visibility"],tr:["önceki kapanış fiyat çizgisi görünürlüğünü değiştir"]}},8662:e=>{e.exports={en:["change price line visibility"],tr:["fiyat çizgi görünürlüğünü değiştir"]}},2509:e=>{e.exports={en:["change price to bar ratio"],tr:["fiyatı çubuk oranını değiştir"]}},32829:e=>{e.exports={en:["change resolution"],tr:["çözünürlüğü değiştir"]}},35400:e=>{e.exports={en:["change symbol"],tr:["Sembolu değiştir"]}},73357:e=>{e.exports={en:["change symbol labels visibility"],tr:["sembol etiketlerinin görünürlüğünü değiştir"]}},67453:e=>{e.exports={en:["change symbol last value visibility"],tr:["sembolün son değer görünürlüğünü değiştir"]}},4729:e=>{e.exports={en:["change symbol previous close value visibility"],tr:["sembolün önceki yakın değer görünürlüğü değiştir"]}},87041:e=>{e.exports={en:["change session"],tr:["oturumu değiştir"]}},38413:e=>{e.exports={en:["change session breaks visibility"],tr:["oturum sonu görünürlüğü değiştir"]}},49965:e=>{e.exports={en:["change series style"],tr:["seri stilini değiştir"]}},47474:e=>{e.exports={en:["change splits visibility"],tr:["bölme görünürlüğünü değiştir"]}},20137:e=>{e.exports={en:["change timezone"],tr:["saat dilimini değiştir"]}},85975:e=>{e.exports={en:["change unit"],tr:["birim değiştir"]}},1924:e=>{e.exports={en:["change visibility"],tr:["görünürlük değiştir"]}},84331:e=>{e.exports={en:["change visibility at current interval"],tr:["geçerli aralıktaki görünürlüğü değiştir"]}},45800:e=>{e.exports={en:["change visibility at current interval and above"],tr:["mevcut aralıkta ve üzerinde görünürlüğü değiştir"]}},75645:e=>{e.exports={en:["change visibility at current interval and below"],tr:["mevcut aralıkta ve altında görünürlüğü değiştir"]}},57916:e=>{e.exports={en:["change visibility at all intervals"],tr:["tüm aralıklardaki görünürlüğü değiştir"]}},94566:e=>{e.exports={en:["charts by TradingView"],tr:["grafik sağlayıcı TradingView"]}},32943:e=>{e.exports={en:["clone line tools"],tr:["çizgi araçlarını klonla"]}},46219:e=>{e.exports={en:["create line tools group"],tr:["çizim araçları grubu oluştur"]}},95394:e=>{e.exports={en:["create line tools group from selection"],tr:["seçimden çizim araçları grubu oluşturma"]}},12898:e=>{e.exports={en:["create {tool}"],tr:["{tool} oluştur"]}},94227:e=>{e.exports={en:["cut sources"],tr:["kaynak kes"]}},11500:e=>{e.exports={en:["cut {title}"],tr:["{title} kes"]}},63869:e=>{e.exports={en:["anchor objects"],tr:["Objeleri sabitle"]}},12570:e=>{e.exports={en:["add line tool {lineTool} to group {name}"],tr:["{name} grubuna {lineTool} çizim aracı ekle"]}},21162:e=>{e.exports={en:["add line tool(s) to group {group}"],
tr:["{group} grubuna satır araç(ları) ekle"]}},67608:e=>{e.exports={en:["add this financial metric to entire layout"],tr:["Bu finansalı tüm yerleşime ekle"]}},96677:e=>{e.exports={en:["add this indicator to entire layout"],tr:["Bu İndikatörü Tüm Yerleşime Ekle"]}},58156:e=>{e.exports={en:["add this strategy to entire layout"],tr:["Bu Stratejiyi Tüm Yerleşime Ekle"]}},79290:e=>{e.exports={en:["add this symbol to entire layout"],tr:["Bu sembolü tüm yerleşime ekle"]}},4128:e=>{e.exports={en:["align to 45 degrees"],tr:["45 dereceye hizala"]}},68231:e=>{e.exports={en:["apply chart theme"],tr:["grafik temasını uygula"]}},99551:e=>{e.exports={en:["apply all chart properties"],tr:["tüm grafik özelliğini uygula"]}},89720:e=>{e.exports={en:["apply drawing template"],tr:["çizim taslağına uygula"]}},27851:e=>{e.exports={en:["apply factory defaults to selected sources"],tr:["seçilen kaynaklara fabrika varsayılanlarını uygula"]}},70507:e=>{e.exports={en:["apply indicators to entire layout"],tr:["göstergeleri tüm düzene uygula"]}},69604:e=>{e.exports={en:["apply study template {template}"],tr:["çalışma şablonunu uygula {template}"]}},86708:e=>{e.exports={en:["apply toolbars theme"],tr:["araç çubuk teması uygula"]}},1979:e=>{e.exports={en:["bring group {title} forward"],tr:["{title} grubunu öne getir"]}},53159:e=>{e.exports={en:["bring {title} to front"],tr:["{title} öne getir"]}},41966:e=>{e.exports={en:["bring {title} forward"],tr:["{title} 'ı öne getir"]}},44676:e=>{e.exports={en:["by TradingView"],tr:["TradingView'den"]}},58850:e=>{e.exports={en:["date range lock"],tr:["tarih aralığı kilidi"]}},99395:e=>{e.exports={en:["exclude line tools from group {group}"],tr:["çizim araçlarını {group} grubundan hariç tut"]}},13017:e=>{e.exports={en:["hide {title}"],tr:["{title} gizle"]}},62249:e=>{e.exports={en:["hide marks on bars"],tr:["çubuklardaki işaretleri gizle"]}},56558:e=>{e.exports={en:["interval lock"],tr:["aralık kilidi"]}},6830:e=>{e.exports={en:["invert scale"],tr:["Ölçeği Ters Çevir"]}},48818:e=>{e.exports={en:["insert {title}"],tr:["{title} ekle"]}},56307:e=>{e.exports={en:["insert {title} after {targetTitle}"],tr:["{targetTitle}'ın arkasına {title} ekle"]}},32960:e=>{e.exports={en:["insert {title} after {target}"],tr:["{target} 'ten sonra {title} ekle"]}},57106:e=>{e.exports={en:["insert {title} before {target}"],tr:["{target} 'ten sonra {title} ekle"]}},46229:e=>{e.exports={en:["insert {title} before {targetTitle}"],tr:["{targetTitle} 'ten sonra {title} ekle"]}},43364:e=>{e.exports={en:["load default drawing template"],tr:["varsayılan çizim taslağı yükle"]}},62011:e=>{e.exports={en:["loading..."],tr:["yüklüyor..."]}},76104:e=>{e.exports={en:["lock {title}"],tr:["{title} kilitle"]}},20453:e=>{e.exports={en:["lock group {group}"],tr:["satır araçları grubu {group} kaldırılıyor"]}},18942:e=>{e.exports={en:["lock objects"],tr:["nesneleri kilitle"]}},98277:e=>{e.exports={en:["move"],tr:["hareket ettir"]}},58228:e=>{e.exports={en:["move {title} to new left scale"],tr:["{title} yeni sol ölçeğe taşı"]}},77482:e=>{e.exports={
en:["move {title} to new right scale"],tr:["{title} 'ı yeni sağ ölçeğe taşı"]}},64077:e=>{e.exports={en:["move all scales to left"],tr:["tüm ölçekleri sola taşı"]}},19013:e=>{e.exports={en:["move all scales to right"],tr:["tüm ölçekleri sağa taşı"]}},52510:e=>{e.exports={en:["move drawing(s)"],tr:["çizim(leri) taşı"]}},79209:e=>{e.exports={en:["move left"],tr:["Sola hareket et"]}},60114:e=>{e.exports={en:["move right"],tr:["sağa hareket et"]}},44854:e=>{e.exports={en:["move scale"],tr:["ölçeği taşı"]}},10625:e=>{e.exports={en:["make {title} no scale (Full screen)"],tr:["{title} ölçeksiz yap (Tam ekran)"]}},76709:e=>{e.exports={en:["make group {group} invisible"],tr:["{group} grubunu görünmez yap"]}},45987:e=>{e.exports={en:["make group {group} visible"],tr:["{group} grubunu görünür yap"]}},78055:e=>{e.exports={en:["merge down"],tr:["aşağı birleştir"]}},41866:e=>{e.exports={en:["merge to pane"],tr:["bölmede birleştir"]}},52458:e=>{e.exports={en:["merge up"],tr:["birleştir"]}},90091:e=>{e.exports={en:["n/a"]}},94981:e=>{e.exports={en:["scale price"],tr:["ölçek fiyatı"]}},63796:e=>{e.exports={en:["scale price chart only"],tr:["sadece fiyat grafiğini ölçeklendir"]}},70771:e=>{e.exports={en:["scale time"],tr:["ölçek zamanı"]}},42070:e=>{e.exports={en:["scroll"],tr:["kaydır"]}},87840:e=>{e.exports={en:["scroll time"],tr:["kaydırma zamanı"]}},82241:e=>{e.exports={en:["set price scale selection strategy to {title}"],tr:["fiyat ölçeği seçim stratejisi {title} ayarla"]}},40962:e=>{e.exports={en:["send {title} backward"],tr:["{title} 'ı geriye gönder"]}},5005:e=>{e.exports={en:["send {title} to back"],tr:["{title}'ı arkaya gönder"]}},69546:e=>{e.exports={en:["send group {title} backward"],tr:["{title} grubunu geriye gönder"]}},63934:e=>{e.exports={en:["share line tools globally"],tr:["hat araçlarını küresel olarak paylaş"]}},90221:e=>{e.exports={en:["share line tools in layout"],tr:["düzende satır araçlarını paylaş"]}},13336:e=>{e.exports={en:["show all ideas"],tr:["tüm fikirleri göster"]}},91395:e=>{e.exports={en:["show ideas of followed users"],tr:["takip edilen kullanıcıların fikirlerini göster"]}},57460:e=>{e.exports={en:["show my ideas only"],tr:["sadece fikirlerimi göster"]}},4114:e=>{e.exports={en:["stay in drawing mode"],tr:["çizim modunda kal"]}},3350:e=>{e.exports={en:["stop syncing drawing"],tr:["çizim eşitleme durdur"]}},49183:e=>{e.exports={en:["stop syncing line tool(s)"],tr:["çizgi araç(lar)ı eşitlemeyi durdur"]}},53278:e=>{e.exports={en:["symbol lock"],tr:["sembol kilidi"]}},91677:e=>{e.exports={en:["sync time"],tr:["senkronizasyon zamanı"]}},3140:e=>{e.exports={en:["powered by"],tr:["sağlayıcı"]}},92800:e=>{e.exports={en:["powered by TradingView"],tr:["grafiği sağlayan TradingView"]}},62192:e=>{e.exports={en:["paste drawing"],tr:["çizimi yapıştır"]}},1064:e=>{e.exports={en:["paste indicator"],tr:["gösterge yapıştır"]}},57010:e=>{e.exports={en:["paste {title}"],tr:["{title} yapıştır"]}},78690:e=>{e.exports={en:["pin to left scale"],tr:["sol ölçeğe sabitle"]}},7495:e=>{e.exports={en:["pin to right scale"],
tr:["Sağ ölçeğe bağla"]}},81566:e=>{e.exports={en:["pin to scale {label}"],tr:["{label}'e ölçeklendir"]}},2618:e=>{e.exports={en:["rearrange panes"],tr:["bölmeyi tekrar düzenle"]}},60806:e=>{e.exports={en:["remove all indicators"],tr:["tüm göstergeleri kaldırın"]}},29096:e=>{e.exports={en:["remove all indicators and drawing tools"],tr:["tüm göstergeleri ve çizim araçlarını kaldırın"]}},27171:e=>{e.exports={en:["remove deselected empty line tools"],tr:["seçilmeyen boş satır araçlarını kaldır"]}},30538:e=>{e.exports={en:["remove drawings"],tr:["çizimleri kaldır"]}},1193:e=>{e.exports={en:["remove drawings group"],tr:["çizim grubunu kaldır"]}},38199:e=>{e.exports={en:["remove line data sources"],tr:["çizgi veri kaynaklarını kaldır"]}},93333:e=>{e.exports={en:["remove pane"],tr:["bölmeyi kaldır"]}},94543:e=>{e.exports={en:["remove {title}"],tr:["{title} kaldır"]}},41430:e=>{e.exports={en:["removing line tools group {name}"],tr:["satır araçları grubu {name} kaldırılıyor"]}},80491:e=>{e.exports={en:["rename group {group} to {newName}"],tr:["{group} grubunu {newName} olarak yeniden adlandırın"]}},85366:e=>{e.exports={en:["reset layout sizes"],tr:["düzen boyutlarını sıfırla"]}},3323:e=>{e.exports={en:["reset scales"],tr:["ölçekleri sıfırla"]}},17336:e=>{e.exports={en:["reset time scale"],tr:["Zaman Ölçeğini Sıfırla"]}},47418:e=>{e.exports={en:["resize layout"],tr:["düzeni yeniden boyutlandır"]}},85815:e=>{e.exports={en:["restore defaults"],tr:["varsayılanları geri yükle"]}},96881:e=>{e.exports={en:["restore study defaults"],tr:["çalışma varsayılanlarını geri yükle"]}},63095:e=>{e.exports={en:["toggle maximized pane state"],tr:["büyütülmüş bölme durumunu değiştir"]}},42240:e=>{e.exports={en:["toggle auto scale"],tr:["otomatik ölçeklendirmeyi aç/kapat"]}},46054:e=>{e.exports={en:["toggle collapsed pane state"],tr:["bölme durumunu değiştir"]}},24736:e=>{e.exports={en:["toggle indexed to 100 scale"],tr:["100 ölçeğe indekslenmiş geçiş"]}},49695:e=>{e.exports={en:["toggle lock scale"],tr:["geçiş kilidi ölçeği"]}},49403:e=>{e.exports={en:["toggle log scale"],tr:["log ölçeğini değiştir"]}},98994:e=>{e.exports={en:["toggle percentage scale"],tr:["yüzde skalası aç/kapat"]}},80688:e=>{e.exports={en:["toggle regular scale"],tr:["normal ölçeği aç/kapat"]}},46807:e=>{e.exports={en:["track time"],tr:["takip süresi"]}},8040:e=>{e.exports={en:["turn line tools sharing off"],tr:["hat araçları paylaşımını kapat"]}},99234:e=>{e.exports={en:["unanchor objects"],tr:["Objeleri serbest bırak"]}},23230:e=>{e.exports={en:["unlock objects"],tr:["nesnelerin kilidini aç"]}},74590:e=>{e.exports={en:["unlock group {group}"],tr:["{group} grubunda kilidi kaldır"]}},12525:e=>{e.exports={en:["unlock {title}"],tr:["{title} kilidini aç"]}},81576:e=>{e.exports={en:["unmerge to new bottom pane"],tr:["yeni alt bölmeye ayır"]}},79443:e=>{e.exports={en:["unmerge up"],tr:["ayır"]}},46453:e=>{e.exports={en:["unmerge down"],tr:["çöz"]}},94656:e=>{e.exports={en:["{chartStyle} chart type isn't currently available for tick-based intervals."],
tr:["Hayır! {chartStyle} grafik türü şu anda fiyat adımlı (tik) tabanlı aralıklar için kullanılamıyor."]}},41643:e=>{e.exports={en:["{count} bars"],tr:["{count} çubukta"]}},83470:e=>{e.exports={en:["{symbol} financials by TradingView"],tr:["{symbol} finansal bilgilerini sağlayan TradingView"]}},40947:e=>{e.exports={en:["{userName} published on {customer}, {date}"],tr:["{userName} {customer}, {date} tarihinde yayınlandı"]}},91084:e=>{e.exports={en:["zoom"],tr:["yakınlaştırma"]}},49856:e=>{e.exports={en:["zoom in"],tr:["yakınlaş"]}},73638:e=>{e.exports={en:["zoom out"],tr:["uzaklaş"]}},41807:e=>{e.exports={en:["day","days"],tr:["gün","gün"]}},42328:e=>{e.exports={en:["hour","hours"],tr:["saat","saat"]}},98393:e=>{e.exports={en:["month","months"],tr:["ay","ay"]}},78318:e=>{e.exports={en:["minute","minutes"],tr:["dakika","dakika"]}},33232:e=>{e.exports={en:["second","seconds"],tr:["saniye","saniye"]}},89937:e=>{e.exports={en:["range","ranges"],tr:["aralık","aralık"]}},48898:e=>{e.exports={en:["week","weeks"],tr:["hafta","hafta"]}},11913:e=>{e.exports={en:["tick","ticks"],tr:["Kademe","Kademe"]}},22299:e=>{e.exports={en:["{amount} drawing","{amount} drawings"],tr:["{amount} çizim","{amount} çizim"]}},68984:e=>{e.exports={en:["{amount} indicator","{amount} indicators"],tr:["{amount} indikatör","{amount} indikatör"]}},58590:e=>{e.exports={en:["{count}m","{count}m"],tr:["{count}a","{count}a"]}},47801:e=>{e.exports={en:["{count}d","{count}d"]}},46766:e=>{e.exports={en:["{count}y","{count}y"]}},56316:e=>{e.exports=Object.create(null),e.exports["#AAPL-symbol-description"]={en:["Apple Inc"]},e.exports["#AMEX:SCHO-symbol-description"]={en:["Schwab Short-Term U.S. Treasury ETF"],tr:["Schwab Kısa-Vade ABD Hazine BYF"]},e.exports["#AMEX:SHYG-symbol-description"]={en:["Shares 0-5 YEAR High Yield Corporate Bond ETF"],tr:["0-5 YIL Yüksek Getir Bileşik Tahvil BYF"]},e.exports["#ASX:XAF-symbol-description"]={en:["S&P/ASX All Australian 50 Index"],tr:["S&P/ASX Tüm Avustralya 50 Endeksi"]},e.exports["#ASX:XAT-symbol-description"]={en:["S&P/ASX All Australian 200 Index"],tr:["S&P/ASX Tüm Avustralya 200 Endeksi"]},e.exports["#ASX:XJO-symbol-description"]={en:["S&P/ASX 200 Index"],tr:["S&P/ASX 200 Endeksi"]},e.exports["#AUDCAD-symbol-description"]={en:["Australian Dollar/Canadian Dollar"],tr:["Avustralya Doları/Kanada Doları"]},e.exports["#AUDCHF-symbol-description"]={en:["Australian Dollar / Swiss Franc"],tr:["Avustralya Doları/İsviçre Frankı"]},e.exports["#AUDJPY-symbol-description"]={en:["Australian Dollar / Japanese Yen"],tr:["Avustralya Doları / Japon Yeni"]},e.exports["#AUDNZD-symbol-description"]={en:["Australian Dollar / New Zealand Dollar"],tr:["Avustralya Doları / Yeni Zelanda Doları"]},e.exports["#AUDRUB-symbol-description"]={en:["Australian Dollar / Russian Ruble"],tr:["Avusturalya Doları / Rus Rublesi"]},e.exports["#AUDUSD-symbol-description"]={en:["Australian Dollar / U.S. Dollar"],tr:["Avustralya Doları / ABD Doları"]},e.exports["#BCBA:IMV-symbol-description"]={en:["S&P MERVAL Index"],tr:["S&P MERVAL Endeksi"]},
e.exports["#BCHEUR-symbol-description"]={en:["Bitcoin Cash / Euro"]},e.exports["#BCHUSD-symbol-description"]={en:["Bitcoin Cash / U.S. Dollar"],tr:["Bitcoin Cash / Dolar"]},e.exports["#BELEX:BELEX15-symbol-description"]={en:["BELEX 15 Index"],tr:["BELEX 15 Endeksi"]},e.exports["#BIST:XU100-symbol-description"]={en:["BIST 100 Index"],tr:["BIST 100 Endeksi"]},e.exports["#BITMEX:XBT-symbol-description"]={en:["Bitcoin / U.S. Dollar Index"],tr:["Bitcoin / ABD Doları Endeksi"]},e.exports["#BME:IBC-symbol-description"]={en:["IBEX 35 Index"],tr:["IBEX 35 Endeksi"]},e.exports["#BMFBOVESPA:IBOV-symbol-description"]={en:["Bovespa Index"],tr:["Bovespa Endeksi"]},e.exports["#BMFBOVESPA:IBRA-symbol-description"]={en:["IBrasil Index"],tr:["IBrasil Endeksi"]},e.exports["#BMFBOVESPA:IBXL-symbol-description"]={en:["Brazil 50 Index"],tr:["Brezilya 50 Endeksi"]},e.exports["#BMV:CT-symbol-description"]={en:["China SX20 RT"]},e.exports["#BMV:F-symbol-description"]={en:["Ford Motor Company"],tr:["Ford Motor Şirketi"]},e.exports["#BMV:ME-symbol-description"]={en:["S&P/BMV IPC Index"],tr:["S&P/BMV IPC Endeksi"]},e.exports["#BRLJPY-symbol-description"]={en:["Brazilian Real / Japanese Yen"],tr:["Brezilya Reali / Japon Yeni"]},e.exports["#BSE:ITI-symbol-description"]={en:["ITI Ltd"]},e.exports["#BSE:SENSEX-symbol-description"]={en:["S&P BSE Sensex Index"],tr:["S&P BSE Sensex Endeksi"]},e.exports["#BTCBRL-symbol-description"]={en:["Bitcoin / Brazilian Real"],tr:["Bitcoin / Brezilya Reali"]},e.exports["#BTCCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"],tr:["Bitcoin / Kanada Doları"]},e.exports["#BTCCNY-symbol-description"]={en:["Bitcoin / Chinese Yuan"],tr:["Bitcoin / Çin Yuanı"]},e.exports["#BTCEUR-symbol-description"]={en:["Bitcoin / Euro"]},e.exports["#BTCGBP-symbol-description"]={en:["Bitcoin / British Pound"],tr:["Bitcoin / İngiliz Poundu"]},e.exports["#BTCJPY-symbol-description"]={en:["Bitcoin / Japanese Yen"],tr:["Bitcoin / Japon Yeni"]},e.exports["#BTCKRW-symbol-description"]={en:["Bitcoin / South Korean Won"],tr:["Bitcoin / Güney Korea Wonu"]},e.exports["#BTCPLN-symbol-description"]={en:["Bitcoin / Polish Zloty"],tr:["Bitcoin / Polonya Zlotisi"]},e.exports["#BTCRUB-symbol-description"]={en:["Bitcoin / Russian Ruble"],tr:["Bitcoin / Rus Rublesi"]},e.exports["#BTCTHB-symbol-description"]={en:["Bitcoin / Thai Baht"],tr:["Bitcoin / Tayland Bahtı"]},e.exports["#BTCUSD-symbol-description"]={en:["Bitcoin / U.S. Dollar"],tr:["Bitcoin / Dolar"]},e.exports["#BTGUSD-symbol-description"]={en:["Bitcoin Gold / U.S. Dollar"],tr:["Bitcoin Gold / U.S. Dolar"]},e.exports["#BVL:SPBLPGPT-symbol-description"]={en:["S&P / BVL Peru General Index (PEN)"],tr:["S&P / BVL Peru Genel Endeksi (PEN)"]},e.exports["#BVSP-symbol-description"]={en:["Brazil Bovespa Index"],tr:["Brezilya Bovespa Endeksi"]},e.exports["#CADJPY-symbol-description"]={en:["Canadian Dollar / Japanese Yen"],tr:["Kanada Doları / Japon Yeni"]},e.exports["#CADUSD-symbol-description"]={en:["Canadian Dollar / U.S. Dollar"],tr:["Kanada Doları / ABD Doları"]},
e.exports["#CBOE:OEX-symbol-description"]={en:["S&P 100 Index"],tr:["S&P 100 Endeksi"]},e.exports["#CBOE:VIX-symbol-description"]={en:["Volatility S&P 500 Index"],tr:["Volatilite S&P 500 Endeksi"]},e.exports["#CBOT:ZB1!-symbol-description"]={en:["T-Bond Futures"],tr:["T-Bond Vadelileri"]},e.exports["#CBOT:ZC1!-symbol-description"]={en:["Corn Futures"],tr:["Mısır Vadelileri"]},e.exports["#CBOT:ZM1!-symbol-description"]={en:["Soybean Meal Futures"],tr:["Soya Küspesi Vadelileri"]},e.exports["#CBOT:ZN1!-symbol-description"]={en:["10 Year T-Note Futures"],tr:["10 Yıllık T-Note Vadelileri"]},e.exports["#CBOT:ZO1!-symbol-description"]={en:["Oat Futures"],tr:["Yulaf Vadelileri"]},e.exports["#CBOT:ZQ1!-symbol-description"]={en:["30 Day Federal Funds Interest Rate Futures"],tr:["30 Günlük Federal Fon Faizi Vadelileri"]},e.exports["#CBOT:ZR1!-symbol-description"]={en:["Rice Futures"],tr:["Pirinç Vadelileri"]},e.exports["#CBOT:ZS1!-symbol-description"]={en:["Soybean Futures"],tr:["Soya Vadelileri"]},e.exports["#CBOT:ZW1!-symbol-description"]={en:["Wheat Futures"],tr:["Buğday Vadelileri"]},e.exports["#CBOT_MINI:XK1!-symbol-description"]={en:["Soybean Mini Futures"],tr:["Soya Mini Vadelileri"]},e.exports["#CBOT_MINI:XW1!-symbol-description"]={en:["Wheat Mini Futures"],tr:["Buğday Mini Vadelileri"]},e.exports["#CBOT_MINI:YM1!-symbol-description"]={en:["E-mini Dow Jones ($5) Futures"],tr:["E-MINI DOW JONES ($5) Vadelileri"]},e.exports["#CHFJPY-symbol-description"]={en:["Swiss Franc / Japanese Yen"],tr:["İsviçre Frankı / Japon Yeni"]},e.exports["#CHFUSD-symbol-description"]={en:["Swiss Franc / U.S. Dollar"],tr:["İsviçre Frankı / ABD Doları"]},e.exports["#CME:BTC1!-symbol-description"]={en:["Bitcoin CME Futures"],tr:["Bitcoin CME Vadelileri"]},e.exports["#CME:CB1!-symbol-description"]={en:["Butter Futures-Cash (Continuous: Current contract in front)"],tr:["Tereyağı Vadeli İşlemleri-Nakit (Sürekli: Öncesinde cari sözleşme)"]},e.exports["#CME:GF1!-symbol-description"]={en:["Feeder Cattle Futures"],tr:["Besi Sığırı Vadelileri"]},e.exports["#CME:HE1!-symbol-description"]={en:["Lean Hogs Futures"],tr:["Yağsız Domuz eti Vadelileri"]},e.exports["#CME:LE1!-symbol-description"]={en:["Live Cattle Futures"],tr:["Canlı Sığır Vadelileri"]},e.exports["#CME_MINI:E71!-symbol-description"]={en:["Euro E-mini Futures"],tr:["Euro E-min Vadelileri"]},e.exports["#CME_MINI:ES1!-symbol-description"]={en:["S&P 500 E-mini Futures"],tr:["S&P 500 E-mini Vadelileri"]},e.exports["#CME_MINI:J71!-symbol-description"]={en:["Japanese Yen E-mini Futures"],tr:["Japon Yeni E-mini Vadelileri"]},e.exports["#CME_MINI:NQ1!-symbol-description"]={en:["NASDAQ 100 E-mini Futures"],tr:["NASDAQ 100 E-MINI Vadelileri"]},e.exports["#CME_MINI:RTY1!-symbol-description"]={en:["E-Mini Russell 2000 Index Futures"],tr:["E-Mini Russell 2000 Endeks Vadeli İşlemleri"]},e.exports["#COMEX:AEP1!-symbol-description"]={en:["Aluminium European Premium Futures"],tr:["Aluminyum Avrupa Premium Vadelileri"]},e.exports["#COMEX:AUP1!-symbol-description"]={
en:["Aluminum MW U.S. Transaction Premium Platts (25MT) Futures"],tr:["Aluminyum MW U.S Transaction Premium Platts (25MT) Vadelileri"]},e.exports["#COMEX:GC1!-symbol-description"]={en:["Gold Futures"],tr:["Altın Vadelileri"]},e.exports["#COMEX:HG1!-symbol-description"]={en:["Copper Futures"],tr:["Bakır Vadelileri"]},e.exports["#COMEX:SI1!-symbol-description"]={en:["Silver Futures"],tr:["Gümüş Vadelileri"]},e.exports["#COMEX_MINI:QC1!-symbol-description"]={en:["E-mini Copper Futures"],tr:["E-mini Bakır Vadelileri"]},e.exports["#COMEX_MINI:QI1!-symbol-description"]={en:["Silver (Mini) Futures"],tr:["Gümüş (Mini) Vadelileri"]},e.exports["#COMEX_MINI:QO1!-symbol-description"]={en:["Gold (Mini) Futures"],tr:["Altın (Mini) Vadelileri"]},e.exports["#COPPER-symbol-description"]={en:["CFDs on Copper"],tr:["Bakır KFS'leri"]},e.exports["#CORNUSD-symbol-description"]={en:["CFDs on Corn"],tr:["Mısır KFS'leri"]},e.exports["#COTUSD-symbol-description"]={en:["CFDs on Cotton"],tr:["Pamuk KFS'leri"]},e.exports["#CRYPTOCAP:TOTAL-symbol-description"]={en:["Crypto Total Market Cap, $"],tr:["Kripto Toplam Piyasa Değeri, $"]},e.exports["#DFM:DFMGI-symbol-description"]={en:["DFM Index"],tr:["DFM Endeksi"]},e.exports["#DJ:DJA-symbol-description"]={en:["Dow Jones Composite Average Index"],tr:["Dow Jones Bileşik Ortalama Endeksi"]},e.exports["#DJ:DJCIAGC-symbol-description"]={en:["Dow Jones Commodity Index Agriculture Capped Component"],tr:["Dow Jones Emtia Endeksi Tarım Başlıklı Bileşenler"]},e.exports["#DJ:DJCICC-symbol-description"]={en:["Dow Jones Commodity Index Cocoa"],tr:["Dow Jones Emtia Endeksi Kakao"]},e.exports["#DJ:DJCIEN-symbol-description"]={en:["Dow Jones Commodity Index Energy"],tr:["Dow Jones Emtia Endeksi Enerji"]},e.exports["#DJ:DJCIGC-symbol-description"]={en:["Dow Jones Commodity Index Gold"],tr:["Dow Jones Emtia Endeksi Altın"]},e.exports["#DJ:DJCIGR-symbol-description"]={en:["Dow Jones Commodity Index Grains"],tr:["Dow Jones Emtia Endeksi Tahıllar"]},e.exports["#DJ:DJCIIK-symbol-description"]={en:["Dow Jones Commodity Index Nickel"],tr:["Dow Jones Emtia Endeksi Nikel"]},e.exports["#DJ:DJCIKC-symbol-description"]={en:["Dow Jones Commodity Index Coffee"],tr:["Dow Jones Emtia Endeksi Kahve"]},e.exports["#DJ:DJCISB-symbol-description"]={en:["Dow Jones Commodity Index Sugar"],tr:["Dow Jones Emtia Endeksi Şeker"]},e.exports["#DJ:DJCISI-symbol-description"]={en:["Dow Jones Commodity Index Silver"],tr:["Dow Jones Emtia Endeksi Gümüş"]},e.exports["#DJ:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"],tr:["Dow Jones Endüstri Ortalaması Endeksi"]},e.exports["#DJ:DJT-symbol-description"]={en:["Dow Jones Transportation Average Index"],tr:["Doe Jones Ulaştırma Ortalama Endeksi"]},e.exports["#DJ:DJU-symbol-description"]={en:["Dow Jones Utility Average Index"],tr:["Dow Jones Hizmet Ortalama Endeksi"]},e.exports["#DJ:DJUSCL-symbol-description"]={en:["Dow Jones U.S. Coal Index"],tr:["Dow Jones ABD Kömür Endeksi"]},e.exports["#EGX:EGX30-symbol-description"]={en:["EGX 30 Index"],tr:["EGX 30 Endeksi"]},
e.exports["#ETCBTC-symbol-description"]={en:["Ethereum Classic / Bitcoin"]},e.exports["#ETCEUR-symbol-description"]={en:["Ethereum Classic / Euro"],tr:["Ethereum Klasik / Euro"]},e.exports["#ETCUSD-symbol-description"]={en:["Ethereum Classic / U.S. Dollar"],tr:["Ethereum Classic / Dolar"]},e.exports["#ETHBTC-symbol-description"]={en:["Ethereum / Bitcoin"]},e.exports["#ETHEUR-symbol-description"]={en:["Ethereum / Euro"]},e.exports["#ETHGBP-symbol-description"]={en:["Ethereum / British Pound"],tr:["Ethereum / İngiliz Poundu"]},e.exports["#ETHJPY-symbol-description"]={en:["Ethereum / Japanese Yen"],tr:["Ethereum / Japon Yeni"]},e.exports["#ETHKRW-symbol-description"]={en:["Ethereum / South Korean Won"],tr:["Ethereum / Güney Kore Wonu"]},e.exports["#ETHTHB-symbol-description"]={en:["Ethereum / Thai Baht"],tr:["Ethereum / Tayland Bahtı"]},e.exports["#ETHUSD-symbol-description"]={en:["Ethereum / U.S. Dollar"],tr:["Ethereum / Dolar"]},e.exports["#EUBUND-symbol-description"]={en:["Euro Bund"]},e.exports["#EURAUD-symbol-description"]={en:["Euro / Australian Dollar"],tr:["Euro / Avustralya Doları"]},e.exports["#EURBRL-symbol-description"]={en:["Euro / Brazilian Real"],tr:["Euro / Brezilya Realı"]},e.exports["#EURCAD-symbol-description"]={en:["Euro / Canadian Dollar"],tr:["Euro / Kanada Doları"]},e.exports["#EURCHF-symbol-description"]={en:["Euro / Swiss Franc"],tr:["Euro / İsviçre Frankı"]},e.exports["#EURGBP-symbol-description"]={en:["Euro / British Pound"],tr:["Euro / İngiliz Sterlini"]},e.exports["#EURJPY-symbol-description"]={en:["Euro / Japanese Yen"],tr:["Euro / Japon Yeni"]},e.exports["#EURNOK-symbol-description"]={en:["Euro / Norwegian Krone"],tr:["Euro / Norveç Kronu"]},e.exports["#EURNZD-symbol-description"]={en:["Euro / New Zealand Dollar"],tr:["Euro / Yeni Zelanda Doları"]},e.exports["#EURONEXT:AEX-symbol-description"]={en:["AEX Index"],tr:["AEX Endeksi"]},e.exports["#EURONEXT:BEL20-symbol-description"]={en:["BEL 20 Index"],tr:["BEL 20 Endeksi"]},e.exports["#EURONEXT:PX1-symbol-description"]={en:["CAC 40 Index"],tr:["CAC 40 Endeksi"]},e.exports["#EURRUB-symbol-description"]={en:["Euro / Russian Ruble"],tr:["EURO / RUS RUBLESİ"]},e.exports["#EURRUB_TOM-symbol-description"]={en:["Euro / Russian Ruble TOM"],tr:["EUR/RUB TOM"]},e.exports["#EURSEK-symbol-description"]={en:["Euro / Swedish Krona"],tr:["Euro / İsveç Kronu"]},e.exports["#EURTRY-symbol-description"]={en:["Euro / Turkish Lira"],tr:["Euro / Türk Lirası"]},e.exports["#EURUSD-symbol-description"]={en:["Euro / U.S. Dollar"],tr:["Euro / ABD Doları"]},e.exports["#EUSTX50-symbol-description"]={en:["Euro Stoxx 50 Index"],tr:["Euro Stoxx 50 Endeksi"]},e.exports["#FOREXCOM:US2000-symbol-description"]={en:["US Small Cap 2000"]},e.exports["#FRA40-symbol-description"]={en:["CAC 40 Index"],tr:["CAC40 Endeksi"]},e.exports["#FRED:GDP-symbol-description"]={en:["Gross Domestic Product, 1 Decimal"],tr:["Gayrisafi Yurt İçi Hasıla, 1 Basamak"]},e.exports["#FRED:POP-symbol-description"]={en:["Total Population: All Ages Including Armed Forces Overseas"],
tr:["Toplam Nüfus: Yurtdışı Silahlı Kuvvetler Dahil Tüm Yaşlar"]},e.exports["#FRED:UNRATE-symbol-description"]={en:["Civilian Unemployment Rate"],tr:["Sivil İşsizlik Oranı"]},e.exports["#FTSEMYX:FBMKLCI-symbol-description"]={en:["FTSE Bursa Malaysia KLCI Index"],tr:["FTSE Malezya Borsası KLCI Endeksi"]},e.exports["#FWB:KT1-symbol-description"]={en:["Key Tronic Corр."],tr:["Key Tronic Corp."]},e.exports["#FX:AUS200-symbol-description"]={en:["S&P/ASX Index"],tr:["S&P/ASX Endeksi"]},e.exports["#FX:US30-symbol-description"]={en:["Dow Jones Industrial Average Index"],tr:["Dow Jones Endüstri Ortalaması Endeksi"]},e.exports["#GBPAUD-symbol-description"]={en:["British Pound / Australian Dollar"],tr:["İngiliz Sterlini / Avustralya Doları"]},e.exports["#GBPCAD-symbol-description"]={en:["British Pound / Canadian Dollar"],tr:["İngiliz Sterlini / Kanada Doları"]},e.exports["#GBPCHF-symbol-description"]={en:["British Pound / Swiss Franc"],tr:["İngiliz Sterlini / İsviçre Frankı"]},e.exports["#GBPEUR-symbol-description"]={en:["British Pound / Euro"],tr:["İngiliz Sterlini / Euro"]},e.exports["#GBPJPY-symbol-description"]={en:["British Pound / Japanese Yen"],tr:["İngiliz Sterlini / Japon Yeni"]},e.exports["#GBPNZD-symbol-description"]={en:["British Pound / New Zealand Dollar"],tr:["İngiliz Sterlini / Yeni Zelanda Doları"]},e.exports["#GBPPLN-symbol-description"]={en:["British Pound / Polish Zloty"],tr:["İngiliz Sterlini / Polonya Zlotisi"]},e.exports["#GBPRUB-symbol-description"]={en:["British Pound / Russian Ruble"],tr:["İngiliz Sterlini / Rus Rublesi"]},e.exports["#GBPUSD-symbol-description"]={en:["British Pound / U.S. Dollar"],tr:["İngiliz Sterlini / ABD Doları"]},e.exports["#GER30-symbol-description"]={en:["DAX Index"],tr:["DAX Almanya listelenmiş hisseler endeksi"]},e.exports["#GOOG-symbol-description"]={en:["Alphabet Inc (Google) Class C"]},e.exports["#GOOGL-symbol-description"]={en:["Alphabet Inc (Google) Class A"],tr:["Google Inc. (Google) Class A"]},e.exports["#GPW:ACG-symbol-description"]={en:["Acautogaz"]},e.exports["#GPW:WIG20-symbol-description"]={en:["WIG20 Index"],tr:["WIG20 Endeksi"]},e.exports["#HSI:HSI-symbol-description"]={en:["Hang Seng Index"],tr:["Hang Seng Endeksi"]},e.exports["#ICEUS:DX1!-symbol-description"]={en:["U.S. Dollar Index Futures"],tr:["ABD Doları Endeks Vadeli İşlemleri"]},e.exports["#IDX:COMPOSITE-symbol-description"]={en:["IDX Composite Index"],tr:["IDX Bileşik Endeksi"]},e.exports["#INDEX:HSCE-symbol-description"]={en:["Hang Seng China Enterprises Index"],tr:["Hang Seng Çin Şirketleri Endeksi"]},e.exports["#INDEX:JKSE-symbol-description"]={en:["Jakarta Composite Index"],tr:["Jakarta Bileşik Endeksi"]},e.exports["#INDEX:KLSE-symbol-description"]={en:["Bursa Malaysia KLCI Index"],tr:["Bursa Malezya KLCI Endeksi"]},e.exports["#INDEX:MIB-symbol-description"]={en:["MIB Index"],tr:["MIB Endeksi"]},e.exports["#INDEX:MOY0-symbol-description"]={en:["Euro Stoxx 50 Index"],tr:["Euro Stoxx 50 Endeksi"]},e.exports["#INDEX:STI-symbol-description"]={en:["STI Index"],tr:["STI Endeksi"]},
e.exports["#INDEX:TWII-symbol-description"]={en:["Taiwan Weighted Index"],tr:["Tayvan Ağırlık Endeksi"]},e.exports["#INDEX:XLY0-symbol-description"]={en:["Shanghai Composite Index"],tr:["Şangay Bileşik Endeksi"]},e.exports["#IOTUSD-symbol-description"]={en:["IOTA / U.S. Dollar"],tr:["IOTA / U.S. Dolar"]},e.exports["#JPN225-symbol-description"]={en:["Japan 225 Index"],tr:["Japan 225 Endeksi"]},e.exports["#JPYKRW-symbol-description"]={en:["Japanese Yen / South Korean Won"],tr:["Japon Yeni / Güney Kore Wonu"]},e.exports["#JPYRUB-symbol-description"]={en:["Japanese Yen / Russian Ruble"],tr:["Japon Yeni / Rus Rublesi"]},e.exports["#JPYUSD-symbol-description"]={en:["Japanese Yen / U.S. Dollar"],tr:["Japon Yeni / ABD Doları"]},e.exports["#LKOH-symbol-description"]={en:["LUKOIL"]},e.exports["#LSE:SCHO-symbol-description"]={en:["Scholium Group Plc Ord 1P"]},e.exports["#LTCBRL-symbol-description"]={en:["Litecoin / Brazilian Real"],tr:["Litecoin / Brezilya Reali"]},e.exports["#LTCBTC-symbol-description"]={en:["Litecoin / Bitcoin"]},e.exports["#LTCUSD-symbol-description"]={en:["Litecoin / U.S. Dollar"],tr:["Litecoin / Dolar"]},e.exports["#LUNAUSD-symbol-description"]={en:["Luna / U.S. Dollar"],tr:["Luna / ABD Doları"]},e.exports["#MOEX:BR1!-symbol-description"]={en:["Brent Oil Futures"],tr:["Brent Petrol Vadelileri"]},e.exports["#MOEX:GAZP-symbol-description"]={en:["GAZPROM"]},e.exports["#MOEX:IMOEX-symbol-description"]={en:["MOEX Russia Index"],tr:["MOEX Rusya Endeksi"]},e.exports["#MOEX:MGNT-symbol-description"]={en:["MAGNIT"]},e.exports["#MOEX:MICEXINDEXCF-symbol-description"]={en:["MOEX Russia Index"],tr:["MOEX Rusya Endeksi"]},e.exports["#MOEX:MX1!-symbol-description"]={en:["MICEX Index Futures"],tr:["MICEX Endeks Vadelileri"]},e.exports["#MOEX:MX2!-symbol-description"]={en:["MICEX Index Futures"],tr:["MICEX Endeks Vadelileri"]},e.exports["#MOEX:RI1!-symbol-description"]={en:["RTS Index Futures"],tr:["RTS Endeks Vadelileri"]},e.exports["#MOEX:RTSI-symbol-description"]={en:["RTS Index"],tr:["RTS Endeksi"]},e.exports["#MOEX:RUAL-symbol-description"]={en:["United Company RUSAL PLC"]},e.exports["#MOEX:SBER-symbol-description"]={en:["SBERBANK"]},e.exports["#MOEX:VTBR-symbol-description"]={en:["VTB"]},e.exports["#MSFT-symbol-description"]={en:["Microsoft Corp."]},e.exports["#NAS100-symbol-description"]={en:["US 100 Cash CFD"],tr:["US 100 Nakit CFD"]},e.exports["#NASDAQ:AMD-symbol-description"]={en:["Advanced Micro Devices Inc"]},e.exports["#NASDAQ:GOOG-symbol-description"]={en:["Alphabet Inc (Google) Class C"]},e.exports["#NASDAQ:GOOGL-symbol-description"]={en:["Alphabet Inc (Google) Class A"]},e.exports["#NASDAQ:HGX-symbol-description"]={en:["PHLX Housing Sector Index"],tr:["PHLX Konut Sektörü Endeksi"]},e.exports["#NASDAQ:IEF-symbol-description"]={en:["Ishares 7-10 Year Treasury Bond ETF"],tr:["iShares 7-10 Yıl Hazine Bonosu BYF"]},e.exports["#NASDAQ:IEI-symbol-description"]={en:["Ishares 3-7 Year Treasury Bond ETF"],tr:["iShares 3-7 Yıl Hazine Bonosu BYF"]},e.exports["#NASDAQ:ITI-symbol-description"]={en:["Iteris Inc"]},
e.exports["#NASDAQ:IXIC-symbol-description"]={en:["Nasdaq Composite Index"],tr:["Nasdaq Composite Endeksi"]},e.exports["#NASDAQ:LCID-symbol-description"]={en:["Lucid Group, Inc."]},e.exports["#NASDAQ:LE-symbol-description"]={en:["Lands' End Inc"]},e.exports["#NASDAQ:NDX-symbol-description"]={en:["Nasdaq 100 Index"],tr:["Nasdaq 100 Endeksi"]},e.exports["#NASDAQ:OSX-symbol-description"]={en:["PHLX Oil Service Sector Index"],tr:["PHLX Petrol Hizmet Sektörü Endeksi"]},e.exports["#NASDAQ:SHY-symbol-description"]={en:["Ishares 1-3 Year Treasury Bond ETF"],tr:["Ishares 1-3 Yıl Hazine Bonosu BYF"]},e.exports["#NASDAQ:SOX-symbol-description"]={en:["Philadelphia Semiconductor Index"],tr:["Philadelphia Yarı İletken Endeksi"]},e.exports["#NASDAQ:TLT-symbol-description"]={en:["Ishares 20+ Year Treasury Bond ETF"],tr:["iShares 20+ Yıl Hazine Bonosu BYF"]},e.exports["#NASDAQ:UTY-symbol-description"]={en:["PHLX Utility Sector Index"],tr:["PHLX Hizmet Sektörü Endeksi"]},e.exports["#NASDAQ:XAU-symbol-description"]={en:["PHLX Gold and Silver Sector Index"],tr:["PHLX Altın ve Gümüş Sektörü Endeksi"]},e.exports["#NASDAQ:ZS-symbol-description"]={en:["Zscaler Inc"]},e.exports["#NEOUSD-symbol-description"]={en:["NEO / U.S. Dollar"],tr:["NEO / ABD Doları"]},e.exports["#NGAS-symbol-description"]={en:["Natural Gas (Henry Hub)"],tr:["Doğal Gaz (Henry Hub)"]},e.exports["#NKY-symbol-description"]={en:["Japan 225 Index"],tr:["Japan 225 Endeksi"]},e.exports["#NSE:ITI-symbol-description"]={en:["Indian Telephone Industries Limited"]},e.exports["#NSE:NIFTY-symbol-description"]={en:["Nifty 50 Index"],tr:["Nifty 50 Endeksi"]},e.exports["#NYMEX:AEZ1!-symbol-description"]={en:["NY Ethanol Futures"],tr:["NY Ethanol Vadelileri"]},e.exports["#NYMEX:CJ1!-symbol-description"]={en:["Cocoa Futures"],tr:["Kakao Vadelileri"]},e.exports["#NYMEX:CL1!-symbol-description"]={en:["Light Crude Oil Futures"],tr:["Hafif Ham Petrol Vadelileri"]},e.exports["#NYMEX:HO1!-symbol-description"]={en:["NY Harbor ULSD Futures"],tr:["NY Liman Çok Düşük Sülfür Dizel Vadelileri"]},e.exports["#NYMEX:KT1!-symbol-description"]={en:["Coffee Futures"],tr:["Kahve Vadelileri"]},e.exports["#NYMEX:NG1!-symbol-description"]={en:["Natural Gas Futures"],tr:["Doğal Gaz Vadelileri"]},e.exports["#NYMEX:PA1!-symbol-description"]={en:["Palladium Futures"],tr:["Palladyum Vadelileri"]},e.exports["#NYMEX:PL1!-symbol-description"]={en:["Platinum Futures"],tr:["Platin Vadelileri"]},e.exports["#NYMEX:RB1!-symbol-description"]={en:["RBOB Gasoline Futures"],tr:["RBOB Benzin Vadelileri"]},e.exports["#NYMEX:TT1!-symbol-description"]={en:["Cotton Futures"],tr:["Pamuk Vadeli İşlemleri"]},e.exports["#NYMEX_MINI:QG1!-symbol-description"]={en:["E-mini Natural Gas Futures"],tr:["E-mini Doğal Gaz Vadelileri"]},e.exports["#NYMEX_MINI:QM1!-symbol-description"]={en:["E-mini Light Crude Oil Futures"],tr:["E-mini Hafif Ham Petrol Vadelileri"]},e.exports["#NYMEX_MINI:QU1!-symbol-description"]={en:["E-mini Gasoline Futures"],tr:["E-mini Gazolin Vadelileri"]},e.exports["#NYSE:BABA-symbol-description"]={
en:["Alibaba Group Holdings Ltd."]},e.exports["#NYSE:F-symbol-description"]={en:["FORD MTR CO DEL"]},e.exports["#NYSE:HE-symbol-description"]={en:["Hawaiian Electric Industries"],tr:["Hawaii Elektrik Endüstrisi"]},e.exports["#NYSE:NYA-symbol-description"]={en:["NYSE Composite Index"],tr:["NYSE Bileşik Endeksi"]},e.exports["#NYSE:PBR-symbol-description"]={en:["PETROLEO BRASILEIRO SA PETROBR"]},e.exports["#NYSE:XAX-symbol-description"]={en:["AMEX Composite Index"],tr:["AMEX Bileşik Endeksi"]},e.exports["#NYSE:XMI-symbol-description"]={en:["NYSE ARCA Major Market Index"],tr:["NYSE ARCA Majör Piyasa Endeksi"]},e.exports["#NZDJPY-symbol-description"]={en:["New Zealand Dollar / Japanese Yen"],tr:["Yeni Zelanda Doları / Japon Yeni"]},e.exports["#NZDUSD-symbol-description"]={en:["New Zealand Dollar / U.S. Dollar"],tr:["Yeni Zelanda Doları / ABD Doları"]},e.exports["#NZX:ALLC-symbol-description"]={en:["S&P/NZX All Index (Capital Index)"],tr:["S&P/NZX TÜM Endeksler ( Kapital Endeksi )"]},e.exports["#NZX:NZ50G-symbol-description"]={en:["S&P / NZX 50 Index Gross"],tr:["S&P / NZX 50 Brüt Endeksi"]},e.exports["#OANDA:NATGASUSD-symbol-description"]={en:["CFDs on Natural Gas"],tr:["Doğal Gaz KFS'leri"]},e.exports["#OANDA:SPX500USD-symbol-description"]={en:["S&P 500 Index"],tr:["S&P 500 Endeksi"]},e.exports["#OANDA:XCUUSD-symbol-description"]={en:["CFDs on Copper (US$ / lb)"],tr:["Bakır KFS'leri (US$ / lb)"]},e.exports["#OMXCOP:OMXC25-symbol-description"]={en:["OMX Copenhagen 25 Index"],tr:["OMX Kopenhag 25 Endeksi"]},e.exports["#OMXCOP:SCHO-symbol-description"]={en:["Schouw & Co A/S"]},e.exports["#OMXHEX:OMXH25-symbol-description"]={en:["OMX Helsinki 25 Index"],tr:["OMX Helsinki 25 Endeksi"]},e.exports["#OMXRSE:OMXRGI-symbol-description"]={en:["OMX Riga Gross Index"],tr:["OMX Riga Brüt Endeksi"]},e.exports["#OMXSTO:OMXS30-symbol-description"]={en:["OMX Stockholm 30 Index"],tr:["OMX Stokholm 30 Endeksi"]},e.exports["#OMXTSE:OMXTGI-symbol-description"]={en:["OMX Tallinn Gross Index"],tr:["OMX Tallinn Brüt Endeksi"]},e.exports["#OMXVSE:OMXVGI-symbol-description"]={en:["OMX Vilnius Gross Index"],tr:["OMX Vilnius Brüt Endeksi"]},e.exports["#OTC:IHRMF-symbol-description"]={en:["Ishares MSCI Japan SHS"],tr:["Isahres MSCI Japonya SHS"]},e.exports["#QSE:GNRI-symbol-description"]={en:["QE Index"],tr:["QE Endeksi"]},e.exports["#RTS-symbol-description"]={en:["Russian RTS Index"],tr:["Rusya RTS Borsası Endeksi"]},e.exports["#RUSSELL:RUA-symbol-description"]={en:["Russell 3000 Index"],tr:["Russell 3000 Endeksi"]},e.exports["#RUSSELL:RUI-symbol-description"]={en:["Russell 1000 Index"],tr:["Russell 1000 Endeksi"]},e.exports["#RUSSELL:RUT-symbol-description"]={en:["Russell 2000 Index"],tr:["Russell 2000 Endeksi"]},e.exports["#SET:GC-symbol-description"]={en:["Global Connections Public Company"]},e.exports["#SIX:F-symbol-description"]={en:["Ford Motor Company"],tr:["Ford Motor Şirketi"]},e.exports["#SIX:SMI-symbol-description"]={en:["Swiss Market Index"],tr:["İsviçre Piyasası Endeksi"]},e.exports["#SOLUSD-symbol-description"]={
en:["Solana / U.S. Dollar"],tr:["Solana / ABD Doları"]},e.exports["#SOYBNUSD-symbol-description"]={en:["CFDs on Soybeans"],tr:["Soya Fasülyesi KFS'leri"]},e.exports["#SP:OEX-symbol-description"]={en:["S&P 100 Index"],tr:["S&P 100 Endeksi"]},e.exports["#SP:SPGSCI-symbol-description"]={en:["S&P Goldman Sachs Commodity Index"],tr:["S&P Goldman Sachs Emtia Endeksi"]},e.exports["#SP:SPX-symbol-description"]={en:["S&P 500 Index"],tr:["S&P 500 Endeksi"]},e.exports["#SP:SVX-symbol-description"]={en:["S&P 500 Value Index"],tr:["S&P 500 Değer Endeksi"]},e.exports["#SPX500-symbol-description"]={en:["S&P 500 Index"],tr:["S&P 500 Endeksi"]},e.exports["#SUGARUSD-symbol-description"]={en:["CFDs on Sugar"],tr:["Şeker KFS'leri"]},e.exports["#SZSE:399001-symbol-description"]={en:["Shenzhen Component Index"],tr:["Shenzhen Bileşen Endeksi"]},e.exports["#TADAWUL:2370-symbol-description"]={en:["Middle East Specialized Cables Co."]},e.exports["#TADAWUL:TASI-symbol-description"]={en:["Tadawul All Shares Index"],tr:["Tadawul Tüm Hisseler Endeksi"]},e.exports["#TASE:TA35-symbol-description"]={en:["TA-35 Index"],tr:["TA-35 Endeksi"]},e.exports["#TSX:TSX-symbol-description"]={en:["S&P/TSX Composite Index"],tr:["S&P/TSX Bileşik Endeksi"]},e.exports["#TSX:TX60-symbol-description"]={en:["S&P/TSX 60 Index"],tr:["S&P/TSX 60 Endeksi"]},e.exports["#TVC:AU10-symbol-description"]={en:["Australia Government Bonds 10 YR"],tr:["Avusturalya Devlet Tahvilleri 10Y"]},e.exports["#TVC:AU10Y-symbol-description"]={en:["Australia Government Bonds 10 YR Yield"],tr:["Avusturalya 10 Yıllık Devlet Tahvilleri"]},e.exports["#TVC:AXY-symbol-description"]={en:["Australian Dollar Currency Index"],tr:["Avusturalya Dolar Endeksi"]},e.exports["#TVC:BXY-symbol-description"]={en:["British Pound Currency Index"],tr:["İngiliz Pound Endeksi"]},e.exports["#TVC:CA10-symbol-description"]={en:["Canadian Government Bonds, 10 YR"],tr:["Kanada Devlet Tahvilleri, 10 YL"]},e.exports["#TVC:CA10Y-symbol-description"]={en:["Canadian Government Bonds 10 YR Yield"],tr:["Kanada Devlet Tahvilleri 10 YL Getiri"]},e.exports["#TVC:CAC40-symbol-description"]={en:["CAC 40 Index"],tr:["CAC 40 Endeksi"]},e.exports["#TVC:CN10-symbol-description"]={en:["China Government Bonds 10 YR"],tr:["Çin Devlet Tahvilleri 10Y"]},e.exports["#TVC:CN10Y-symbol-description"]={en:["China Government Bonds 10 YR Yield"],tr:["Çin Devlet 10 Yıllık Tahvilleri Getiri"]},e.exports["#TVC:CXY-symbol-description"]={en:["Canadian Dollar Currency Index"],tr:["Kanada Dolar Endeksi"]},e.exports["#TVC:DE10-symbol-description"]={en:["German Government Bonds 10 YR"],tr:["Almanya Devlet Tahvili 10Y"]},e.exports["#TVC:DE10Y-symbol-description"]={en:["German Government Bonds 10 YR Yield"],tr:["Almanya 10 Yıllık Devlet Tahvili Getiri"]},e.exports["#TVC:DEU30-symbol-description"]={en:["DAX Index"],tr:["DAX Endeksi"]},e.exports["#TVC:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"],tr:["Dow Jones Endüstri Ortalaması Endeksi"]},e.exports["#TVC:DXY-symbol-description"]={en:["U.S. Dollar Index"],tr:["A.B.D. Dolar Endeksi"]},
e.exports["#TVC:ES10-symbol-description"]={en:["Spain Government Bonds 10 YR"],tr:["İspanya Devlet Tahvilleri 10Y"]},e.exports["#TVC:ES10Y-symbol-description"]={en:["Spain Government Bonds 10 YR Yield"],tr:["İspanya 10 Yıllık Devlet Tahvilleri Getiri"]},e.exports["#TVC:EUBUND-symbol-description"]={en:["Euro Bund"]},e.exports["#TVC:EXY-symbol-description"]={en:["Euro Currency Index"],tr:["Euro Para Endeksi"]},e.exports["#TVC:FR10-symbol-description"]={en:["France Government Bonds 10 YR"],tr:["Fransa Devlet Tahvilleri 10Y"]},e.exports["#TVC:FR10Y-symbol-description"]={en:["France Government Bonds 10 YR Yield"],tr:["Fransa 10 Yıllık Devlet Tahvilleri Getiri"]},e.exports["#TVC:FTMIB-symbol-description"]={en:["Milano Italia Borsa Index"],tr:["Milano Italia Borsa Endeksi"]},e.exports["#TVC:GB02-symbol-description"]={en:["UK Government Bonds 2 YR"],tr:["İngiltere Devlet Tahvilleri 2Y"]},e.exports["#TVC:GB10-symbol-description"]={en:["UK Government Bonds 10 YR"],tr:["İngiltere Devlet Tahvilleri 10Y"]},e.exports["#TVC:GB10Y-symbol-description"]={en:["UK Government Bonds 10 YR Yield"],tr:["İngiltere 10 Yıllık Devlet Tahvilleri Getiri"]},e.exports["#TVC:GOLD-symbol-description"]={en:["CFDs on Gold (US$ / OZ)"],tr:["Altın KFS'leri (US$/OZ)"]},e.exports["#TVC:HSI-symbol-description"]={en:["Hang Seng Index"],tr:["Hang Seng Endeksi"]},e.exports["#TVC:IBEX35-symbol-description"]={en:["IBEX 35 Index"],tr:["IBEX 35 Endeksi"]},e.exports["#TVC:ID03-symbol-description"]={en:["Indonesia Government Bonds 3 YR"],tr:["Endonezya Devlet Tahvilleri 3Y"]},e.exports["#TVC:ID10-symbol-description"]={en:["Indonesia Government Bonds 10 YR"],tr:["Endonezya Devlet Tahvilleri 10Y"]},e.exports["#TVC:ID10Y-symbol-description"]={en:["Indonesia Government Bonds 10 YR Yield"],tr:["Endonezya Devlet Tahvilleri 10 YL Getiri"]},e.exports["#TVC:IN10-symbol-description"]={en:["India Government Bonds 10 YR"],tr:["Hindistan Devlet Tahvilleri 10Y"]},e.exports["#TVC:IN10Y-symbol-description"]={en:["India Government Bonds 10 YR Yield"],tr:["Hindistan 10 Yıllık Devlet Tahvilleri Getiri"]},e.exports["#TVC:IT10-symbol-description"]={en:["Italy Government Bonds 10 YR"],tr:["İtalya Devlet Tahvilleri 10Y"]},e.exports["#TVC:IT10Y-symbol-description"]={en:["Italy Government Bonds 10 YR Yield"],tr:["İtalya 10 Yıllık Devlet Tahvilleri Getiri"]},e.exports["#TVC:IXIC-symbol-description"]={en:["US Composite Index"],tr:["US Bileşik Endeksi"]},e.exports["#TVC:JP10-symbol-description"]={en:["Japan Government Bonds 10 YR"],tr:["Japonya Devlet Tahvilleri 10Y"]},e.exports["#TVC:JP10Y-symbol-description"]={en:["Japan Government Bonds 10 YR Yield"],tr:["Japonya 10 Yıllık Devlet Tahvilleri Getiri"]},e.exports["#TVC:JXY-symbol-description"]={en:["Japanese Yen Currency Index"],tr:["Japon Yen Endeksi"]},e.exports["#TVC:KOSPI-symbol-description"]={en:["Korea Composite Stock Price Index"],tr:["Kore Bileşik Hisse Senedi Endeksi"]},e.exports["#TVC:KR10-symbol-description"]={en:["Korea Government Bonds 10 YR"],tr:["Kore Devlet Tahvilleri 10Y"]},e.exports["#TVC:KR10Y-symbol-description"]={
en:["Korea Government Bonds 10 YR Yield"],tr:["Kore 10 Yıllık Devlet Tahvilleri Getiri"]},e.exports["#TVC:MY10-symbol-description"]={en:["Malaysia Government Bonds 10 YR"],tr:["Malezya Hükümet Tahvilleri 10Y"]},e.exports["#TVC:MY10Y-symbol-description"]={en:["Malaysia Government Bonds 10 YR Yield"],tr:["Malezya 10 Yıllık Hükümet Tahvilleri Getiri"]},e.exports["#TVC:NDX-symbol-description"]={en:["US 100 Index"],tr:["US 100 Endeksi"]},e.exports["#TVC:NI225-symbol-description"]={en:["Japan 225 Index"],tr:["Japan 225 Endeksi"]},e.exports["#TVC:NL10-symbol-description"]={en:["Netherlands Government Bonds, 10 YR"],tr:["Hollanda Devlet Tahvilleri, 10 YL"]},e.exports["#TVC:NL10Y-symbol-description"]={en:["Netherlands Government Bonds 10 YR Yield"],tr:["Hollanda Devlet Tahvilleri 10 YL Getiri"]},e.exports["#TVC:NYA-symbol-description"]={en:["NYSE Composite Index"],tr:["NYSE Bileşik Endeksi"]},e.exports["#TVC:NZ10-symbol-description"]={en:["New Zealand Government Bonds, 10 YR"],tr:["Yeni Zelanda Devlet Tahvilleri, 10 YL"]},e.exports["#TVC:NZ10Y-symbol-description"]={en:["New Zealand Government Bonds 10 YR Yield"],tr:["Yeni Zelanda Devlet Tahvilleri 10 YL Getiri"]},e.exports["#TVC:PALLADIUM-symbol-description"]={en:["CFDs on Palladium (US$ / OZ)"],tr:["Paladyum KFS'leri (US$/OZ)"]},e.exports["#TVC:PL05Y-symbol-description"]={en:["Poland Government Bonds 5 YR Yield"],tr:["Polonya Hükümeti 5-yıllık Bono Getirileri"]},e.exports["#TVC:PL10Y-symbol-description"]={en:["Poland Government Bonds 10 YR Yield"],tr:["Polonya Hükümeti 10-yıllık Bono Getirileri"]},e.exports["#TVC:PLATINUM-symbol-description"]={en:["CFDs on Platinum (US$ / OZ)"],tr:["Platin KFS'leri (US$ / OZ)"]},e.exports["#TVC:PT10-symbol-description"]={en:["Portugal Government Bonds 10 YR"],tr:["Portekiz Devlet Tahvilleri 10Y"]},e.exports["#TVC:PT10Y-symbol-description"]={en:["Portugal Government Bonds 10 YR Yield"],tr:["Portekiz 10 Yıllık Devlet Tahvilleri Getiri"]},e.exports["#TVC:SA40-symbol-description"]={en:["South Africa Top 40 Index"],tr:["Güney Afrika İlk 40 Endeksi"]},e.exports["#TVC:SILVER-symbol-description"]={en:["CFDs on Silver (US$ / OZ)"],tr:["Gümüş KFS'leri (US$/OZ)"]},e.exports["#TVC:SPX-symbol-description"]={en:["S&P 500 Index"],tr:["S&P 500"]},e.exports["#TVC:SSMI-symbol-description"]={en:["Swiss Market Index"],tr:["İsviçre Piyasa Endeksi"]},e.exports["#TVC:STI-symbol-description"]={en:["Straits Times Index"],tr:["Straits Times Endeksi"]},e.exports["#TVC:SX5E-symbol-description"]={en:["Euro Stoxx 50 Index"],tr:["Euro Stoxx 50 Endeksi"]},e.exports["#TVC:SXY-symbol-description"]={en:["Swiss Franc Currency Index"],tr:["İsviçre Frank Endeksi"]},e.exports["#TVC:TR10-symbol-description"]={en:["Turkey Government Bonds 10 YR"],tr:["Türkiye Devlet Tahvilleri 10Y"]},e.exports["#TVC:TR10Y-symbol-description"]={en:["Turkey Government Bonds 10 YR Yield"],tr:["Türkiye 10 Yıllık Devlet Tahvili Getiri"]},e.exports["#TVC:UKOIL-symbol-description"]={en:["CFDs on Brent Crude Oil"],tr:["Brent Ham Petrol KFS'leri"]},e.exports["#TVC:UKX-symbol-description"]={
en:["UK 100 Index"],tr:["UK 100 Endeksi"]},e.exports["#TVC:US02-symbol-description"]={en:["US Government Bonds 2 YR"],tr:["ABD Devlet Tahvilleri 2Y"]},e.exports["#TVC:US02Y-symbol-description"]={en:["US Government Bonds 2 YR Yield"],tr:["ABD 2 Yıllık Devlet Tahvilleri Getiri"]},e.exports["#TVC:US05-symbol-description"]={en:["US Government Bonds 5 YR"],tr:["ABD Devlet Tahvilleri 5Y"]},e.exports["#TVC:US05Y-symbol-description"]={en:["US Government Bonds 5 YR Yield"],tr:["ABD 5 Yıllık Devlet Tahvilleri Getiri"]},e.exports["#TVC:US10-symbol-description"]={en:["US Government Bonds 10 YR"],tr:["ABD Devlet Tahvilleri 10Y"]},e.exports["#TVC:US10Y-symbol-description"]={en:["US Government Bonds 10 YR Yield"],tr:["ABD 10 Yıllık Devlet Tahvili Getiri"]},e.exports["#TVC:US30-symbol-description"]={en:["US Government Bonds 30 YR"],tr:["ABD 30 Yıllık Tahvili"]},e.exports["#TVC:USOIL-symbol-description"]={en:["CFDs on WTI Crude Oil"],tr:["WTI Ham Petrol KFS'leri"]},e.exports["#TVC:VIX-symbol-description"]={en:["Volatility S&P 500 Index"],tr:["S&P 500 Volatilite Endeksi"]},e.exports["#TVC:ZXY-symbol-description"]={en:["New Zealand Dollar Currency Index"],tr:["Yeni Zelanda Dolar Endeksi"]},e.exports["#TWII-symbol-description"]={en:["Taiwan Weighted Index"],tr:["Tayvan Ağırlıklı Endeks"]},e.exports["#TWSE:TAIEX-symbol-description"]={en:["Taiwan Capitalization Weighted Stock Index"],tr:["Tayvan Sermaye Ağırlıklı Hisse Endeksi"]},e.exports["#TWTR-symbol-description"]={en:["Twitter Inc"],tr:["TWITTER INC"]},e.exports["#UK100-symbol-description"]={en:["FTSE 100 Index"],tr:["FTSE 100 İngiltere Kayıtlı Hisseler Endeksi"]},e.exports["#UKOIL-symbol-description"]={en:["CFDs on Crude Oil (Brent)"],tr:["Ham Petrol KFS'leri (Brent)"]},e.exports["#UNIUSD-symbol-description"]={en:["Uniswap / U.S. Dollar"],tr:["Uniswap / ABD Doları"]},e.exports["#US30-symbol-description"]={en:["Dow Jones Industrial Average Index"],tr:["Dow Jones Endüstri Ortalaması Endeksi"]},e.exports["#USDAUD-symbol-description"]={en:["U.S. Dollar / Australian Dollar"],tr:["ABD Doları / Avustralya Doları"]},e.exports["#USDBRL-symbol-description"]={en:["U.S. Dollar / Brazilian Real"],tr:["ABD Doları / Brezilya Reali"]},e.exports["#USDCAD-symbol-description"]={en:["U.S. Dollar / Canadian Dollar"],tr:["ABD Doları / Kanada Doları"]},e.exports["#USDCHF-symbol-description"]={en:["U.S. Dollar / Swiss Franc"],tr:["ABD Doları / İsviçre Frankı"]},e.exports["#USDCNY-symbol-description"]={en:["U.S. Dollar / Chinese Yuan"],tr:["ABD Doları / Çin Yuanı"]},e.exports["#USDDKK-symbol-description"]={en:["U.S. Dollar / Danish Krone"],tr:["ABD Doları / Danimarka Kronu"]},e.exports["#USDEUR-symbol-description"]={en:["U.S. Dollar / Euro"],tr:["ABD Doları / Euro"]},e.exports["#USDGBP-symbol-description"]={en:["U.S. Dollar / Pound Sterling"],tr:["ABD Doları / Pound Sterlin"]},e.exports["#USDHKD-symbol-description"]={en:["U.S. Dollar / Hong Kong Dollar"],tr:["ABD Doları / Hong Kong Doları"]},e.exports["#USDHUF-symbol-description"]={en:["U.S. Dollar / Hungarian Forint"],tr:["ABD Doları / Macar Forinti"]},
e.exports["#USDIDR-symbol-description"]={en:["U.S. Dollar / Rupiah"],tr:["ABD Doları / Endonezya Rupisi"]},e.exports["#USDILS-symbol-description"]={en:["U.S. Dollar / Israeli Shekel"],tr:["ABD Doları / İsrail Şekeli"]},e.exports["#USDINR-symbol-description"]={en:["U.S. Dollar / Indian Rupee"],tr:["Amerikan Doları / Hint Rupisi"]},e.exports["#USDJPY-symbol-description"]={en:["U.S. Dollar / Japanese Yen"],tr:["ABD Doları / Japon Yeni"]},e.exports["#USDKRW-symbol-description"]={en:["U.S. Dollar / South Korean"],tr:["ABD Doları / Güney Kore Wonu"]},e.exports["#USDMXN-symbol-description"]={en:["U.S. Dollar / Mexican Peso"],tr:["ABD Doları / Meksika Pezosu"]},e.exports["#USDNZD-symbol-description"]={en:["U.S. Dollar / New Zealand Dollar"],tr:["ABD Doları / Yeni Zelanda Doları"]},e.exports["#USDPHP-symbol-description"]={en:["U.S. Dollar / Philippine peso"],tr:["ABD Doları / Filipin Pezosu"]},e.exports["#USDPLN-symbol-description"]={en:["U.S. Dollar / Polish Zloty"],tr:["ABD Doları / Polonya Zlotisi"]},e.exports["#USDRUB-symbol-description"]={en:["U.S. Dollar / Russian Ruble"],tr:["ABD Doları / Rus Rublesi"]},e.exports["#USDRUB_TOM-symbol-description"]={en:["U.S. Dollar / Russian Ruble TOM"],tr:["ABD Doları / Rus Rublesi TOM"]},e.exports["#USDSEK-symbol-description"]={en:["U.S. Dollar / Swedish Krona"],tr:["ABD Doları / İsveç Kronu"]},e.exports["#USDSGD-symbol-description"]={en:["U.S. Dollar / Singapore Dollar"],tr:["ABD DOLARI / SİNGAPUR DOLARI"]},e.exports["#USDTHB-symbol-description"]={en:["U.S. Dollar / Thai Baht"],tr:["ABD Doları / Tayland Bahtı"]},e.exports["#USDTRY-symbol-description"]={en:["U.S. Dollar / Turkish Lira"],tr:["ABD Doları / Türk Lirası"]},e.exports["#USDZAR-symbol-description"]={en:["U.S. Dollar / South African Rand"],tr:["ABD Doları / G. Afrika Randı"]},e.exports["#USOIL-symbol-description"]={en:["CFDs on Crude Oil (WTI)"],tr:["Ham Petrol KFS'leri (WTI)"]},e.exports["#WHEATUSD-symbol-description"]={en:["CFDs on Wheat"],tr:["Buğday KFS'leri"]},e.exports["#XAGUSD-symbol-description"]={en:["Silver / U.S. Dollar"],tr:["Gümüş/ABD Doları"]},e.exports["#XAUUSD-symbol-description"]={en:["Gold Spot / U.S. Dollar"],tr:["Spot Altın/ABD Doları"]},e.exports["#XBTCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"],tr:["Bitcoin / Kanada Doları"]},e.exports["#XETR:DAX-symbol-description"]={en:["DAX Index"],tr:["DAX Endeksi"]},e.exports["#XMRUSD-symbol-description"]={en:["Monero / U.S. Dollar"],tr:["Monero / ABD Doları"]},e.exports["#XPDUSD-symbol-description"]={en:["CFDs on Palladium"],tr:["Paladyum KFS'leri"]},e.exports["#XPTUSD-symbol-description"]={en:["Platinum / U.S. Dollar"],tr:["Platin/ABD Doları"]},e.exports["#XRPBTC-symbol-description"]={en:["XRP / Bitcoin"]},e.exports["#XRPEUR-symbol-description"]={en:["XRP / Euro"]},e.exports["#XRPUSD-symbol-description"]={en:["XRP / U.S. Dollar"],tr:["XRP / ABD Doları"]},e.exports["#ZECUSD-symbol-description"]={en:["Zcash / U.S. Dollar"],tr:["Acash / ABD Doları"]}}}]);