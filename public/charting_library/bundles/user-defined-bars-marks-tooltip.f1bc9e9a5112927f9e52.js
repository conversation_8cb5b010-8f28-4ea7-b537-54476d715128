(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[8020],{43010:(t,e,i)=>{"use strict";i.d(e,{useIsomorphicLayoutEffect:()=>n});var o=i(50959);function n(t,e){("undefined"==typeof window?o.useEffect:o.useLayoutEffect)(t,e)}},27267:(t,e,i)=>{"use strict";function o(t,e,i,o,n){function s(n){if(t>n.timeStamp)return;const s=n.target;void 0!==i&&null!==e&&null!==s&&s.ownerDocument===o&&(e.contains(s)||i(n))}return n.click&&o.addEventListener("click",s,!1),n.mouseDown&&o.addEventListener("mousedown",s,!1),n.touchEnd&&o.addEventListener("touchend",s,!1),n.touchStart&&o.addEventListener("touchstart",s,!1),()=>{o.removeEventListener("click",s,!1),o.removeEventListener("mousedown",s,!1),o.removeEventListener("touchend",s,!1),o.removeEventListener("touchstart",s,!1)}}i.d(e,{addOutsideEventListener:()=>o})},36383:(t,e,i)=>{"use strict";i.d(e,{useOutsideEvent:()=>r});var o=i(50959),n=i(43010),s=i(27267);function r(t){const{click:e,mouseDown:i,touchEnd:r,touchStart:c,handler:d,reference:h}=t,u=(0,o.useRef)(null),a=(0,o.useRef)("undefined"==typeof window?0:new window.CustomEvent("timestamp").timeStamp);return(0,n.useIsomorphicLayoutEffect)((()=>{const t={click:e,mouseDown:i,touchEnd:r,touchStart:c},o=h?h.current:u.current;return(0,s.addOutsideEventListener)(a.current,o,d,document,t)}),[e,i,r,c,d]),h||u}},59345:t=>{t.exports={css_value_arrow_size:"13",tooltip:"tooltip-eSLcXvvL",show:"show-eSLcXvvL",right:"right-eSLcXvvL"}},48395:t=>{t.exports={text:"text-hF57_4zZ"}},6897:(t,e,i)=>{"use strict";i.r(e),i.d(e,{TooltipRenderer:()=>h});var o,n=i(50959),s=i(32227),r=i(36383),c=i(59345);!function(t){t[t.AdditionalXOffset=8]="AdditionalXOffset",t[t.HideTooltipTimeoutMsecs=100]="HideTooltipTimeoutMsecs",t[t.ShowTooltipTimeoutMsecs=400]="ShowTooltipTimeoutMsecs",t[t.IgnoreShowTimeoutMsecs=100]="IgnoreShowTimeoutMsecs"}(o||(o={}));const d=parseInt(c.css_value_arrow_size);class h{constructor(t){this._container=null,this._props=null,this._deferredActions={hideItemTime:0},this._updatePosition=()=>{if(null===this._props||null===this._container)return;const{width:t,height:e}=this._tooltipContainer.getBoundingClientRect(),i=this._container.getBoundingClientRect(),o=Math.round(this._props.itemSize/2),n=Math.min(8,o);let s=!1,r=this._props.x-t-o-d-n;r<0&&(r=this._props.x+o+d+n,s=!0);const h=i.height;let u=Math.max(0,this._props.y-e/2);u>0&&(u=Math.min(u,h-e)),this._tooltipContainer.classList.toggle(c.right,s),this._tooltipContainer.style.top=`${u}px`,this._tooltipContainer.style.left=`${r}px`},this._tooltipFactory=t,this._tooltipContainer=document.createElement("div"),this._tooltipContainer.classList.add(c.tooltip)}destroy(){this._unmountComponent()}contains(t){return this._tooltipContainer.contains(t)}hide(t){this._deferredActions.hideItemTime=performance.now(),this._clearTimeouts(),t?this._tooltipContainer.classList.remove(c.show):this._deferredActions.hideItemTimerId=setTimeout((()=>{this._tooltipContainer.classList.remove(c.show)}),100)}show(t){this._clearTimeouts(),
performance.now()<this._deferredActions.hideItemTime+100?this._showImpl(t):this._deferredActions.showItemTimerId=setTimeout((()=>this._showImpl(t)),400)}_showImpl(t){this._props=t,this._render(t),this._clearTimeouts(),this._tooltipContainer.classList.add(c.show)}_render(t){const e=t.container;this._container!==e&&(this._unmountComponent(),this._container=e,this._container.appendChild(this._tooltipContainer)),s.render(n.createElement(u,{handler:t.onClickOutside,child:n.createElement(this._tooltipFactory,t.factoryProps)}),this._tooltipContainer,this._updatePosition)}_unmountComponent(){null!==this._container&&(s.unmountComponentAtNode(this._tooltipContainer),this._tooltipContainer.remove(),this._container=null,this._clearTimeouts())}_clearTimeouts(){void 0!==this._deferredActions.showItemTimerId&&(clearTimeout(this._deferredActions.showItemTimerId),this._deferredActions.showItemTimerId=void 0),void 0!==this._deferredActions.hideItemTimerId&&(clearTimeout(this._deferredActions.hideItemTimerId),this._deferredActions.hideItemTimerId=void 0)}}function u(t){const{handler:e,child:i}=t,o=(0,r.useOutsideEvent)({mouseDown:!0,touchStart:!0,handler:e});return n.createElement("div",{ref:o},i)}},16608:(t,e,i)=>{"use strict";i.r(e),i.d(e,{UserDefinedBarsMarksTooltip:()=>s});var o=i(50959),n=i(48395);function s(t){const{text:e}=t;return o.createElement("div",{className:n.text},e)}}}]);