<svg xmlns="http://www.w3.org/2000/svg" width="351" height="92" viewBox="0 0 351 92" fill="none">
  <g filter="url(#filter0_i_24075_1699587)">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H351V81.5C351 87.0228 346.523 91.5 341 91.5H9.99999C4.47715 91.5 0 87.0229 0 81.5V0Z" fill="url(#paint0_linear_24075_1699587)" style=""/>
  </g>
  <path d="M350.5 0.5V81.5C350.5 86.7467 346.247 91 341 91H10C4.7533 91 0.5 86.7467 0.5 81.5V0.5H350.5Z" stroke="url(#paint1_linear_24075_1699587)" style=""/>
  <defs>
    <filter id="filter0_i_24075_1699587" x="0" y="0" width="351" height="95.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="2"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.966667 0 0 0 0 1 0 0 0 0.3 0"/>
      <feBlend mode="normal" in2="shape" result="effect1_innerShadow_24075_1699587"/>
    </filter>
    <linearGradient id="paint0_linear_24075_1699587" x1="302.976" y1="0" x2="302.976" y2="91.5" gradientUnits="userSpaceOnUse">
      <stop stop-color="#004A54" stop-opacity="0" style="stop-color:none;stop-opacity:0;"/>
      <stop offset="1" stop-color="#004540" style="stop-color:#004540;stop-color:color(display-p3 0.0000 0.2706 0.2510);stop-opacity:1;"/>
    </linearGradient>
    <linearGradient id="paint1_linear_24075_1699587" x1="302.976" y1="0" x2="302.976" y2="91.5" gradientUnits="userSpaceOnUse">
      <stop stop-color="#00221C" stop-opacity="0" style="stop-color:none;stop-opacity:0;"/>
      <stop offset="1" stop-color="#004540" style="stop-color:#004540;stop-color:color(display-p3 0.0000 0.2697 0.2517);stop-opacity:1;"/>
    </linearGradient>
  </defs>
</svg>