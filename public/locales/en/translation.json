{"activityTable": {"filters": {"addpood": "Add a pool", "all": "all", "buyit": "Buy it in", "reduce": "Reduce the pool", "sell": "<PERSON>ll"}}, "ai": {"analyzing": "Analyzing...", "avatarAnalysis": "Avatar", "avgComments": "Avg. <PERSON><PERSON>", "avgLikes": "Avg. <PERSON>s", "avgRetweets": "Avg. Ret<PERSON>s", "avgViews": "Avg. Views", "button": {"cancel": "cancel", "confirm": "confirm", "reset": "reset", "save": "save"}, "buttons": {"avatarAnalysis": "Avatar", "narrativeTheme": "Narrative", "websiteAnalysis": "Website"}, "collapse": "Collapse", "contract": "Contract", "devAddress": "Developer Address", "devHistory": "Development History", "devHoldRatio": "DEV Holding Ratio", "devStartups": "DEV Startups", "expand": "Expand", "firstPostTime": "First Post Time", "followers": "Followers", "influential": {"followerCount": "Followers", "title": "Influential Twitter Followers ({{count}})"}, "insiderRatio": "Insider <PERSON><PERSON>", "launchDate": "Launch Date", "narrative": "Narrative", "officialExpand": "Click to Expand", "officialTitle": "Official Data Analysis", "onchainTextContent": "This Twitter account appears to be managed by the well-known entrepreneur <PERSON><PERSON>. However, the tweets do not present any content specifically related to meme coins, nor do they mention anything directly connected to blockchain or cryptocurrencies. The tweets mainly focus on technology, artificial intelligence, and personal opinions. There are no references to token contract addresses or token names. This Twitter account appears to be managed by <PERSON><PERSON>, but none of the tweets suggest involvement with any specific meme coin or crypto project.", "onchainTextNote": "This narrative analysis is automatically generated by the XBIT bot and is for reference only.", "onchainTextTitle": "On-chain Text Analysis", "onChainTitle": "On-chain Analytics", "poolAddress": "Pool Address", "poolTime": "Pool Launch Time", "relatedProjects": "Related Projects", "relatedWallets": "Related Wallets", "totalPosts": "Total Posts", "twitterChanges": "Twitter Name", "twitterCreatedAt": "Twitter Registered", "walletBalance": "Wallet Balance", "websiteAnalysis": "Website"}, "aiAnalysis": {"avatarAnalysis": "Avatar", "narrative": "Narrative", "websiteAnalysis": "Website"}, "appSettings": {"aboutUs": {"cancel": "Cancel", "clearCache": "Clear cache", "clearCacheMessage": "Are you sure to clear cache?", "clearCacheSuccess": "<PERSON><PERSON> cleared", "confirm": "Confirm", "contactUs": "Contact us", "currentVersion": "Latest version installed", "officalEmail": "Official Email", "officialEmail": "Official Email", "privacyPolicy": "Privacy policy", "rateXBit": "Rate XBIT", "slogan": "On-chain is future, trade is freedom", "termsOfService": "Terms of service", "title": "About Us"}, "browsingHistory": "Browsing history", "cancel": "Cancel", "color": {"greenUpRedDown": "Green Up / Red Down", "redUpGreenDown": "Red Up / Green Down"}, "colorPreference": "Color preference", "colors": {"gainLossColor": "Price Change Color", "title": "Color settings"}, "contactCustomerService": "Contact customer service", "contactUs": "Contact us", "contactUsSubtitle": "Version, Privacy policy", "customerService": "Customer service", "disabled": "Disabled", "enabled": "Enabled", "fundingHistory": "Funding history", "googleAuth": {"app": "Google Authenticator", "cancel": "Cancel", "confirmReset": "Confirm", "confirmResetTitle": "Confirm reset Google Authenticator?", "confirmUnlink": "Confirm unlink", "download": "Download", "guide": "Use Google Authenticate to protect your account and funds", "linked": "Linked", "linkNow": "Link Now", "notLinked": "Unlinked", "reset": "Reset", "title": "Google Authenticator", "unlink": "Unlink", "unlinkToast": "Unlinked successfully", "warningMessage": "To ensure your account security, withdrawals from the Telegram wallet are not allowed within <span>24 hours</span> after unlinking Google Authenticator."}, "googleAuthenticator": "Google Authenticator", "language": "Language", "languages": {"title": "Language Settings"}, "linkWallets": "Link wallets", "loginRequired": "Please log in first", "messageNotifications": "Notification preference", "myRewards": "Rewards", "notifications": {"enableBtn": "Enable", "enablePushNotifications": "Enable push notifications", "enablePushNotificationsSubtitle": "Enable system notifications to receive real-time alerts", "guideManual": "Please manually enable notification permission in your browser settings.", "othersSubtitle": "Stay informed about general updates and background events", "othersTitle": "Others", "priceAlertSubtitle": "Real-time token price alerts", "priceAlertTitle": "Token Price Alert", "smartMoneySubtitle": "Get the latest trading updates from your followed smart money", "smartMoneyTitle": "Smart Money Activities", "title": "Notification Settings", "tradingSubtitle": "Subscribe futures trade signal alerts        ", "tradingTitle": "Futures Signal Alert"}, "saveImage": "Save", "shareXBIT": "Share XBIT", "shareXBITSubtitle": "Decentralized Exchange (DEX)", "spotSettings": "Trade settings", "unlinked": "Unlinked", "unset": "Unset", "userFeedback": "<PERSON><PERSON><PERSON>", "userFeedbackSubtitle": "Accepted feedback earns 20 USDT", "userManual": "Tutorial", "withdrawalAddress": "<PERSON>drawal addresses", "withdrawalWarningMessage": "For the safety of your funds, you need to bind Google Authenticator and set a whitelist address before you can withdraw funds. Withdrawals are not allowed within <span>3 hours</span> after setting a whitelist address."}, "assers": {"transfers": {"availableBalance": "Available balance"}}, "assets": {"crossChainBridge": "Cross-chain Bridge", "deposit": {"address": " Address", "amountReived": "Amount Received", "availableBalance": "Available Balance", "chooseAccount": "<PERSON><PERSON> Account", "chooseToken": "<PERSON><PERSON>", "confirmDeposit": "Confirm", "enterAmount": "Enter Amount", "fee": "Fee", "max": "Max", "min": "Minimum Deposit", "quantity": " Quantity", "searchToken": "Search Token", "selectNetwork": "Select Network", "selectToken": "Select Token", "shareAddress": "Share Address", "title": "<PERSON><PERSON><PERSON><PERSON>", "warning": "Only accept Solana's SOL recharges, and do not support recharges in other currencies, otherwise it may cause capital loss", "warningEthereum": "Only accept Ethereum's ETH recharges, and do not support recharges in other currencies, otherwise it may cause capital loss", "warningSolana": "Only accept Solana's SOL recharges, and do not support recharges in other currencies, otherwise it may cause capital loss"}, "funding": {"all": "All", "allNetworks": "All Networks", "assets": "Assets", "averageCost": "Average Cost", "crypto": "Crypto", "hideSmallAssets": "Hide assets < 1U", "holding": "Holding", "orderHistory": "Order History", "pnl": "PnL", "positionValue": "Position Value", "title": "Funding"}, "fundingAccount": "Funding Account", "futures": {"account": "Account", "assets": "Assets", "availableBalance": "Available Balance", "availableForWithdraw": "Available for With<PERSON>wal", "availableForWithdrawal": "assets.futures.availableForWithdrawal", "availableMargin": "Available Margin", "balance": "Balance", "connectWallet": "Connect Wallet", "crossMarginLeverage": "Cross Margin Leverage", "crossMarginRatio": "Cross Margin Ratio", "fullMarginRatio": "Full Margin <PERSON>io", "inOrder": "In Order", "leverage": "Leverage", "maintenanceMargin": "Maintenance Margin", "name": "Name", "noDataYet": "No data yet", "pnl": "PnL", "positionHistory": "Position History", "positions": "Positions", "positionValue": "Position Value", "title": "Futures", "todayPnL": "Today PnL", "unrealizedPnl": "Unrealized PnL"}, "futuresAccount": "Futures Account", "login": {"thirdParty": "Third-party login", "web3Wallets": "Web3 wallets"}, "orderHistory": "Order History", "overview": {"allAssets": "All assets", "allTypes": "All types", "amount": "Amount", "change": "Change", "confirmDeposit": "Deposit into futures account", "confirmWithdrawal": "Withdraw from futures account", "cost": "Cost", "cross": "Cross", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "deposite": "<PERSON><PERSON><PERSON><PERSON>", "depositeVault": "Deposite into vault", "depositMessage": "Logging in with a third-party wallet only supports depositing futures account", "depositVault": "Deposit into vault", "dontRemindNextTime": "Don't remind me next time", "estimatedAssets": "Estimated assets", "estimatedAssetsAgree": "OK", "estimatedAssetsDescription": "Total estimated assets = futures account assets + spot account assets", "exchange": "Exchange", "fundingHistory": {"date": "Date", "fee": "Fee", "fromAccount": "From", "receiverAddress": "Receiver address", "senderAddress": "Sender address", "titleAddVaultFailed": "Deposit into vault failed", "titleAddVaultSuccess": "Deposit into vault successful", "titleDepositeSuccess": "Depo<PERSON><PERSON> failed", "titleDepositFailed": "Depo<PERSON><PERSON> failed", "titleDepositSuccess": "Deposit successful", "titleExchangeFailed": "Exchange failed", "titleExchangeSuccess": "Exchange successful", "titleRedemptionFailed": "Redemption failed", "titleRedemptionSuccess": "Redemption successful", "titleWithdrawalFailed": "<PERSON><PERSON><PERSON> failed", "titleWithdrawalSuccess": "<PERSON><PERSON><PERSON> successful", "toAccount": "To", "transactionHash": "Transaction hash", "type": "Type"}, "futures": "Futures", "futuresSpotTransfer": "Transfer", "isolated": "Isolated", "long": "<PERSON>", "perpetual": "{{pair}}", "pnl": "PnL", "price": "Price", "redeemVault": "Redeem from vault", "short": "Short", "spot": "Spot", "tabAssets": "Assets", "tabFundingRecords": "Funding records", "title": "Overview", "todayPnL": "Today's PnL", "vaultName": "Name", "vaults": "Vaults", "widthdrawal": "<PERSON><PERSON><PERSON>", "withdrawal": "<PERSON><PERSON><PERSON>", "withdrawalMessage": "Logging in with a third-party wallet only supports withdrawing funds from the futures account to the wallet"}, "splash": {"connectWallet": "Connect wallet", "slogan": "Decentralized exchanges, realtime market information", "sub": "Quick transaction, trade now!"}, "spot": {"all": "All", "allNetwork": "All network", "allNetworks": "All Networks", "assets": "Assets", "averageCost": "Average Cost", "crypto": "Crypto", "hideSmallAssets": "Hide assets < 1U", "orderHistory": "Order History", "pnl": "PnL", "positionValue": "Position Value", "title": "Spot"}, "spotAccount": "Spot Account", "switchNetwork": {"allNetwork": "All network"}, "title": "Assets", "token": {"averageCost": "Average Cost", "buy": "Buy", "costPrice": "Cost Price", "estimatedAssets": "Estimated assets", "pnl": "PnL", "sell": "<PERSON>ll", "transactionHistory": "Transaction History", "yieldRate": "Yield Rate"}, "transfer": "Transfer", "transfers": {"amount": "Transfer amount", "amountPlaceholder": "Enter transfer amount", "availableBalance": "Available balance", "availableBalanceInUsd": "Available balance {{balance}} USD", "balanceUnavailable": "There is no transferable asset in the current token, please select another", "confirm": "Confirm", "fromAccount": "From", "insufficientBalance": "Insufficient balance", "max": "Max", "selectToken": "Select token", "toAccount": "To", "token": "Token"}, "withdraw": "Withdraw", "withdrawal": {"addNewAddress": "Add new address", "amount": "Amount", "availableBalance": "Available balance", "checkTheDetails": "Check the details", "chooseToken": "Choose token", "confirmWithdraw": "Confirm withdraw", "fee": "Fee", "fundsReceived": "Funds received", "gasFee": "Gas fee", "goToTransaction": "Go to transaction", "history": "History", "insufficientBalance": "Insufficient balance", "learnMore": "Learn more", "modifyWhitelist": "Modify whitelist", "receivingAmount": "Receiving amount", "recipientAddress": "Recipient address", "requestWithdrawSuccess": "Withdraw request successful", "searchToken": "Search for token name or address", "selectNetwork": "Choose network", "selectToken": "Select token", "tokenRecipientAddress": "{{token}} recipient address", "transactionInprogress": "Transaction in progress", "tryAgain": "Try again", "wallet": "{{name}}", "warningWithdrawTele": "Please make sure that the withdrawal address supports the Solana network. If the receiving address does not support Solana chain assets, it will cause your asset loss.", "warningWithdrawWallet": "USDC will be sent to your address via the Arbitrum network.\nA fee of 1 USDC will be deducted from the withdrawn USDC.\nWithdrawals are estimated to arrive within 5 minutes.", "withdrawAll": "Max", "withdrawFailed": "Withdraw failed", "withdrawFailedMessage": "Transaction failed due to a blockchain network error", "withdrawSuccessMessage": "Your {{token}} has been sent to your futures account.", "withdrawToken": "Withdraw {{token}}", "withdrawTokenOnNetwork": "Withdraw {{token}} on {{network}}"}}, "Avg price": "Cost Price", "bottomNav": {"assets": "Assets", "chat": "Cha<PERSON>", "discover": "Discover", "homepage": "Home", "market": "Market", "monitoring": "Monitoring", "smartMoney": "Smart Money", "trading": "Trade"}, "button": {"add": "Add", "apply": "Apply", "cancel": "Cancel", "confirm": "Confirm", "reset": "Reset", "save": "Save"}, "cardTag": {"limitBuy": "Limit Buy", "limitSell": "<PERSON><PERSON>", "movingStopLossSell": "Trailing TP/SL"}, "categories": {"change24h": "24h Change", "marketCap": "Total MC", "topToken": "Top", "upDown": "Up/Down", "volume24h": "24h Volume"}, "categoryDetail": {"all": "All", "avgIncrease": "24h Price Change", "downCount": "Losers", "h24ChangePercent": "24h %", "h24Volume": "24h Volume", "h24VolumeLabel": "24h Volume", "hot": "Hot", "latestPrice": "Price", "new": "New", "tag": "<PERSON>", "title": "Categories", "tokenType": "Token/MC", "top1": "Top 1", "top2": "Top 2", "top3": "Top 3", "totalMarketCap": "Total MC", "totalTokens": "Total tokens", "upCount": "Gaine<PERSON>"}, "chart": {"area": "Area", "averageCandlestick": "<PERSON><PERSON><PERSON>", "bar": "Bars", "buttons": {"cancel": "Cancel", "confirm": "Confirm"}, "candlestick": "Candles", "dataFormat": {"amount": "Amt", "price": "Price", "total": "Total"}, "hollowCandlestick": "Hollow Candles", "line": "Line", "period": {"currentPeriodNotInList": "Can not remove the active timeframe: {{period}}", "day": "1D", "maximum6Periods": "Maximum 6 periods", "month": "1M", "resetToDefault": "Reset to De<PERSON>ult", "selectedPeriod": "Selected Period", "supportsUpTo6Periods": "Supports up to 6 periods", "title": "Time Period", "week": "1W", "year": "1Y"}, "toolbar": {"indicator": "Indicator", "klineStyle": "Chart Type", "marketCap": "MCap", "price": "Price", "setting": "Settings"}}, "connectingToTelegram": "Connecting to Telegram...", "const": {"time": {"d": "d", "day": "day", "days": "days", "h": "h", "hour": "hour", "hours": "hours", "m": "m", "minute": "minute", "minutes": "minute", "month": "month", "months": "months", "s": "s", "second": "second", "year": "year", "years": "years"}}, "constant": {"all": "All"}, "contractMonitoring": {"attentionItems": "Attention Items", "blacklist": "Blacklist", "blacklistEnabled": "Blacklist Enabled", "blacklistEnabledDesc": "This contract contains a blacklist mechanism that may restrict trading for some users (honeypot risk).", "burnPool": "Burn Pool", "buyTax": "Buy Tax", "canMint": "Can Mint", "contractAddress": "Contract Address", "contractCreator": "Contract Creator", "contractInfo": "Contract Info", "contractOwner": "Contract Owner", "disclaimer": "Disclaimer: This contract monitoring result is based on public data. XBIT does not endorse or recommend this result in any form.", "lowRiskItems": "Low Risk Items", "mediumRisk": "Medium Risk", "noMintFunction": "No Mint Function", "noMintFunctionDesc": "This token has no hidden minting mechanism. Hidden minting could increase token supply and affect price.", "noProxyContract": "No Proxy Contract", "noProxyContractDesc": "Contract does not contain proxy mechanisms. Proxy mechanisms allow project teams to potentially replace token logic, affecting price and mechanisms.", "ownershipRenounced": "Ownership Renounced", "ownershipRenouncedDesc": "This token contract is open source and can be verified. Non-open source contracts may contain malicious mechanisms to defraud users.", "riskDetection": "Risk Detection", "riskItems": "Risk Items", "sellTax": "Sell Tax", "taxModifiable": "Tax Modifiable", "taxModifiableDesc": "Project team may have retained the ability to modify trading taxes. If taxes are increased above 49%, tokens may become untradeable (honeypot risk).", "title": "Contract Monitoring", "tokenInfo": "Token Info", "tokenName": "Token Name", "tokenSymbol": "Token Symbol", "top10": "TOP10 19.99%", "whaleProtection": "Whale Protection (Trade Limit)", "whaleProtectionDesc": "This token has trading limits. Many projects limit trading amounts, which may prevent users from liquidating their positions."}, "CopyTrade_CreateConfig_DuplicateConfig": "You've already copied this address.", "crossChainBridge": {"back": "Back", "checkDetails": "View Details", "crossChainExchange": "Cross-Chain Exchange", "enterAmount": "Enter Amount", "estimatedTime": "Estimated Time", "exchange": "Exchange", "exchangeRate": "Exchange Rate", "fee": "Fee", "from": "From", "link": {"authorization": "Authorization", "authorizationNote": "Allow XBIT to top up {{token}} from your wallet.", "l2Confirmation": "L2 Confirmation", "l2ConfirmationNote": "Confirm your top-up details and sign to proceed with this transaction.", "linkNote": "Connect Wallet", "linkYourWallet": "Link your wallet to XBIT", "title": "Link"}, "redeemFailed": "Failed", "redeemFailedNote": "Failure reason...", "redeeming": "Redeeming", "redeemingNote": "Your {{token}} will arrive within 30 minutes or less.", "redeemSuccess": "Received", "redeemSuccessNote": "Your {{token}} has been sent to your contract account.", "redemptionPath": {"fastest": "Fastest", "lowestGasFee": "Lowest Gas Fee", "optimal": "Optimal", "title": "Redemption Path"}, "title": "Cross-Chain Bridge", "to": "To", "viewTransaction": "View Transaction"}, "currentOrdersList": {"activationPrice": "Activation Price", "allDirections": "All Directions", "allOrders": "All Orders", "buy": "Buy", "callbackRate": "Callback Rate %", "cancelOrder": "Cancel", "cancelOrderConfirm": "Cancel Order?", "doublePrincipalAfterPurchase": "Double Principal After Purchase", "limitOrder": "Limit Order", "marketTrade": "Market Trade", "modify": "Modify", "orderAmount": "Order Amount", "orderQuantity": "Order Quantity", "orderQuantityUnit": "Order Quantity", "sell": "<PERSON>ll", "showCurrentCoinOnly": "Show current token", "trailingStopLoss": "Trailing SL", "triggerMarketCapPrice": "Trigger Market Cap / Price"}, "detail": {"alert": {"lowLiquidity": "Liquidity is too low, please trade carefully"}, "buyersStatusDrawer": {"allSold": "Sell all", "holdingOrIncrease": "{{count}} addresses hold or buy more", "holdingUnchanged": "Hold", "iconExplanation": "Icon Explanation", "increased": "Buy more", "latest100Holding": "Top 100 Holders", "latest10Holding": "Top 10 Holders", "partialSold": "Sell part", "proportion": "Proportion", "targetedBuyer": "<PERSON><PERSON><PERSON>", "title": "Top {{total}} buyers of {{token}} ({{holding}}/{{total}})"}, "filters": {"all": "All", "basicVersion": "Basic", "follow8SmartMoney": "Follow 8 Smart Monies", "followed": "Followed", "isRobot": "<PERSON><PERSON>", "kol": "KOL", "newWallet": "New Wallet", "projectParty": "DEV Team", "ratWarehouse": "Insider", "sameOrigin": "Same Origin Wallet", "showCurrentTokenOnly": "Show only current token", "smartMoney": "Smart Money", "sniper": "<PERSON><PERSON><PERSON>", "sniper1": "<PERSON><PERSON><PERSON>", "transactionAmount": "Transaction Amount", "transactionType": "Transaction Type", "whale": "Whale"}, "holderTable": {"avgBuySell": "Avg Buy / Sell", "fundSource": "Source / Tx Time", "holder": "Holder", "holdingLength": "Holding Duration", "lastActive": "Last Active", "positionPercentage": "% Held", "realized": "Realized PnL", "solBalance": "SOL / Created", "totalBuy": "Total Buy", "totalProfit": "Total PnL", "totalProit": "Total PnL", "totalSell": "Total Sell", "txCount": "Tx Count", "unrealized": "Unrealized PnL"}, "internal": {"percentage": "55%", "title": "Internal"}, "monitoring": {"attentionItems": "Warnings", "lowRiskItems": "Low Risk", "riskItems": "Risks"}, "myPositions": {"activatePrice": "Activate price", "activeStopLoss": "Activate stop loss (optional)", "avgBuyInMarketValue": "Avg MC", "commissionAmount": "Order Amount", "confirmClearance": "Are you sure to sell off {{symbol}} at the market price?", "costPrice": "Avg cost", "decentralizedExchange": "Decentralized Exchange (DEX)", "hiddenSmallPoll": "Hide Low Liq", "hiddenSmallThan1U": "Hide assets < 1$", "highPointPullback": "High point pullback", "moments": "Moments", "more": "More", "network": "Network", "noData": "No data", "numberOfPosition": "Holding quantity", "oneClickSell": "One-click sell", "orderCreated": "{{symbol}}: Order Created", "orderProcessing": "{{symbol}}: Order Created", "positionValue": "Holding value", "profitAndLoss": "PnL", "recharge": "Recharge TGBot Wallet", "returnRate": "ROS", "saveToAlbum": "Save to Album", "sharePage": "Share Page", "showOnlyCurrentCurrency": "Show current token", "soldQuantity": "Sold quantity", "trailingStopLoss": "Trailing TPSL", "transactionValue": "Transaction Value", "type": "Type", "walletAddress": "Wallet Address", "wechat": "WeChat"}, "pool": {"address": "Address", "chart": "Chart", "liquidity": "Liquidity", "poolCount": "Pool Count", "quantity": "Quantity", "time": "Time", "totalLiquidity": "Total Liquidity", "totalValue": "Total Value", "trading": "Trading", "type": "Type"}, "smartMoney": {"addLiquidity": "Add Liquidity", "all": "All", "buy": "Buy", "cancel": "Cancel", "clear": "Clear", "confirm": "Confirm", "follow8SmartMoney": "Followed {{length}} SM", "noData": "No data", "removeLiquidity": "Remove Liquidity", "sell": "<PERSON>ll", "showCurrentTokenOnly": "Show only current token", "smartMoney": "Followed Smart Money", "transactionAmount": "Transaction Amount", "transactionType": "Transaction Type"}, "statistics": {"costPrice": "Cost", "holdingAmount": "Amount", "holdingValue": "Holding value", "profitLoss": "P/L", "profitLossPercentage": "P/L%"}, "tabs": {"aiAnalysis": "AI", "all": "All", "currentCommission": "Pending Orders", "currentCommissionWithCount": "Pending Orders ({{total}})", "data": "Data", "followed": "Followed", "holders": "Holders{{total}}", "holdersWithCount": "Holders", "infomation": "Info", "information": "Info", "latest": "Latest", "makeMoneyPound": "Profit <PERSON>", "myPositions": "Holdings", "payOrder": "Buy Order", "pool": "Pool", "sellOrder": "Sell Order", "smartMoney": "Followed SM", "trading": "Trade", "transactionHistory": "Transactions"}, "tags": {"addLiquidity": "Add <PERSON>", "burnt": "Burn", "chipAnalysis": "Bubble map", "health": "Health", "healthScore": "Health({{value}}/{{total}})", "hold": "Hold", "internalDiskPercentage": "Intestine ", "official": "Official", "removeLiquidity": "Rm Liq\t", "sellAll": "Sell All"}, "tokenDetail": {"action": "Action", "ago": "ago", "amount": "Amt", "balance": "Balance", "bought": "Total buy ({{times}} times)", "buy": "Buy", "clearPosition": "Sell all", "day": "D", "decreasePosition": "<PERSON>ll", "direction": "Type", "endTime": "End Time", "errorInputTransactionFilter": "Minimum value cannot be greater than maximum value", "filterAddress": "Filter addresses", "finalPrice": "Sold Price", "finalType": "Filter type", "holdingDuration": "Duration", "holdingRatio": "Holding ratio", "hour": "H", "increasePosition": "Buy more", "inputAddress": "Enter wallet address", "inputTraders": "Enter address", "internalDisk": " ", "marketCap": "MC", "marketValue": "Market value", "marketValueSpecial": "@Market value", "maximumTime": "The maximum time span is 1 month", "maximumTransaction": "Maximum Transaction Amount", "maximumVolumeWithToken": "maximum ({{token}})", "minimumTransaction": "Minimum Transaction Amount", "minimumVolume": "minimum volume", "minute": "m", "month": "M", "notSelected": "Not Selected", "openPosition": "Buy", "overMaxTime": ", please reselect", "pairCreated": "Pair created", "percentage": "Holding ratio", "pnl": "PnL", "position": "Buy in", "positionValue": "Position", "price": "Price", "recentTrades": "{{count}} smart money trades in the past {{hours}} hours", "reselect": "Reselect", "selectEndTime": "Please select end time", "selectStartTime": "Please select start time", "sell": "<PERSON>ll", "sold": "Total sell ({{times}} times)", "startTime": "Start Time", "time": "Time", "timeWalletAddress": "Time / Wallet address", "total": "Total", "totalValue": "total value", "tpsl": "TPSL", "traders": "Traders", "transactionAmount": "Transaction amount", "turnover": "Amount", "type": "Type", "value": "Value", "viewOnExplorer": "View on explorer", "volume": "Volume", "wallet": "Wallet", "year": "Y"}, "tokenInfo": {"blacklist": "Blacklist", "burnt": "<PERSON><PERSON>", "contractAddress": "Contract", "createdAgo": "{{time}} ago", "fdv": "FDV", "fromATH": "From ATH", "fromATL": "From ATL", "fromOpen": "From open price", "mintDisabled": "Mint disabled", "name": "Name", "no": "No", "symbol": "Symbol", "title": "Token", "yes": "Yes"}, "trade": {"paused": "Paused", "running": "Running"}, "trading": {"marketCap": "{{time}} MCap", "priceChange": "Change", "transactions": "{{time}} Txs", "volume": "{{time}} Vol"}}, "detailStatistic": {"buyValue": "Buy Value", "costPrice": "Avg Price", "pnl": "PnL", "pnlPercent": "ROS", "positionsHeldNum": "Positions Held", "positionsHeldValue": "Position Value", "sellValue": "Sell Value"}, "filter": {"1hTransactions": "1h Txs", "1hVolume": "1h Volume", "creationTime": "Creation Time", "customize": "Custom", "internalProgress": "Internal Progress", "marketCap": "Market Cap", "maxHolders": "<PERSON> Holders", "maximum": "Maximum", "maxMarketCap": "Max Cap", "maxPool": "Max Pool", "maxProgress": "Max Progress", "maxTime": "Max Time", "maxTransactions": "Max Txs", "maxVolume": "Max Vol", "minHolders": "<PERSON>ers", "minimum": "Minimum", "minMarketCap": "Min <PERSON>", "minMaxError": "Error: Min value cannot exceed max value", "minPool": "Min Pool", "minProgress": "Min Progress", "minTime": "Min Time", "minTransactions": "<PERSON> Txs", "minVolume": "Min Vol", "transactions": "tx"}, "followingWallet": {"addWallet": "Add Wallet", "addWalletSuccess": "Wallet added successfully", "batchCopyToClipboard": "<PERSON><PERSON> Copy to Clipboard", "copyToClipboard": "Copy to Clipboard", "export": "Export", "exportSuccess": "Batch export successful, copied to clipboard", "import": "Import", "importAndExportWallets": "Import/Export Wallets", "importError": "Incorrect format, please check", "importExample": "Format example:\n0x2b2fbbe4...1307:Wallet Name 1,\n0x7hd8s7au...2esc:Wallet Name 2,", "importNote": "Format instructions: Separate each wallet address with a comma ','. Use colon ':' to separate remarks. You can follow up to 300 addresses. Only Solana wallet addresses are supported.", "importSuccess": "Batch import successful", "inputWalletAddress": "Enter Solana wallet address", "inputWalletAddress2": "Paste the imported Solana wallet address", "inputWalletAddressError": "Not a Solana address, please re-enter", "inputWalletAddressError2": "Incorrect format, please check", "optional": "Optional", "walletAddress": "Wallet Address", "walletName": "Wallet Name"}, "google": {"auth": {"button": {"abnormality": "Network abnormality", "averagePageviews": "Average Pageviews", "binding": "binding", "cancel": "Cancel", "cancelAverage": "Cancel", "confirmReset": "Confirm reset", "copied": "copied", "copy": "copy", "download": "Download", "exceeds": "exceeds size", "nextStep": "Next step", "paste": "Paste", "reset": "reset", "return": "Return to asset home page", "save": "save", "saveAverage": "save"}, "completed": {"bindingIntro": "Your Google Authenticator has been successfully bound. For the safety of your funds, please set up an address whitelist", "bindingTitle": "Google Authenticator binding successful", "msg": "The review is expected to be completed within 1 working day, and the review results will be sent to you via email.", "title": "Submission successful"}, "copyright": "Copyright © 2024 XBIT.COM. All rights reserved.", "dear": "Dear user:", "decode": "16-bit key", "descriptionQR": "Open the Google Authenticator APP, click the + sign in the lower right corner, scan the QR code below, or copy the 16-digit key below to Google Authenticator", "introduction": "The Google verification reset you applied for has been passed. Please use the Google Authenticator APP to scan the QR code below, or copy the 16-digit key below to Google Authenticator for binding. The key is very important, please keep it properly:", "modal": {"reject": {"intro1": "You have not bound Google Authenticator", "intro2": "Can't reset"}}, "not_valid_wallet": "Invalid {{coin}} address format", "note": "For the safety of your funds, the system only supports transfers to whitelist addresses. After setting the whitelist, you need to wait 3 hours for it to take effect. After taking effect, no additional verification is required for transfers to whitelist addresses, making the withdrawal process safer and faster.", "pincode": {"description": "Please enter the 6-digit verification code from Google Authenticator", "title": "Google verification"}, "qrcode": "QR code:", "reset": {"confirmMsg": "Are you sure you want to reset Google Verification?", "ensureMsg": "To ensure the security of the account, rebind the Google Authenticator and withdraw money from the Telegram wallet within {{time}}.", "ensureMsg1": "To ensure account security, rebind Google Authenticator.", "ensureMsg2": "Within 24 hours", "ensureMsg3": "Withdrawals from Telegram wallets are not allowed.", "form": {"exampleWallet": "For example, if you use MetaMask wallet to recharge to XBIT's TG wallet, or withdraw money from XBIT's TG wallet to MetaMask wallet, please provide MetaMask's wallet address.", "label1": "Your TG account number (be sure to fill it in correctly to facilitate customer service contact)", "label2": "Your Telegram User ID (enter \"/userid\" in XBIT's official Telegram Bot to obtain)", "label3": "Your email address (to receive reset emails)", "label4": "Please provide the wallet address that has had transfer records with XBIT’s TG wallet in the past 3 months.", "label5": "Please upload screenshots of the deposit or withdrawal records of the above wallet and XBIT’s TG wallet in the past three months.", "offlineTelegram": "Official Telegram", "placeholder1": "Your TG account", "placeholder2": "Your TG User ID", "placeholder3": "your email address", "placeholder4": "enter confirmation code", "placeholder5": "Enter wallet address", "sendVerify": "Resend 60s", "submit": "submit", "supportWallet": "(Only supports PNG, JPG, JPEG formats, each picture is up to 10M), supports up to 20 pictures."}, "intro": "For the safety of your funds, the system only supports transfers to whitelist addresses. After setting the whitelist, you need to wait 3 hours for it to take effect. After taking effect, no additional verification is required for transfers to whitelist addresses, making the withdrawal process safer and faster.", "title": "Reset Google Verification"}, "riskwarning": "Risk Warning: Cryptocurrency trading carries a high level of risk. XBIT.COM provides you with first-class trading services on the chain, but we will not be responsible for your trading losses. Please pay attention to transaction risks and trade with caution.", "safety": {"intro": "For the safety of your funds, the system only supports transfers to whitelist addresses. After setting the whitelist, you need to wait 3 hours for it to take effect. After taking effect, no additional verification is required for transfers to whitelist addresses, making the withdrawal process safer and faster.", "intro ": "For the safety of your funds, the system only supports transfers to whitelist addresses. After setting the whitelist, you need to wait 3 hours for it to take effect. After taking effect, no additional verification is required for transfers to whitelist addresses, making the withdrawal process safer and faster.", "warning": "For the safety of your funds, the system only supports transfers to whitelisted addresses. After setting the whitelist, you need to wait 3 hours for it to take effect. After it takes effect, transfers to whitelisted addresses do not require additional verification, and the withdrawal process is safer and faster."}, "settings": {"numbers": "The Google verification reset you applied for failed, please resubmit based on the following failure reasons:"}, "setup": {"success": "Setup successful"}, "slogan": "Google verification reset passed!", "steps": {"step1": "Bind Google Authenticator", "step2": "Set up an address whitelist", "step3": "<PERSON><PERSON><PERSON>"}, "team": "team", "thankyou": "Thank you for choosing XBIT. Need help? Please contact", "title": "Bind Google Authenticator", "warning": {"not_available": "Google Authenticator is not available,", "note": "Please keep this key in a safe location. This key is used to restore your identity verification. Once lost, it cannot be recovered. If your Google Authenticator is uninstalled, you can use the key to generate a verification code the next time you install it.", "verifycode": "Verification code error"}, "whitelist": {"edit": "edit", "intro": "For the safety of your funds, the system only supports transfers to whitelist addresses. After setting the whitelist, you need to wait 3 hours for it to take effect. After taking effect, no additional verification is required for transfers to whitelist addresses, making the withdrawal process safer and faster.", "notset": "Whitelist not set"}}}, "header": {"crypto": "Crypto", "meme": "Meme"}, "history": {"addLiquidity": "Add Liquidity", "all": "All", "allDirections": "All Dirs", "allOrders": "All Orders", "amount": "Amount", "buy": "Buy", "buyAmount": "Buy Amount", "buyAndDouble": "Double after buy", "cancel": "Cancel", "facilitationPayment": "Facilitation Payment", "filterByToken": "Filter by <PERSON><PERSON>", "filterByType": "Filter by Type", "gasFee": "Gas Fee", "modify": "Edit", "nodata": "No data", "orderAmount": "Amount", "orderValue": "Value", "pnl": "Profit/Loss", "price": "Price", "priorityFee": "Priority fee", "pump": "Pump", "removeLiquidity": "Remove Liquidity", "searchToken": "Search Token", "sell": "<PERSON>ll", "sellAmount": "<PERSON><PERSON>", "showCurrentCoinOnly": "Current Token Only", "slippage": "Slippage", "slippageLoss": "Slippage Loss", "time": "Time", "token": "Token", "tradeVolume": "Trade Volume", "transactionMarketValue": "Transaction MC", "triggerMarketCap": "Trigger Cap/Price", "txHash": "Tx Hash", "type": "Type", "xbitFee": "XBIT Fee(1%)"}, "iconsDrawer": {"activeWallets": "1097+ wallets active (1h)", "contractExplorer": "Explorer", "copyContract": "Copy address", "copyTextSuffix": "Copy", "devAddLiquidity": "Dev add liquidity", "dexScreenerAd": "Advertises on DEX", "dexScreenerSocial": "Updated socials on DEX", "goToTwitter": "Twitter", "isHot": "More than {{total}} transactions in the last hour", "lowLiquidity": "Low liquidity!", "nameChanges": "10 Twitter name changes", "officialTelegram": "Official Telegram", "officialTwitter": "Official Twitter", "officialWebsite": "Official website", "projectAbandoned": "Abandoned (community-run)", "pumpLaunch": "Launched on Pump", "revise": "Live Tweet Preview", "symbolChanges": "Renamed {{total}} times on Twitter", "tokenAge": "Age since creation", "tokenPortrait": "Token portrait"}, "liquidityChart": {"comingSoon": "Coming Soon", "currentInitial": "Current/Initial", "fundPool": "Fund Pool", "liquidity": "Liquidity", "time": "Time", "totalLiquidity": "Total Liquidity", "tradingPair": "Trading Pair", "underDevelopment": "This feature is under development"}, "listCoin": {"amount": "Amount", "columns": {"holders": "Holders", "marketCap": "Market Cap", "pool": "Pool", "priceChange": "Chg", "transactions": "Txs", "transactionsWithTime": "{{time}}Txs", "volume": "Vol", "volumeWithTime": "{{time}}Vol"}, "copyTrade": {"actions": "Actions", "all": "All", "buySellCount": "Buy/Sell Count", "cancel": "Cancel", "canceled": "cancel", "confirmDelete": "Confirm deletion?", "copyTradeDelete": "Stop", "copyTradeEditParams": "Edit", "copyTradePaused": "Pause", "copyTradeRestart": "<PERSON><PERSON>", "createCopyTrade": "Create", "currentMarketCap": "Market Cap", "deleteMessage": "Delete this wallet's copy trade? This action cannot be undone!", "followStatus": "Follow Status", "hint": {"batch": {"intro": "Take-profit and stop-loss in batches: \nThe positions being followed can be sold in batches, and multiple take-profit/stop-loss rules can be configured; \nwhen the stop-profit and stop-loss is triggered, N% of the currently followed position will be sold (triggered from the 1st level); \nthe stop-profit/stop-loss is only triggered once for each level of the same token; \nif the same token is added to the position, the take-profit/stop-loss will be re-run at the average price (triggered from the 1st level again)"}, "blacklist": "Tokens added to the currency blacklist will no longer be purchased following orders.", "buySettings": {"intro1": "Maximum follow-up purchase: \n\nMaximum follow-up purchase means that the user is willing to use up to N SOL as the copy order amount; \nif the purchase amount of the target address > the maximum purchase amount, the purchase amount will be the maximum purchase amount; \nif the purchase amount of the target address = < the maximum purchase amount, the copy purchase will be based on the purchase amount of the target address. \n\nExample: If the user sets a maximum buy-in of 10 SOL. \nIf the copy address buys 5 SOL, the user will follow the order and buy 5 SOL; \nIf the target copy address buys 20 SOL, the user will follow the order and buy 10 SOL.", "intro2": "Fixed buy: \n\nFixed buy means that no matter how much the target address buys, each time you follow the order, you will buy according to the set fixed amount. \n\nExample: If the user sets a fixed buy-in of 10 SOL. \nIf the copy address buys 5 SOL, the user will follow the order and buy 10 SOL; \nif the target copy address buys 20 SOL, the user will still follow the order and buy 10 SOL."}, "copyTrade": {"intro1": "Minimum order amount filter", "intro2": "When the purchase amount of the target copy address >= the minimum copy amount, the copy purchase will be executed. Prevent the copying wallet from making frequent small purchases, causing you to make frequent copying purchases."}, "followAmount": {"intro1": "Minimum order amount filter", "intro2": "When the purchase amount of the target copy address >= the minimum copy amount, the copy purchase will be executed. Prevent the copying wallet from making frequent small purchases, causing you to make frequent copying purchases."}, "minBurnPool": {"intro1": "Only when the token destruction ratio >= the set value, copy buying will be executed.", "intro2": "When the purchase amount of the target copy address >= the minimum copy amount, the copy purchase will be executed. Prevent the copying wallet from making frequent small purchases, causing you to make frequent copying purchases."}, "platform": {"intro": "Only when the token purchased by the copy wallet is the token of the selected platform, will it copy the purchase (it does not affect the copy selling).\nFor example, if token A is created in Pump, the user only sets the Pump platform.\nWhen the copy target wallet buys A in the Pump internal market, it can copy the purchase;\nAfter the liquidity of A is migrated to Raydium, the copy target wallet buys A again and no longer copy the purchase; the user sells A on Raydium and can copy the sale;\nIf the user adjusts the copy platform to: Pump + Raydium; when the copy target wallet buys again on Raydium, it can copy the purchase;"}, "sellSettings": {"intro1": "Automatic follow-up selling: \n\nWhen the copying address sells tokens, the tokens purchased by the copying order will be sold in equal proportion according to the proportion of the position sold. Note that this is only for the position of the selling copying order. If you also buy the token yourself, the tokens you bought will not be sold. \n\nFor example: The user's copying address A buys Doge. When address A sells 10% of its own Doge position, the user's copying address A's purchased Doge position will sell 10% in equal proportion.\n", "intro2": "Do not follow the sale: \n\nThe system will not automatically help you sell the tokens, and you need to sell the tokens manually.", "intro3": "Customized take profit and stop loss on: \n\nAfter the automatic take profit and stop loss is turned on, when the copy wallet sells, the system will not perform the selling operation, but will sell according to the take profit and stop loss parameters you set."}, "singleAddPositionCount": {"intro1": "The maximum number of positions for the same token under position conditions (the number will be restored after liquidation)", "intro2": "Enter 0: It means that after copying the order, the same token will not be purchased again (after clearing and selling, it will be purchased again)", "intro3": "Input 1: means that after copying the order, the same token can be added to the position and bought once (that is, up to 2 times of copying. After clearing the position and selling, the number of times will be restored)"}, "trailingStopLoss": {"intro": "The stop-loss price maintains a fixed ratio to the highest price and rises as the token price rises, aiming to protect profits. \nScenario description: Assume that the buying price is $100 and the stop-loss ratio is set to 10%; if the price drops to $90 (a 10% drop), the stop-loss sell will be triggered; if the price rises to $150, the stop-loss price will be automatically adjusted to $135 (a fixed ratio of 10% from the highest point is maintained)"}}, "lastTradeTime": "Last Trade Time", "myWallet": "My Wallet", "oneDayPnL": "1-Day PnL", "paused": "Paused", "playTradeRestart": "Play", "realizedProfit": "Realized Profit", "running": "Running", "sevenDayAvgBuyCost": "7-Day Avg Buy Cost", "sevenDayPnL": "7-Day PnL", "sevenDayWinRate": "7-Day Win Rate", "thirtyDayPnL": "30-Day PnL", "toast": {"warningActiveTelegram": "Please login by telegram to create copy trade"}, "totalBuy": "Total Buy", "totalSell": "Total Sell"}, "fields": {"liquidityPool": "LP"}, "filter": "Filter", "filters": {"aiMining": "AI Mining", "all": "All", "almostFull": "Completing", "apply": "Apply", "ascending": "Asc", "by": "by", "defaultSortLabel": "Add↓", "descending": "Desc", "fields": {"creationTime": {"maxLabel": "Max", "minLabel": "Min", "title": "Created"}, "holders": {"maxLabel": "Max", "minLabel": "Min", "title": "Holders"}, "liquidityPool": {"maxLabel": "Max", "minLabel": "Min", "title": "Pool"}, "marketCap": {"maxLabel": "Max", "minLabel": "Min", "title": "MCap"}, "progress": {"maxLabel": "Max", "minLabel": "Min", "title": "Internal"}, "transactions": {"maxLabel": "Max", "minLabel": "Min", "title": "1h Txs", "unit": "tx"}, "volumes": {"maxLabel": "Max", "minLabel": "Min", "title": "1h Vol"}}, "gainers": "Gaine<PERSON>", "launched": "Completed", "losers": "Losers", "newListing": "New", "paused": "Paused", "placeholder": "All", "popular": "Hot", "pumping": "Soaring", "reset": "Reset", "resetDefault": "Reset", "running": "Running", "sortBy": "Sort", "sortBy5minMarketCapChangeDesc": "Sort: 5min MCap in descending order", "sortByCreationTimeDesc": "Sort: Creation time in descending order", "sortByField": {"creationTimeAsc": "Creation time in ascending order", "creationTimeDesc": "Creation time in descending order", "holdersAsc": "Holders in ascending order", "holdersDesc": "Holders in descending order", "liquidityPoolAsc": "Liquidity Pool in ascending order", "liquidityPoolDesc": "Liquidity Pool in descending order", "marketCapAsc": "Market Cap in ascending order", "marketCapDesc": "Market Cap in descending order", "progressAsc": "Progress in ascending order", "progressDesc": "Progress in descending order", "transactionsAsc": "{{time}} transactions in ascending order", "transactionsDesc": "{{time}} transactions in descending order", "volumesAsc": "{{time}} volume in ascending order", "volumesDesc": "{{time}} volume in descending order"}, "sortByInternalProgressDesc": "Sort: Internal Progress in descending order", "sortByVolumeDesc": "Sort: Volume in descending order", "sortByWatchlistTimeDesc": "Sort: Adding time in descending order", "title": "Filter", "topTraders": "Top Traders", "walletCopyTrade": "Wallet Copy Trade", "walletOptions": {"all": "All", "kolVc": "KOL/VC", "newWallet": "New Wallet", "pumpSmartMoney": "Pump Smart Money", "smartMoney": "Smart Money", "sniper": "<PERSON><PERSON><PERSON>", "viewSmartMoney": "Check out the smart money following"}}, "modal": {"config": {"cancel": "Cancel", "delete": "Confirm", "title": "Confirm deletion?"}}, "noData": "No data", "quickBuy": "Quick buy", "removeWatchlist": "Remove {{coinName}}?", "requireLoginToFavorite": "Please log in to add this token to your favorites.", "sortLabels": {"mainstream": "Vol↓", "newCoin": "Create↓", "optional": "Add↓"}, "tabs": {"category": "Categories", "mainstream": "Trending", "newCoin": "New Tokens", "optional": "Watchlist", "walletCopy": "Copy trade"}, "tagFilters": {"aiMining": "AI", "fullSoon": "Full Soon", "gainers": "↑", "hot": "Hot", "losers": "↓", "new": "New", "opened": "Open", "pumped": "Pumped"}, "toasts": {"buyCount": " buy transactions", "insiderTrading": "Insider trading", "numberOfHolders": "Holders", "numberOfSnipers": "Snipers", "sameSourceWallet": "Same source wallet", "sellCount": " sell transactions", "smartMoney": "Smart money", "tokensLaunched": "Number of tokens launched", "top10Holder": "Top 10 holders", "totalTransactions": " Total transactions", "volume": "{{time}} volume"}, "toBeOpened": "To be opened", "trendingScore": "Trending score"}, "login": {"bindingNotice": "Connecting to SOL", "bindTelegramBot": "Bind Telegram Bot", "cancel": "Cancel", "notLogined": "You are not logged in to {{name}}", "open": "Open", "openInBrowser": "Copy link to browser", "openTelegramPrompt": "Open Telegram?", "privacyPolicy": "Privacy Policy", "recommended": "Recommended", "telegramLogin": "Telegram <PERSON>", "terms": "Terms of Use", "termsAgreement": "By connecting, you agree to our", "tradingPromptDetail": "No signature required", "tradingPromptTitle": "Start trading with XBIT", "with": "and"}, "modifyOrder": {"available": "Available", "buyPrice": "Buy Price(USDT)", "callbackRate": "High Point Pullback", "confirm": "Confirm", "doublePrincipalAfterPurchase": "Double principal after purchase", "estimatedExchange": "Estimated Exchange", "insufficientBalance": "Insufficient balance for Gas fee", "latestMarketCap": "Latest Market Cap", "latestPrice": "Latest Price", "quantity": "Quantity", "trailingTPSL": "Trailing Take Profit/Stop Loss"}, "monitoring": {"followedWallets": "Followed Wallets", "following": "Following", "manager": "Manager", "realTimeTransactions": "Real-time Transactions", "title": "Monitoring", "transactionAmount": "Transaction Amount", "transactionType": "Transaction Type"}, "notifications": {"title": "Notifications"}, "orderBook": {"all": "All", "currentMarketCap": "Market Cap", "marketCap": "MC", "pool": "Pool", "price": "Price", "quantity": "Qty", "time": "Time", "volume": "Vol"}, "orderForm": {"buySettings": {"buyAmount": "Buy", "complete": "Done", "confirm": "Confirm", "inputAmount": "Enter Amt", "more": "More", "orderProcess": "Order processing", "reset": "Reset"}, "confirmation": {"confirmButton": "Buy", "noPrompt": "Don't ask", "title": "Confirm?"}, "doubleAfterBuy": "Double after buy", "errors": {"changeTeleWalletToContinue": "Coming soon. Please change the Telegram wallet to continue.", "firstTradeToken": "You’re trading this token for the first time. A small fee (~{{amount}} {{symbol}}) will be charged to initialize your account.", "insufficientBalance": "No funds", "minimumOrderQuantity": "The minimum order quantity is 0.000001.", "noTokenAvailable": "No tokens available.", "orderFailed": "Failed", "orderInvalidAmount": "Invalid Order Amount", "rechargeIfBalanceInsufficient": "Insufficient balance, please recharge.", "rechargeIfBalanceInsufficientMinimum": "Insufficient {{chain}} balance. Please ensure you have at least {{balance}} {{chain}} in your wallet to cover network fees.", "selectTransactionQuantity": "Please select the transaction quantity.", "signMessageWalletInfo": "Please sign the message to trade.", "toastCreatOrderErrorTitle": "Transaction Failed", "tokenInsufficient": "Insufficient token sales.", "transactionLoadingFailed": "Transaction data loading failed"}, "form": {"antiClip": "Anti-MEV", "automatic": "Auto", "available": "Available", "availableAssets": "Available Assets", "cancel": "Cancel", "close": "Off", "confirmBuying": "Confirm Buy/Sell", "connectExternalWallet": "Connect External Wallet", "connectTelegramWallet": "Connect Telegram Wallet", "doublePrincipalAfterPurchase": "Double principal after purchase", "errExternalWallet": "External wallets are not supported for limit orders", "hodingtoken": "Wallets holding {{token}} tokens", "limitCommissionTelegram": "Limit orders are only available with Telegram login, not supported with wallet login", "linked": "Linked", "marketCap": "Market Cap", "noMorePrompt": "Don't show again", "open": "On", "otherToken": "Other wallets", "price": "Price", "priorityFee": "Priority Fee", "slipPoint": "Slippage", "supportHoldText": "Assume you hold Trump at current price $100 and set trailing stop at 10%:", "supportTextListItem1": "·If price drops 10% from $100 to $90, your trailing stop loss order will trigger and execute a market sell order.", "supportTextListItem2": "·If price rises to $150 then drops 7% to $140, your trailing stop loss sell order will not trigger.", "supportTextListItem3": "·If price rises to $200 then drops 10% to $180, your trailing stop loss order will trigger and execute a market sell order.", "sure": "Confirm"}, "gasFee": {"baseFee": "Base", "customGasFee": "Custom Gas", "explanation": "Priority fee increases transaction speed. Higher fees get faster execution. Fees are charged even if transactions fail.", "fast": "Fast", "market": "Market", "priorityFee": "Priority", "superFast": "Super", "title": "Gas", "totalFee": "Total"}, "inputs": {"amount": "Amount", "price": "Price"}, "marketTab": {"amount": "Amount"}, "mevProtect": {"off": "Off", "on": "On", "title": "MEV"}, "orderSetting": {"antiClipMode": "Anti-Clipping Mode (MEV Protection)", "antiClipModeTooltip": "MEV attacks, commonly known as clipping, happen when attackers front-run your trade to raise or lower prices. Enabling this will protect your trade from such attacks.", "basicFee": "Basic Fee", "cancel": "Cancel", "cannotOver100": "Slippage cannot exceed 100%.", "customFee": "Custom Priority Fee", "fast": "Fastest", "fee": "Priority Fee", "marketPrice": "Market Price", "maximumPriorityFee": "Priority fee cannot exceed", "open": "Confirm", "priorityFee": "Priority Fee", "priorityFeeRequire": "Priority fee is required.", "priorityFeeTooLow": "Priority fee is below market rate, which may cause long transaction times or failure.", "priorityFeeTooltip": "Priority fee is the additional amount a user is willing to pay to miners or validators to prioritize their order and speed up execution. The higher the fee (within the recommended range), the greater the success rate and speed. If the transaction fails, the fee is still deducted from your balance.", "quick": "Quick", "slippageTooHigh": "Slippage is too high, which may lead to asset loss. It is recommended to set it between 1% and 50%.", "slippageTooLow": "Slippage is too low, which may cause the transaction to fail. It is recommended to set it between 1% and 50%.", "slipPoint": "Slippage", "slipPointPlaceholder": "Custom (1~50)", "slipPointTooltip": "Slippage refers to the difference between the expected transaction price and the execution price. If the price change exceeds the set slippage, the transaction will be canceled, but network fees will still be charged.", "title": "Transaction Settings (Global)", "totalCost": "Total Cost", "warning": "Note: These settings are global. If you manually adjust any setting for this transaction, it will be used as default for future trades."}, "priorityFee": {"customFee": "Custom", "errors": {"required": "Required", "tooHigh": "Max {{maxFee}} {{chain}}", "tooLow": "Too low - may fail"}, "title": "Priority"}, "slippage": {"customPlaceholder": "1~50", "errors": {"max": "Max 100%", "tooHigh": "Too high (use 1-50%)", "tooLow": "Too low (use 1-50%)"}, "explanation": "Price difference tolerance. Txs exceeding this are canceled (fees still apply).", "title": "Slip"}, "status": {"BALANCE_INSUFFICIENT": "Submit order failed, insufficient token balance in wallet.", "BALANCE_INSUFFICIENT_FEE": "Insufficient balance to cover gas fees. Please top up.", "BONDING_ENDED": "Token cap reached. Pool is being added on Raydium, please wait.", "Canceled": "Canceled", "Completed": "Completed", "createOrderSuccess": "Create order successfully", "FEE_BUDGET": "Priority fee too low, transaction timed out. Please increase the priority fee.", "Filled": "Filled", "INTERNAL_ERROR": "Internal system error. Please try again later.", "LIQUIDITY_LOW": "Insufficient liquidity in pool, please split into multiple trades.", "NET_BLOCKHASH": "Transaction expired. Please retry to get updated data.", "NETWORK_UNSUPPORT": "This blockchain network is not supported.", "Order_BaseTokenNotSupported": "Transaction Failed: Base token is not supported. Please use a supported token", "Order_ChargeFail": "Transaction Failed: Failed to charge the wallet. Please make sure your wallet is connected", "Order_ChargeFail_LowBalance": "Transaction Failed: Insufficient balance. Please top up your wallet and try again", "Order_Close_AlreadyDeleted": "Transaction Failed: This order is already deleted. Please no further actions required", "Order_Close_InvalidExitReason": "Transaction Failed: Invalid reason for closing order. Please select a valid exit reason", "Order_Close_InvalidStatus": "Transaction Failed: Cannot close: invalid order status. Please check the current order state", "Order_Close_InvalidType": "Transaction Failed: Cannot close: invalid order type. Please review the type of your order", "Order_Close_PublishFailed": "Transaction Failed: Failed to publish order close request. Please retry or contact support", "Order_Close_SomethingPending": "Transaction Failed: Pending actions detected. Please complete all steps before closing", "Order_Close_StatusNotPassed": "Transaction Failed: Order status does not allow closing. Please wait for order to activate", "Order_ConfigNotFound": "Order configuration not found. Please reload and try again", "Order_CreateFail": "Transaction Failed: Failed to create order. Please retry after checking your input", "Order_CrossChain": "Transaction Failed: Cross-chain orders are not supported. Please use same-chain tokens only", "Order_CurrencyNotSupported": "Transaction Failed: Selected currency is not supported. Please choose a different token", "Order_GetEnabledPairFail": "Transaction Failed: Failed to fetch available pairs. Please refresh the page", "Order_GetMarketCapFail": "Transaction Failed: Failed to retrieve market cap. Please try again later", "Order_GetPriceFail": "Transaction Failed: Failed to retrieve token price. Please check token availability", "Order_GetRateFail": "Transaction Failed: Failed to get conversion rate. Please check your network and retry", "Order_InvalidAmount": "Transaction Failed: Invalid amount. Please enter a valid trade amount", "Order_InvalidCopyTradeId": "Transaction Failed: Invalid copy trade ID. Please refresh or reselect the trade target", "Order_InvalidCurrency": "Transaction Failed: Invalid currency provided. Please check the token details and retry", "Order_InvalidEntry": "Transaction Failed: Invalid entry point. Please review order entry details", "Order_InvalidPair": "Transaction Failed: Invalid trading pair. Please select a valid token pair", "Order_InvalidParam_Amount": "Transaction Failed: Invalid trade amount. Please enter a valid amount", "Order_InvalidParam_AmountRange": "Transaction Failed: Amount out of range. Please adjust the amount to allowed range", "Order_InvalidParam_Burst": "Transaction Failed: Invalid burst mode setting. Please turn off burst mode if unsure", "Order_InvalidParam_CallbackRate": "Transaction Failed: Invalid callback rate. Please check callback rate value", "Order_InvalidParam_DoublePrincipalAfterPurchase": "Transaction Failed: Invalid double principal setting. Please disable or review this option", "Order_InvalidParam_DoublePrinciplaAfterPurchase": "Transaction Failed: Invalid double principal setting. Please disable or review this option", "Order_InvalidParam_Leverage": "Transaction Failed: Invalid leverage. Please select a valid leverage", "Order_InvalidParam_LimitMarketCap": "Transaction Failed: Invalid limit market cap. Please adjust limit market cap", "Order_InvalidParam_LimitPrice": "Transaction Failed: Invalid limit price. Please enter a correct limit price", "Order_InvalidParam_LimitRange": "Transaction Failed: Invalid limit range. Please adjust limit price range", "Order_InvalidParam_OnlyTrailingTpSlWithSell": "Transaction Failed: Trailing TP/SL only allowed with sell orders. Please use with sell orders only", "Order_InvalidParam_PriorityFeePrice": "Transaction Failed: Invalid priority fee. Please increase priority fee (≥ 0.002 SOL)", "Order_InvalidParam_Sl": "Transaction Failed: Invalid stop loss (SL). Please enter a valid SL value", "Order_InvalidParam_Slippage": "Transaction Failed: Invalid slippage. Please use slippage of 1–50%", "Order_InvalidParam_SlRangeBuy": "Transaction Failed: Invalid SL range for buy. Please adjust SL range for buy orders", "Order_InvalidParam_SlRangeSell": "Transaction Failed: Invalid SL range for sell. Please adjust SL range for sell orders", "Order_InvalidParam_Tp": "Transaction Failed: Invalid take profit (TP). Please enter a valid TP value", "Order_InvalidParam_TpRangeBuy": "Transaction Failed: Invalid TP range for buy. Please adjust TP range for buy orders", "Order_InvalidParam_TpRangeSell": "Transaction Failed: Invalid TP range for sell. Please adjust TP range for sell orders", "Order_InvalidParam_TpSlBuy": "Transaction Failed: Invalid TP/SL for buy order. Please review TP/SL values", "Order_InvalidParam_TpSlSell": "Transaction Failed: Invalid TP/SL for sell order. Please review TP/SL values", "Order_InvalidParam_TriggerPrice": "Transaction Failed: Invalid trigger price. Please enter a valid trigger price", "Order_InvalidParam_Txid": "Transaction Failed: Invalid transaction ID. Please check the TxID and try again", "Order_ModifyOrder_BaseAmount": "Transaction Failed: Invalid base amount update. Please adjust base amount", "Order_ModifyOrder_Exited": "Transaction Failed: Order already exited. Please place a new order", "Order_ModifyOrder_LimitPrice": "Transaction Failed: Invalid limit price update. Please check new limit price", "Order_ModifyOrder_MarketOrder": "Transaction Failed: Cannot modify market order. Please cancel and create a new order", "Order_ModifyOrder_MevProtect": "Transaction Failed: Invalid MEV protection update. Please reset Anti-MEV settings", "Order_ModifyOrder_NotChange": "Transaction Failed: No changes detected in update. Please change a value before updating", "Order_ModifyOrder_PriorityFeePrice": "Transaction Failed: Invalid priority fee. Please enter a valid number", "Order_ModifyOrder_Slippage": "Transaction Failed: Invalid slippage update. Please set slippage between 1–50%", "Order_ModifyOrder_TriggerPrice": "Transaction Failed: Invalid trigger price update. Please check new trigger price", "Order_QuoteTokenNotSupported": "Transaction Failed: Base token is not supported. Please use a supported token", "Order_RemoveOrderCacheFail": "Transaction Failed: Failed to publish order close request. Please retry or contact support", "Order_SaveWeb3Order_PublishFailed": "Transaction Failed: Failed to publish Web3 order. Please try again later or contact support", "Order_SubmitFail": "Transaction Failed: Order submission failed. Please check your connection and try again", "Order_WalletNotSupported": "Transaction Failed: Wallet type is not supported. Please connect a supported wallet", "orderProcess": "Order processing", "orderSuccess": "Order successful", "orderSucess": "Success", "PAIR_NOT_FOUND": "Trading pair not found. Please check the token.", "Pending": "Pending", "ROUTE_NOT_FOUND": "This token is currently not tradable. No available routing path.", "RPC_CONNECTION": "Network connection timed out.", "RPC_ERROR": "Node error.", "SIMULATION_FAILED": "Buy Order Submission Failed — Possibly Due to Contract Restrictions", "SPLIPPAGE_SURGE": "Slippage exceeded. Try increasing your slippage setting.", "TOKEN_UNSUPPORT": "This token is not supported for trading.", "TRANSACTION_BUILD_FAILED": "Failed to build the transaction. Please check token info or try again.", "TRANSACTION_SIGN_FAILED": "Failed to sign the transaction. Please check your wallet.", "transactionFail": "Transaction failed", "USER_NOT_FOUND": "User wallet not found. Please connect your wallet to continue."}, "tabs": {"buy": "Buy", "limitOrder": "Limit", "marketTrade": "Market", "quickTrade": "Quick", "quickTradeBuy": "Quick buy", "quickTradeSell": "Quick sell", "sell": "<PERSON>ll", "trailingStopLoss": "Trailing TP/SL"}, "trailingTab": {"activationPrice": "Activation Price", "highPointPullback": "High Point Pullback", "soldQuantity": "Sold Quantity", "tooltipActive": "Activate Take-Profit/Stop-Loss (Optional)", "tooltipText": "The activation price is the condition to trigger trailing take-profit/stop-loss. The trailing order is activated only when the latest market price reaches or exceeds the activation price."}}, "orderSettings": {"globalSettings": "Global Settings", "mevProtectMode": "MEV Protection"}, "position": {"annualizedReturn": "Annualized Return", "cancel": "Cancel", "closePosition": "Close Position", "confirm": "Confirm", "cross": "Cross", "currentPosition": "Current Position", "entryPrice": "Entry Price", "fee": "Fee", "funding": "Funding", "holdingValue": "Holding Value", "inddexPrice": "index price", "indexPrice": "Index Price", "isolated": "Isolated", "leverage": "Leverage", "liquidationPrice": "Liq<PERSON>", "long": "<PERSON>", "margin": "<PERSON><PERSON>", "markPrice": "<PERSON>", "openInterest": "Open Interest", "perpetual": "Per<PERSON>", "pnl": "PnL", "priceAndChange": "Price/Change", "quantity": "Quantity", "realizedPnl": "Realized PnL", "setupTPSL": "Setup TP/SL", "share": "Share", "short": "Short", "size": "Size", "slPrice": "SL Price", "stopLoss": "SL", "stopLossPrice": "SL Price", "takeProfit": "TP", "takeProfitPrice": "TP Price", "title": "Position", "todayPnL": "Today PnL", "unrealizedPnl": "Unrealized PnL", "updateSuccessful": "Update successful", "value": "Value", "viewOrder": "View Order"}, "privacyPolicy": {"title": "Privacy Policy"}, "search": {"24hTrending": "24h Trending", "cancel": "Cancel", "clear": "Clear", "holders": "Holders", "placeholder": "Search token or contract address", "pool": "Pool", "popularSearch": "Popular Search", "recent": "Recent", "search": "Search", "tokens": "Tokens", "transactions": "Txs", "volume": "Vol"}, "settings": {"addRule": "Please enter a valid SOL token address", "addToBlacklist": "Chinese", "advancedSettings": "EN", "amount": "amt", "antiSandwich": "Japanese", "apply": "Apply", "autoFollow": "automatic copy sale", "balance": "balance", "balanceWithUnit": "balance:", "batch": "Batch Profit/Loss Cut", "blacklistTooltip": "Tokens added to the blacklist will not be copied purchased", "buySettings": "Purchase settings", "cancel": "cancel", "contactCustomerService": "Contact customer service", "creationTime": "Creation time", "customTPSL": "Custom profit/stop loss", "enterAddress": "Enter copy wallet address", "enterPositiveInteger": "Enter an integer >=0", "enterStopLoss": "Enter stop loss rate", "enterTakeProfit": "Enter profit probability", "enterTokenAddress": "Enter SOL token address", "estimatedLoss": "expected loss", "estimatedProfit": "Expected profit", "estimatedValue": "≈$0", "fixedBuy": "fixed purchase", "followAddress": "copy address", "followAmount": "Copy amount", "global": "global", "invalidAddress": "Please enter a valid SOL wallet address", "invalidToken": "Please enter a valid SOL token address", "language": {"chinese": "中文", "english": "EN", "hindi": "हिंदी", "japanese": "日本語"}, "level": "level", "linkWallet": "Link a wallet", "marketValue": "Market price", "max": "maximum", "maxFollow": "maximum copy purchase", "min": "minimum", "minBurnPool": "minimum burn pool", "noFullSellWarning": "Currently, there is no rule for ``100%'' sale. Unable to completely sell copy position", "noSell": "No copies sold", "off": "off", "others": "others", "paste": "paste", "platform": "platform", "pool": "pool", "priorityFee": "priority fee", "quantity": "quantity", "save": "keep", "saveImage": "Save", "sellOutPercent": "sale rate", "sellSettings": "Sale settings", "single": "single", "singleAddPositionCount": "Number of single positions added", "slippage": "slippage", "stopLoss": "Loss cut", "takeProfit": "Profitability", "title": "Wallet copy trade settings", "tokenBlacklist": "token blacklist", "tpPercent": "profit probability", "trailingStopLoss": "Trailing stop loss", "transactionSettings": "Trading settings", "tutorial": "Usage tutorial", "value": "value"}, "shareBottomSheet": {"title": "Share Page"}, "smartMoney": {"filter": {"7dayWinningRate": "By 7-day winning rate", "latestAttention": "Latest attention"}, "tabs": {"Activities": "Activites", "smartMoney": "Smart money", "topTalents": "Top talents", "walletCopy": "Copy Trade"}, "totalFollowers": "{{followers}} Smart Money Follows"}, "status": {"accountAlreadyExist": "This account is already logged in", "loginFail": "<PERSON><PERSON> failed! Please try again later", "loginSuccess": "Login success"}, "termsOfUse": {"title": "Terms of Use"}, "test": "test", "toast": {"addFavoriteFailed": "Add to favorite failed", "addFavoriteSuccess": "Add to favorite successfully", "cancel": "Cancel", "cancelOrderSuccess": "Order canceled", "confirm": "Confirm", "connectWalletFailed": "Connect failed", "copiedSuccess": "<PERSON>pied", "linkSuccess": "Linked", "loginFailed": "<PERSON><PERSON> failed", "maxPeriods": "Max 6 periods", "modifyOrderSuccess": "Modification successfully", "periodInUse": "Period in use", "removeFavoriteFailed": "Remove from favorite failed", "removeFavoriteSuccess": "Remove from favorite successfully", "saveSuccess": "Saved", "termsAgreement": "Accept terms & privacy policy", "walletNotInstalled": "Not installed"}, "tokenData": {"ageToken": "Age Token", "athDate": "ATH Date", "athPrice": "ATH Price", "atlDate": "ATL Date", "atlPrice": "ATL Price", "blackList": "Blacklist", "burn": "<PERSON><PERSON>", "circulatingSupply": "Circulating Supply", "holders": "Holders", "issueDate": "Issue Date", "liquidityPool": "Liquidity Pool", "marketCap": "Market Cap", "noMint": "NoMint", "openPrice": "Open Price", "pumpfun": {"amount": "Amount", "buyAddresses": "Buy Addresses", "buyAmount": "Buy Amount", "buyTransaction": "Buy Transactions", "sellAddresses": "Sell Addresses", "sellAmount": "<PERSON><PERSON>", "sellTransaction": "Sell Transactions", "title": "Pump Fun", "transactions": "Transactions", "uniqueAddresses": "Unique Addresses", "volume": "Volume"}, "statistic": {"amount": "Amount", "buyAddresses": "Buy Addresses", "buyAmount": "Buy Amount", "buyTransaction": "Buy Transactions", "poolValue": "Pool Value", "sellAddresses": "Sell Addresses", "sellAmount": "<PERSON><PERSON>", "sellTransaction": "Sell Transactions", "transactions": "Transactions", "uniqueAddresses": "Unique Addresses", "volume": "Volume"}, "top10": "Top 10", "top10Holdings": "Top 10 Holdings", "totalSupply": "Total Supply", "turnoverRate24h": "24h Turnover Rate", "vol24h": "24h Vol"}, "tokenSelectionDrawer": {"marketCap": "Market Cap", "searchPlaceholder": "Token name or contract address", "trending24h": "24h Trending"}, "tradeSettings": {"antiClipMode": "Anti-Clipping Mode (MEV Protection)", "antiClipModeTooltip": "MEV attacks, commonly known as clipping, happen when attackers front-run your trade to raise or lower prices. Enabling this will protect your trade from such attacks.", "antiPinchMode": "Anti-pinch mode (MEV reduction)", "apply": "Apply", "auto": "Auto (20%)", "autoFee": "Auto Fee", "basicFee": "Basic Fee", "custom": "Custom", "customGasFeeError": "Gas fee must be greater than or equal to {{min}}", "customPriorityFeeError": "Priority fee must be between {{min}} and 2 SOL.", "customSlippageError": "Slippage must be between 1% and 50%.", "danggerNote": "Please note that transaction settings are global settings. If you manually adjust any settings for this transaction, future transactions will use these settings by default.", "fast": "Fast", "fastest": "Fastest", "gasFee": "Gas Fee", "gasFeeTooltip": "Gas fee is the cost of processing a transaction on the blockchain. It is paid to miners or validators for including the transaction in a block. The higher the gas fee, the faster the transaction will be processed.", "marketPrice": "Market Price", "preset": "Preset {{preset}}", "priorityFee": "Priority Fee", "priorityFeeTooltip": "Priority fee is the additional amount a user is willing to pay to miners or validators to prioritize their order and speed up execution. The higher the fee (within the recommended range), the greater the success rate and speed. If the transaction fails, the fee is still deducted from your balance.", "priotyFee": "Priority Fee", "quick": "Quick", "reset": "Reset", "slippage": "Slippage", "slippageTooltip": "Slippage refers to the difference between the expected transaction price and the execution price. If the price change exceeds the set slippage, the transaction will be canceled, but network fees will still be charged.", "title": "Trade Settings (Global)", "totalCost": "Total Cost"}, "transaction": {"amount": "Amount", "buy": "Buy", "marketCap": "Market Cap", "price": "Price", "quantity": "Quantity", "sell": "<PERSON>ll", "time": "Time", "token": "Token", "type": "Type"}, "unknown": "unknown", "wallet": {"addWallet": "Add a Wallet", "applications": "Apps", "available": "Avail", "availableAssets": "Assets", "availableQuantity": "Quantity", "balance": "{{chain}} Balance", "clickConnect": "Click to connect", "confirmDisconnect": "Disconnect?", "connected": "Connected", "connectFailed": "Connect failed", "connectGuide": "Connect", "connectToNetwork": "Connect to {{chain}}", "current": "Current", "disconnect": "Disconnect", "downloadWallet": "Download", "installed": "Installed", "linked": "Linked", "manage": "Manage", "manageWallet": "Manage Wallet", "noData": "No data", "notInstalled": "Not installed", "positionCurrency": "Holding token", "qrCodeInstruction": "If your wallet isn't listed, scan this QR code with your wallet (requires WalletConnect v2)", "returnToApp": "Return to XBIT", "saveToAlbum": "Save", "selectNetwork": "Select Network", "selectWallet": "Select wallet", "switchWallet": "Switch Wallet", "telegramWallet": "Telegram", "title": "Wallet", "totalAssets": "Total Assets", "warning": {"walletConnectd": "Only one wallet can be connected per chain at a time. Please disconnect the current wallet before connecting a new one."}}, "walletCopy": {"amount": "Amount", "asset": "<PERSON><PERSON>", "autoFollowSell": "Auto Sell", "batch": "<PERSON><PERSON>", "buy": "Buy", "buySellCount": "Buy/Sell", "buyType": {"fixedamount": "Fixed Buy", "FixedAmount": "Fixed Buy", "maxamount": "Max Buy", "MaxAmount": "Max Buy", "null": "null"}, "cancel": "delete", "changeWalletName": "Change Wallet Name", "confirmDelete": "Are you sure you want to delete?", "copyType": "Copy type", "customBatch": "Custom TP/SL", "customSingle": "Custom TP/SL", "deleteMessage": " wallet copy trade? Once deleted, the record cannot be recovered!", "duration": "Duration", "enterName": "Enter name", "errorUpdatingStatus": "Update status error", "failedList": "Failed list", "failedReason": "Failed reason", "filter": {"all": "All", "asset": "Asset filter", "maxVolume": "Max ($)", "minVolume": "Min ($)", "type": "Type filter", "volume": "Volume filter"}, "fixedBuy": "Fixed Buy", "maxFollowBuy": "Max Buy", "modifyParameters": "Modify", "orderRecord": "Order Record", "pauseCopy": "Coping paused", "paused": "Paused", "price": "price", "profit": "Profit", "realizedProfit": "Realized", "recentFollow": "Recent follow", "running": "Running", "save": "Save", "searchToken": "Search token", "sell": "<PERSON>ll", "sellParameters": "Parameters", "sellType": {"auto": "Auto Sell", "Auto": "Auto Sell", "multitpsl": "Customized TP-SL", "MultiTPSL": "Customized TP-SL", "nocopy": "No Copy", "NoCopy": "No Copy", "null": "null", "singletpsl": "Customized TP-SL", "SingleTPSL": "Customized TP-SL"}, "settings": {"addRule": "Add Rule", "addToBlacklist": "Add to Blacklist", "advancedSettings": "Advanced Settings", "amount": "Amount", "antiSandwich": "Anti-SW", "apply": "Applicable", "autoFollow": "Auto Copy Sell", "balance": "Balance", "balanceWithUnit": "Balance: ", "batch": "Batch TP/SL", "blacklistTooltip": "Tokens added to blacklist will not be copied", "buySettings": "Buy Settings", "cancel": "Cancel", "creationTime": "Creation Time", "currencyBlacklist": "<PERSON><PERSON>", "customTPSL": "Custom TP/SL", "enterAddress": "Enter copy wallet address", "enterPositiveInteger": "Enter integer >=0", "enterStopLoss": "Enter stop loss %", "enterTakeProfit": "Enter take profit %", "enterTokenAddress": "Enter SOL token address", "error": {"createConfig": "has some error on create config"}, "estimatedLoss": "Estimated Loss", "estimatedProfit": "Estimated Profit", "estimatedValue": "≈$0", "fixedBuy": "Fixed Buy", "followAddress": "Copy Address", "followAmount": "<PERSON><PERSON> Amount", "global": "Global", "invalidAddress": "Please enter a valid SOL wallet address", "invalidToken": "Please enter a valid SOL token address", "level": "Level", "marketValue": "Market Value", "max": "Max", "maxFollow": "Max Copy Buy", "min": "Min", "minBurnPool": "Min Burn Pool", "noFullSellWarning": "No \"100%\" sell rule currently, cannot fully sell copy position", "noSell": "No Copy Sell", "off": "Off", "others": "Others", "paste": "Paste", "platform": {"error": {"minimumOne": "At least one platform must be selected"}, "moonshot": "moonshot", "other": "other", "platform": "Platform", "pump": "pump", "raydium": "raydium"}, "pool": "Pool", "priorityFee": "Prio <PERSON>e", "quantity": "Quantity", "reset": "Reset", "save": "Save", "sellOutPercent": "SR", "sellSettings": "<PERSON><PERSON>", "single": "Single", "singleAddPositionCount": "Single Add Position Count", "slippage": "Slip", "stopLoss": "Stop Loss", "takeProfit": "Take Profit", "title": "Copy Trading Settings", "tokenBlacklist": "<PERSON><PERSON>", "tpPercent": "TP", "trailingStopLoss": "Trailing SL", "transactionSettings": "Tx Settings", "tutorial": "Tutorial", "value": "Value"}, "single": "Single", "soldPrice": "Sold price", "startCopy": "Start copying", "stopLoss": "SL ", "successList": "Success list", "tags": {"fresh": "new", "kol": "KOL/VC", "pumpsm": "spy", "smartmoney": "<PERSON>g", "sniper": "spy", "toptrader": "top trader", "whale": "spy"}, "takeProfit": "TP ", "toast": {"modifyNameError": "modify name error", "modifyNameSuccess": "modify name success"}, "totalProfit": "Total", "transactionHash": "Tnx hash", "type": "Type", "unrealizedProfit": "Unrealized", "volume": "Volume", "walletFollow": "Wallet follow", "warning": {"login": "Please log in first to follow this wallet."}, "winRate": "Win Rate"}, "walletDetail": {"activity": {"actions": "Actions", "age": "Age", "amount": "Amount", "buy": "Buy", "pnl": "PnL", "price": "Price", "quantity": "Quantity", "sell": "<PERSON>ll", "time": "Time", "title": "Activity", "token": "Token", "type": "Type", "volume": "Volume"}, "activityTable": {"actions": "Actions", "date": "Date", "finalPrice": "Sold Price", "pnl": "PnL", "type": "Type", "volume1": "Amount", "volume2": "Volume"}, "analysis": {"avgDuration": "{{period}} Avg Duration", "balance": "Balance", "markPrice": "Token Avg Cost", "pnl": "Profit and loss in the past {{period}}", "tokenAvgCost": "{{period}} Total Cost", "tokenAvgRealizedProfits": "{{period}} Token Avg Realized Profits", "totalCost": "{{period}} Total Cost", "txs": "{{period}} Txs"}, "averageHoldingTime": "Average Holding Time", "btn": {"follow": "Follow", "unfollow": "Unfollow"}, "distribution": {"priceRange": "Price Range", "profit": "Profit", "title": "Profit distribution in the past {{period}}", "tradedAmount": "Traded Amount"}, "focus": "focus on", "holderTable": {"amount": "Amount", "avgBuyPrice": "Avg Buy Price", "avgSellPrice": "Avg <PERSON><PERSON>", "holding": "Holding value", "holdingLength": "Holding Duration", "lastActive": "Last Active", "positionPercentage": "Holding ratio", "realized": "Realized PnL", "totalBuy": "Total Buy", "totalProfit": "Total PnL", "totalSell": "Total Sell", "txCount": "Tx Count", "unrealized": "Unrealized PnL"}, "holdings": {"actions": "Actions", "askPrice": "Ask Price", "avgBuyPrice": "Avg Buy Price", "avgSellPrice": "Avg <PERSON><PERSON>", "balance": "Balance", "bidPrice": "<PERSON><PERSON>", "boughtAmount": "Bought Amount", "holdingDuration": "Holding Duration", "holdingRate": "Holding Rate", "holdingValue": "Holding Value", "lastActive": "Last Active", "positions": "Holding", "realizedPnL": "Realized PnL", "souldAmount": "Sold Amount", "title": "Holdings", "token": "Token", "totalPnL": "Total PnL", "txs": "Txs", "unrealizedPnL": "Unrealized PnL"}, "msg": {"flow": "Followed successfully!", "unflow": "Unfollowed successfully!"}, "periodPnLWithPeriod": "{{period}} PnL", "profitDistribution": "Profit Distribution", "realizedPnL": "Realized PnL", "realizedProfitWithPeriod": "{{period}} Average Realized Profit", "summary": "Summary", "token": {"buy": "Buy", "sell": "<PERSON>ll"}, "tooltip": {"markPrice": "1D Average purchase cost of a currency = ΣTotal purchase amount in the most recent day ÷ Number of currencies purchased in the most recent day (rounded to two decimal places)", "tokenAvgCost": "Click the question mark next to it and a pop-up window will appear: the total cost of buying tokens in the last 1D/7D/30D.", "tokenAvgRealizedProfits": "Click the question mark next to it, and a pop-up window will appear: the average realized profit of each token sold in the last 1D/7D/30D (not counting unrealized profit)"}, "totalPnL": "Total PnL", "totalPurchaseCostWithPeriod": "Order successful", "txsWithPeriod": "{{period}} Txs", "unrealizedPnL": "Unrealized PnL", "walletBalance": "Wallet Balance"}, "withdrawal": {"button": {"failed": "Depo<PERSON><PERSON> failed", "insufficientFunds": "Insufficient funds", "processing": "Withdraw {{amount}} USDC, awaiting confirm", "success": "Withdrawal successful: {{amount}} USDC", "withdraw": "Withdraw", "withdrawWithAmount": "Withdraw {{amount}} USDC"}, "errors": {"orderBookTransactionFailed": "Transaction failed"}, "estimatedTime": "Estimated time: {{time}} min", "fromContractAccount": "From your contract account", "insufficientFunds": "Insufficient funds", "max": "Max", "modal": {"failed": "Deposit Failed", "failureReason": "Reason for failure", "success": "<PERSON><PERSON><PERSON> Successful"}, "networkMessage": "USDC will be sent to your wallet via the Arbitrum network. The withdrawal is expected to arrive within 5 minutes. Fee: $1.", "title": "Withdraw USDC", "toArbitrum": "To Arbitrum", "transactionHistory": "Transaction History"}}