{"activityTable": {"filters": {"addpood": "加池子", "all": "全部", "buyit": "买入", "reduce": "减池子", "sell": "卖出"}}, "ai": {"analyzing": "分析中...", "avatarAnalysis": "头像分析", "avgComments": "平均评论数", "avgLikes": "平均点赞数", "avgRetweets": "平均转发数", "avgViews": "平均浏览量", "button": {"cancel": "取消", "confirm": "确认", "reset": "重置", "save": "保存"}, "buttons": {"avatarAnalysis": "头像分析", "narrativeTheme": "主题叙事", "websiteAnalysis": "官网分析"}, "collapse": "收起", "contract": "合约", "devAddress": "开发者地址", "devHistory": "开发历史", "devHoldRatio": "DEV持仓占比", "devStartups": "DEV创业次数", "expand": "展开", "firstPostTime": "首次发推时间", "followers": "粉丝数", "influential": {"followerCount": "粉丝数量", "title": "有影响力的推特关注者（{{count}}）"}, "insiderRatio": "老鼠仓比例", "launchDate": "上线日期", "narrative": "主题叙事", "officialExpand": "点击展开", "officialTitle": "官方数据分析", "onchainTextContent": "该推特似乎是由著名企业家Elon Musk管理，但其推文并未展示与任何特定meme币相关的信息，也没有提到与区块链或加密货币直接相关的内容。推文集中在科技、人工智能和一些个人观点上，并未涉及任何代币的合约地址或名称。该推特似乎是由著名企业家Elon Musk管理，但其推文并未展示与任何特定meme币相关的信息，也没有提到与区块链或加密货币直接相关的内容。推文集中在科技、人工智能和一些个人观点上，并未涉及任何代币的合约地址或名称。", "onchainTextNote": "本叙事分析由XBIT机器人自动生成，仅供参考。", "onchainTextTitle": "链上数据分析", "onChainTitle": "链上数据分析", "poolAddress": "流动池地址", "poolTime": "流动池开启时间", "relatedProjects": "相关项目", "relatedWallets": "同源钱包数", "totalPosts": "总推文数", "twitterChanges": "推特改名次数", "twitterCreatedAt": "推特注册时间", "walletBalance": "钱包余额", "websiteAnalysis": "官网分析"}, "aiAnalysis": {"avatarAnalysis": "头像分析", "narrative": "主题叙事", "websiteAnalysis": "官网分析"}, "appSettings": {"aboutUs": {"cancel": "取消", "clearCache": "清除缓存", "clearCacheMessage": "确定清除缓存吗？", "clearCacheSuccess": "清除缓存成功\t", "confirm": "确定", "contactUs": "联系我们", "currentVersion": "当前已是最新版本", "officalEmail": "官方邮箱", "officialEmail": "官方邮箱", "privacyPolicy": "隐私政策", "rateXBit": "给XBIT评分", "slogan": "链上即未来，交易即自由", "termsOfService": "使用条款", "title": "关于我们"}, "browsingHistory": "浏览历史", "cancel": "取消", "color": {"greenUpRedDown": "绿涨红跌", "redUpGreenDown": "红涨绿跌"}, "colorPreference": "涨跌颜色", "colors": {"gainLossColor": "涨跌颜色", "title": "颜色设置"}, "contactCustomerService": "联系客服", "contactUs": "联系我们", "contactUsSubtitle": "版本、隐私政策", "customerService": "客服", "disabled": "未开启", "enabled": "已启用", "fundingHistory": "资金记录", "googleAuth": {"app": "谷歌验证码", "cancel": "取消", "confirmReset": "确定重置", "confirmResetTitle": "确定重置谷歌验证吗？", "confirmUnlink": "确定解绑", "download": "下载谷歌验证器", "guide": "使用谷歌验证码保护您的账户和资金安全", "linked": "已绑定", "linkNow": "立即绑定", "notLinked": "未绑定", "reset": "重置", "title": "谷歌验证", "unlink": "解绑", "unlinkToast": "解绑成功", "warningMessage": "为了确保您的账户安全，解除绑定谷歌验证器后<span>24小时内</span>不允许从Telegram钱包提现。"}, "googleAuthenticator": "谷歌验证", "language": "语言", "languages": {"title": "语言设置"}, "linkWallets": "添加钱包", "loginRequired": "请先登录", "messageNotifications": "消息通知", "myRewards": "我的反佣", "notifications": {"enableBtn": "开启通知", "enablePushNotifications": "打开推送通知", "enablePushNotificationsSubtitle": "开启系统通知，实时接收消息推送", "guideManual": "请在浏览器设置中手动开启通知权限", "othersSubtitle": "及时了解常规更新和后台事件。", "othersTitle": "其他的", "priceAlertSubtitle": "实时合约价格预警推送通知", "priceAlertTitle": "合约价格预警", "smartMoneySubtitle": "获取您关注的聪明钱最新交易动态", "smartMoneyTitle": "关注的聪明钱活动", "title": "消息通知", "tradingSubtitle": "订阅合约交易信号推送通知", "tradingTitle": "合约信号通知"}, "saveImage": "保存相册", "shareXBIT": "分享 XBIT", "shareXBITSubtitle": "去中心化交易所(DEX)", "spotSettings": "现货设置", "unlinked": "未绑定", "unset": "未设置", "userFeedback": "用户反馈", "userFeedbackSubtitle": "反馈被采纳可获得 20U 奖励", "userManual": "使用教程", "withdrawalAddress": "提现地址", "withdrawalWarningMessage": "为了您的资金安全，需要绑定谷歌验证器及设置白名单地址后才能进行提现，设置白名单地址后<span>3小时内不允许提现"}, "assers": {"transfers": {"availableBalance": "可用余额"}}, "assets": {"crossChainBridge": "跨链桥", "deposit": {"address": " 存入地址", "amountReived": "到账数量", "availableBalance": "可用余额", "chooseAccount": "选择账户", "chooseToken": "选择代币", "confirmDeposit": "确认存入", "enterAmount": "输入金额", "fee": "手续费", "max": "全部存入", "min": "最低存入", "quantity": " 存入数量", "searchToken": "搜索代币", "selectNetwork": "选择网络", "selectToken": "选择代币", "shareAddress": "共有", "title": "充值", "warning": "仅接收Solana的SOL充值，不支持其它币种充值，否则可能会造成资金损失", "warningEthereum": "仅接收Ethereum的ETH充值，不支持其它币种充值，否则可能会造成资金损失", "warningSolana": "仅接收Solana的SOL充值，不支持其它币种充值，否则可能会造成资金损失"}, "funding": {"all": "全部", "allNetworks": "所有网络", "assets": "总资产", "averageCost": "平均成本", "crypto": "加密货币", "hideSmallAssets": "隐藏<1U资产", "holding": "持仓价值", "orderHistory": "订单记录", "pnl": "盈亏", "positionValue": "持仓价值", "title": "资金账户"}, "fundingAccount": "资金账户", "futures": {"account": "合约账户", "assets": "总资产", "availableBalance": "可用", "availableForWithdraw": "asset.futures.availableForWithdraw", "availableForWithdrawal": "可提取金额", "availableMargin": "可用保证金", "balance": "余额", "connectWallet": "链接钱包", "crossMarginLeverage": "全仓账户杠杆", "crossMarginRatio": "全仓保证金比率", "fullMarginRatio": "全额保证金比例", "inOrder": "在委托", "leverage": "杠杆", "maintenanceMargin": "维持保证金", "name": "名称", "noDataYet": "暂无数据", "pnl": "盈亏", "positionHistory": "成交价值", "positions": "持仓", "positionValue": "持仓价值", "title": "合约", "todayPnL": "今日盈亏", "unrealizedPnl": "未实现盈亏"}, "futuresAccount": "合约账户", "login": {"thirdParty": "第三方登录", "web3Wallets": "Web3钱包"}, "orderHistory": "订单记录", "overview": {"allAssets": "全部资产", "allTypes": "全部方向", "amount": "持仓数量", "change": "涨跌幅", "confirmDeposit": "充值至合约账户", "confirmWithdrawal": "从合约账户提现至钱包", "cost": "持仓价值", "cross": "全仓", "deposit": "充值", "deposite": "充值", "depositeVault": "购买金库", "depositMessage": "用第三方钱包登录仅支持向合约账户充值", "depositVault": "购买金库", "dontRemindNextTime": "下次不再提醒", "estimatedAssets": "预估资产", "estimatedAssetsAgree": "我知道了", "estimatedAssetsDescription": "预估总资产=合约账户资产+现货账户资产", "exchange": "兑换", "fundingHistory": {"date": "时间", "fee": "手续费", "fromAccount": "从", "receiverAddress": "接收地址", "senderAddress": "发送地址", "titleAddVaultFailed": "购买失败", "titleAddVaultSuccess": "购买成功", "titleDepositeSuccess": "充值失败", "titleDepositFailed": "充值失败", "titleDepositSuccess": "充值成功", "titleExchangeFailed": "兑换失败", "titleExchangeSuccess": "兑换成功", "titleRedemptionFailed": "赎回失败", "titleRedemptionSuccess": "赎回成功", "titleWithdrawalFailed": "提现失败", "titleWithdrawalSuccess": "提现成功", "toAccount": "到", "transactionHash": "交易哈希", "type": "类型"}, "futures": "合约", "futuresSpotTransfer": "合约现货兑换", "isolated": "逐仓", "long": "做多", "perpetual": "{{pair}}永续", "pnl": "盈亏", "price": "价格", "redeemVault": "赎回金库", "short": "做空", "spot": "现货", "tabAssets": "资产", "tabFundingRecords": "资金记录", "title": "总览", "todayPnL": "今盈亏", "vaultName": "名称", "vaults": "金库", "widthdrawal": "提现", "withdrawal": "提现", "withdrawalMessage": "用第三方钱包登录仅支持从合约账户提现至钱包"}, "splash": {"connectWallet": "链接钱包", "slogan": "去中心化交易所，毫秒级行情", "sub": "极速成交，马上交易！"}, "spot": {"all": "全部", "allNetwork": "全部网络", "allNetworks": "所有网络", "assets": "总资产", "averageCost": "平均成本", "crypto": "加密货币", "hideSmallAssets": "隐藏<1U资产", "orderHistory": "订单记录", "pnl": "盈亏", "positionValue": "持仓价值", "title": "现货"}, "spotAccount": "现货账户", "switchNetwork": {"allNetwork": "全部网络"}, "title": "资产", "token": {"averageCost": "平均成本", "buy": "买入", "costPrice": "成本价", "estimatedAssets": "预估资产", "pnl": "盈亏", "sell": "卖出", "transactionHistory": "交易记录", "yieldRate": "收益率"}, "transfer": "兑换", "transfers": {"amount": "划转数量", "amountPlaceholder": "请输入数量", "availableBalance": "可用余额", "availableBalanceInUsd": "可用余额 {{balance}} USD", "balanceUnavailable": "当前币种无可划转资产，请选择其他币种", "confirm": "确认", "fromAccount": "从", "insufficientBalance": "余额不足", "max": "最大", "selectToken": "选择币种", "toAccount": "到", "token": "币种"}, "withdraw": "提币", "withdrawal": {"addNewAddress": "添加新地址", "amount": "转出数量", "availableBalance": "可用余额", "checkTheDetails": "查看详情", "chooseToken": "选择币种", "confirmWithdraw": "确认提币", "fee": "手续费", "fundsReceived": "已到账", "gasFee": "Gas费", "goToTransaction": "去交易", "history": "资金记录", "insufficientBalance": "余额不足", "learnMore": "了解详情", "modifyWhitelist": "修改白名单", "receivingAmount": "到账数量", "recipientAddress": "转出地址", "requestWithdrawSuccess": "提现请求成功", "searchToken": "搜索币种名称、合约地址", "selectNetwork": "选择网络:", "selectToken": "选择代币", "tokenRecipientAddress": "接收{{token}}地址", "transactionInprogress": "交易正在进行中", "tryAgain": "再试一次", "wallet": "{{name}} 钱包", "warningWithdrawTele": "请确保提现地址支持Solana网络。如接收地址不支持Solana链资产，会造成您的资产损失。", "warningWithdrawWallet": "USDC将通过Arbitrum网络发送到您的地址。\n从提取的USDC中将扣除1USDC的费用。\n提款预估在5分钟内到账。", "withdrawAll": "全部提现", "withdrawFailed": "兑换失败", "withdrawFailedMessage": "失败原因失败原因失败原因失败原因失败原因失败原因失败原因失败原因失败原因", "withdrawSuccessMessage": "您的 {{token}} 已发送至您的合约账户。", "withdrawToken": "提现{{token}}", "withdrawTokenOnNetwork": "提现 {{network}}的 {{token}}"}}, "Avg price": "成本价", "bottomNav": {"assets": "资产", "chat": "聊天", "discover": "发现", "homepage": "首页", "market": "市场", "monitoring": "监控", "smartMoney": "聪明钱", "trading": "交易"}, "button": {"add": "添加", "apply": "确定", "cancel": "取消", "confirm": "确认", "reset": "重置", "save": "节省"}, "cardTag": {"limitBuy": "限价买入", "limitSell": "限价卖出", "movingStopLossSell": "移动止盈止损卖出"}, "categories": {"change24h": "24h平均涨幅", "marketCap": "总市值", "topToken": "龙一", "upDown": "上涨/下跌", "volume24h": "24h成交额"}, "categoryDetail": {"all": "全部", "avgIncrease": "24h平均涨幅", "downCount": "下跌", "h24ChangePercent": "24小时%", "h24Volume": "24h成交额", "h24VolumeLabel": "24小时成交额", "hot": "热门", "latestPrice": "最新价", "new": "新上", "tag": "龙一", "title": "分类", "tokenType": "币种/市值", "top1": "龙一", "top2": "龙二", "top3": "龙三", "totalMarketCap": "总市值", "totalTokens": "已收录代币", "upCount": "上涨"}, "chart": {"area": "面积图", "averageCandlestick": "平均K线图", "bar": "美国线", "buttons": {"cancel": "取消", "confirm": "确定"}, "candlestick": "K线图", "dataFormat": {"amount": "数量", "price": "价格", "total": "总额"}, "hollowCandlestick": "空心K线图", "line": "线形图", "period": {"currentPeriodNotInList": "无法删除活动时间段：{{period}}", "day": "1日", "maximum6Periods": "最多支持6个周期", "month": "1月", "resetToDefault": "恢复默认", "selectedPeriod": "已选周期", "supportsUpTo6Periods": "最多支持6个周期", "title": "时间段", "week": "1周", "year": "1年"}, "toolbar": {"indicator": "指标", "klineStyle": "K线样式", "marketCap": "市值", "price": "价格", "setting": "设置"}}, "connectingToTelegram": "正在连接到 Telegram...", "const": {"time": {"d": "d", "day": "天", "days": "天", "h": "h", "hour": "小时", "hours": "小时", "m": "米", "minute": "分钟", "minutes": "分钟", "month": "月", "months": "个月", "s": "秒", "second": "第二", "year": "年", "years": "年"}}, "constant": {"all": "全部"}, "contractMonitoring": {"attentionItems": "注意项", "blacklist": "黑名单", "blacklistEnabled": "黑名单已启用", "blacklistEnabledDesc": "该合约包含黑名单机制，可能限制部分用户的交易（蜜罐风险）。", "burnPool": "烧池子", "buyTax": "买入税", "canMint": "能否增发", "contractAddress": "合约地址", "contractCreator": "合约创建者", "contractInfo": "合约信息", "contractOwner": "合约所有者", "disclaimer": "免责声明：本合约监控结果基于公开数据。XBIT不对该结果做任何形式的背书或推荐。", "lowRiskItems": "低风险项", "mediumRisk": "中风险", "noMintFunction": "无增发功能", "noMintFunctionDesc": "该代币没有隐藏的增发机制。隐藏增发可能增加代币供应量，影响价格。", "noProxyContract": "无代理合约", "noProxyContractDesc": "合约不包含代理机制。代理机制允许项目方替换代币逻辑，影响价格和机制。", "ownershipRenounced": "所有权已放弃", "ownershipRenouncedDesc": "该代币合约是开源的，可以验证。非开源合约可能包含欺骗用户的恶意机制。", "riskDetection": "风险检测", "riskItems": "风险项", "sellTax": "卖出税", "taxModifiable": "税收可修改", "taxModifiableDesc": "项目方可能保留了修改交易税收的权限。如果税收提高到49%以上，代币可能变得无法交易（蜜罐风险）。", "title": "合约监控", "tokenInfo": "代币信息", "tokenName": "代币名称", "tokenSymbol": "代币符号", "top10": "TOP10 19.99%", "whaleProtection": "巨鲸保护（交易限制）", "whaleProtectionDesc": "该代币存在交易限制。许多项目会限制交易数量，可能导致用户无法将持仓变现。"}, "CopyTrade_CreateConfig_DuplicateConfig": "您已经复制该地址。", "crossChainBridge": {"back": "返回", "checkDetails": "查看详情", "crossChainExchange": "跨链兑换", "enterAmount": "输入金额", "estimatedTime": "预计时间", "exchange": "兑换", "exchangeRate": "汇率", "fee": "手续费", "from": "从", "link": {"authorization": "授权", "authorizationNote": "允许 XBIT 从您的钱包中充值 {{token}}。", "l2Confirmation": "L2确认", "l2ConfirmationNote": "确认您的充值详情并签名以继续此交易。", "linkNote": "连接钱包", "linkYourWallet": "将您的钱包连接到 XBIT", "title": "链接"}, "redeemFailed": "失败", "redeemFailedNote": "失败原因...", "redeeming": "正在兑换", "redeemingNote": "您的 {{token}} 将在30分钟或更短的时间内到达。", "redeemSuccess": "已到账", "redeemSuccessNote": "您的 {{token}} 已发送至您的合约账户。", "redemptionPath": {"fastest": "最快", "lowestGasFee": "最低Gas费", "optimal": "最优", "title": "赎回路径"}, "title": "跨链桥", "to": "到", "viewTransaction": "查看交易"}, "currentOrdersList": {"activationPrice": "激活价格", "allDirections": "全部方向", "allOrders": "全部委托", "buy": "买入", "callbackRate": "回调幅度%", "cancelOrder": "撤单", "cancelOrderConfirm": "取消订单?", "doublePrincipalAfterPurchase": "买入后翻倍出本金", "limitOrder": "限价委托", "marketTrade": "市价交易", "modify": "修改", "orderAmount": "委托金额", "orderQuantity": "委托数量", "orderQuantityUnit": "委托数量", "sell": "卖出", "showCurrentCoinOnly": "只展示当前币种", "trailingStopLoss": "移动止盈止损", "triggerMarketCapPrice": "触发市值 / 价格"}, "detail": {"alert": {"lowLiquidity": "流动性过小，请谨慎交易"}, "buyersStatusDrawer": {"allSold": "全部卖出", "holdingOrIncrease": "有{{count}}名持仓不变或加仓", "holdingUnchanged": "持仓不变", "iconExplanation": "图标说明", "increased": "加仓", "latest100Holding": "最新前100持仓", "latest10Holding": "最新前10持仓", "partialSold": "部分卖出", "proportion": "占比", "targetedBuyer": "狙击者", "title": "{{token}}前{{total}}名买家({{holding}}/{{total}})"}, "filters": {"all": "全部", "basicVersion": "基础版", "follow8SmartMoney": "关注8个聪明钱", "followed": "已关注", "isRobot": "过滤机器人", "kol": "KOL", "newWallet": "新钱包", "projectParty": "项目方", "ratWarehouse": "老鼠仓", "sameOrigin": "同源钱包", "showCurrentTokenOnly": "只展示当前币种", "smartMoney": "聪明钱", "sniper": "狙击者", "sniper1": "狙击者", "transactionAmount": "交易金额", "transactionType": "交易类型", "whale": "鲸鱼"}, "holderTable": {"avgBuySell": "平均买价/平均卖价", "fundSource": "资金来源/转账时间", "holder": "持有者", "holdingLength": "持仓时长", "lastActive": "最后活跃", "positionPercentage": "持仓占比", "realized": "已实现利润", "solBalance": "SOL余额/创建时间", "totalBuy": "总买入", "totalProfit": "总利润", "totalProit": "总利润", "totalSell": "总卖出", "txCount": "交易数", "unrealized": "未实现利润"}, "internal": {"percentage": "55%", "title": "内盘"}, "monitoring": {"attentionItems": "关注项目", "lowRiskItems": "低风险项目", "riskItems": "风险项目"}, "myPositions": {"activatePrice": "激活价格", "activeStopLoss": "激活止盈止损(选填)", "avgBuyInMarketValue": "平均买入市值", "commissionAmount": "委托金额", "confirmClearance": "确定以市价清仓{{symbol}}?", "costPrice": "成本价", "decentralizedExchange": "去中心化交易所(DEX)", "hiddenSmallPoll": "隐藏小池子/貔貅", "hiddenSmallThan1U": "隐藏<1U资产", "highPointPullback": "高点回调幅度", "moments": "朋友圈", "more": "更多", "network": "网络", "noData": "没有数据", "numberOfPosition": "持仓数量", "oneClickSell": "一键清仓", "orderCreated": "{{symbol}}:订单创建", "orderProcessing": "{{symbol}}：订单处理中", "positionValue": "持仓价值", "profitAndLoss": "盈亏", "recharge": "向TGBot钱包充值", "returnRate": "收益率", "saveToAlbum": "保存相册", "sharePage": "分享页面", "showOnlyCurrentCurrency": "只展示当前币种", "soldQuantity": "卖出数量", "trailingStopLoss": "移动止盈止损", "transactionValue": "成交市值", "type": "类型", "walletAddress": "钱包地址", "wechat": "微信"}, "pool": {"address": "地址", "chart": "图表", "liquidity": "流动性", "poolCount": "池数量", "quantity": "数量", "time": "时间", "totalLiquidity": "总流动性", "totalValue": "总价值", "trading": "交易", "type": "类型"}, "smartMoney": {"addLiquidity": "加仓", "all": "全部", "buy": "建仓", "cancel": "取消", "clear": "清除", "confirm": "确定", "follow8SmartMoney": "关注{{length}}个聪明钱", "noData": "没有数据", "removeLiquidity": "减仓", "sell": "清仓", "showCurrentTokenOnly": "只展示当前币种", "smartMoney": "关注的聪明钱", "transactionAmount": "交易金额", "transactionType": "交易类型"}, "statistics": {"costPrice": "成本价", "holdingAmount": "持仓数量", "holdingValue": "持仓价值", "profitLoss": "盈亏", "profitLossPercentage": "盈亏%"}, "tabs": {"aiAnalysis": "AI分析", "all": "全部", "currentCommission": "当前委托", "currentCommissionWithCount": "当前委托({{total}})", "data": "数据", "followed": "已关注", "holders": "持有者{{total}}", "holdersWithCount": "持有者", "infomation": "信息", "information": "信息", "latest": "最新交易", "makeMoneyPound": "赚钱镑", "myPositions": "持仓", "payOrder": "买单", "pool": "池子", "sellOrder": "卖单", "smartMoney": "关注的聪明钱", "trading": "交易", "transactionHistory": "成交历史"}, "tags": {"addLiquidity": "加池子", "burnt": "销毁", "chipAnalysis": "筹码分析", "health": "健康", "healthScore": "健康({{value}}/{{total}})", "hold": "持有", "internalDiskPercentage": "内盘", "official": "官方", "removeLiquidity": "移除", "sellAll": "清仓"}, "tokenDetail": {"action": "操作", "address": "地址", "ago": "前", "amount": "数量", "balance": "持仓数量", "bought": "总买入({{times}}次）", "buy": "买入", "clearPosition": "减仓", "day": "日", "decreasePosition": "清仓", "direction": "方向", "endTime": "结束时间", "errorInputTransactionFilter": "输入错误，最小值不能大于最大值", "filterAddress": "筛选地址", "finalPrice": "成交价", "finalType": "筛选类型", "holdingDuration": "持仓时长", "holdingRatio": "持仓占比", "hour": "时", "increasePosition": "加仓", "inputAddress": "输入钱包地址", "inputTraders": "输入地址", "internalDisk": "内盘:", "marketCap": "市值", "marketValue": "市值", "marketValueSpecial": "@市值", "maximumTime": "最大时间跨度为1个月", "maximumTransaction": "最大成交额", "maximumVolumeWithToken": "最大({{token}})", "minimumTransaction": "最小成交额", "minimumVolume": "最小成交量", "minute": "分", "month": "月", "notSelected": "未选择", "openPosition": "建仓", "overMaxTime": "，请重新选择", "pairCreated": "已建对", "percentage": "占比", "pnl": "利润", "position": "建仓", "positionValue": "持仓价值", "price": "价格", "recentTrades": "近{{hours}}小时有{{count}}个聪明钱交易", "reselect": "重新选择", "selectEndTime": "请选择结束时间", "selectStartTime": "请选择开始时间", "sell": "卖出", "sold": "总卖出({{times}}次)", "startTime": "开始时间", "time": "时间", "timeWalletAddress": "时间/交易地址", "total": "总额", "totalValue": "总价值", "tpsl": "加池子", "traders": "交易者", "transactionAmount": "交易金额", "turnover": "成交额", "type": "类型", "value": "价值", "viewOnExplorer": "在区块链浏览器查看", "volume": "成交量", "wallet": "钱包", "year": "年"}, "tokenInfo": {"blacklist": "黑名单", "burnt": "烧池子", "contractAddress": "合约地址", "createdAgo": "{{time}} 前", "fdv": "总市值", "fromATH": "距最高价", "fromATL": "距最低价", "fromOpen": "开盘回报", "mintDisabled": "mint丢弃", "name": "代币名称", "no": "否", "symbol": "代币符号", "title": "代币信息", "yes": "是"}, "trade": {"paused": "已暂停", "running": "运行中"}, "trading": {"marketCap": "{{time}}市值", "priceChange": "涨幅", "transactions": "{{time}}交易数", "volume": "{{time}}成交额"}}, "detailStatistic": {"buyValue": "买入价值", "costPrice": "成本价", "pnl": "盈亏", "pnlPercent": "盈亏%", "positionsHeldNum": "持仓数量", "positionsHeldValue": "持仓价值", "sellValue": "卖出价值"}, "filter": {"1hTransactions": "1小时交易数", "1hVolume": "1小时成交额", "creationTime": "创建时间", "customize": "自定义", "internalProgress": "内盘进度", "marketCap": "市值", "maxHolders": "最大持有者", "maximum": "最大", "maxMarketCap": "最大市值", "maxPool": "最大池子", "maxProgress": "最大进度", "maxTime": "最大时间", "maxTransactions": "最大交易数", "maxVolume": "最大成交额", "minHolders": "最小持有者", "minimum": "最小", "minMarketCap": "最小市值", "minMaxError": "输入错误，最小值不能大于最大值", "minPool": "最小池子", "minProgress": "最小进度", "minTime": "最小时间", "minTransactions": "最小交易数", "minVolume": "最小成交额", "transactions": "笔"}, "followingWallet": {"addWallet": "添加钱包", "addWalletSuccess": "添加钱包成功", "batchCopyToClipboard": "批量复制到粘贴板", "copyToClipboard": "复制到粘贴板", "export": "导出", "exportSuccess": "批量导出成功，已复制在粘贴板", "import": "导入", "importAndExportWallets": "批量导入导出钱包", "importError": "格式不正确，请检查", "importExample": "格式示例：\n0x2b2fbbe4...1307:钱包名称1,\n0x7hd8s7au...2esc:钱包名称2,", "importNote": "格式说明：每个钱包地址之间用英文的\",\"分隔; 若有备注名请用英文\":\"隔开; 最多可关注300个地址; 仅支持Solana钱包地址导入", "importSuccess": "批量导入成功", "inputWalletAddress": "输入Solana钱包地址", "inputWalletAddress2": "粘贴导入的Solana钱包地址", "inputWalletAddressError": "非Solana链地址, 请重新输入", "inputWalletAddressError2": "格式不正确，请检查", "optional": "选填", "walletAddress": "钱包地址", "walletName": "钱包名称"}, "google": {"auth": {"button": {"abnormality": "网络异常", "averagePageviews": "设置白名单", "binding": "绑定", "cancel": "取消", "cancelAverage": "暂不设置", "confirmReset": "确定重置", "copied": "复制的", "copy": "复制", "download": "下载谷歌验证器", "exceeds": "超出大小", "nextStep": "下一步", "paste": "粘贴", "reset": "申请重置", "return": "返回资产首页", "save": "保存", "saveAverage": "保存"}, "completed": {"bindingIntro": "你的谷歌验证器已绑定成功。为了您的资金安全，请设置地址白名单", "bindingTitle": "谷歌验证器绑定成功", "msg": "预计在1个工作日内完成审核，审核结果将通过邮件发送给您。", "title": "提交成功"}, "copyright": "版权所有© 2024 XBIT.COM。保留所有权利。", "dear": "尊敬的用户：", "decode": "16位密钥", "descriptionQR": "打开谷歌验证器APP，点击右下角 + 号，扫描下方二维码，或复制下面的16位密钥到谷歌验证器", "introduction": "您申请的谷歌验证重置已通过，请用谷歌验证器APP扫描下方二维码，或复制下方16位密钥到谷歌验证器进行绑定，密钥非常重要，请妥善保存：", "modal": {"reject": {"intro1": "您未绑定谷歌验证器", "intro2": "不能重置"}}, "not_valid_wallet": "{{coin}} 地址格式无效", "note": "为了您的资金安全，系统仅支持向白名单地址转账。设置白名单后，需等待3小时生效。生效后，向白名单地址转账无需额外验证，提现过程更安全、更快捷。", "pincode": {"description": "请输入谷歌验证器上的6位验证码", "title": "谷歌验证"}, "qrcode": "二维码：", "reset": {"confirmMsg": "确定重置谷歌验证吗？", "ensureMsg": "为确保账户安全，重新绑定谷歌验证器，{{time}}不允许从Telegram钱包提现。", "ensureMsg1": "为确保账户安全，重新绑定谷歌验证器，", "ensureMsg2": "24小时内", "ensureMsg3": "不允许从Telegram钱包提现。", "form": {"exampleWallet": "例如您用MetaMask钱包充值到XBIT的TG钱包，或从XBIT的TG钱包提现到MetaMask钱包，请提供MetaMask的钱包地址\n", "label1": "您的TG账号(务必填写正确方便客服联系)", "label2": "您的Telegram User ID (在XBIT的官方Telegram Bot中输入 \"/userid\" 获取)", "label3": "您的邮箱地址 (用于接收重置邮件)", "label4": "请提供过去3个月内与XBIT的TG钱包发生过转账记录的钱包地址", "label5": "请上传上述钱包在过去三个月内与XBIT的TG钱包发生充值或提现的记录截图", "offlineTelegram": "官方Telegram", "placeholder1": "您的TG账号", "placeholder2": "您的TG User ID", "placeholder3": "您的邮箱地址", "placeholder4": "输入验证码", "placeholder5": "输入钱包地址", "sendVerify": "重新发送60s", "submit": "提交", "supportWallet": "(仅支持PNG、JPG、JPEG格式，每张图片最大为10M)，最多支持20张。"}, "intro": "为了您的资金安全，系统仅支持向白名单地址转账。设置白名单后，需等待3小时生效。生效后，向白名单地址转账无需额外验证 ，提现过程更安全、更快捷。", "title": "重置谷歌验证"}, "riskwarning": "风险警告: 加密货币交易存在高风险。 XBIT.COM为您提供链上一流的交易服务，但我们不会对您的交易损失负责。 请注意交易风险，谨慎交易。", "safety": {"intro": "为了您的资金安全，系统仅支持向白名单地址转账。设置白名单后，需等待3小时生效。生效后，向白名单地址转账无需额外验证，提现过程更安全、更快捷。", "intro ": "为了您的资金安全，系统仅支持向白名单地址转账。设置白名单后，需等待3小时生效。生效后，向白名单地址转账无需额外验证，提现过程更安全、更快捷。", "warning": "为了您的资金安全，系统仅支持向白名单地址转账。设置白名单后，需等待3小时生效。生效后，向白名单地址转账无需额外验证 ，提现过程更安全、更快捷。"}, "settings": {"numbers": "您申请的谷歌验证重置失败，请根据以下失败原因重新提交："}, "setup": {"success": "设置成功"}, "slogan": "谷歌验证重置通过！", "steps": {"step1": "绑定谷歌验证器", "step2": "设置地址白名单", "step3": "提现"}, "team": "团队", "thankyou": "感谢您选择XBIT。需要帮助？请联系", "title": "绑定谷歌验证器", "warning": {"not_available": "谷歌验证器不可用，", "note": "请将此密钥保存于安全位置，此密钥用于恢复您的身份验证，一旦丢失，将无法恢复；若您的谷歌验证器被卸载，下次安装时可用密钥生成验证码。", "verifycode": "验证码错误"}, "whitelist": {"edit": "编辑", "intro": "为了您的资金安全，系统仅支持向白名单地址转账。设置白名单后，需等待3小时生效。生效后，向白名单地址转账无需额外验证，提现过程更安全、更快捷。", "notset": "未设置白名单"}}}, "header": {"crypto": "加密货币", "meme": "Meme币"}, "history": {"addLiquidity": "减池子", "all": "全部", "allDirections": "全部方向", "allOrders": "全部委托", "amount": "成交额", "buy": "买入", "buyAmount": "买入金额", "buyAndDouble": "买入后翻倍出本金", "cancel": "撤单", "facilitationPayment": "防夹费", "filterByToken": "筛选币种", "filterByType": "筛选方向", "gasFee": "Gas费用", "modify": "修改", "nodata": "没有数据", "orderAmount": "委托数量", "orderValue": "委托金额", "pnl": "盈亏", "price": "价格", "priorityFee": "优先费", "pump": "Pump(内盘1%)", "removeLiquidity": "减池子", "searchToken": "搜索币种", "sell": "卖出", "sellAmount": "卖出金额", "showCurrentCoinOnly": "只展示当前币种", "slippage": "滑点", "slippageLoss": "滑点损失", "time": "时间", "token": "币种", "tradeVolume": "成交量", "transactionMarketValue": "成交市值", "triggerMarketCap": "触发市值 / 价格", "txHash": "交易哈", "type": "类型", "xbitFee": "XBIT费(1%)"}, "iconsDrawer": {"activeWallets": "最近1小时内超过1097个独立钱包交易", "contractExplorer": "该代币在区块链浏览器的合约", "copyContract": "复制合约地址", "copyTextSuffix": "复制", "devAddLiquidity": "DEV加池子", "dexScreenerAd": "该代币在DEX Screener上投放广告", "dexScreenerSocial": "该代币在DEX Screener更新社交媒体", "goToTwitter": "去推特代币合约", "isHot": "最近1小时内超过{{total}}个独立钱包交易", "lowLiquidity": "流动性过小，请谨慎交易!", "nameChanges": "该代币的推特曾改过10次名", "officialTelegram": "该代币的官方电报", "officialTwitter": "该代币的官方推特", "officialWebsite": "该代币的官方网站", "projectAbandoned": "项目方已跑路，由社区接管(CTO)", "pumpLaunch": "该代币在Pump上发射", "revise": "实时推特帖子预览", "symbolChanges": "该代币的推特曾改过{{total}}次名", "tokenAge": "该代币创建以来的时长", "tokenPortrait": "币种画像说明"}, "liquidityChart": {"comingSoon": "即将推出", "currentInitial": "当前/初始", "fundPool": "资金池", "liquidity": "流动性", "time": "时间", "totalLiquidity": "总流动性", "tradingPair": "交易对", "underDevelopment": "此功能正在开发中"}, "listCoin": {"amount": "金额", "columns": {"holders": "持有者", "marketCap": "当前市值", "pool": "池子", "priceChange": "涨幅", "transactions": "交易数", "transactionsWithTime": "{{time}}交易数", "volume": "成交额", "volumeWithTime": "{{time}}成交额"}, "copyTrade": {"actions": "操作", "all": "全部", "buySellCount": "跟买/跟卖次数", "cancel": "取消", "canceled": "取消", "confirmDelete": "确定删除？", "copyTradeDelete": "关闭跟单", "copyTradeEditParams": "修改参数", "copyTradePaused": "暂停跟单", "copyTradeRestart": "重启跟单", "createCopyTrade": "创建跟单", "currentMarketCap": "当前市值", "deleteMessage": "钱包的跟单？删除后记录无法恢复！", "followStatus": "跟单状态", "hint": {"batch": {"intro": "分批止盈止损：\n\n跟单的仓位支持分批卖出，支持配置多条止盈/止损规则；\n当触发止盈止损后，将卖出当前跟单的N%仓位(从第1档开始触发)；\n相同代币每档止盈/止损只触发一次；\n若相同代币发生加仓操作，则按平均价重新运行止盈/止损(重新从第1档开始触发)"}, "blacklist": "添加到币种黑名单的代币，不再跟单买入", "buySettings": {"intro1": "最大跟买：\n\n最大跟买指用户愿意用最多N个SOL做为跟单金额；\n若目标地址买入金额>最大买入金额，以最大买入金额买入；\n若目标地址买入金额=<最大买入金额，将按目标地址的买入金额进行跟单买入。\n\n举例: 若用户设定了最大买入10 SOL。\n若跟单地址买入5 SOL , 则用户将跟单买入5 SOL；\n若目标跟单地址买入20 SOL，用户将跟单买入10 SOL。", "intro2": "固定买入：\n\n固定买入指无论跟单目标地址买入多少金额，每次跟单时将按照设置的固定数量进行买入。\n\n举例: 若用户设定了固定买入10 SOL。\n若跟单地址买入5 SOL , 此时用户将跟单买入10 SOL；\n若目标跟单地址买入20 SOL，此时用户还是跟单买入10 SOL。"}, "copyTrade": {"intro1": "最小跟单金额过滤", "intro2": "当目标跟单地址的买入金额>=最小跟单金额时，才会执行跟单买入。防止跟单钱包频繁的小额购买，导致您也频繁的跟单买入。"}, "followAmount": {"intro1": "最小跟单金额过滤", "intro2": "当目标跟单地址的买入金额>=最小跟单金额时，才会执行跟单买入。防止跟单钱包频繁的小额购买，导致您也频繁的跟单买入。"}, "minBurnPool": {"intro1": "仅当代币销毁比例>=设置的值时，才会执行跟单买入", "intro2": "当目标跟单地址的买入金额>=最小跟单金额时，才会执行跟单买入。防止跟单钱包频繁的小额购买，导致您也频繁的跟单买入。"}, "platform": {"intro": "只有当跟单钱包买入的代币是所选平台的代币，才会跟单买入（不影响跟单卖出）。\n如代币A在Pump创建，用户仅设置了Pump这个平台\n当跟单目标钱包在Pump内盘买入A的时候，可以跟单买入；\n当A流动性迁移到Raydium后，跟单目标钱包再买入A，不再跟单买入；用户在Raydium上卖出A，可跟单卖出；\n若用户调整跟单平台为：Pump + Raydium；跟单目标钱包在Raydium上再买入时，可以跟单买入；"}, "sellSettings": {"intro1": "自动跟卖：\n\n当跟单地址卖出代币后，将按其仓位卖出的比例，等比例的卖出跟单买入部分的代币；注意这里仅针对卖出跟单的仓位，若你自己也买入了该代币，则你自己买入的不会被卖出。\n\n举例: 用户跟单的地址A买入了Doge，当地址A卖出自身持仓10%的Doge时，用户跟单地址A买入的Doge仓位将等比卖出10%。\n", "intro2": "不跟卖：\n\n系统将不会自动帮你卖出代币，需要您手动卖币。", "intro3": "自定义止盈止损开：\n\n启自动止盈止损后，跟单钱包在卖出时，系统将不会执行卖出操作，而是按您设置的止盈止损参数进行卖出。"}, "singleAddPositionCount": {"intro1": "相同代币在持仓条件下的最大加仓次数（清仓后次数恢复）", "intro2": "输入0: 表示在跟单买入后，相同代币不会再重复购买（清仓卖出后，还会再买1次）", "intro3": "输入1: 表示在跟单买入后，相同代币可以加仓买入1次（即最多跟买2次。清仓卖出后，次数恢复）"}, "trailingStopLoss": {"intro": "止损价与最高价维持固定比例，随代币价格上涨而上升，旨在保护收益。\n场景说明：假设买入价格为$100，止损比例设置为 10%；如价格跌至$90(下跌10%)，将触发止损卖出；如价格升至 $150，止损价将自动调整为$135(距离最高点保持固定比例10%)"}}, "lastTradeTime": "最近交易时间", "myWallet": "我的钱包", "oneDayPnL": "1日盈亏", "paused": "已暂停", "playTradeRestart": "玩", "realizedProfit": "已实现利润", "running": "运行中", "sevenDayAvgBuyCost": "7日平均买入成本", "sevenDayPnL": "7日盈亏", "sevenDayWinRate": "7日胜率", "thirtyDayPnL": "30日盈亏", "toast": {"warningActiveTelegram": "请通过电报登录以创建复制交易"}, "totalBuy": "总跟买", "totalSell": "总跟卖"}, "fields": {"liquidityPool": "池子"}, "filter": "筛选", "filters": {"aiMining": "AI挖掘", "all": "全部", "almostFull": "即将打满", "apply": "应用全部", "ascending": "升序", "by": "经过", "defaultSortLabel": "排序: 按添加自选时间降序排列", "descending": "降序", "fields": {"creationTime": {"maxLabel": "最大时间", "minLabel": "最小时间", "title": "创建时间"}, "holders": {"maxLabel": "最大持有者", "minLabel": "最小持有者", "title": "持有者"}, "liquidityPool": {"maxLabel": "最大池子", "minLabel": "最小池子", "title": "池子"}, "marketCap": {"maxLabel": "最大市值", "minLabel": "最小市值", "title": "市值"}, "progress": {"maxLabel": "最大内盘进度", "minLabel": "最小内盘进度", "title": "内盘进度"}, "transactions": {"maxLabel": "最大交易数", "minLabel": "最小交易数", "title": "1h交易数", "unit": "笔"}, "volumes": {"maxLabel": "最大成交额", "minLabel": "最小成交额", "title": "1h交易额"}}, "gainers": "涨幅榜", "launched": "已开盘", "losers": "跌幅榜", "newListing": "上新", "paused": "已暂停", "placeholder": "全部", "popular": "热门", "pumping": "爆拉", "reset": "重置", "resetDefault": "恢复默认", "running": "运行中", "sortBy": "排序", "sortBy5minMarketCapChangeDesc": "排序: 按5分钟市值变化降序排列", "sortByCreationTimeDesc": "排序: 按创建时间降序排列", "sortByField": {"creationTimeAsc": "按创建时间升序", "creationTimeDesc": "按创建时间降序", "holdersAsc": "按持有者升序", "holdersDesc": "按持有者降序", "liquidityPoolAsc": "按池子升序", "liquidityPoolDesc": "按池子降序", "marketCapAsc": "按市值升序", "marketCapDesc": "按市值降序", "progressAsc": "按内盘进度升序", "progressDesc": "按内盘进度降序", "transactionsAsc": "{{time}}交易数升序", "transactionsDesc": "{{time}}交易数降序", "volumesAsc": "{{time}}按成交额降序升序", "volumesDesc": "{{time}}按成交额降序排列"}, "sortByInternalProgressDesc": "排序: 按内盘进度降序排列", "sortByVolumeDesc": "排序: 按成交额降序排列", "sortByWatchlistTimeDesc": "排序: 按添加自选时间降序排列", "title": "筛选", "topTraders": "牛人榜", "walletCopyTrade": "钱包跟单", "walletOptions": {"all": "全部", "kolVc": "KOL/VC", "newWallet": "新钱包", "pumpSmartMoney": "Pump聪明钱", "smartMoney": "聪明钱", "sniper": "狙击者", "viewSmartMoney": "查看关注的聪明钱"}}, "modal": {"config": {"cancel": "取消", "delete": "确定", "title": "确定删除？"}}, "noData": "没有数据", "quickBuy": "快捷买入", "removeWatchlist": "确定取消\"{{coinName}}\"自选？", "requireLoginToFavorite": "请先登录，以将此项目添加到您的收藏夹", "sortLabels": {"mainstream": "排序:按成交额降序排列", "newCoin": "排序:按创建时间降序排列", "optional": "排序:按添加自选时间降序排列"}, "tabs": {"category": "分类", "mainstream": "主流", "newCoin": "新币", "optional": "自选", "walletCopy": "钱包跟单"}, "tagFilters": {"aiMining": "AI挖掘", "fullSoon": "即将打满", "gainers": "涨幅榜", "hot": "热门", "losers": "跌幅榜", "new": "上新", "opened": "已开盘", "pumped": "爆拉"}, "toasts": {"buyCount": "买入数", "insiderTrading": "老鼠仓", "numberOfHolders": "持币人数", "numberOfSnipers": "狙击人数", "sameSourceWallet": "同源钱包", "sellCount": "卖出数", "smartMoney": "聪明钱", "tokensLaunched": "成功发射代币数", "top10Holder": "Top 10 持仓", "totalTransactions": "交易数", "volume": "{{time}} 成交额"}, "toBeOpened": "待开盘", "trendingScore": "主流分数"}, "login": {"bindingNotice": "您正在绑定Teleg<PERSON>(SOL网络)", "bindTelegramBot": "绑定Telegram Bot", "cancel": "取消", "notLogined": "您尚未登录{{name}}", "open": "打开", "openInBrowser": "如无法打开Telegram，请复制链接到浏览器打开", "openTelegramPrompt": "\"XBIT\"想要打开\"Telegram\"", "privacyPolicy": "隐私政策", "recommended": "推荐", "telegramLogin": "Telegram登录", "terms": "使用条款", "termsAgreement": "连接钱包即代表同意", "tradingPromptDetail": "无需打开钱包签名授权，快人一步", "tradingPromptTitle": "点击即交易，极速成交", "with": "与"}, "modifyOrder": {"available": "可用", "buyPrice": "买入价格(USDT)", "callbackRate": "高点回调幅度", "confirm": "确定", "doublePrincipalAfterPurchase": "买入后翻倍出本金", "estimatedExchange": "预估兑换", "insufficientBalance": "可用余额不足以支付Gas费用", "latestMarketCap": "最新市值", "latestPrice": "最新价格", "quantity": "数量", "trailingTPSL": "移动止盈止损"}, "monitoring": {"followedWallets": "关注的钱包", "following": "下列的", "manager": "经理", "realTimeTransactions": "实时交易", "title": "监控", "transactionAmount": "交易金额", "transactionType": "交易类型"}, "notifications": {"title": "消息通知"}, "orderBook": {"all": "全部", "currentMarketCap": "当前市值", "marketCap": "市值", "pool": "池子", "price": "价格", "quantity": "数量", "time": "时间", "volume": "成交额"}, "orderForm": {"buySettings": {"buyAmount": "买入数量", "complete": "完成", "confirm": "确定", "inputAmount": "输入买入数量", "more": "更多", "orderProcess": "订单处理", "reset": "重置"}, "confirmation": {"confirmButton": "确认买入", "noPrompt": "不再提示", "title": "确认买入?"}, "doubleAfterBuy": "买入后翻倍本金", "errors": {"changeTeleWalletToContinue": "即将推出。请更改电Telegram钱包继续", "firstTradeToken": "您是首次交易此代币。将收取少量费用（~{{amount}} {{symbol}}）以初始化您的账户。", "insufficientBalance": "钱包余额不足。", "minimumOrderQuantity": "最小下单数量为 0.000001", "noTokenAvailable": "没有可用的令牌", "orderFailed": "订单失败", "orderInvalidAmount": "订单金额无效", "rechargeIfBalanceInsufficient": "余额不足请充值", "rechargeIfBalanceInsufficientMinimum": "{{chain}}余额不足。请确保您的钱包中至少有{{balance}} {{chain}}，以支付网络费用。", "selectTransactionQuantity": "请选择交易数量 。", "signMessageWalletInfo": "请签署消息以进行交易。", "toastCreatOrderErrorTitle": "交易失败", "tokenInsufficient": "代币销售不足", "transactionLoadingFailed": "交易数据加载失败"}, "form": {"antiClip": "防夹", "automatic": "自动", "available": "可用", "availableAssets": "可用资产", "cancel": "取消", "close": "关", "confirmBuying": "确认买入", "connectExternalWallet": "连接外部钱包", "connectTelegramWallet": "连接Telegram钱包", "doublePrincipalAfterPurchase": "买入后翻倍本金", "errExternalWallet": "外部钱包不支持挂限价单", "hodingtoken": "持有 {{token}} 代币钱包", "limitCommissionTelegram": "限价委托仅支持Telegram登录使用，不支持钱包登录使用", "linked": "已链接", "marketCap": "市值", "noMorePrompt": "不再提示", "open": "开", "otherToken": "其他钱包", "price": "价格", "priorityFee": "优先费", "slipPoint": "滑点", "supportHoldText": "假设您持有Trump，当前价格为$100，并设置高点回调幅度为10%：", "supportTextListItem1": "·如果价格从$100下跌10%至$90，您的移动止盈止损订单将被触发，并以市价卖出订单。", "supportTextListItem2": "·如果价格上升至$150，然后下跌7%至$140，您的移动止盈止损卖出订单将不会被触发。", "supportTextListItem3": "·如果价格上升至$200，然后下跌10%至$180，您的移动止盈止损订单将被触发，并以市价卖出订单。", "sure": "确定"}, "gasFee": {"baseFee": "基础费用", "customGasFee": "自定义Gas费", "explanation": "优先费则是用户愿意额外支付给矿工或验证者的附加费用（小费），从而提高你的订单成交优先级，加快成交速度。在推荐的费率区间内，优先费越高，成交时成功率越高且成交速度越快。如果交易失败，优先费仍会从你的余额中被扣除。", "fast": "快", "market": "市价", "priorityFee": "优先费用", "superFast": "极速", "title": "Gas费", "totalFee": "总费用"}, "inputs": {"amount": "金额", "price": "价格"}, "marketTab": {"amount": "金额"}, "mevProtect": {"off": "关", "on": "开", "title": "防夹"}, "orderSetting": {"antiClipMode": "防夹模式(MEV防御)", "antiClipModeTooltip": "MEV攻击俗称夹子，夹子会在你买入或卖出前抢先拉高或打压价格，从而令你的买入价格变高或卖出价格变低；开启后，系统会保护你的交易不被夹子攻击，从而保护你的交易。", "basicFee": "基础费用", "cancel": "取消", "cannotOver100": "滑点不能高于 100%", "customFee": "自定义优先费", "fast": "极速", "fee": "优先费用", "marketPrice": "市价", "maximumPriorityFee": "优先费最高不能超过", "open": "打开", "priorityFee": "优先费", "priorityFeeRequire": "优先费是必填项", "priorityFeeTooLow": "优先费低于市价，可能会导致交易时间过长或交易失败", "priorityFeeTooltip": "优先费则是用户愿意额外支付给矿工或验证者的附加费用（小费），从而提高你的订单成交优先级，加快成交速度。在推荐的费率区间内，优先费越高，成交时成功率越高且成交速度越快。如果交易失败，优先费仍会从你的余额中被扣除。", "quick": "快", "slippageTooHigh": "滑点过高，容易造成资产损失，建议设置在 1% 至 50% 之间", "slippageTooLow": "滑点过低，可能会导致交易失败，建议设置在 1% 至 50% 之间", "slipPoint": "滑点", "slipPointPlaceholder": "自定义(1~50)", "slipPointTooltip": "滑点是指交易的预期价格与交易执行价格之间的差额。如果交易的价格变化超过滑点设置，你的交易将被取消，但依然会收取网络费用。", "title": "成交设置(全局)", "totalCost": "总费用", "warning": "请注意，成交设置为全局通用设置，如你手动调整了这笔交易的任何设置，以后交易将默认沿用该设置。"}, "priorityFee": {"customFee": "自定义优先费", "errors": {"required": "优先费是必填项", "tooHigh": "优先费最高不能超过 {{maxFee}} {{chain}}", "tooLow": "优先费低于市价，可能会导致交易时间过长或交易失败"}, "title": "优先费"}, "slippage": {"customPlaceholder": "自定义(1~50)", "errors": {"max": "滑点不能高于 100%", "tooHigh": "滑点过高，容易造成资产损失，建议设置在 1% 至 50% 之间", "tooLow": "滑点过低，可能会导致交易失败，建议设置在 1% 至 50% 之间"}, "explanation": "滑点是指交易的预期价格与交易执行价格之间的差额。如果交易的价格变化超过滑点设置，你的交易将被取消，但依然会收取网络费用。", "title": "滑点"}, "status": {"BALANCE_INSUFFICIENT": "交易失败：提交订单失败，钱包中代币余额不足。", "BALANCE_INSUFFICIENT_FEE": "交易失败：余额不足扣Gas费，请充值", "BONDING_ENDED": "交易失败：无可用路由", "Canceled": "已取消", "Completed": "已完成", "createOrderSuccess": "订单创建成功", "FEE_BUDGET": "交易失败：优先费过低，交易超时，需提高优先费", "Filled": "已成交", "INTERNAL_ERROR": "系统内部错误。请稍后再试。", "LIQUIDITY_LOW": "交易失败：池子流动性不足，请分批交易", "NET_BLOCKHASH": "交易已过期。请重新操作以获取最新数据。", "NETWORK_UNSUPPORT": "该区块链网络暂不支持。", "Order_BaseTokenNotSupported": "交易失败：基础代币不受支持。请使用受支持的代币。", "Order_ChargeFail": "交易失败：钱包扣款失败。请确认钱包已连接。", "Order_ChargeFail_LowBalance": "交易失败：余额不足。请充值后再试。", "Order_Close_AlreadyDeleted": "交易失败：订单已被删除。无需执行进一步操作。", "Order_Close_InvalidExitReason": "交易失败：关闭订单原因无效。请选择有效的退出理由。", "Order_Close_InvalidStatus": "交易失败：无法关闭订单：订单状态无效。请检查当前订单状态。", "Order_Close_InvalidType": "交易失败：无法关闭订单：订单类型无效。请检查订单类型。", "Order_Close_PublishFailed": "交易失败：发布订单关闭请求失败。请重试或联系客服。", "Order_Close_SomethingPending": "交易失败：存在未完成操作。请完成所有步骤后再关闭订单。", "Order_Close_StatusNotPassed": "交易失败：订单状态不允许关闭。请等待订单激活。", "Order_ConfigNotFound": "交易失败：未找到订单配置。请刷新页面后重试。", "Order_CreateFail": "交易失败：创建订单失败。请检查输入信息后重试。", "Order_CrossChain": "交易失败：不支持跨链订单。请仅使用同链代币。", "Order_CurrencyNotSupported": "交易失败：不支持所选货币。请选择其他代币。", "Order_GetEnabledPairFail": "交易失败：获取可用交易对失败。请刷新页面。", "Order_GetMarketCapFail": "交易失败：获取市值失败。请稍后重试。", "Order_GetPriceFail": "交易失败：获取代币价格失败。请检查代币是否可用。", "Order_GetRateFail": "交易失败：获取兑换汇率失败。请检查您的网络连接并重试。", "Order_InvalidAmount": "交易失败：金额无效。请输入有效的交易金额。", "Order_InvalidCopyTradeId": "交易失败：复制交易 ID 无效。请刷新页面或重新选择目标交易。", "Order_InvalidCurrency": "交易失败：提供的货币无效。请检查代币详情后重试。", "Order_InvalidEntry": "交易失败：入场点无效。请检查订单入场详情。", "Order_InvalidPair": "交易失败：交易对无效。请选择有效的代币交易对。", "Order_InvalidParam_Amount": "交易失败：无效的交易金额。请输入有效的金额。", "Order_InvalidParam_AmountRange": "交易失败：金额超出范围。请将金额调整到允许的范围内。", "Order_InvalidParam_Burst": "交易失败：无效的爆发模式设置。如果不确定，请关闭爆发模式。", "Order_InvalidParam_CallbackRate": "交易失败：无效的回调率。请检查回调率值。", "Order_InvalidParam_DoublePrincipalAfterPurchase": "交易失败：无效的双重本金设置。请禁用或审查此选项。", "Order_InvalidParam_DoublePrinciplaAfterPurchase": "交易失败：无效的双重本金设置。请禁用或审查此选项。", "Order_InvalidParam_Leverage": "交易失败：无效的杠杆。请选择有效的杠杆。", "Order_InvalidParam_LimitMarketCap": "交易失败：无效的限市值。请调整限市值。", "Order_InvalidParam_LimitPrice": "交易失败：无效的限价。请输入正确的限价。", "Order_InvalidParam_LimitRange": "交易失败：无效的限价范围。请调整限价范围。", "Order_InvalidParam_OnlyTrailingTpSlWithSell": "交易失败：仅允许在卖出订单中使用追踪TP/SL。请仅与卖出订单一起使用。", "Order_InvalidParam_PriorityFeePrice": "交易失败：无效的优先费用。", "Order_InvalidParam_Sl": "交易失败：无效的止损（SL）。请输入有效的SL值。", "Order_InvalidParam_Slippage": "交易失败：无效的滑点。请使用1–50%的滑点。", "Order_InvalidParam_SlRangeBuy": "交易失败：买入的SL范围无效。请调整买入订单的SL范围。", "Order_InvalidParam_SlRangeSell": "交易失败：卖出的SL范围无效。请调整卖出订单的SL范围。", "Order_InvalidParam_Tp": "交易失败：无效的止盈（TP）。请输入有效的TP值。", "Order_InvalidParam_TpRangeBuy": "交易失败：买入的TP范围无效。请调整买入订单的TP范围。", "Order_InvalidParam_TpRangeSell": "交易失败：卖出的TP范围无效。请调整卖出订单的TP范围。", "Order_InvalidParam_TpSlBuy": "交易失败：买入订单的TP/SL无效。请检查TP/SL值。", "Order_InvalidParam_TpSlSell": "交易失败：卖出订单的TP/SL无效。请检查TP/SL值。", "Order_InvalidParam_TriggerPrice": "交易失败：无效的触发价格。请输入有效的触发价格。", "Order_InvalidParam_Txid": "交易失败：无效的交易ID。请检查TxID并重试。", "Order_ModifyOrder_BaseAmount": "交易失败：无效的基础金额更新。请调整基础金额。", "Order_ModifyOrder_Exited": "交易失败：订单已退出。请下新订单。", "Order_ModifyOrder_LimitPrice": "交易失败：无效的限价更新。请检查新的限价。", "Order_ModifyOrder_MarketOrder": "交易失败：无法修改市价单。请取消并创建新订单。", "Order_ModifyOrder_MevProtect": "交易失败：无效的MEV保护更新。请重置反MEV设置。", "Order_ModifyOrder_NotChange": "交易失败：更新中未检测到更改。请在更新前更改一个值。", "Order_ModifyOrder_PriorityFeePrice": "交易失败：优先费无效。请输入有效数字", "Order_ModifyOrder_Slippage": "交易失败：无效的滑点更新。请将滑点设置为1–50%。", "Order_ModifyOrder_TriggerPrice": "交易失败：无效的触发价格更新。请检查新的触发价格。", "Order_QuoteTokenNotSupported": "交易失败：计价代币不受支持。请更换计价货币。", "Order_RemoveOrderCacheFail": "交易失败：清除订单缓存失败。请刷新页面后重试。", "Order_SaveWeb3Order_PublishFailed": "交易失败：发布 Web3 订单失败。请稍后重试或联系客服。", "Order_SubmitFail": "交易失败：订单提交失败。请检查您的网络连接并重试。", "Order_WalletNotSupported": "交易失败：钱包类型不受支持。请连接受支持的钱包。", "orderProcess": "订单处理中", "orderSuccess": "买入成功", "orderSucess": "成功", "PAIR_NOT_FOUND": "找不到交易对。请检查代币是否正确。", "Pending": "待处理", "ROUTE_NOT_FOUND": "交易失败：无可用路由", "RPC_CONNECTION": "交易失败： 网络连接超时", "RPC_ERROR": "交易失败：节点异常", "SIMULATION_FAILED": "交易失败：买入失败，合约限制 ", "SPLIPPAGE_SURGE": "滑点过大。请尝试提高滑点设置后重试。", "TOKEN_UNSUPPORT": "该代币暂不支持交易。", "TRANSACTION_BUILD_FAILED": "构建交易失败。请检查代币信息或重试。", "TRANSACTION_SIGN_FAILED": "交易签名失败。请检查您的钱包。", "transactionFail": "交易失败", "USER_NOT_FOUND": "未找到用户钱包。请连接钱包以继续。"}, "tabs": {"buy": "买入", "limitOrder": "限价委托", "marketTrade": "市价交易", "quickTrade": "一键买卖", "quickTradeBuy": "一键买卖", "quickTradeSell": "一键买卖", "sell": "卖出", "trailingStopLoss": "移动止盈止损"}, "trailingTab": {"activationPrice": "激活价格", "highPointPullback": "高点回调幅度", "soldQuantity": "卖出数量", "tooltipActive": "激活止盈止损(选填)", "tooltipText": "激活止盈价格是移动止盈止损的激活条件，当市场最新成交价达到或超过激活止盈价格时，移动止盈止损的委托单才被激活。"}}, "orderSettings": {"globalSettings": "成交设置(全局)", "mevProtectMode": "防夹模式(MEV防御)"}, "position": {"annualizedReturn": "年化收益率", "cancel": "取消", "closePosition": "平仓", "confirm": "确认", "cross": "全仓", "currentPosition": "当前持仓", "entryPrice": "开仓价格", "fee": "手续费", "funding": "资金费率", "holdingValue": "持有价值", "inddexPrice": "指数价格", "indexPrice": "指数价格", "isolated": "逐仓", "leverage": "杠杆", "liquidationPrice": "强平价格", "long": "多头", "margin": "保证金", "markPrice": "标记价格", "openInterest": "持仓量", "perpetual": "永续合约", "pnl": "盈亏", "priceAndChange": "价格/变化", "quantity": "数量", "realizedPnl": "已实现盈亏", "setupTPSL": "设置止盈止损", "share": "分享", "short": "空头", "size": "仓位大小", "slPrice": "止损价格", "stopLoss": "止损", "stopLossPrice": "止损价格", "takeProfit": "止盈", "takeProfitPrice": "止盈价格", "title": "持仓", "todayPnL": "今日盈亏", "unrealizedPnl": "未实现盈亏", "updateSuccessful": "更新成功", "value": "仓位价值", "viewOrder": "查看订单"}, "privacyPolicy": {"title": "隐私政策"}, "search": {"24hTrending": "24h热门", "cancel": "取消", "clear": "清空", "holders": "持有者", "placeholder": "搜索币种名称或合约地址", "pool": "池子", "popularSearch": "热门搜索", "recent": "搜索历史", "search": "搜索", "tokens": "代币", "transactions": "交易数", "volume": "成交额"}, "settings": {"addRule": "添加规则", "addToBlacklist": "添加到黑名单", "advancedSettings": "高级设置", "amount": "数量", "antiSandwich": "反三明治", "apply": "应用", "autoFollow": "自动复制销售", "balance": "平衡", "balanceWithUnit": "平衡：", "batch": "批量止盈/止损", "blacklistTooltip": "加入黑名单的代币将不会被复制购买", "buySettings": "购买设置", "cancel": "取消", "contactCustomerService": "联系客服", "creationTime": "创建时间", "customTPSL": "自定义盈利/止损", "enterAddress": "输入复制钱包地址", "enterPositiveInteger": "输入一个>=0的整数", "enterStopLoss": "输入止损率", "enterTakeProfit": "输入盈利概率", "enterTokenAddress": "输入 SOL 代币地址", "estimatedLoss": "预期损失", "estimatedProfit": "预期利润", "estimatedValue": "≈$0", "fixedBuy": "固定购买", "followAddress": "复制地址", "followAmount": "复印数量", "global": "全球的", "invalidAddress": "请输入有效的 SOL 钱包地址", "invalidToken": "请输入有效的 SOL 代币地址", "language": {"chinese": "中国人", "english": "EN", "hindi": "हिंदी", "japanese": "日本語"}, "level": "等级", "linkWallet": "添加钱包", "marketValue": "市价", "max": "最大限度", "maxFollow": "最大复制购买量", "min": "最低限度", "minBurnPool": "最小燃烧池", "noFullSellWarning": "目前，没有“100%”销售的规则。无法完全卖出跟单仓位", "noSell": "没有出售任何副本", "off": "离开", "others": "其他的", "paste": "粘贴", "platform": "平台", "pool": "水池", "priorityFee": "优先费", "quantity": "数量", "save": "保持", "saveImage": "保存相册", "sellOutPercent": "销售率", "sellSettings": "销售设置", "single": "单身的", "singleAddPositionCount": "新增单仓数量", "slippage": "滑移", "stopLoss": "减损", "takeProfit": "盈利能力", "title": "钱包跟单交易设置", "tokenBlacklist": "令牌黑名单", "tpPercent": "盈利概率", "trailingStopLoss": "追踪止损", "transactionSettings": "交易设置", "tutorial": "使用教程", "value": "价值"}, "shareBottomSheet": {"title": "分享页面"}, "smartMoney": {"filter": {"7dayWinningRate": "按7日胜率", "latestAttention": "最新关注"}, "tabs": {"Activities": "聪明钱活动", "smartMoney": "关注的聪明钱", "topTalents": "牛人榜", "walletCopy": "钱包跟单"}, "totalFollowers": "共关注了{{followers}}个聪明钱"}, "status": {"accountAlreadyExist": "该账号已登录", "loginFail": "登录失败！请重试", "loginSuccess": "绑定成功"}, "termsOfUse": {"title": "使用条款"}, "test": "test", "toast": {"addFavoriteFailed": "收藏失败", "addFavoriteSuccess": "收藏成功", "cancel": "取消", "cancelOrderSuccess": "撤单成功", "confirm": "确认", "connectWalletFailed": "连接钱包失败", "copiedSuccess": "复制成功", "linkSuccess": "链接成功", "loginFailed": "登录失败！请重试", "maxPeriods": "最多支持6个周期", "modifyOrderSuccess": "修改成功", "periodInUse": "不能移除当前K线选中的周期", "removeFavoriteFailed": "取消自选失败", "removeFavoriteSuccess": "确定取消自选", "saveSuccess": "保存成功", "termsAgreement": "请同意使用条款和隐私政策。", "walletNotInstalled": "您未安装"}, "tokenData": {"ageToken": "Token 年龄", "athDate": "ATH 日期", "athPrice": "历史最高价", "atlDate": "ATL 日期", "atlPrice": "历史最低价", "blackList": "黑名单", "burn": "烧池子", "circulatingSupply": "流通量", "holders": "持币地址数", "issueDate": "发行日期", "liquidityPool": "流动性池", "marketCap": "总市值", "noMint": "mint丢弃", "openPrice": "开盘价", "pumpfun": {"amount": "总成交额", "buyAddresses": "买入地址数", "buyAmount": "买入金额", "buyTransaction": "买入笔数", "sellAddresses": "卖出地址数", "sellAmount": "卖出金额", "sellTransaction": "卖出笔数", "title": "Pump Fun 内盘", "transactions": "总交易数", "uniqueAddresses": "独立地址数", "volume": "成交量"}, "statistic": {"amount": "总成交额", "buyAddresses": "买入地址数", "buyAmount": "买入金额", "buyTransaction": "买入笔数", "poolValue": "池子价值", "sellAddresses": "卖出地址数", "sellAmount": "卖出金额", "sellTransaction": "卖出笔数", "transactions": "总交易数", "uniqueAddresses": "独立地址数", "volume": "成交量"}, "top10": "前十", "top10Holdings": "前十持仓", "totalSupply": "总发行量", "turnoverRate24h": "24小时换手率", "vol24h": "24小时交易量"}, "tokenSelectionDrawer": {"marketCap": "市值", "searchPlaceholder": "代币名称或者合约地址", "trending24h": "24h热门"}, "tradeSettings": {"antiClipMode": "防夹模式(MEV防御)", "antiClipModeTooltip": "MEV攻击俗称夹子，夹子会在你买入或卖出前抢先拉高或打压价格，从而令你的买入价格变高或卖出价格变低；开启后，系统会保护你的交易不被夹子攻击，从而保护你的交易。", "antiPinchMode": "防夹模式(MEV防御)", "apply": "确定", "auto": "自动(20%)", "autoFee": "自定义优先费", "basicFee": "基础费用", "custom": "自定义", "customGasFeeError": "Gas 费用必须大于或等于 {{min}}", "customPriorityFeeError": "优先费用必须在{{min}} 到 2 SOL 之间。", "customSlippageError": "滑点过高，容易造成资产损失，建议设置在 1% 至 50% 之间", "danggerNote": "请注意，成交设置为全局通用设置，如你手动调整了这笔交易的任何设置，以后交易将默认沿用该设置。", "fast": "快", "fastest": "极速", "gasFee": "Gas费", "gasFeeTooltip": "Gas 费是指在区块链上处理交易的成本。矿工或验证者需要支付 Gas 费才能将交易打包到区块中。Gas 费越高，交易处理速度越快。", "marketPrice": "市价", "preset": "预设{{preset}}", "priorityFee": "优先费", "priorityFeeTooltip": "优先费则是用户愿意额外支付给矿工或验证者的附加费用（小费），从而提高你的订单成交优先级，加快成交速度。在推荐的费率区间内，优先费越高，成交时成功率越高且成交速度越快。如果交易失败，优先费仍会从你的余额中被扣除。", "priotyFee": "优先费", "quick": "快", "reset": "重置", "slippage": "滑点", "slippageTooltip": "滑点是指交易的预期价格与交易执行价格之间的差额。如果交易的价格变化超过滑点设置，你的交易将被取消，但依然会收取网络费用。", "title": "成交设置(全局)", "totalCost": "总费用"}, "transaction": {"amount": "金额", "buy": "买入", "marketCap": "市值", "price": "价格", "quantity": "数量", "sell": "卖出", "time": "时间", "token": "代币", "type": "类型"}, "unknown": "unknown", "wallet": {"addWallet": "添加钱包", "applications": "经过", "available": "可用", "availableAssets": "可用资产", "availableQuantity": "数量", "balance": "{{chain}}余额", "clickConnect": "点击连接", "confirmDisconnect": "确定断开连接？", "connected": "已连接", "connectFailed": "连接钱包失败", "connectGuide": "点击连接", "connectToNetwork": "连接您的钱包到{{chain}}网络", "current": "当前", "disconnect": "断开链接", "downloadWallet": "下载", "installed": "已安装", "linked": "已链接", "manage": "管理", "manageWallet": "管理钱包", "noData": "没有数据", "notInstalled": "您未安装", "positionCurrency": "持有代币", "qrCodeInstruction": "如果您的钱包未列出，请使用您的钱包扫描此二维码（需要 WalletConnect v2）", "returnToApp": "连接钱包后需回到XBIT APP进行交易", "saveToAlbum": "保存到相册", "selectNetwork": "选择网络", "selectWallet": "请选择钱包", "switchWallet": "切换钱包", "telegramWallet": "Telegram钱包", "title": "钱包", "totalAssets": "总资产", "warning": {"walletConnectd": "每条链同一时间只能连接一个钱包。请先断开当前钱包，再连接其他钱包。"}}, "walletCopy": {"amount": "成交量", "asset": "币种", "autoFollowSell": "自动跟卖", "batch": "分批", "buy": "买入", "buySellCount": "跟单买/卖", "buyType": {"fixedamount": "固定买入", "FixedAmount": "固定买入", "maxamount": "最大购买量", "MaxAmount": "最大购买量", "null": "无效的"}, "cancel": "删除", "changeWalletName": "修改钱包名称", "confirmDelete": "确定删除？", "copyType": "跟单类型", "customBatch": "自定义止盈止损", "customSingle": "自定义止盈止损", "deleteMessage": "钱包的跟单？删除后记录无法恢复！", "duration": "跟单时长", "enterName": "输入名称", "errorUpdatingStatus": "更新状态错误", "failedList": "跟单失败", "failedReason": "失败原因", "filter": {"all": "全部", "asset": "筛选币种", "maxVolume": "最大($)", "minVolume": "最小($)", "type": "筛选类型", "volume": "成交额"}, "fixedBuy": "固定买入", "maxFollowBuy": "最大跟买", "modifyParameters": "修改参数", "orderRecord": "跟单记录", "pauseCopy": "暂停跟单", "paused": "已暂停", "price": "价格", "profit": "盈利", "realizedProfit": "已实现利润", "recentFollow": "最近跟单", "running": "运行中", "save": "保存", "searchToken": "搜索币种", "sell": "卖出", "sellParameters": "卖出参数", "sellType": {"auto": "汽车", "Auto": "汽车", "multitpsl": "定制TP-SL", "MultiTPSL": "定制TP-SL", "nocopy": "禁止复制", "NoCopy": "禁止复制", "null": "无效的", "singletpsl": "定制TP-SL", "SingleTPSL": "定制TP-SL"}, "settings": {"addRule": "增加规则", "addToBlacklist": "增加黑名单", "advancedSettings": "高级设置", "amount": "数量", "antiSandwich": "防夹", "apply": "适用的", "autoFollow": "自动跟卖", "balance": "余额", "balanceWithUnit": "余额：", "batch": "分批止盈止损", "blacklistTooltip": "添加到黑名单的代币不会被跟单购买", "buySettings": "买入设置", "cancel": "取消", "creationTime": "创建时间", "currencyBlacklist": "市币黑名单", "customTPSL": "自定义止盈止损", "enterAddress": "输入跟单钱包地址", "enterPositiveInteger": "输入>=0的整数", "enterStopLoss": "输入止损比例", "enterTakeProfit": "输入止盈比例", "enterTokenAddress": "输入SOL代币约", "error": {"createConfig": "创建配置时出现一些错误"}, "estimatedLoss": "预估亏损", "estimatedProfit": "预估盈利", "estimatedValue": "≈$0", "fixedBuy": "固定买入", "followAddress": "跟单地址", "followAmount": "跟单金额", "global": "全局", "invalidAddress": "请输入正确的SOL钱包地址", "invalidToken": "请输入正确的SOL代币地址", "level": "第档", "marketValue": "市值", "max": "最大", "maxFollow": "最大跟买", "min": "最小", "minBurnPool": "最低烧池子", "noFullSellWarning": "当前无\"100%\"卖出的规则，将无法全部卖出跟单仓位", "noSell": "不跟卖", "off": "关", "others": "其他", "paste": "粘贴", "platform": {"error": {"minimumOne": "必须至少选择一个平台"}, "moonshot": "moonshot", "other": "其他", "platform": "平台", "pump": "pump", "raydium": "raydium"}, "pool": "池子", "priorityFee": "优先率", "quantity": "数量", "reset": "重置", "save": "保存", "sellOutPercent": "卖出比例", "sellSettings": "卖出设置", "single": "单次", "singleAddPositionCount": "单次加仓次数", "slippage": "滑点", "stopLoss": "止损", "takeProfit": "止盈", "title": "钱包跟单设置", "tokenBlacklist": "市币黑名单", "tpPercent": "止盈比例", "trailingStopLoss": "追踪止损", "transactionSettings": "成交设置", "tutorial": "使用教程", "value": "金额"}, "single": "单次", "soldPrice": "成交价", "startCopy": "开启跟单成功", "stopLoss": "卖出", "successList": "跟单成功", "tags": {"fresh": "新", "kol": "K", "pumpsm": "狙", "smartmoney": "聪", "sniper": "狙", "toptrader": "顶级交易员", "whale": "新"}, "takeProfit": "止盈", "toast": {"modifyNameError": "修改名称错误", "modifyNameSuccess": "修改名称成功"}, "totalProfit": "跟单总利润", "transactionHash": "交易哈希", "type": "类型", "unrealizedProfit": "未实现利润", "volume": "成交额", "walletFollow": "钱包跟单", "warning": {"login": "请先登录才能关注此钱包。"}, "winRate": "跟单胜率"}, "walletDetail": {"activity": {"actions": "行动", "age": "持有时间", "amount": "金额", "buy": "买入", "pnl": "盈亏", "price": "价格", "quantity": "数量", "sell": "卖出", "time": "时间", "title": "交易记录", "token": "代币", "type": "类型", "volume": "交易量"}, "activityTable": {"actions": "操作", "date": "时间", "finalPrice": "成交价", "pnl": "盈亏", "type": "类型", "volume1": "成交额", "volume2": "成交量"}, "analysis": {"avgDuration": "{{period}} 平均持有时长", "balance": "钱包余额", "markPrice": "标记价格", "pnl": "近{{period}}盈亏", "tokenAvgCost": "{{period}}买入总成本", "tokenAvgRealizedProfits": "{{period}}代币平均实现利润", "totalCost": "{{period}}买入总成本", "txs": "{{period}}交易数"}, "averageHoldingTime": "平均持仓时间", "btn": {"follow": "关注", "unfollow": "已关注"}, "distribution": {"priceRange": "代币涨幅区间", "profit": "盈利", "title": "近{{period}}盈利分布", "tradedAmount": "交易代币数量"}, "focus": "盈利", "holderTable": {"amount": "持仓数量", "avgBuyPrice": "平均买价", "avgSellPrice": "平均卖价", "holding": "持仓价值", "holdingLength": "持仓时长", "lastActive": "最后活跃", "positionPercentage": "持仓占比", "realized": "已实现利润", "totalBuy": "总买入", "totalProfit": "总利润", "totalSell": "总卖出", "txCount": "交易数", "unrealized": "未实现利润"}, "holdings": {"actions": "行动", "askPrice": "卖出价", "avgBuyPrice": "平均买价", "avgSellPrice": "平均卖价", "balance": "余额", "bidPrice": "买入价", "boughtAmount": "买入数量", "holdingDuration": "持仓时长", "holdingRate": "持仓比例", "holdingValue": "持仓价值", "lastActive": "上次活跃时间", "positions": "持仓数量", "realizedPnL": "已实现盈亏", "souldAmount": "卖出数量", "title": "持币代币", "token": "代币", "totalPnL": "总盈亏", "txs": "交易数", "unrealizedPnL": "未实现盈亏"}, "msg": {"flow": "关注成功！", "unflow": "取消关注成功！"}, "periodPnLWithPeriod": "{{period}} 盈亏", "profitDistribution": "利润分布", "realizedPnL": "已实现盈亏", "realizedProfitWithPeriod": "{{period}} 平均已实现利润", "summary": "币种汇总", "token": {"buy": "买入", "sell": "卖出"}, "tooltip": {"markPrice": "1D 币种平均买入成本=Σ最近1日买入总金额÷最近1日买入的币种数量（结果四舍五入保留两位小数）", "tokenAvgCost": "点击旁边的问号，出现弹窗提示：最近1D/7D/30D，买入代币花费的总成本。", "tokenAvgRealizedProfits": "点击旁边的问号，出现弹窗提示：最近1D/7D/30D，平均每个代币已售出部分实现的盈利（未计算未实现盈利）"}, "totalPnL": "总盈亏", "totalPurchaseCostWithPeriod": "{{period}} 总购买成本", "txsWithPeriod": "{{period}} 交易数", "unrealizedPnL": "未实现利润", "walletBalance": "钱包余额"}, "withdrawal": {"button": {"failed": "存入失败", "insufficientFunds": "资金不足", "processing": "提现 {{amount}} USDC，链上确认中", "success": "提现成功 {{amount}} USDC", "withdraw": "提现", "withdrawWithAmount": "提现 {{amount}} USDC"}, "errors": {"orderBookTransactionFailed": "交易失败"}, "estimatedTime": "预估时间 {{time}}min", "fromContractAccount": "从 您的合约账户", "insufficientFunds": "资金不足", "max": "最大", "modal": {"failed": "存入失败", "failureReason": "失败原因失败原因失败原因", "success": "提现成功"}, "networkMessage": "USDC将通过Arbitrum网络发送到您的钱包。提现预估5分钟内到账，手续费$1。", "title": "提现USDC", "toArbitrum": "至 Arbitrum", "transactionHistory": "资金记录"}}