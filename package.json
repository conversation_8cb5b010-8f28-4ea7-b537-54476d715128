{"name": "xbit-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3000 --host --open", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint --ext .js,.jsx,.ts,.tsx src", "format": "prettier --write .", "codegen": "graphql-codegen --config codegen.ts", "g": "node scripts/generateSheet.mjs"}, "dependencies": {"@apollo/client": "^3.11.3", "@apollographql/graphql-playground-react": "^1.7.42", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@ethersproject/transactions": "^5.8.0", "@fingerprintjs/fingerprintjs": "^4.6.2", "@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/typescript": "^4.0.9", "@graphql-codegen/typescript-react-apollo": "^4.3.0", "@graphql-codegen/typescript-resolvers": "^4.2.1", "@hookform/resolvers": "^4.1.0", "@jnwng/walletconnect-solana": "^0.3.0", "@msgpack/msgpack": "^3.1.1", "@nktkas/hyperliquid": "^0.17.4", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@rainbow-me/rainbowkit": "^2.2.4", "@reduxjs/toolkit": "^2.5.1", "@sentry/react": "^9.12.0", "@sentry/tracing": "^7.120.3", "@solana/spl-token": "^0.4.13", "@solana/wallet-adapter-base": "^0.9.23", "@solana/wallet-adapter-phantom": "^0.9.27", "@solana/wallet-adapter-react": "^0.15.35", "@solana/wallet-adapter-solflare": "^0.6.28", "@solana/wallet-adapter-trust": "^0.1.16", "@solana/wallet-adapter-walletconnect": "^0.1.16", "@solana/web3.js": "1", "@tanstack/react-query": "^5.66.7", "@tanstack/react-table": "^8.21.2", "@tanstack/react-virtual": "^3.13.6", "@wagmi/connectors": "^5.8.3", "@walletconnect/solana-adapter": "^0.0.6", "apexcharts": "^4.5.0", "axios": "^1.9.0", "bignumber.js": "^9.2.0", "buffer": "^6.0.3", "dayjs": "^1.11.13", "decimal.js": "^10.5.0", "embla-carousel-react": "^8.5.2", "ethers": "^6.13.5", "firebase": "^11.8.1", "framer-motion": "^12.6.2", "graphql": "^16.9.0", "graphql-playground-react": "^1.7.28", "graphql-ws": "^5.16.0", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.3", "i18next-http-backend": "^3.0.2", "input-otp": "^1.4.2", "jwt-decode": "^4.0.0", "lodash-es": "^4.17.21", "mqtt": "^5.8.0", "next-themes": "^0.4.4", "qr-code-styling": "^1.9.2", "qrcode.react": "^4.2.0", "rango-sdk": "^0.1.64", "rango-sdk-basic": "^0.1.64", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-device-detect": "^2.2.3", "react-dom": "^19.0.0", "react-grid-layout": "^1.5.1", "react-helmet": "^6.1.0", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-mobile-picker": "^1.1.2", "react-moveable": "^0.56.0", "react-number-format": "^5.4.4", "react-redux": "^9.2.0", "react-router-dom": "^7.1.5", "react-scroll-wheel-handler": "^2.2.0", "react-simple-wheel-picker": "^1.2.0", "react-slick": "^0.30.3", "react-use": "^17.6.0", "recharts": "^2.15.3", "redux-persist": "^6.0.0", "slick-carousel": "^1.8.1", "sonner": "^2.0.1", "styled-components": "^6.1.17", "uuidv4": "^6.2.13", "vaul": "^1.1.2", "viem": "2.x", "wagmi": "^2.14.16", "walletconnect": "^1.7.8", "yarn": "^1.22.22", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.19.0", "@faker-js/faker": "^9.5.0", "@tailwindcss/postcss": "^4.0.6", "@tailwindcss/vite": "^4.0.6", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/react-grid-layout": "^1.3.5", "@types/react-helmet": "^6.1.11", "@types/react-slick": "^0.23.13", "@types/redux-persist": "^4.3.1", "@vitejs/plugin-react-swc": "^3.5.0", "class-variance-authority": "^0.7.1", "clipboard": "^2.0.11", "clsx": "^2.1.1", "eslint": "^9.20.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.18", "fintech-number": "^0.0.4", "globals": "^15.14.0", "google-auth-library": "^9.15.1", "google-spreadsheet": "^4.1.4", "husky": "^9.1.7", "lucide-react": "^0.475.0", "prettier": "^3.5.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0", "vite-bundle-analyzer": "^0.20.1", "vite-plugin-node-polyfills": "^0.23.0"}}