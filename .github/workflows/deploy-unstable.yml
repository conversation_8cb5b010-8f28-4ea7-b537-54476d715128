name: Deploy unstable

on:
  push:
    branches: ['unstable']

  workflow_dispatch:

env:
  STAGE: unstable
  SERVICE_NAME: xbit-app-web

jobs:
  build:
    runs-on: [self-hosted, xbit]

    steps:
      - uses: actions/checkout@v4
      - name: Build docker image
        run: |
          cp .env.$STAGE .env
          /deploy/build.sh $SERVICE_NAME $STAGE.$GITHUB_RUN_ID $STAGE

  deploy:
    needs: build
    runs-on: [self-hosted, xbit]

    steps:
      - name: Run service
        run: |
          /deploy/deploy_to_k8s.sh $SERVICE_NAME $STAGE.$GITHUB_RUN_ID $STAGE
