<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/webp" href="/images/logo-xbit.webp" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    
    <!-- iOS Add to Home Screen -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="XBIT Platform" />

    <!-- iOS icons -->
    <link rel="apple-touch-icon" href="/images/logo-xbit-rounded.webp" />
    <link rel="apple-touch-icon" sizes="152x152" href="/images/logo-xbit-rounded.webp" />
    <link rel="apple-touch-icon" sizes="180x180" href="/logo-xbit-rounded.webp" />
    <link rel="apple-touch-icon" sizes="167x167" href="/logo-xbit-rounded.webp" />

    <!-- Web App Manifest -->
    <link rel="manifest" href="/manifest.json" />
    <title>XBIT Platform</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600&display=swap"
      rel="stylesheet"
    />
    <script src="/charting_library/charting_library.standalone.js"></script>
    <script src="/datafeeds/udf/dist/bundle.js"></script>
    <script src="/scripts/preload.js"></script>
    <style lang="css">
      body {
        background-color: #121214;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
